<?php
session_start();
require_once 'config.php';
require_once 'includes/security.php';
require_once 'includes/helpers.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header('Location: index.php');
    exit;
}

// Include permissions class
require_once 'lib/Permissions.php';
$permissions = new Permissions($conn, $_SESSION['user_id']);

// Initialize variables
$success_message = '';
$error_message = '';
$user_id = $_SESSION['user_id'];

// Get user data
$stmt = $conn->prepare("SELECT * FROM users WHERE id = ?");
$stmt->bind_param("i", $user_id);
$stmt->execute();
$result = $stmt->get_result();
$user = $result->fetch_assoc();

if (!$user) {
    header('Location: logout.php');
    exit;
}



// Helper functions
function updateProfile($conn, $user_id, $post_data, $files) {
    $first_name = sanitize($post_data['first_name'] ?? '');
    $last_name = sanitize($post_data['last_name'] ?? '');
    $username = sanitize($post_data['username'] ?? '');
    $email = sanitize($post_data['email'] ?? '');

    if (empty($username) || empty($email)) {
        return "Username and email are required.";
    }

    // Check if username is already taken
    $stmt = $conn->prepare("SELECT id FROM users WHERE username = ? AND id != ?");
    $stmt->bind_param("si", $username, $user_id);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result->num_rows > 0) {
        return "Username is already taken.";
    }

    // Check if email is already taken
    $stmt = $conn->prepare("SELECT id FROM users WHERE email = ? AND id != ?");
    $stmt->bind_param("si", $email, $user_id);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result->num_rows > 0) {
        return "Email is already taken.";
    }

    // Handle profile image upload
    $profile_image = null;
    if (isset($files['profile_image']) && $files['profile_image']['error'] === UPLOAD_ERR_OK) {
        $upload_result = handleProfileImageUpload($files['profile_image']);
        if ($upload_result['success']) {
            $profile_image = $upload_result['filename'];
        } else {
            return $upload_result['error'];
        }
    }

    // Update user profile
    if ($profile_image) {
        $stmt = $conn->prepare("UPDATE users SET first_name = ?, last_name = ?, username = ?, email = ?, profile_image = ? WHERE id = ?");
        $stmt->bind_param("sssssi", $first_name, $last_name, $username, $email, $profile_image, $user_id);
    } else {
        $stmt = $conn->prepare("UPDATE users SET first_name = ?, last_name = ?, username = ?, email = ? WHERE id = ?");
        $stmt->bind_param("ssssi", $first_name, $last_name, $username, $email, $user_id);
    }

    if ($stmt->execute()) {
        $_SESSION['username'] = $username;

        // Log the activity
        require_once 'includes/admin-functions.php';
        log_activity('update', 'Updated profile information', $user_id);

        return '';
    } else {
        return "Error updating profile: " . $conn->error;
    }
}

function changePassword($conn, $user_id, $post_data) {
    $current_password = $post_data['current_password'] ?? '';
    $new_password = $post_data['new_password'] ?? '';
    $confirm_password = $post_data['confirm_password'] ?? '';

    if (empty($current_password) || empty($new_password) || empty($confirm_password)) {
        return "All password fields are required.";
    }

    if ($new_password !== $confirm_password) {
        return "New passwords do not match.";
    }

    if (strlen($new_password) < 8) {
        return "Password must be at least 8 characters long.";
    }

    // Verify current password
    $stmt = $conn->prepare("SELECT password FROM users WHERE id = ?");
    $stmt->bind_param("i", $user_id);
    $stmt->execute();
    $result = $stmt->get_result();
    $user = $result->fetch_assoc();

    if (!password_verify($current_password, $user['password'])) {
        return "Current password is incorrect.";
    }

    // Update password
    $hashed_password = password_hash($new_password, PASSWORD_DEFAULT);
    $stmt = $conn->prepare("UPDATE users SET password = ? WHERE id = ?");
    $stmt->bind_param("si", $hashed_password, $user_id);

    if ($stmt->execute()) {
        // Log the activity
        require_once 'includes/admin-functions.php';
        log_activity('update', 'Changed password', $user_id);

        return '';
    } else {
        return "Error updating password: " . $conn->error;
    }
}



function handleProfileImageUpload($file) {
    $upload_dir = '../uploads/profiles/';

    // Create directory if it doesn't exist
    if (!is_dir($upload_dir)) {
        mkdir($upload_dir, 0755, true);
    }

    // Validate file
    $allowed_types = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'];
    if (!in_array($file['type'], $allowed_types)) {
        return ['success' => false, 'error' => 'Invalid file type. Only JPG, PNG, and GIF are allowed.'];
    }

    if ($file['size'] > 2097152) { // 2MB
        return ['success' => false, 'error' => 'File size too large. Maximum size is 2MB.'];
    }

    // Generate unique filename
    $extension = pathinfo($file['name'], PATHINFO_EXTENSION);
    $filename = uniqid('profile_') . '.' . $extension;
    $filepath = $upload_dir . $filename;

    if (move_uploaded_file($file['tmp_name'], $filepath)) {
        return ['success' => true, 'filename' => $filename];
    } else {
        return ['success' => false, 'error' => 'Failed to upload file.'];
    }
}

// Process form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Validate CSRF token
    if (!isset($_POST['csrf_token']) || !validate_csrf_token($_POST['csrf_token'])) {
        $error_message = "Security validation failed. Please try again.";
    } else {
        $action = $_POST['action'] ?? '';

        switch ($action) {
            case 'update_profile':
                $error_message = updateProfile($conn, $user_id, $_POST, $_FILES);
                if (empty($error_message)) {
                    $success_message = "Profile updated successfully!";
                    // Refresh user data
                    $stmt = $conn->prepare("SELECT * FROM users WHERE id = ?");
                    $stmt->bind_param("i", $user_id);
                    $stmt->execute();
                    $result = $stmt->get_result();
                    $user = $result->fetch_assoc();

                    // Set flag to update dropdown avatar
                    $profile_updated = true;
                }
                break;

            case 'change_password':
                $error_message = changePassword($conn, $user_id, $_POST);
                if (empty($error_message)) {
                    $success_message = "Password changed successfully!";
                }
                break;
        }
    }
}
// Set page title for header
$page_title = 'Profile Settings';

// Include header
include 'includes/header.php';
?>

    <div class="admin-container">
        <div class="admin-content">
            <div class="admin-content-header">
                <div class="admin-content-title-group">
                    <h2 class="admin-content-title">
                        <i class="fas fa-user-circle"></i>
                        Profile Settings
                    </h2>
                    <p class="admin-content-subtitle">Manage your account information and preferences</p>
                </div>
            </div>

            <?php if ($success_message): ?>
                <div class="alert alert-success">
                    <i class="fas fa-check-circle"></i>
                    <?php echo htmlspecialchars($success_message); ?>
                </div>
            <?php endif; ?>

            <?php if ($error_message): ?>
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-circle"></i>
                    <?php echo htmlspecialchars($error_message); ?>
                </div>
            <?php endif; ?>

            <div class="profile-container">
                <!-- Profile Overview Card -->
                <div class="admin-card profile-overview-card">
                    <div class="admin-card-header">
                        <div class="profile-overview">
                            <div class="profile-avatar-section">
                                <?php if (!empty($user['profile_image'])): ?>
                                    <img src="../uploads/profiles/<?php echo htmlspecialchars($user['profile_image']); ?>" alt="Profile Picture" class="profile-avatar-img">
                                <?php else: ?>
                                    <div class="profile-avatar-placeholder">
                                        <i class="fas fa-user"></i>
                                    </div>
                                <?php endif; ?>
                            </div>
                            <div class="profile-info-section">
                                <h2 class="profile-display-name"><?php echo htmlspecialchars($user['username']); ?></h2>
                                <p class="profile-display-email"><?php echo htmlspecialchars($user['email']); ?></p>
                                <div class="profile-badges">
                                    <span class="admin-badge admin-badge-primary">
                                        <i class="fas fa-shield-alt"></i>
                                        Administrator
                                    </span>
                                    <span class="admin-badge admin-badge-secondary">
                                        <i class="fas fa-calendar"></i>
                                        Joined <?php echo date('M Y', strtotime($user['created_at'])); ?>
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Profile Tabs Card -->
                <div class="admin-card profile-tabs-card">
                    <!-- Tab Navigation -->
                <div class="admin-tabs">
                    <div class="admin-tab-nav">
                        <button class="admin-tab-btn active" data-tab="profile">
                            <i class="fas fa-user"></i>
                            Profile Information
                        </button>
                        <button class="admin-tab-btn" data-tab="security">
                            <i class="fas fa-lock"></i>
                            Security
                        </button>
                    </div>

                    <!-- Tab Content -->
                    <div class="admin-tab-content">
                        <!-- Profile Information Tab -->
                        <div class="admin-tab-pane active" id="profile">
                            <form method="post" enctype="multipart/form-data" class="admin-form">
                                <input type="hidden" name="csrf_token" value="<?php echo generate_csrf_token(); ?>">
                                <input type="hidden" name="action" value="update_profile">

                                <div class="admin-form-section">
                                    <h3 class="admin-section-title">
                                        <i class="fas fa-user"></i>
                                        Basic Information
                                    </h3>

                                    <div class="admin-form-grid admin-form-grid-2">
                                        <div class="admin-form-group">
                                            <label for="first_name" class="admin-form-label">First Name</label>
                                            <input type="text" id="first_name" name="first_name" class="admin-form-input" value="<?php echo htmlspecialchars($user['first_name'] ?? ''); ?>">
                                        </div>

                                        <div class="admin-form-group">
                                            <label for="last_name" class="admin-form-label">Last Name</label>
                                            <input type="text" id="last_name" name="last_name" class="admin-form-input" value="<?php echo htmlspecialchars($user['last_name'] ?? ''); ?>">
                                        </div>
                                    </div>

                                    <div class="admin-form-grid admin-form-grid-2">
                                        <div class="admin-form-group">
                                            <label for="username" class="admin-form-label">Username</label>
                                            <input type="text" id="username" name="username" class="admin-form-input" value="<?php echo htmlspecialchars($user['username']); ?>" required>
                                        </div>

                                        <div class="admin-form-group">
                                            <label for="email" class="admin-form-label">Email Address</label>
                                            <input type="email" id="email" name="email" class="admin-form-input" value="<?php echo htmlspecialchars($user['email']); ?>" required>
                                        </div>
                                    </div>
                                </div>

                                <div class="admin-form-section">
                                    <h3 class="admin-section-title">
                                        <i class="fas fa-image"></i>
                                        Profile Picture
                                    </h3>

                                    <div class="admin-form-group">
                                        <label for="profile_image" class="admin-form-label">Upload New Picture</label>
                                        <input type="file" id="profile_image" name="profile_image" class="admin-form-input" accept="image/*">
                                        <div class="admin-form-hint">JPG, PNG, or GIF. Maximum size: 2MB</div>
                                        <div id="image-preview" style="display: none; margin-top: 10px;">
                                            <img id="preview-img" src="" alt="Preview" style="max-width: 150px; max-height: 150px; border-radius: 8px;">
                                        </div>
                                    </div>
                                </div>

                                <div class="admin-form-actions">
                                    <button type="submit" class="admin-btn admin-btn-primary">
                                        <i class="fas fa-save"></i>
                                        Update Profile
                                    </button>
                                </div>
                            </form>
                        </div>

                        <!-- Security Tab -->
                        <div class="admin-tab-pane" id="security">
                            <form method="post" class="admin-form">
                                <input type="hidden" name="csrf_token" value="<?php echo generate_csrf_token(); ?>">
                                <input type="hidden" name="action" value="change_password">

                                <div class="admin-form-section">
                                    <h3 class="admin-section-title">
                                        <i class="fas fa-lock"></i>
                                        Change Password
                                    </h3>

                                    <div class="admin-form-group">
                                        <label for="current_password" class="admin-form-label">Current Password</label>
                                        <input type="password" id="current_password" name="current_password" class="admin-form-input" required>
                                    </div>

                                    <div class="admin-form-grid admin-form-grid-2">
                                        <div class="admin-form-group">
                                            <label for="new_password" class="admin-form-label">New Password</label>
                                            <input type="password" id="new_password" name="new_password" class="admin-form-input" required minlength="8">
                                            <div class="admin-form-hint">Minimum 8 characters</div>
                                        </div>

                                        <div class="admin-form-group">
                                            <label for="confirm_password" class="admin-form-label">Confirm New Password</label>
                                            <input type="password" id="confirm_password" name="confirm_password" class="admin-form-input" required minlength="8">
                                        </div>
                                    </div>
                                </div>

                                <div class="admin-form-actions">
                                    <button type="submit" class="admin-btn admin-btn-primary">
                                        <i class="fas fa-key"></i>
                                        Change Password
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    </div>

    <script>
        // Enhanced Tab functionality with animations
        document.addEventListener('DOMContentLoaded', function() {
            const tabBtns = document.querySelectorAll('.admin-tab-btn');
            const tabPanes = document.querySelectorAll('.admin-tab-pane');
            const tabNav = document.querySelector('.admin-tab-nav');

            // Update tab indicator position
            function updateTabIndicator() {
                const activeBtn = document.querySelector('.admin-tab-btn.active');
                if (activeBtn && tabNav) {
                    const btnIndex = Array.from(tabBtns).indexOf(activeBtn);
                    const indicator = tabNav.querySelector('::after');
                    // The CSS handles the animation via the :has() selector
                }
            }

            tabBtns.forEach((btn, index) => {
                btn.addEventListener('click', function() {
                    const targetTab = this.getAttribute('data-tab');

                    // Remove active class from all tabs and panes
                    tabBtns.forEach(b => b.classList.remove('active'));
                    tabPanes.forEach(p => p.classList.remove('active'));

                    // Add active class to clicked tab and corresponding pane
                    this.classList.add('active');
                    const targetPane = document.getElementById(targetTab);
                    if (targetPane) {
                        targetPane.classList.add('active');
                    }

                    // Update indicator position
                    updateTabIndicator();

                    // Add a subtle animation effect
                    this.style.transform = 'scale(0.95)';
                    setTimeout(() => {
                        this.style.transform = '';
                    }, 150);
                });
            });

            // Initialize indicator position
            updateTabIndicator();

            // Profile image preview
            const profileImageInput = document.getElementById('profile_image');
            const imagePreview = document.getElementById('image-preview');
            const previewImg = document.getElementById('preview-img');

            if (profileImageInput) {
                profileImageInput.addEventListener('change', function(e) {
                    const file = e.target.files[0];

                    if (file) {
                        // Validate file type
                        const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'];
                        if (!allowedTypes.includes(file.type)) {
                            alert('Please select a valid image file (JPG, PNG, or GIF)');
                            e.target.value = '';
                            imagePreview.style.display = 'none';
                            return;
                        }

                        // Validate file size (2MB)
                        if (file.size > 2097152) {
                            alert('File size must be less than 2MB');
                            e.target.value = '';
                            imagePreview.style.display = 'none';
                            return;
                        }

                        // Show preview
                        const reader = new FileReader();
                        reader.onload = function(e) {
                            previewImg.src = e.target.result;
                            imagePreview.style.display = 'block';
                        };
                        reader.readAsDataURL(file);
                    } else {
                        imagePreview.style.display = 'none';
                    }
                });
            }

            // Password confirmation validation
            const newPassword = document.getElementById('new_password');
            const confirmPassword = document.getElementById('confirm_password');

            if (newPassword && confirmPassword) {
                function validatePasswords() {
                    if (newPassword.value !== confirmPassword.value) {
                        confirmPassword.setCustomValidity('Passwords do not match');
                    } else {
                        confirmPassword.setCustomValidity('');
                    }
                }

                newPassword.addEventListener('input', validatePasswords);
                confirmPassword.addEventListener('input', validatePasswords);
            }

            <?php if (isset($profile_updated) && $profile_updated && !empty($user['profile_image'])): ?>
            // Update dropdown avatar after successful profile picture upload
            function updateDropdownAvatar() {
                const newImageSrc = '../uploads/profiles/<?php echo htmlspecialchars($user['profile_image']); ?>';

                // Update dropdown avatar
                const dropdownAvatar = document.querySelector('.user-dropdown-avatar');
                if (dropdownAvatar) {
                    const existingImg = dropdownAvatar.querySelector('.avatar-img-large');
                    const placeholder = dropdownAvatar.querySelector('.avatar-placeholder-large');

                    if (existingImg) {
                        // Update existing image
                        existingImg.src = newImageSrc;
                    } else if (placeholder) {
                        // Replace placeholder with image
                        dropdownAvatar.innerHTML = '<img src="' + newImageSrc + '" alt="<?php echo htmlspecialchars($_SESSION['username']); ?>" class="avatar-img-large">';
                    }
                }

                // Update user button avatar
                const userAvatar = document.querySelector('.user-avatar');
                if (userAvatar) {
                    const existingImg = userAvatar.querySelector('.avatar-img');
                    const placeholder = userAvatar.querySelector('.avatar-placeholder');

                    if (existingImg) {
                        // Update existing image
                        existingImg.src = newImageSrc;
                    } else if (placeholder) {
                        // Replace placeholder with image
                        const statusSpan = userAvatar.querySelector('.avatar-status');
                        userAvatar.innerHTML = '<img src="' + newImageSrc + '" alt="<?php echo htmlspecialchars($_SESSION['username']); ?>" class="avatar-img">';
                        if (statusSpan) {
                            userAvatar.appendChild(statusSpan); // Re-add the status indicator
                        }
                    }
                }

                // Also update the profile page avatar if it exists
                const profileAvatar = document.querySelector('.profile-avatar .avatar-img');
                if (profileAvatar) {
                    profileAvatar.src = newImageSrc;
                }
            }

            // Call the function to update the avatar
            updateDropdownAvatar();
            <?php endif; ?>
        });
    </script>

<?php include 'includes/footer.php'; ?>