/**
 * Editor Fixes CSS
 *
 * Fixes for the editor layout and appearance
 */

/* Fix for multi-layer boxes behind content */
.simple-editor {
    background-color: #fff !important;
    z-index: 1;
    position: relative;
    height: calc(100vh - 300px) !important; /* Dynamic height based on viewport */
    min-height: 800px !important; /* Minimum height */
}

.simple-editor-toolbar {
    position: sticky;
    top: 0;
    z-index: 100;
    background-color: #f8f8f8;
}

/* Fix for editor content area to ensure scrolling */
.simple-editor-content {
    height: calc(100% - 40px) !important; /* Subtract toolbar height */
    overflow-y: auto !important;
    min-height: 600px !important; /* Increased minimum height */
}

/* Fix for admin card body to contain editor properly */
.admin-card-body {
    padding: 0;
    position: relative;
    overflow: visible;
}

/* Fix for admin card to have proper height */
.editor-layout .admin-card {
    box-shadow: 0 1px 3px rgba(0,0,0,0.1) !important;
    border: 1px solid #ddd !important;
    background-color: #fff !important;
    display: flex !important;
    flex-direction: column !important;
}

/* Make the admin card footer sticky at the bottom */
.admin-card-footer {
    position: sticky !important;
    bottom: 0 !important;
    background-color: #f8f9fa !important;
    z-index: 90 !important;
    box-shadow: 0 -1px 3px rgba(0,0,0,0.1) !important;
}

/* Fix for admin footer positioning */
.admin-footer {
    display: flex;
    justify-content: flex-start;
    padding: 15px 20px;
    background-color: #f8f9fa;
    border-top: 1px solid #e9ecef;
    margin-top: 20px;
}

.admin-footer-content {
    width: 100%;
    text-align: left;
}

.admin-footer-content p {
    margin: 0;
    color: #6c757d;
    font-size: 14px;
}

/* Unlock button styling */
.unlock-btn {
    margin-left: 10px !important;
    padding: 2px 8px !important;
    font-size: 11px !important;
    height: auto !important;
    min-width: auto !important;
    vertical-align: middle !important;
}

.file-info-right {
    display: flex !important;
    align-items: center !important;
    flex-wrap: wrap !important;
    gap: 10px !important;
}

/* Responsive styles */
@media (max-width: 768px) {
    .admin-footer {
        padding: 10px 15px;
    }

    .admin-footer-content p {
        font-size: 12px;
    }

    .simple-editor {
        height: 600px !important; /* Increased height for mobile view */
    }

    .file-info-right {
        margin-top: 5px !important;
    }

    .unlock-btn {
        margin-left: 0 !important;
        margin-top: 5px !important;
    }
}
