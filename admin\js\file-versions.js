/**
 * File Versions JavaScript
 *
 * Handles file version history functionality
 */

// Initialize version history
function initVersionHistory() {
    // Set up event listeners for version history modal
    const showVersionsBtn = document.getElementById('showVersionsBtn');
    const closeVersionsBtn = document.getElementById('closeVersionsBtn');
    const versionHistoryModal = document.getElementById('versionHistoryModal');

    if (showVersionsBtn && versionHistoryModal) {
        showVersionsBtn.addEventListener('click', function() {
            versionHistoryModal.style.display = 'block';
        });
    }

    if (closeVersionsBtn && versionHistoryModal) {
        closeVersionsBtn.addEventListener('click', function() {
            versionHistoryModal.style.display = 'none';
        });
    }

    // Close modal when clicking outside
    window.addEventListener('click', function(event) {
        if (event.target === versionHistoryModal) {
            versionHistoryModal.style.display = 'none';
        }
    });

    // Set up event listeners for version preview modal
    const closePreviewBtn = document.getElementById('closePreviewBtn');
    const closePreviewFooterBtn = document.getElementById('closePreviewFooterBtn');
    const versionPreviewModal = document.getElementById('versionPreviewModal');

    if (closePreviewBtn && versionPreviewModal) {
        closePreviewBtn.addEventListener('click', function() {
            versionPreviewModal.style.display = 'none';
        });
    }

    if (closePreviewFooterBtn && versionPreviewModal) {
        closePreviewFooterBtn.addEventListener('click', function() {
            versionPreviewModal.style.display = 'none';
        });
    }

    // Close preview modal when clicking outside
    window.addEventListener('click', function(event) {
        if (event.target === versionPreviewModal) {
            versionPreviewModal.style.display = 'none';
        }
    });
}

// Load version history
function loadVersionHistory() {
    if (currentFilePath) {
        getAllVersions(currentFilePath)
            .then(versions => {
                updateVersionHistory(versions);
            });
    }
}

// Get all versions of a file
function getAllVersions(filePath) {
    return fetch('ajax/frontend_editor.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
            'Accept': 'application/json'
        },
        body: 'action=get_all_versions&file_path=' + encodeURIComponent(filePath)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            return data.versions;
        } else {
            console.error('Failed to get versions:', data.message);
            return [];
        }
    })
    .catch(error => {
        console.error('Error getting versions:', error);
        return [];
    });
}

// Get a specific version of a file
function getVersion(filePath, version) {
    return fetch('ajax/frontend_editor.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
            'Accept': 'application/json'
        },
        body: 'action=get_version&file_path=' + encodeURIComponent(filePath) + '&version=' + version
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            return data.version;
        } else {
            console.error('Failed to get version:', data.message);
            return null;
        }
    })
    .catch(error => {
        console.error('Error getting version:', error);
        return null;
    });
}

// Restore a specific version of a file
function restoreVersion(filePath, version) {
    return fetch('ajax/html_editor.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
            'Accept': 'application/json'
        },
        body: 'action=restore_version&file_path=' + encodeURIComponent(filePath) + '&version=' + version
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            return true;
        } else {
            console.error('Failed to restore version:', data.message);
            return false;
        }
    })
    .catch(error => {
        console.error('Error restoring version:', error);
        return false;
    });
}

// Update version history in UI
function updateVersionHistory(versions) {
    const versionHistoryTable = document.querySelector('#versionHistoryModal .table-responsive table tbody');
    const noVersionsMessage = document.querySelector('#versionHistoryModal .alert-info');

    if (versionHistoryTable) {
        // Clear existing rows
        versionHistoryTable.innerHTML = '';

        if (versions && versions.length > 0) {
            // Hide no versions message
            if (noVersionsMessage) {
                noVersionsMessage.style.display = 'none';
            }

            // Add rows for each version
            versions.forEach(version => {
                const row = document.createElement('tr');

                // Version number
                const versionCell = document.createElement('td');
                versionCell.textContent = version.version;
                row.appendChild(versionCell);

                // Date
                const dateCell = document.createElement('td');
                dateCell.textContent = new Date(version.created_at).toLocaleString();
                row.appendChild(dateCell);

                // User
                const userCell = document.createElement('td');
                userCell.textContent = version.username;
                row.appendChild(userCell);

                // Comment
                const commentCell = document.createElement('td');
                commentCell.textContent = version.comment || 'No comment';
                row.appendChild(commentCell);

                // Actions
                const actionsCell = document.createElement('td');

                // View button
                const viewButton = document.createElement('button');
                viewButton.type = 'button';
                viewButton.className = 'btn btn-sm btn-outline-primary view-version-btn';
                viewButton.dataset.version = version.version;
                viewButton.innerHTML = '<i class="fas fa-eye"></i> View';
                viewButton.addEventListener('click', function() {
                    viewVersion(currentFilePath, version.version);
                });
                actionsCell.appendChild(viewButton);

                // Restore button
                const restoreButton = document.createElement('button');
                restoreButton.type = 'button';
                restoreButton.className = 'btn btn-sm btn-outline-success restore-version-btn';
                restoreButton.dataset.version = version.version;
                restoreButton.innerHTML = '<i class="fas fa-undo"></i> Restore';
                restoreButton.addEventListener('click', function() {
                    if (confirm('Are you sure you want to restore this version? This will overwrite the current file.')) {
                        restoreVersion(currentFilePath, version.version).then(success => {
                            if (success) {
                                alert('Version restored successfully.');
                                window.location.reload();
                            }
                        });
                    }
                });
                actionsCell.appendChild(restoreButton);

                row.appendChild(actionsCell);

                versionHistoryTable.appendChild(row);
            });
        } else {
            // Show no versions message
            if (noVersionsMessage) {
                noVersionsMessage.style.display = 'block';
            }
        }
    }
}

// View a specific version
function viewVersion(filePath, version) {
    getVersion(filePath, version).then(versionData => {
        if (versionData) {
            // Update preview modal
            document.getElementById('previewVersion').textContent = versionData.version;
            document.getElementById('previewDate').textContent = new Date(versionData.created_at).toLocaleString();
            document.getElementById('previewUser').textContent = versionData.username;
            document.getElementById('previewComment').textContent = versionData.comment || 'No comment';

            // Update preview content
            const previewContent = document.getElementById('previewContent');
            previewContent.textContent = versionData.content;

            // Set up restore button
            const restorePreviewBtn = document.getElementById('restorePreviewBtn');
            if (restorePreviewBtn) {
                restorePreviewBtn.dataset.version = versionData.version;
                restorePreviewBtn.addEventListener('click', function() {
                    if (confirm('Are you sure you want to restore this version? This will overwrite the current file.')) {
                        restoreVersion(filePath, versionData.version).then(success => {
                            if (success) {
                                alert('Version restored successfully.');
                                window.location.reload();
                            }
                        });
                    }
                });
            }

            // Show preview modal
            document.getElementById('versionPreviewModal').style.display = 'block';
        }
    });
}
