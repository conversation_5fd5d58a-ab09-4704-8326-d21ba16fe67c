<?php
/**
 * XML Sitemap Generator
 * This script generates an XML sitemap for the website
 */

// Include database configuration
require_once 'admin/config.php';

// Set the content type to XML
header('Content-Type: application/xml; charset=utf-8');

// Start the XML document
echo '<?xml version="1.0" encoding="UTF-8"?>' . PHP_EOL;
echo '<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">' . PHP_EOL;

// Get the base URL
$protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
$host = $_SERVER['HTTP_HOST'];
$baseUrl = $protocol . '://' . $host;

// Add static pages
$staticPages = [
    '' => '1.0', // Homepage
    'news.html' => '0.8',
    'about-us.html' => '0.7',
    'contact-us.html' => '0.7',
    'services.html' => '0.7',
    'projects.html' => '0.7'
];

foreach ($staticPages as $page => $priority) {
    $url = $baseUrl . '/' . $page;
    $lastmod = date('Y-m-d');
    
    echo '  <url>' . PHP_EOL;
    echo '    <loc>' . htmlspecialchars($url) . '</loc>' . PHP_EOL;
    echo '    <lastmod>' . $lastmod . '</lastmod>' . PHP_EOL;
    echo '    <changefreq>weekly</changefreq>' . PHP_EOL;
    echo '    <priority>' . $priority . '</priority>' . PHP_EOL;
    echo '  </url>' . PHP_EOL;
}

// Add news articles
if ($conn) {
    $sql = "SELECT slug, updated_at FROM news WHERE status = 'published' ORDER BY updated_at DESC";
    $result = $conn->query($sql);
    
    if ($result && $result->num_rows > 0) {
        while ($row = $result->fetch_assoc()) {
            $url = $baseUrl . '/' . $row['slug'] . '.html';
            $lastmod = date('Y-m-d', strtotime($row['updated_at']));
            
            echo '  <url>' . PHP_EOL;
            echo '    <loc>' . htmlspecialchars($url) . '</loc>' . PHP_EOL;
            echo '    <lastmod>' . $lastmod . '</lastmod>' . PHP_EOL;
            echo '    <changefreq>monthly</changefreq>' . PHP_EOL;
            echo '    <priority>0.6</priority>' . PHP_EOL;
            echo '  </url>' . PHP_EOL;
        }
    }
    
    // Close the database connection
    $conn->close();
}

// End the XML document
echo '</urlset>';

// Save the sitemap to a file
$sitemapContent = ob_get_contents();
file_put_contents('sitemap.xml', $sitemapContent);
?>
