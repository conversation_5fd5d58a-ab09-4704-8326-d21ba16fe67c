<?php
/**
 * AJAX Handler for User Settings Updates
 *
 * This file handles AJAX requests to update user settings.
 */

// Start session and include config directly to avoid HTML output
session_start();
require_once '../config.php';

// Set JSON content type immediately
header('Content-Type: application/json');

// Clean any output buffer
while (ob_get_level()) {
    ob_end_clean();
}

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    echo json_encode(['success' => false, 'message' => 'Not authorized']);
    exit;
}

// Get user ID
$user_id = $_SESSION['user_id'];

// Check if action is set
if (!isset($_POST['action'])) {
    echo json_encode(['success' => false, 'message' => 'No action specified']);
    exit;
}

// Handle different actions
$action = $_POST['action'];

// Update sidebar preference
if ($action === 'update_sidebar_preference') {
    // Check if sidebar_collapsed is set
    if (!isset($_POST['sidebar_collapsed'])) {
        echo json_encode(['success' => false, 'message' => 'No sidebar_collapsed value provided']);
        exit;
    }

    // Get sidebar_collapsed value
    $sidebar_collapsed = (int) $_POST['sidebar_collapsed'];

    // Check if user_settings table exists
    $check_table = $conn->query("SHOW TABLES LIKE 'user_settings'");
    $user_settings_table_exists = ($check_table && $check_table->num_rows > 0);

    if ($user_settings_table_exists) {
        // Check if the table has setting_key and setting_value columns
        $check_column_key = $conn->query("SHOW COLUMNS FROM user_settings LIKE 'setting_key'");
        $check_column_value = $conn->query("SHOW COLUMNS FROM user_settings LIKE 'setting_value'");

        $has_key_value_structure = ($check_column_key && $check_column_key->num_rows > 0 &&
                                   $check_column_value && $check_column_value->num_rows > 0);

        if ($has_key_value_structure) {
            // Check if setting already exists
            $check_setting = $conn->prepare("SELECT id FROM user_settings WHERE user_id = ? AND setting_key = 'sidebar_collapsed'");
            $check_setting->bind_param('i', $user_id);
            $check_setting->execute();
            $result = $check_setting->get_result();

            if ($result->num_rows > 0) {
                // Update existing setting
                $update = $conn->prepare("UPDATE user_settings SET setting_value = ? WHERE user_id = ? AND setting_key = 'sidebar_collapsed'");
                $update->bind_param('si', $sidebar_collapsed, $user_id);
                $success = $update->execute();
            } else {
                // Insert new setting
                $insert = $conn->prepare("INSERT INTO user_settings (user_id, setting_key, setting_value) VALUES (?, 'sidebar_collapsed', ?)");
                $insert->bind_param('is', $user_id, $sidebar_collapsed);
                $success = $insert->execute();
            }
        } else {
            // Table has direct column names
            // Check if sidebar_collapsed column exists
            $check_column = $conn->query("SHOW COLUMNS FROM user_settings LIKE 'sidebar_collapsed'");
            $column_exists = ($check_column && $check_column->num_rows > 0);

            if (!$column_exists) {
                // Add column if it doesn't exist
                $conn->query("ALTER TABLE user_settings ADD COLUMN sidebar_collapsed TINYINT(1) DEFAULT 0");
            }

            // Check if user has a row in the table
            $check_user = $conn->prepare("SELECT id FROM user_settings WHERE user_id = ?");
            $check_user->bind_param('i', $user_id);
            $check_user->execute();
            $result = $check_user->get_result();

            if ($result->num_rows > 0) {
                // Update existing row
                $update = $conn->prepare("UPDATE user_settings SET sidebar_collapsed = ? WHERE user_id = ?");
                $update->bind_param('ii', $sidebar_collapsed, $user_id);
                $success = $update->execute();
            } else {
                // Insert new row
                $insert = $conn->prepare("INSERT INTO user_settings (user_id, sidebar_collapsed) VALUES (?, ?)");
                $insert->bind_param('ii', $user_id, $sidebar_collapsed);
                $success = $insert->execute();
            }
        }
    } else {
        // Check if sidebar_collapsed column exists in users table
        $check_column = $conn->query("SHOW COLUMNS FROM users LIKE 'sidebar_collapsed'");
        $column_exists = ($check_column && $check_column->num_rows > 0);

        if (!$column_exists) {
            // Add column if it doesn't exist
            $conn->query("ALTER TABLE users ADD COLUMN sidebar_collapsed TINYINT(1) DEFAULT 0");
        }

        // Update users table
        $update = $conn->prepare("UPDATE users SET sidebar_collapsed = ? WHERE id = ?");
        $update->bind_param('ii', $sidebar_collapsed, $user_id);
        $success = $update->execute();
    }

    // Return response
    if (isset($success) && $success) {
        echo json_encode(['success' => true, 'message' => 'Sidebar preference updated']);
    } else {
        echo json_encode(['success' => false, 'message' => 'Failed to update sidebar preference']);
    }
    exit;
}

// Default response for unknown action
echo json_encode(['success' => false, 'message' => 'Unknown action']);
exit;
