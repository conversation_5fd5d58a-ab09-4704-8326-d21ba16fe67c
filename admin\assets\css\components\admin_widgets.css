/**
 * Admin Widgets CSS
 * 
 * This file contains styles for dashboard widgets and info boxes in the admin panel.
 */

/* Base Widget */
.widget {
  background-color: var(--white);
  border-radius: var(--card-border-radius);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--border-color);
  overflow: hidden;
  transition: box-shadow var(--transition-fast) ease, transform var(--transition-fast) ease;
  margin-bottom: var(--spacing-4);
}

.widget:hover {
  box-shadow: var(--shadow-md);
}

/* Widget Header */
.widget-header {
  padding: var(--spacing-4);
  border-bottom: 1px solid var(--border-color);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.widget-title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--text-dark);
  margin: 0;
}

.widget-subtitle {
  font-size: var(--font-size-sm);
  color: var(--text-light);
  margin: var(--spacing-1) 0 0;
}

.widget-actions {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
}

/* Widget Body */
.widget-body {
  padding: var(--spacing-4);
}

/* Widget Footer */
.widget-footer {
  padding: var(--spacing-4);
  border-top: 1px solid var(--border-color);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.widget-footer-text {
  font-size: var(--font-size-sm);
  color: var(--text-light);
}

.widget-footer-actions {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
}

/* Stat Widget */
.stat-widget {
  padding: var(--spacing-4);
  display: flex;
  flex-direction: column;
}

.stat-widget-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--spacing-2);
}

.stat-widget-title {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--text-light);
  margin: 0;
}

.stat-widget-icon {
  width: 40px;
  height: 40px;
  border-radius: var(--radius-md);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--font-size-xl);
}

.stat-widget-icon.primary {
  background-color: var(--primary-very-light);
  color: var(--primary-color);
}

.stat-widget-icon.success {
  background-color: rgba(16, 185, 129, 0.1);
  color: var(--success-color);
}

.stat-widget-icon.warning {
  background-color: rgba(245, 158, 11, 0.1);
  color: var(--warning-color);
}

.stat-widget-icon.danger {
  background-color: rgba(239, 68, 68, 0.1);
  color: var(--danger-color);
}

.stat-widget-icon.info {
  background-color: rgba(59, 130, 246, 0.1);
  color: var(--info-color);
}

.stat-widget-value {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-bold);
  color: var(--text-dark);
  margin: var(--spacing-1) 0;
}

.stat-widget-change {
  font-size: var(--font-size-xs);
  display: flex;
  align-items: center;
  gap: var(--spacing-1);
}

.stat-widget-change.positive {
  color: var(--success-color);
}

.stat-widget-change.negative {
  color: var(--danger-color);
}

/* Info Widget */
.info-widget {
  display: flex;
  align-items: flex-start;
  gap: var(--spacing-3);
  padding: var(--spacing-4);
}

.info-widget-icon {
  width: 48px;
  height: 48px;
  border-radius: var(--radius-md);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--font-size-xl);
  flex-shrink: 0;
}

.info-widget-icon.primary {
  background-color: var(--primary-very-light);
  color: var(--primary-color);
}

.info-widget-icon.success {
  background-color: rgba(16, 185, 129, 0.1);
  color: var(--success-color);
}

.info-widget-icon.warning {
  background-color: rgba(245, 158, 11, 0.1);
  color: var(--warning-color);
}

.info-widget-icon.danger {
  background-color: rgba(239, 68, 68, 0.1);
  color: var(--danger-color);
}

.info-widget-icon.info {
  background-color: rgba(59, 130, 246, 0.1);
  color: var(--info-color);
}

.info-widget-content {
  flex: 1;
}

.info-widget-title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--text-dark);
  margin: 0 0 var(--spacing-1);
}

.info-widget-text {
  font-size: var(--font-size-sm);
  color: var(--text-light);
  margin: 0 0 var(--spacing-2);
}

.info-widget-actions {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  margin-top: var(--spacing-2);
}

/* Chart Widget */
.chart-widget {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.chart-widget-header {
  padding: var(--spacing-4);
  border-bottom: 1px solid var(--border-color);
}

.chart-widget-title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--text-dark);
  margin: 0 0 var(--spacing-1);
}

.chart-widget-subtitle {
  font-size: var(--font-size-sm);
  color: var(--text-light);
  margin: 0;
}

.chart-widget-body {
  flex: 1;
  padding: var(--spacing-4);
  display: flex;
  align-items: center;
  justify-content: center;
}

.chart-widget-container {
  width: 100%;
  height: 100%;
  min-height: 200px;
}

.chart-widget-legend {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-3);
  margin-top: var(--spacing-3);
}

.chart-widget-legend-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  font-size: var(--font-size-sm);
  color: var(--text-color);
}

.chart-widget-legend-color {
  width: 12px;
  height: 12px;
  border-radius: var(--radius-sm);
}

/* List Widget */
.list-widget {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.list-widget-header {
  padding: var(--spacing-4);
  border-bottom: 1px solid var(--border-color);
}

.list-widget-title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--text-dark);
  margin: 0;
}

.list-widget-body {
  flex: 1;
  overflow-y: auto;
}

.list-widget-item {
  display: flex;
  align-items: center;
  padding: var(--spacing-3) var(--spacing-4);
  border-bottom: 1px solid var(--border-light);
  transition: background-color var(--transition-fast) ease;
}

.list-widget-item:last-child {
  border-bottom: none;
}

.list-widget-item:hover {
  background-color: var(--gray-50);
}

.list-widget-item-content {
  flex: 1;
}

.list-widget-item-title {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--text-dark);
  margin: 0 0 var(--spacing-1);
}

.list-widget-item-subtitle {
  font-size: var(--font-size-xs);
  color: var(--text-light);
  margin: 0;
}

.list-widget-item-actions {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
}

.list-widget-footer {
  padding: var(--spacing-3) var(--spacing-4);
  border-top: 1px solid var(--border-color);
  text-align: center;
}

.list-widget-footer-link {
  font-size: var(--font-size-sm);
  color: var(--primary-color);
  text-decoration: none;
  transition: color var(--transition-fast) ease;
}

.list-widget-footer-link:hover {
  color: var(--primary-dark);
  text-decoration: underline;
}

/* Progress Widget */
.progress-widget {
  padding: var(--spacing-4);
}

.progress-widget-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--spacing-3);
}

.progress-widget-title {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--text-dark);
  margin: 0;
}

.progress-widget-value {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-semibold);
  color: var(--text-dark);
}

.progress-widget-bar {
  height: 8px;
  background-color: var(--gray-100);
  border-radius: var(--radius-full);
  overflow: hidden;
  margin-bottom: var(--spacing-2);
}

.progress-widget-fill {
  height: 100%;
  background-color: var(--primary-color);
  border-radius: var(--radius-full);
}

.progress-widget-fill.success {
  background-color: var(--success-color);
}

.progress-widget-fill.warning {
  background-color: var(--warning-color);
}

.progress-widget-fill.danger {
  background-color: var(--danger-color);
}

.progress-widget-text {
  font-size: var(--font-size-xs);
  color: var(--text-light);
  margin: 0;
}

/* Activity Widget */
.activity-widget {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.activity-widget-header {
  padding: var(--spacing-4);
  border-bottom: 1px solid var(--border-color);
}

.activity-widget-title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--text-dark);
  margin: 0;
}

.activity-widget-body {
  flex: 1;
  padding: var(--spacing-4);
  overflow-y: auto;
}

.activity-widget-timeline {
  position: relative;
  padding-left: var(--spacing-6);
}

.activity-widget-timeline::before {
  content: "";
  position: absolute;
  top: 0;
  bottom: 0;
  left: 9px;
  width: 2px;
  background-color: var(--border-color);
}

.activity-widget-item {
  position: relative;
  padding-bottom: var(--spacing-4);
}

.activity-widget-item:last-child {
  padding-bottom: 0;
}

.activity-widget-item::before {
  content: "";
  position: absolute;
  top: 0;
  left: -20px;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background-color: var(--white);
  border: 2px solid var(--primary-color);
  z-index: 1;
}

.activity-widget-item.success::before {
  border-color: var(--success-color);
}

.activity-widget-item.warning::before {
  border-color: var(--warning-color);
}

.activity-widget-item.danger::before {
  border-color: var(--danger-color);
}

.activity-widget-item.info::before {
  border-color: var(--info-color);
}

.activity-widget-time {
  font-size: var(--font-size-xs);
  color: var(--text-muted);
  margin-bottom: var(--spacing-1);
}

.activity-widget-content {
  font-size: var(--font-size-sm);
  color: var(--text-color);
}

.activity-widget-content strong {
  font-weight: var(--font-weight-medium);
  color: var(--text-dark);
}

/* Responsive Styles */
@media (max-width: 768px) {
  .widget-header,
  .widget-body,
  .widget-footer {
    padding: var(--spacing-3);
  }
  
  .stat-widget,
  .info-widget,
  .progress-widget {
    padding: var(--spacing-3);
  }
  
  .chart-widget-header,
  .chart-widget-body,
  .list-widget-header,
  .activity-widget-header,
  .activity-widget-body {
    padding: var(--spacing-3);
  }
  
  .info-widget {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .info-widget-icon {
    margin-bottom: var(--spacing-2);
  }
}
