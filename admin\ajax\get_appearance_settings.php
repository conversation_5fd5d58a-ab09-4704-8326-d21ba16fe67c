<?php
/**
 * Get Appearance Settings AJAX Handler
 *
 * This script retrieves appearance settings from the database.
 */

// Start output buffering to prevent any output before JSON
ob_start();

// Enable error logging but disable display
error_reporting(E_ALL);
ini_set('display_errors', 0);
ini_set('log_errors', 1);

// Set proper JSON headers
header('Content-Type: application/json');
header('Cache-Control: no-store, no-cache, must-revalidate, max-age=0');
header('Cache-Control: post-check=0, pre-check=0', false);
header('Pragma: no-cache');

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    echo json_encode([
        'success' => false,
        'message' => 'You must be logged in to perform this action'
    ]);
    exit;
}

// Wrap everything in a try-catch to prevent any errors from breaking JSON output
try {
    // Include database connection
    require_once '../config.php';

// Function to get appearance settings
function getAppearanceSettings($conn) {
    // Get all settings from the appearance category
    $sql = "SELECT setting_key, setting_value FROM system_settings WHERE category = 'appearance'";
    $result = $conn->query($sql);

    if (!$result) {
        return [
            'success' => false,
            'message' => 'Failed to retrieve appearance settings: ' . $conn->error
        ];
    }

    // Group settings by key
    $settings = [];
    while ($row = $result->fetch_assoc()) {
        $settings[$row['setting_key']] = $row['setting_value'];
    }

    // Set default values for missing settings
    $default_settings = [
        'primary_color' => '#f1ca2f',
        'secondary_color' => '#3c3c45',
        'accent_color' => '#4a90e2',
        'text_color' => '#333333',
        'background_color' => '#f5f5f5',
        'header_background_color' => '#ffffff',
        'sidebar_background_color' => '#3c3c45',
        'button_color' => '#f1ca2f',
        'button_text_color' => '#ffffff',
        'link_color' => '#4a90e2',
        'border_radius' => '4',
        'font_family' => 'Open Sans',
        'font_size' => '14',
        'custom_css' => ''
    ];

    // Merge default settings with database settings
    $settings = array_merge($default_settings, $settings);

    return [
        'success' => true,
        'settings' => $settings
    ];
}

// Get appearance settings
$result = getAppearanceSettings($conn);

// Return the result
echo json_encode($result, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);

} catch (Exception $e) {
    // Log the error
    error_log('Error in get_appearance_settings.php: ' . $e->getMessage());

    // Return a generic error message with default settings
    echo json_encode([
        'success' => false,
        'message' => 'An unexpected error occurred',
        'settings' => [
            'primary_color' => '#f1ca2f',
            'secondary_color' => '#3c3c45',
            'accent_color' => '#4a90e2',
            'text_color' => '#333333',
            'background_color' => '#f5f5f5',
            'header_background_color' => '#ffffff',
            'sidebar_background_color' => '#3c3c45',
            'button_color' => '#f1ca2f',
            'button_text_color' => '#ffffff',
            'link_color' => '#4a90e2',
            'border_radius' => '4',
            'font_family' => 'Open Sans',
            'font_size' => '14',
            'custom_css' => ''
        ]
    ], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
}

// Check for JSON encoding errors
if (json_last_error() !== JSON_ERROR_NONE) {
    // Clear any previous output
    ob_clean();

    // Return a simplified error response
    echo json_encode([
        'success' => false,
        'message' => 'JSON encoding error: ' . json_last_error_msg(),
        'settings' => [
            'primary_color' => '#f1ca2f',
            'secondary_color' => '#3c3c45',
            'accent_color' => '#4a90e2',
            'text_color' => '#333333',
            'background_color' => '#f5f5f5',
            'font_family' => 'Open Sans'
        ]
    ], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
}

// End output buffering and flush
ob_end_flush();
exit;
