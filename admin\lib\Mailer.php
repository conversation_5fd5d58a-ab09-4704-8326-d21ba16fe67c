<?php
/**
 * Mailer class for handling email functionality
 */
class Mailer {
    private $host;
    private $port;
    private $username;
    private $password;
    private $encryption;
    private $from_email;
    private $from_name;
    private $reply_to;
    private $use_smtp;
    private $conn;

    /**
     * Constructor
     *
     * @param object $conn Database connection
     */
    public function __construct($conn) {
        $this->conn = $conn;
        $this->loadSettings();
    }

    /**
     * Load email settings from database
     */
    private function loadSettings() {
        // First try to load from system_settings table (new structure)
        $settings = $this->loadFromSystemSettings();

        if ($settings) {
            $this->host = $settings['smtp_host'] ?? '';
            $this->port = $settings['smtp_port'] ?? 587;
            $this->username = $settings['smtp_username'] ?? '';
            $this->password = $settings['smtp_password'] ?? '';
            $this->encryption = $settings['smtp_security'] ?? 'tls'; // Note: smtp_security in system_settings
            $this->from_email = $settings['from_email'] ?? '';
            $this->from_name = $settings['from_name'] ?? '';
            $this->reply_to = $settings['reply_to'] ?? '';
            $this->use_smtp = ($settings['use_smtp'] ?? '0') == '1';

            error_log("Loaded email settings from system_settings table");
            return;
        }

        // Fallback to old email_settings table if system_settings doesn't have email settings
        $this->loadFromEmailSettings();
    }

    /**
     * Load settings from system_settings table
     */
    private function loadFromSystemSettings() {
        $email_keys = [
            'use_smtp', 'smtp_host', 'smtp_port', 'smtp_username',
            'smtp_password', 'smtp_security', 'from_email', 'from_name', 'reply_to'
        ];

        $placeholders = str_repeat('?,', count($email_keys) - 1) . '?';
        $sql = "SELECT setting_key, setting_value FROM system_settings WHERE setting_key IN ($placeholders)";
        $stmt = $this->conn->prepare($sql);
        $stmt->bind_param(str_repeat('s', count($email_keys)), ...$email_keys);
        $stmt->execute();
        $result = $stmt->get_result();

        $settings = [];
        while ($row = $result->fetch_assoc()) {
            $settings[$row['setting_key']] = $row['setting_value'];
        }

        // Return settings only if we have the essential ones
        if (isset($settings['from_email']) && isset($settings['from_name'])) {
            return $settings;
        }

        return null;
    }

    /**
     * Load settings from old email_settings table (fallback)
     */
    private function loadFromEmailSettings() {
        // Check if email_settings table exists
        $check_table = "SHOW TABLES LIKE 'email_settings'";
        $table_result = $this->conn->query($check_table);

        if ($table_result->num_rows == 0) {
            // Table doesn't exist, create it
            $this->createSettingsTable();
        }

        // Load settings
        $sql = "SELECT * FROM email_settings LIMIT 1";
        $result = $this->conn->query($sql);

        if ($result->num_rows > 0) {
            $settings = $result->fetch_assoc();
            $this->host = $settings['smtp_host'];
            $this->port = $settings['smtp_port'];
            $this->username = $settings['smtp_username'];
            $this->password = $settings['smtp_password'];
            $this->encryption = $settings['smtp_encryption'];
            $this->from_email = $settings['from_email'];
            $this->from_name = $settings['from_name'];
            $this->reply_to = $settings['reply_to'];
            $this->use_smtp = $settings['use_smtp'] == 1;

            error_log("Loaded email settings from email_settings table (fallback)");
        } else {
            // No settings found, insert default values
            $this->insertDefaultSettings();
            $this->loadFromEmailSettings(); // Reload settings
        }
    }

    /**
     * Create email_settings table
     */
    private function createSettingsTable() {
        $sql = "CREATE TABLE IF NOT EXISTS `email_settings` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `use_smtp` tinyint(1) NOT NULL DEFAULT 0,
            `smtp_host` varchar(255) NOT NULL DEFAULT '',
            `smtp_port` int(11) NOT NULL DEFAULT 587,
            `smtp_username` varchar(255) NOT NULL DEFAULT '',
            `smtp_password` varchar(255) NOT NULL DEFAULT '',
            `smtp_encryption` enum('tls','ssl','none') NOT NULL DEFAULT 'tls',
            `from_email` varchar(255) NOT NULL,
            `from_name` varchar(255) NOT NULL,
            `reply_to` varchar(255) DEFAULT NULL,
            `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
            `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;";

        $this->conn->query($sql);
    }

    /**
     * Insert default email settings
     */
    private function insertDefaultSettings() {
        $sql = "INSERT INTO `email_settings`
                (`use_smtp`, `smtp_host`, `smtp_port`, `smtp_username`, `smtp_password`,
                 `smtp_encryption`, `from_email`, `from_name`, `reply_to`)
                VALUES
                (0, 'smtp.example.com', 587, '<EMAIL>', 'password',
                 'tls', '<EMAIL>', 'Manage Inc.', '<EMAIL>')";

        $this->conn->query($sql);
    }

    /**
     * Save email settings
     *
     * @param array $settings Email settings
     * @return bool True if successful, false otherwise
     */
    public function saveSettings($settings) {
        // Convert boolean or string 'true'/'false' to integer 1/0
        $use_smtp = (isset($settings['use_smtp']) && ($settings['use_smtp'] === true || $settings['use_smtp'] === 1 || $settings['use_smtp'] === '1')) ? 1 : 0;
        $smtp_host = $this->conn->real_escape_string($settings['smtp_host']);
        $smtp_port = (int)$settings['smtp_port'];
        $smtp_username = $this->conn->real_escape_string($settings['smtp_username']);
        $smtp_password = $this->conn->real_escape_string($settings['smtp_password']);
        $smtp_encryption = $this->conn->real_escape_string($settings['smtp_encryption']);
        $from_email = $this->conn->real_escape_string($settings['from_email']);
        $from_name = $this->conn->real_escape_string($settings['from_name']);
        $reply_to = $this->conn->real_escape_string($settings['reply_to']);

        $sql = "UPDATE `email_settings` SET
                `use_smtp` = $use_smtp,
                `smtp_host` = '$smtp_host',
                `smtp_port` = $smtp_port,
                `smtp_username` = '$smtp_username',
                `smtp_password` = '$smtp_password',
                `smtp_encryption` = '$smtp_encryption',
                `from_email` = '$from_email',
                `from_name` = '$from_name',
                `reply_to` = '$reply_to'
                WHERE `id` = 1";

        $result = $this->conn->query($sql);

        if ($result) {
            // Reload settings
            $this->loadSettings();
            return true;
        } else {
            // Log the error for debugging
            error_log("Email settings update failed: " . $this->conn->error);
            error_log("SQL Query: " . $sql);
        }

        return false;
    }

    /**
     * Send email
     *
     * @param string $to Recipient email
     * @param string $subject Email subject
     * @param string $message Email message (HTML)
     * @param string $name Recipient name (optional)
     * @return array Result with status and message
     */
    public function sendEmail($to, $subject, $message, $name = '') {
        // Basic validation
        if (empty($to) || empty($subject) || empty($message)) {
            return [
                'success' => false,
                'message' => 'Missing required fields (to, subject, message)'
            ];
        }

        // Log the email attempt with more details
        error_log("===== EMAIL SENDING ATTEMPT =====");
        error_log("To: $to" . ($name ? " ($name)" : ""));
        error_log("Subject: $subject");
        error_log("From: {$this->from_name} <{$this->from_email}>");
        error_log("Reply-To: {$this->reply_to}");

        // Log email settings
        error_log("Email Settings:");
        error_log("- use_smtp: " . ($this->use_smtp ? 'true' : 'false'));
        if ($this->use_smtp) {
            error_log("- smtp_host: {$this->host}");
            error_log("- smtp_port: {$this->port}");
            error_log("- smtp_encryption: {$this->encryption}");
            error_log("- smtp_username: {$this->username}");
        }

        // Ensure we have a valid reply_to address
        if (empty($this->reply_to)) {
            $this->reply_to = $this->from_email;
            error_log("Reply-to is empty, using from_email as reply-to: " . $this->reply_to);
        }

        // Check if SMTP is enabled and properly configured
        if ($this->use_smtp && !empty($this->host) && !empty($this->username)) {
            error_log("Using SMTP for email delivery (SMTP enabled and configured)");
            error_log("SMTP Host: {$this->host}, Port: {$this->port}, Username: {$this->username}");
            return $this->sendWithSmtp($to, $subject, $message, $name);
        } elseif ($this->use_smtp) {
            error_log("SMTP is enabled but not properly configured. Missing host or username. Falling back to PHP mail()");
            error_log("Host: " . ($this->host ?: 'empty') . ", Username: " . ($this->username ?: 'empty'));
        } else {
            error_log("Using PHP mail() function for email delivery (SMTP not enabled)");
        }

        // Ensure we have basic email settings for PHP mail()
        if (empty($this->from_email)) {
            $this->from_email = 'noreply@' . $_SERVER['HTTP_HOST'];
            error_log("From email was empty, using default: " . $this->from_email);
        }
        if (empty($this->from_name)) {
            $this->from_name = 'Website Contact Form';
            error_log("From name was empty, using default: " . $this->from_name);
        }

        return $this->sendWithPhpMail($to, $subject, $message, $name);
    }

    /**
     * Send email using PHP's mail function
     */
    private function sendWithPhpMail($to, $subject, $message, $name) {
        // Log the attempt
        error_log("Attempting to send email via PHP mail() function");
        error_log("To: $to, Subject: $subject");

        // Log PHP mail configuration
        error_log("PHP mail() configuration:");
        error_log("- sendmail_path: " . ini_get('sendmail_path'));
        error_log("- SMTP setting: " . ini_get('SMTP'));
        error_log("- smtp_port: " . ini_get('smtp_port'));

        // Check if running on Windows
        if (strtoupper(substr(PHP_OS, 0, 3)) === 'WIN') {
            error_log("WARNING: Running on Windows. PHP mail() function may not work without proper SMTP configuration in php.ini");
        }

        // Prepare headers
        $headers = "MIME-Version: 1.0" . "\r\n";
        $headers .= "Content-type:text/html;charset=UTF-8" . "\r\n";
        $headers .= "From: {$this->from_name} <{$this->from_email}>" . "\r\n";
        $headers .= "X-Mailer: PHP/" . phpversion() . "\r\n";

        if (!empty($this->reply_to)) {
            $headers .= "Reply-To: {$this->reply_to}" . "\r\n";
        }

        // Log headers for debugging
        error_log("Email headers: " . str_replace("\r\n", " | ", $headers));

        // Format recipient
        $recipient = empty($name) ? $to : "$name <$to>";
        error_log("Formatted recipient: " . $recipient);

        // Additional parameters for mail()
        $additional_params = "-f{$this->from_email}";
        error_log("Additional parameters: " . $additional_params);

        // Send the email without suppressing errors
        error_log("Calling mail() function...");
        $success = mail($recipient, $subject, $message, $headers, $additional_params);

        // Log the result
        if ($success) {
            error_log("Email sent successfully via PHP mail()");
        } else {
            $error = error_get_last();
            error_log("Failed to send email via PHP mail(). Error: " . ($error ? $error['message'] : 'Unknown error'));
        }

        return [
            'success' => $success,
            'message' => $success ? 'Email sent successfully' : 'Failed to send email: ' . (error_get_last() ? error_get_last()['message'] : 'Unknown error')
        ];
    }

    /**
     * Send email using SMTP
     */
    private function sendWithSmtp($to, $subject, $message, $name) {
        error_log("Initializing PHPMailer for SMTP...");

        // Use SimpleMailer instead of PHPMailer
        require_once __DIR__ . '/SimpleMailer.php';

        $mailer = new SimpleMailer($this->conn);
        $mailer_loaded = true;

        error_log("Using SimpleMailer for email delivery");

        // SimpleMailer is always available
        if (!$mailer_loaded) {
            error_log("WARNING: Could not load SimpleMailer. Falling back to PHP mail() function.");
            return $this->sendWithPhpMail($to, $subject, $message, $name);
        }

        try {
            // Log SMTP connection details
            error_log("SMTP Connection Details:");
            error_log("- Host: " . $this->host);
            error_log("- Port: " . $this->port);
            error_log("- Username: " . $this->username);
            error_log("- Encryption: " . $this->encryption);

            // Use SimpleMailer to send the email
            $result = $mailer->send($to, $subject, $message);

            error_log("Email sending result: " . ($result['success'] ? "Success" : "Failed"));

            return $result;
        } catch (\Exception $e) {
            error_log("Email sending exception: " . $e->getMessage());
            return [
                'success' => false,
                'message' => "Failed to send email: " . $e->getMessage()
            ];
        }
    }

    /**
     * Get current email settings
     *
     * @return array Email settings
     */
    public function getSettings() {
        return [
            'use_smtp' => $this->use_smtp,
            'smtp_host' => $this->host,
            'smtp_port' => $this->port,
            'smtp_username' => $this->username,
            'smtp_password' => $this->password,
            'smtp_encryption' => $this->encryption,
            'from_email' => $this->from_email,
            'from_name' => $this->from_name,
            'reply_to' => $this->reply_to
        ];
    }

    /**
     * Send verification email
     *
     * @param string $to Recipient email
     * @param string $username Username
     * @param string $token Verification token
     * @return array Result with success status and message
     */
    public function sendVerificationEmail($to, $username, $token) {
        // Create verification URL with absolute path
        $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https://' : 'http://';
        $host = $_SERVER['HTTP_HOST'];

        // Get the document root path
        $doc_root = $_SERVER['DOCUMENT_ROOT'];

        // Get the current script's directory path (admin/lib)
        $current_path = dirname(__FILE__);

        // Go up one level to get admin directory
        $admin_path = dirname($current_path);

        // Get the relative path from document root to the current directory
        $relative_path = str_replace('\\', '/', str_replace($doc_root, '', $admin_path));

        // Build the verification URL with the correct path structure
        // This will create a URL like: https://redconic.com/manageinc-html/admin/verify.php?token=xyz
        $verification_url = $protocol . $host . $relative_path . '/verify.php?token=' . $token;

        // Clean up the URL (remove double slashes, etc.)
        $verification_url = preg_replace('#([^:])//+#', '$1/', $verification_url);

        // Log the verification URL for debugging
        error_log("Verification URL: " . $verification_url);

        // Get site URL from settings
        $site_url = '';
        $site_name = '';
        $site_url_query = "SELECT setting_value FROM system_settings WHERE setting_key = 'site_url'";
        $site_name_query = "SELECT setting_value FROM system_settings WHERE setting_key = 'site_name'";

        $site_url_result = $this->conn->query($site_url_query);
        if ($site_url_result && $site_url_result->num_rows > 0) {
            $site_url = $site_url_result->fetch_assoc()['setting_value'];
        } else {
            $site_url = 'https://manageinc.redconic.com';
        }

        $site_name_result = $this->conn->query($site_name_query);
        if ($site_name_result && $site_name_result->num_rows > 0) {
            $site_name = $site_name_result->fetch_assoc()['setting_value'];
        } else {
            $site_name = 'Manage Inc.';
        }

        $subject = "Verify Your Account - " . $site_name;

        $message = "
        <html>
        <head>
            <title>Verify Your Account</title>
        </head>
        <body>
            <div style='max-width: 600px; margin: 0 auto; padding: 20px; font-family: Arial, sans-serif;'>
                <div style='text-align: center; margin-bottom: 20px;'>
                    <img src='{$site_url}/images/logo.png' alt='{$site_name}' style='max-width: 200px;'>
                </div>
                <div style='background-color: #f9f9f9; padding: 20px; border-radius: 5px;'>
                    <h2 style='color: #333; margin-top: 0;'>Welcome to {$site_name}!</h2>
                    <p>Hello {$username},</p>
                    <p>Thank you for creating an account with us. Please click the button below to verify your email address:</p>
                    <div style='text-align: center; margin: 30px 0;'>
                        <a href='{$verification_url}' style='background-color: #f1ca2f; color: #333; padding: 12px 24px; text-decoration: none; border-radius: 4px; font-weight: bold;'>Verify Your Email</a>
                    </div>
                    <p>If the button above doesn't work, you can also copy and paste the following link into your browser:</p>
                    <p style='word-break: break-all;'><a href='{$verification_url}'>{$verification_url}</a></p>
                    <p>This link will expire in 24 hours.</p>
                    <p>If you did not create an account, please ignore this email.</p>
                </div>
                <div style='margin-top: 20px; text-align: center; color: #777; font-size: 12px;'>
                    <p>&copy; " . date('Y') . " {$site_name}. All rights reserved.</p>
                </div>
            </div>
        </body>
        </html>
        ";

        return $this->sendEmail($to, $subject, $message);
    }

    /**
     * Send password reset email
     *
     * @param string $to Recipient email
     * @param string $username Username
     * @param string $token Reset token
     * @return array Result with success status and message
     */
    public function sendPasswordResetEmail($to, $username, $token) {
        // Create reset URL with absolute path
        $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https://' : 'http://';
        $host = $_SERVER['HTTP_HOST'];

        // Get the document root path
        $doc_root = $_SERVER['DOCUMENT_ROOT'];
        $admin_path = dirname(dirname(__FILE__)); // Get the admin directory path

        // Calculate the relative path from document root to admin directory
        $relative_path = str_replace('\\', '/', str_replace($doc_root, '', $admin_path));

        // Build the reset URL (this should be in the admin directory)
        $reset_url = $protocol . $host . $relative_path . "/reset-password.php?token=" . $token;

        // Clean up the URL (remove double slashes, etc.)
        $reset_url = preg_replace('#([^:])//+#', '$1/', $reset_url);

        // Log the reset URL for debugging
        error_log("Password Reset URL: " . $reset_url);

        // Get site URL from settings
        $site_url = '';
        $site_name = '';
        $site_url_query = "SELECT setting_value FROM system_settings WHERE setting_key = 'site_url'";
        $site_name_query = "SELECT setting_value FROM system_settings WHERE setting_key = 'site_name'";

        $site_url_result = $this->conn->query($site_url_query);
        if ($site_url_result && $site_url_result->num_rows > 0) {
            $site_url = $site_url_result->fetch_assoc()['setting_value'];
        } else {
            $site_url = 'https://manageinc.redconic.com';
        }

        $site_name_result = $this->conn->query($site_name_query);
        if ($site_name_result && $site_name_result->num_rows > 0) {
            $site_name = $site_name_result->fetch_assoc()['setting_value'];
        } else {
            $site_name = 'Manage Inc.';
        }

        $subject = "Password Reset Request - " . $site_name;

        $message = "
        <html>
        <head>
            <title>Password Reset Request</title>
        </head>
        <body>
            <div style='max-width: 600px; margin: 0 auto; padding: 20px; font-family: Arial, sans-serif;'>
                <div style='text-align: center; margin-bottom: 20px;'>
                    <img src='{$site_url}/images/logo.png' alt='{$site_name}' style='max-width: 200px;'>
                </div>
                <div style='background-color: #f9f9f9; padding: 20px; border-radius: 5px;'>
                    <h2 style='color: #333; margin-top: 0;'>Password Reset Request</h2>
                    <p>Hello {$username},</p>
                    <p>We received a request to reset your password. Please click the button below to reset your password:</p>
                    <div style='text-align: center; margin: 30px 0;'>
                        <a href='{$reset_url}' style='background-color: #f1ca2f; color: #333; padding: 12px 24px; text-decoration: none; border-radius: 4px; font-weight: bold;'>Reset Password</a>
                    </div>
                    <p>If the button above doesn't work, you can also copy and paste the following link into your browser:</p>
                    <p style='word-break: break-all;'><a href='{$reset_url}'>{$reset_url}</a></p>
                    <p>This link will expire in 1 hour.</p>
                    <p>If you did not request a password reset, please ignore this email.</p>
                </div>
                <div style='margin-top: 20px; text-align: center; color: #777; font-size: 12px;'>
                    <p>&copy; " . date('Y') . " {$site_name}. All rights reserved.</p>
                </div>
            </div>
        </body>
        </html>
        ";

        return $this->sendEmail($to, $subject, $message);
    }
}
