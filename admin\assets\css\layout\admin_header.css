/**
 * Admin Header CSS
 *
 * This file contains styles for the admin header/topbar.
 * The header uses the black and yellow brand colors.
 */

/* Topbar Base */
.admin-topbar {
  position: fixed;
  top: 0;
  right: 0;
  left: var(--sidebar-width);
  height: var(--topbar-height);
  background-color: var(--header-bg);
  border-bottom: 1px solid var(--border-color);
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-end; /* Align everything to the right in desktop */
  z-index: var(--z-index-fixed);
  transition: all var(--transition-normal) ease;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1); /* Enhanced shadow */
  padding: 0 var(--spacing-4);
}

body.sidebar-collapsed .admin-topbar {
  left: var(--sidebar-collapsed-width);
}

/* Topbar Main Section - Only visible in mobile */
.admin-topbar-main {
  display: none; /* Hidden in desktop view */
  align-items: center;
  justify-content: flex-start;
  padding: 0 var(--spacing-4);
  height: var(--topbar-height);
  width: 100%;
  background-color: var(--header-bg);
}

/* Hide mobile logo in desktop view */
.admin-topbar-logo-mobile {
  display: none; /* Hidden in desktop view */
}

/* Topbar Actions Section */
.topbar-actions {
  display: flex;
  align-items: center;
  justify-content: flex-end; /* Align to right in desktop */
  gap: var(--spacing-4); /* Add consistent spacing between items */
  height: 100%;
  background-color: var(--header-bg);
  padding-right: var(--spacing-2); /* Add padding to the right */
  transition: all var(--transition-normal) ease;
}

/* Mobile Menu Toggle */
.topbar-mobile-toggle,
.admin-mobile-toggle {
  display: none; /* Hidden by default, shown in mobile view */
  align-items: center;
  justify-content: center;
  background-color: transparent;
  border: none;
  width: 40px;
  height: 40px;
  cursor: pointer;
  padding: var(--spacing-2);
  margin-right: var(--spacing-2);
  border-radius: var(--radius-md);
  transition: all var(--transition-fast) ease;
  color: var(--text-color);
}

.topbar-mobile-toggle:hover,
.admin-mobile-toggle:hover {
  background-color: var(--background-light);
  color: var(--primary-color);
}

.topbar-mobile-toggle.active,
.admin-mobile-toggle.active {
  background-color: var(--background-light);
  color: var(--primary-color);
}

.topbar-mobile-toggle i,
.admin-mobile-toggle i {
  font-size: var(--font-size-lg);
}

/* Topbar Title - Only visible in mobile */
.admin-topbar-title {
  display: none; /* Hidden in desktop view */
  align-items: center;
  gap: var(--spacing-2);
  flex-shrink: 0;
}

.admin-topbar-title h1 {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  margin: 0;
  color: var(--text-color);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* Search Bar */
.admin-search {
  position: relative;
  width: 300px;
  max-width: 300px;
  margin-right: var(--spacing-4);
  transition: all var(--transition-normal) ease;
}

.admin-search-input {
  width: 100%;
  height: 40px;
  padding: var(--spacing-2) var(--spacing-8) var(--spacing-2) var(--spacing-8);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-full);
  background-color: var(--background-light);
  color: var(--text-color);
  font-size: var(--font-size-sm);
  transition: all var(--transition-fast) ease;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.admin-search-input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(241, 202, 47, 0.15);
  background-color: var(--white);
  width: 100%;
}

.admin-search-input::placeholder {
  color: var(--text-muted);
  transition: opacity var(--transition-fast) ease;
}

.admin-search-input:focus::placeholder {
  opacity: 0.7;
}

.admin-search-button {
  position: absolute;
  left: 0;
  top: 0;
  height: 40px;
  width: 40px;
  background: transparent;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-muted);
  cursor: pointer;
  transition: all var(--transition-fast) ease;
  border-radius: 50%;
}

.admin-search-button:hover {
  color: var(--primary-color);
  background-color: rgba(0, 0, 0, 0.03);
}

.admin-search-clear {
  position: absolute;
  right: 0;
  top: 0;
  height: 40px;
  width: 40px;
  background: transparent;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-muted);
  cursor: pointer;
  transition: all var(--transition-fast) ease;
  opacity: 0;
  visibility: hidden;
  border-radius: 50%;
}

.admin-search-input:not(:placeholder-shown) ~ .admin-search-clear {
  opacity: 0.7;
  visibility: visible;
}

.admin-search-clear:hover {
  color: var(--danger-color);
  opacity: 1;
  background-color: rgba(0, 0, 0, 0.03);
}

/* User Actions */
.admin-user-actions {
  display: flex;
  align-items: center;
  gap: var(--spacing-4);
  height: 100%;
}

/* Notifications */
.admin-notifications {
  position: relative;
  height: 100%;
  display: flex;
  align-items: center;
}

.notifications-toggle {
  background: transparent;
  border: none;
  color: var(--text-color);
  font-size: var(--font-size-lg);
  cursor: pointer;
  padding: var(--spacing-2);
  border-radius: var(--radius-full);
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  transition: all var(--transition-fast) ease;
}

.notifications-toggle:hover {
  background-color: var(--gray-100);
  color: var(--secondary-color);
}

.notifications-badge {
  position: absolute;
  top: 0;
  right: 0;
  background-color: var(--danger-color);
  color: var(--white);
  border-radius: var(--radius-full);
  min-width: 18px;
  height: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 10px;
  font-weight: var(--font-weight-bold);
  padding: 0 var(--spacing-1);
}

.notifications-dropdown {
  position: absolute;
  top: 100%;
  right: 0;
  width: 320px;
  background-color: var(--white);
  border-radius: var(--radius-md);
  box-shadow: var(--shadow-lg);
  border: 1px solid var(--border-color);
  z-index: var(--z-index-dropdown);
  display: none;
  opacity: 0;
  visibility: hidden;
  transform: translateY(10px);
  transition: all var(--transition-fast) ease;
}

.notifications-dropdown.show {
  display: block;
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.notifications-header {
  padding: var(--spacing-3) var(--spacing-4);
  border-bottom: 1px solid var(--border-color);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.notifications-header h3 {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-semibold);
  margin: 0;
}

.notifications-list {
  max-height: 300px;
  overflow-y: auto;
}

.notification-item {
  padding: var(--spacing-3) var(--spacing-4);
  border-bottom: 1px solid var(--border-light);
  display: flex;
  align-items: flex-start;
  gap: var(--spacing-3);
  transition: background-color var(--transition-fast) ease;
}

.notification-item:hover {
  background-color: var(--gray-50);
}

.notification-icon {
  width: 36px;
  height: 36px;
  border-radius: var(--radius-full);
  background-color: var(--primary-very-light);
  color: var(--primary-color);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--font-size-base);
  flex-shrink: 0;
}

.notification-content {
  flex: 1;
}

.notification-title {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  margin-bottom: var(--spacing-1);
  color: var(--text-color);
}

.notification-text {
  font-size: var(--font-size-xs);
  color: var(--text-light);
  margin-bottom: var(--spacing-1);
}

.notification-time {
  font-size: var(--font-size-xs);
  color: var(--text-muted);
}

/* Notification Type Styles */
.notification-icon.success {
  background-color: #d4edda;
  color: #28a745;
}

.notification-icon.error {
  background-color: #f8d7da;
  color: #dc3545;
}

.notification-icon.warning {
  background-color: #fff3cd;
  color: #856404;
}

.notification-icon.info {
  background-color: #d1ecf1;
  color: #17a2b8;
}

.notification-icon.contact-form {
  background-color: #fff8e1;
  color: #f1ca2f;
}

/* Contact Form Notification Styling */
.contact-form-notification {
  border-left: 3px solid #f1ca2f !important;
  background: linear-gradient(90deg, rgba(241, 202, 47, 0.05) 0%, transparent 100%) !important;
}

.contact-form-notification:hover {
  background: linear-gradient(90deg, rgba(241, 202, 47, 0.1) 0%, rgba(241, 202, 47, 0.02) 100%) !important;
}

.contact-form-notification .notification-title {
  color: #212529 !important;
  font-weight: 600 !important;
}

.contact-form-notification .notification-text {
  color: #495057 !important;
  font-size: 0.8125rem !important;
  line-height: 1.4 !important;
}

.contact-form-notification .notification-time {
  color: #6c757d !important;
  font-size: 0.75rem !important;
}

.contact-form-notification .notification-icon {
  background-color: #fff8e1 !important;
  color: #f1ca2f !important;
  border: 2px solid rgba(241, 202, 47, 0.2) !important;
}

.notifications-footer {
  padding: var(--spacing-3) var(--spacing-4);
  border-top: 1px solid var(--border-color);
  display: flex;
  align-items: center;
  justify-content: center;
}

.notifications-footer a {
  font-size: var(--font-size-xs);
  color: var(--primary-color);
  text-decoration: none;
  transition: color var(--transition-fast) ease;
}

.notifications-footer a:hover {
  color: var(--primary-dark);
  text-decoration: underline;
}

/* User Profile */
.admin-user {
  position: relative;
  height: 100%;
  display: flex;
  align-items: center;
}

.admin-user-toggle {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  background: transparent;
  border: none;
  cursor: pointer;
  padding: var(--spacing-1) var(--spacing-2);
  border-radius: var(--radius-md);
  transition: all var(--transition-fast) ease;
  height: 40px; /* Fixed height for better alignment */
}

.admin-user-toggle:hover {
  background-color: var(--gray-100);
}

.admin-user-toggle:focus {
  outline: none;
  box-shadow: 0 0 0 2px rgba(var(--primary-rgb), 0.25);
}

.admin-user-avatar {
  width: 32px;
  height: 32px;
  border-radius: var(--radius-full);
  background-color: var(--primary-very-light);
  color: var(--primary-color);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-bold);
  overflow: hidden;
  border: 2px solid transparent;
  transition: all var(--transition-fast) ease;
}

.admin-user-toggle:hover .admin-user-avatar {
  border-color: var(--primary-color);
  transform: scale(1.05);
}

.admin-user-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.admin-user-info {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.admin-user-name {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--text-color);
  transition: color var(--transition-fast) ease;
}

.admin-user-toggle:hover .admin-user-name {
  color: var(--primary-color);
}

.admin-user-role {
  font-size: var(--font-size-xs);
  color: var(--text-muted);
}

.admin-user-dropdown {
  position: absolute;
  top: 100%;
  right: 0;
  width: 240px; /* Increased width */
  background-color: var(--white);
  border-radius: var(--radius-md);
  box-shadow: var(--shadow-xl); /* Enhanced shadow */
  border: 1px solid var(--border-color);
  z-index: var(--z-index-dropdown);
  display: none;
  opacity: 0;
  visibility: hidden;
  transform: translateY(10px);
  transition: all var(--transition-fast) ease;
  overflow: hidden; /* Ensure content doesn't overflow */
}

.admin-user-dropdown.show {
  display: block;
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.admin-user-dropdown-header {
  padding: var(--spacing-3) var(--spacing-4);
  border-bottom: 1px solid var(--border-color);
}

.admin-user-dropdown-name {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-semibold);
  margin-bottom: var(--spacing-1);
  color: var(--text-color);
}

.admin-user-dropdown-email {
  font-size: var(--font-size-xs);
  color: var(--text-muted);
}

.admin-user-dropdown-menu {
  list-style: none;
  padding: var(--spacing-2) 0;
  margin: 0;
}

.admin-user-dropdown-menu li {
  padding: 0;
  margin: 0;
}

.admin-user-dropdown-menu a {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  padding: var(--spacing-2) var(--spacing-4);
  color: var(--text-color);
  font-size: var(--font-size-sm);
  text-decoration: none;
  transition: all var(--transition-fast) ease;
  position: relative;
  overflow: hidden;
}

.admin-user-dropdown-menu a::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: 3px;
  background-color: var(--primary-color);
  transform: translateX(-100%);
  transition: transform var(--transition-fast) ease;
}

.admin-user-dropdown-menu a:hover {
  background-color: var(--gray-50);
  color: var(--primary-color);
  padding-left: calc(var(--spacing-4) + 3px);
}

.admin-user-dropdown-menu a:hover::before {
  transform: translateX(0);
}

.admin-user-dropdown-menu a i {
  width: 16px;
  text-align: center;
  color: var(--text-muted);
  transition: all var(--transition-fast) ease;
}

.admin-user-dropdown-menu a:hover i {
  color: var(--primary-color);
  transform: scale(1.1);
}

.admin-user-dropdown-divider {
  height: 1px;
  background-color: var(--border-color);
  margin: var(--spacing-2) 0;
}

/* Responsive Styles */
@media (max-width: 992px) {
  .admin-search {
    width: 200px;
  }

  .admin-topbar-title h1 {
    max-width: 200px;
  }

  /* Adjust user info for medium screens */
  .admin-user-info {
    display: none;
  }

  /* Adjust spacing between topbar elements */
  .topbar-actions {
    gap: var(--spacing-3);
  }
}

@media (max-width: 768px) {
  /* Topbar structure */
  .admin-topbar {
    left: 0;
    padding: 0;
    flex-direction: column;
    height: auto;
    background-color: var(--sidebar-bg);
    position: fixed;
    top: 0;
    right: 0;
    z-index: var(--z-index-fixed);
    box-shadow: 0 2px 15px rgba(0, 0, 0, 0.15); /* Enhanced shadow for mobile */
  }

  body.sidebar-collapsed .admin-topbar {
    left: 0;
  }

  /* Top row with logo and hamburger */
  .admin-topbar-main {
    display: flex;
    flex-direction: row;
    padding: 0 var(--spacing-3);
    height: var(--topbar-height);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    justify-content: space-between;
    background-color: var(--sidebar-bg);
    width: 100%;
    align-items: center;
  }

  /* Logo for mobile view - aligned to the right */
  .admin-topbar-logo-mobile {
    display: flex;
    align-items: center;
    height: 100%;
    margin-left: auto; /* Push to the right */
    order: 2; /* Second item in flex container */
  }

  .admin-topbar-logo-mobile img {
    height: 30px;
    width: auto;
    transition: all var(--transition-fast) ease;
  }

  /* Bottom row with actions */
  .topbar-actions {
    justify-content: space-between;
    padding: 0 var(--spacing-3);
    height: var(--topbar-height);
    align-items: center;
    display: flex;
    gap: var(--spacing-2);
    width: 100%;
    background-color: white;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05); /* Subtle shadow for the actions bar */
  }

  /* Mobile menu toggle button - aligned to the left */
  .topbar-mobile-toggle {
    display: flex;
    margin-right: auto; /* Push to the left */
    margin-left: 0; /* Remove left margin */
    color: var(--primary-color); /* Yellow color */
    background-color: #000; /* Black background */
    border: none;
    border-radius: 4px;
    padding: var(--spacing-2);
    cursor: pointer;
    font-size: 1.2rem;
    width: 40px;
    height: 40px;
    align-items: center;
    justify-content: center;
    order: 1; /* First item in flex container */
    position: relative; /* Ensure proper stacking */
    transition: all 0.2s ease;
  }

  /* Ensure hamburger icon is visible with high contrast */
  .topbar-mobile-toggle i {
    color: var(--primary-color); /* Bright yellow color for the icon */
    font-size: 1.4rem; /* Slightly larger icon */
  }

  .topbar-mobile-toggle.active {
    color: var(--primary-color);
    background-color: #222; /* Slightly lighter black when active */
    transform: rotate(90deg); /* Rotate icon when active */
  }

  .topbar-mobile-toggle:hover {
    transform: scale(1.05);
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
  }

  /* Search bar */
  .admin-search {
    width: 100%;
    max-width: none;
    margin: 0;
    flex-grow: 1;
  }

  .admin-search-input {
    height: 36px;
    width: 100%;
  }

  .admin-search-button,
  .admin-search-clear {
    height: 36px;
  }

  /* User elements */
  .admin-user-info,
  .user-name {
    display: none;
  }

  .admin-user-actions {
    margin-left: 0;
    gap: var(--spacing-2);
  }

  .admin-user-toggle,
  .user-menu-toggle {
    padding: var(--spacing-1);
    height: 36px;
    width: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .admin-user-toggle .dropdown-icon,
  .user-menu-toggle .dropdown-icon {
    display: none;
  }

  /* Dropdowns positioning */
  .notifications-dropdown,
  .admin-user-dropdown {
    width: 280px;
    right: 0;
    top: calc(var(--topbar-height) * 2);
    position: fixed;
    box-shadow: var(--shadow-xl);
    max-width: calc(100vw - 20px);
    z-index: 9999 !important;
    transform: none !important;
  }

  /* Notification elements */
  .admin-notifications,
  .topbar-notifications {
    position: relative;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .notifications-toggle {
    width: 36px;
    height: 36px;
    padding: var(--spacing-1);
    display: flex;
    align-items: center;
    justify-content: center;
  }

  /* User menu elements */
  .admin-user,
  .topbar-user-menu {
    position: relative;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  /* Adjust main content to account for double-height topbar */
  .admin-main {
    margin-top: 0; /* Remove margin to eliminate gap */
    padding-top: calc(var(--topbar-height) * 2); /* Use padding instead of margin */
  }
}

@media (max-width: 576px) {
  /* Adjust padding for small screens */
  .admin-topbar-main {
    padding: 0 var(--spacing-2);
  }

  .topbar-actions {
    padding: 0 var(--spacing-2);
    gap: var(--spacing-1);
  }

  /* Optimize search for small screens */
  .admin-search {
    max-width: none;
    flex-grow: 1;
  }

  .admin-search-input {
    height: 34px;
    font-size: var(--font-size-xs);
    padding-left: 32px;
    padding-right: 32px;
  }

  .admin-search-button,
  .admin-search-clear {
    height: 34px;
    width: 32px;
  }

  /* Tighten spacing */
  .admin-user-actions {
    gap: var(--spacing-1);
  }

  /* Adjust title */
  .admin-topbar-title {
    max-width: 30%;
  }

  .admin-topbar-title h1 {
    font-size: var(--font-size-sm);
    max-width: 100%;
  }

  /* Make buttons more compact */
  .notifications-toggle,
  .user-menu-toggle,
  .admin-user-toggle,
  .topbar-mobile-toggle {
    padding: var(--spacing-1);
    width: 34px;
    height: 34px;
  }

  /* Adjust logo size */
  .admin-topbar-logo-mobile img {
    height: 24px;
    width: auto;
  }

  /* Adjust dropdown positioning */
  .notifications-dropdown,
  .admin-user-dropdown {
    width: 260px;
    right: 0;
    max-width: calc(100vw - 16px);
    top: calc(var(--topbar-height) * 2);
  }

  /* Reduce icon sizes */
  .notifications-toggle i,
  .user-menu-toggle i,
  .admin-user-toggle i {
    font-size: var(--font-size-base);
  }

  /* Ensure hamburger icon is visible */
  .topbar-mobile-toggle i {
    font-size: var(--font-size-lg);
    color: var(--primary-color);
  }

  /* Hamburger menu styling */
  .topbar-mobile-toggle {
    background-color: #000;
    color: var(--primary-color);
  }

  /* Adjust notification items */
  .notification-item {
    padding: var(--spacing-2);
  }

  .notification-icon {
    width: 32px;
    height: 32px;
  }
}

/* Extra small devices */
@media (max-width: 375px) {
  /* Further optimize for very small screens */
  .admin-search-input {
    font-size: 12px;
    padding-left: 28px;
    padding-right: 28px;
  }

  .admin-search-button,
  .admin-search-clear {
    width: 28px;
  }

  /* Make buttons even smaller */
  .notifications-toggle,
  .user-menu-toggle,
  .admin-user-toggle {
    width: 32px;
    height: 32px;
    padding: 4px;
  }

  /* Keep hamburger menu visible */
  .topbar-mobile-toggle {
    width: 36px;
    height: 36px;
    padding: 6px;
    background-color: #000;
  }

  .topbar-mobile-toggle i {
    color: var(--primary-color);
    font-size: 1.2rem;
  }

  /* Reduce logo size further */
  .admin-topbar-logo-mobile img {
    height: 20px;
  }

  /* Adjust dropdown width */
  .notifications-dropdown,
  .admin-user-dropdown {
    width: calc(100vw - 16px);
    right: 8px;
  }

  /* Hide search placeholder to save space */
  .admin-search-input::placeholder {
    opacity: 0;
  }
}
