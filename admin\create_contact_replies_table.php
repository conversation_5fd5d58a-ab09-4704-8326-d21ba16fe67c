<?php
/**
 * Create Contact Replies Table
 * 
 * This script creates the contact_replies table needed for the inbox functionality.
 * It's designed to work independently without foreign key constraints to avoid dependency issues.
 */

session_start();
require_once 'config.php';

// Check if user is logged in and is admin
if (!isset($_SESSION['user_id'])) {
    die('Access denied. Please log in as admin.');
}

// Set content type
header('Content-Type: text/html; charset=utf-8');
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Create Contact Replies Table</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 40px auto;
            padding: 20px;
            background: #f8f9fa;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2c3e50;
            margin-bottom: 20px;
        }
        .success {
            background: #d4edda;
            color: #155724;
            padding: 12px 16px;
            border-radius: 8px;
            margin: 10px 0;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            padding: 12px 16px;
            border-radius: 8px;
            margin: 10px 0;
            border: 1px solid #f5c6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            padding: 12px 16px;
            border-radius: 8px;
            margin: 10px 0;
            border: 1px solid #bee5eb;
        }
        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            text-decoration: none;
            display: inline-block;
            margin: 10px 5px;
            cursor: pointer;
            font-weight: 500;
        }
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
        }
        .btn-secondary {
            background: #6c757d;
        }
        pre {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            overflow-x: auto;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>📧 Create Contact Replies Table</h1>
        
        <?php
        if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['create_table'])) {
            echo "<h2>🔧 Creating contact_replies table...</h2>";
            
            try {
                // Check if table already exists
                $check_sql = "SHOW TABLES LIKE 'contact_replies'";
                $result = $conn->query($check_sql);
                
                if ($result && $result->num_rows > 0) {
                    echo "<div class='info'>ℹ️ Table 'contact_replies' already exists!</div>";
                } else {
                    // Create contact_replies table without foreign key constraints
                    $create_sql = "CREATE TABLE `contact_replies` (
                        `id` int(11) NOT NULL AUTO_INCREMENT,
                        `submission_id` int(11) NOT NULL,
                        `user_id` int(11) NOT NULL,
                        `username` varchar(255) NOT NULL DEFAULT 'Admin',
                        `subject` varchar(255) NOT NULL,
                        `message` text NOT NULL,
                        `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
                        PRIMARY KEY (`id`),
                        KEY `idx_submission_id` (`submission_id`),
                        KEY `idx_user_id` (`user_id`),
                        KEY `idx_created_at` (`created_at`)
                    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
                    
                    if ($conn->query($create_sql)) {
                        echo "<div class='success'>✅ Table 'contact_replies' created successfully!</div>";
                        
                        // Show table structure
                        echo "<h3>📋 Table Structure:</h3>";
                        echo "<pre>";
                        echo "Table: contact_replies\n";
                        echo "Columns:\n";
                        echo "- id (Primary Key, Auto Increment)\n";
                        echo "- submission_id (Links to contact_submissions.id)\n";
                        echo "- user_id (Links to users.id)\n";
                        echo "- username (Display name for the reply author)\n";
                        echo "- subject (Reply subject line)\n";
                        echo "- message (Reply content)\n";
                        echo "- created_at (Timestamp)\n";
                        echo "</pre>";
                        
                    } else {
                        echo "<div class='error'>❌ Error creating table: " . htmlspecialchars($conn->error) . "</div>";
                    }
                }
                
                // Check if contact_submissions table exists
                $check_submissions = "SHOW TABLES LIKE 'contact_submissions'";
                $submissions_result = $conn->query($check_submissions);
                
                if ($submissions_result && $submissions_result->num_rows > 0) {
                    echo "<div class='success'>✅ contact_submissions table exists</div>";
                } else {
                    echo "<div class='error'>⚠️ contact_submissions table does not exist. You may need to run the main database setup first.</div>";
                }
                
                echo "<div class='success'>";
                echo "<h3>🎉 Setup Complete!</h3>";
                echo "<p>The contact_replies table is now ready for the modern inbox functionality.</p>";
                echo "</div>";
                
                echo "<a href='insert_sample_data.php' class='btn'>📥 Insert Sample Data</a>";
                echo "<a href='inbox.php' class='btn btn-secondary'>📧 View Inbox</a>";
                
            } catch (Exception $e) {
                echo "<div class='error'>";
                echo "<h3>❌ Error occurred:</h3>";
                echo "<p>" . htmlspecialchars($e->getMessage()) . "</p>";
                echo "</div>";
            }
            
        } else {
            ?>
            
            <div class="info">
                <strong>Purpose:</strong> This script creates the contact_replies table needed for the modern inbox functionality.
            </div>
            
            <h2>📋 What this creates:</h2>
            
            <h3>contact_replies table</h3>
            <ul>
                <li><strong>id</strong> - Primary key, auto-increment</li>
                <li><strong>submission_id</strong> - Links to the original contact submission</li>
                <li><strong>user_id</strong> - ID of the admin user who sent the reply</li>
                <li><strong>username</strong> - Display name for the reply author</li>
                <li><strong>subject</strong> - Subject line of the reply</li>
                <li><strong>message</strong> - Content of the reply</li>
                <li><strong>created_at</strong> - Timestamp when reply was sent</li>
            </ul>
            
            <div class="info">
                <strong>Note:</strong> This table is created without foreign key constraints to avoid dependency issues.
                It will work perfectly with the inbox functionality.
            </div>
            
            <form method="post">
                <button type="submit" name="create_table" class="btn">
                    🔧 Create Table
                </button>
            </form>
            
            <a href="inbox.php" class="btn btn-secondary">📧 View Current Inbox</a>
            
            <?php
        }
        ?>
    </div>
</body>
</html>
