/**
 * Carousel Replacer Script
 * This script replaces the existing carousel with a custom implementation
 */
document.addEventListener('DOMContentLoaded', function() {
    // Find the existing carousel
    const oldCarousel = document.querySelector('.et_pb_module.dsm_image_carousel');
    if (!oldCarousel) {
        console.warn('Carousel not found');
        return;
    }

    // Create our custom carousel
    const newCarousel = document.createElement('div');
    newCarousel.className = 'partner-logos-carousel';
    
    // Create container for logos
    const container = document.createElement('div');
    container.className = 'partner-logos-container';
    
    // Get all the images from the old carousel
    const images = oldCarousel.querySelectorAll('.swiper-slide img');
    if (images.length === 0) {
        console.warn('No images found in carousel');
        return;
    }
    
    // Add each image to our custom carousel
    images.forEach(img => {
        const logoDiv = document.createElement('div');
        logoDiv.className = 'partner-logo';
        
        const newImg = document.createElement('img');
        newImg.src = img.src;
        newImg.alt = img.alt || '';
        newImg.title = img.title || '';
        newImg.width = img.width || '';
        newImg.height = img.height || '';
        newImg.loading = 'lazy';
        newImg.decoding = 'async';
        
        logoDiv.appendChild(newImg);
        container.appendChild(logoDiv);
    });
    
    // Add navigation buttons
    const prevButton = document.createElement('button');
    prevButton.className = 'carousel-prev';
    prevButton.innerHTML = '&#10094;';
    
    const nextButton = document.createElement('button');
    nextButton.className = 'carousel-next';
    nextButton.innerHTML = '&#10095;';
    
    // Add everything to the new carousel
    newCarousel.appendChild(container);
    newCarousel.appendChild(prevButton);
    newCarousel.appendChild(nextButton);
    
    // Replace the old carousel with our new one
    oldCarousel.parentNode.replaceChild(newCarousel, oldCarousel);
    
    // Add CSS for the carousel
    const style = document.createElement('style');
    style.textContent = `
    /* Partner logos carousel */
    .partner-logos-carousel {
        position: relative;
        width: 100%;
        max-width: 1200px;
        margin: 0 auto;
        padding: 20px 40px;
        overflow: hidden;
    }
    
    .partner-logos-container {
        display: flex;
        flex-wrap: nowrap;
        transition: transform 0.5s ease;
    }
    
    .partner-logo {
        flex: 0 0 calc(100% / 6);
        padding: 0 10px;
        text-align: center;
        display: flex;
        align-items: center;
        justify-content: center;
    }
    
    .partner-logo img {
        max-width: 100%;
        max-height: 60px;
        object-fit: contain;
        transition: transform 0.3s ease;
    }
    
    .partner-logo img:hover {
        transform: scale(1.05);
    }
    
    /* Navigation buttons */
    .carousel-prev,
    .carousel-next {
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        width: 40px;
        height: 40px;
        background-color: rgba(255, 255, 255, 0.7);
        border: none;
        border-radius: 50%;
        font-size: 18px;
        color: #666;
        cursor: pointer;
        z-index: 10;
        display: flex;
        justify-content: center;
        align-items: center;
    }
    
    .carousel-prev:hover,
    .carousel-next:hover {
        background-color: rgba(255, 255, 255, 0.9);
        color: #333;
    }
    
    .carousel-prev {
        left: 0;
    }
    
    .carousel-next {
        right: 0;
    }
    
    /* Responsive styles */
    @media (max-width: 980px) {
        .partner-logo {
            flex: 0 0 calc(100% / 3);
        }
    }
    
    @media (max-width: 767px) {
        .partner-logo {
            flex: 0 0 calc(100% / 2);
        }
    }
    `;
    document.head.appendChild(style);
    
    // Configuration
    const config = {
        slidesToShow: {
            desktop: 6,
            tablet: 3,
            mobile: 2
        },
        autoplay: true,
        autoplaySpeed: 3000,
        infinite: true
    };
    
    // State
    let currentPosition = 0;
    let autoplayInterval = null;
    
    // Get the number of slides to show based on screen width
    function getSlidesToShow() {
        const width = window.innerWidth;
        if (width < 768) {
            return config.slidesToShow.mobile;
        } else if (width < 981) {
            return config.slidesToShow.tablet;
        } else {
            return config.slidesToShow.desktop;
        }
    }
    
    // Update the carousel display
    function updateCarousel() {
        const slidesToShow = getSlidesToShow();
        const slideWidth = 100 / slidesToShow;
        
        // Update all slides
        const slides = container.querySelectorAll('.partner-logo');
        slides.forEach((slide, index) => {
            slide.style.flex = `0 0 ${slideWidth}%`;
        });
        
        // Calculate the translation amount
        const translateX = currentPosition * slideWidth;
        container.style.transform = `translateX(-${translateX}%)`;
    }
    
    // Move the carousel by a certain number of slides
    function moveSlide(direction) {
        const slidesToShow = getSlidesToShow();
        const slides = container.querySelectorAll('.partner-logo');
        const maxPosition = slides.length - slidesToShow;
        
        // Update position
        currentPosition += direction;
        
        // Handle infinite scrolling
        if (currentPosition < 0) {
            currentPosition = config.infinite ? maxPosition : 0;
        } else if (currentPosition > maxPosition) {
            currentPosition = config.infinite ? 0 : maxPosition;
        }
        
        // Update display
        updateCarousel();
    }
    
    // Set up navigation
    prevButton.addEventListener('click', function() {
        moveSlide(-1);
    });
    
    nextButton.addEventListener('click', function() {
        moveSlide(1);
    });
    
    // Start autoplay
    function startAutoplay() {
        if (config.autoplay && !autoplayInterval) {
            autoplayInterval = setInterval(function() {
                moveSlide(1);
            }, config.autoplaySpeed);
        }
    }
    
    // Stop autoplay
    function stopAutoplay() {
        if (autoplayInterval) {
            clearInterval(autoplayInterval);
            autoplayInterval = null;
        }
    }
    
    // Pause on hover
    newCarousel.addEventListener('mouseenter', stopAutoplay);
    newCarousel.addEventListener('mouseleave', startAutoplay);
    
    // Handle window resize
    window.addEventListener('resize', updateCarousel);
    
    // Initialize
    updateCarousel();
    startAutoplay();
});
