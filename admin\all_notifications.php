<?php
/**
 * All Notifications Page
 *
 * This page displays all notifications for the current user and allows
 * them to manage their notifications.
 */

session_start();
require_once 'config.php';
require_once 'lib/Notifications.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header('Location: login.php');
    exit;
}

$user_id = $_SESSION['user_id'];
$notifications = new Notifications($conn);

// Handle actions
if (isset($_GET['action']) && isset($_GET['id'])) {
    $action = $_GET['action'];
    $notification_id = (int)$_GET['id'];

    if ($action === 'mark_read') {
        $notifications->markAsRead($notification_id, $user_id);
        $_SESSION['success_message'] = "Notification marked as read.";
    } elseif ($action === 'delete') {
        // Check if delete method exists, otherwise use a direct query
        if (method_exists($notifications, 'delete')) {
            $notifications->delete($notification_id, $user_id);
        } else {
            // Direct query as fallback
            $delete_sql = "DELETE FROM notifications WHERE id = $notification_id AND user_id = $user_id";
            $conn->query($delete_sql);
        }
        $_SESSION['success_message'] = "Notification deleted successfully.";
    }

    // Redirect to remove action from URL
    header('Location: all_notifications.php');
    exit;
}

// Handle mark all as read
if (isset($_GET['mark_all_read'])) {
    $notifications->markAllAsRead($user_id);
    $_SESSION['success_message'] = "All notifications marked as read.";
    header('Location: all_notifications.php');
    exit;
}

// Get success/error messages
$success_message = isset($_SESSION['success_message']) ? $_SESSION['success_message'] : '';
$error_message = isset($_SESSION['error_message']) ? $_SESSION['error_message'] : '';

// Clear session messages
unset($_SESSION['success_message']);
unset($_SESSION['error_message']);

// Get all notifications for the user
$all_notifications = $notifications->getNotifications($user_id, 100);
$unread_count = $notifications->getUnreadCount($user_id);

// Page title and metadata
$page_title = "All Notifications";
$page_subtitle = "View and manage your system notifications";
?>

<?php include 'includes/header.php'; ?>

<div class="admin-container">
    <div class="admin-content-header">
        <div class="admin-content-title-group">
            <h2 class="admin-content-title"><i class="fas fa-bell"></i> <?php echo $page_title; ?></h2>
            <p class="admin-content-subtitle"><?php echo $page_subtitle; ?></p>
        </div>
        <div class="admin-content-actions">
            <?php if (count($all_notifications) > 0): ?>
                <a href="all_notifications.php?mark_all_read=1" class="admin-btn secondary">
                    <i class="fas fa-check-double"></i> Mark All as Read
                </a>
            <?php endif; ?>
            <a href="add_notification.php" class="admin-btn primary">
                <i class="fas fa-plus"></i> Add Notification
            </a>
        </div>
    </div>

    <?php if (!empty($success_message)): ?>
        <div class="alert alert-success">
            <i class="fas fa-check-circle"></i> <?php echo $success_message; ?>
        </div>
    <?php endif; ?>

    <?php if (!empty($error_message)): ?>
        <div class="alert alert-danger">
            <i class="fas fa-exclamation-circle"></i> <?php echo $error_message; ?>
        </div>
    <?php endif; ?>

    <div class="admin-content-body">
        <?php if (count($all_notifications) > 0): ?>
            <div class="admin-table-responsive">
                <table class="admin-table wp-style">
                    <thead>
                        <tr>
                            <th class="status-column">Status</th>
                            <th class="type-column">Type</th>
                            <th class="title-column">Title</th>
                            <th class="message-column">Message</th>
                            <th class="date-column">Date</th>
                            <th class="actions-column">Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($all_notifications as $notif): ?>
                            <tr class="<?php echo $notif['is_read'] == 0 ? 'unread-row' : ''; ?>">
                                <td class="status-column" data-label="Status">
                                    <?php if ($notif['is_read'] == 0): ?>
                                        <span class="status-badge unread" title="Unread">
                                            <i class="fas fa-circle"></i>
                                        </span>
                                    <?php else: ?>
                                        <span class="status-badge read" title="Read">
                                            <i class="far fa-circle"></i>
                                        </span>
                                    <?php endif; ?>
                                </td>
                                <td class="type-column" data-label="Type">
                                    <span class="notification-type <?php echo $notif['type']; ?>">
                                        <i class="<?php echo isset($notif['icon']) ? $notif['icon'] : 'fas fa-bell'; ?>"></i>
                                        <?php echo ucfirst($notif['type']); ?>
                                    </span>
                                </td>
                                <td class="title-column" data-label="Title">
                                    <?php echo htmlspecialchars($notif['title']); ?>
                                </td>
                                <td class="message-column" data-label="Message">
                                    <?php echo htmlspecialchars($notif['message']); ?>
                                </td>
                                <td class="date-column" data-label="Date">
                                    <div class="date-time">
                                        <span class="date"><?php echo date('M j, Y', strtotime($notif['created_at'])); ?></span>
                                        <span class="time"><?php echo date('g:i A', strtotime($notif['created_at'])); ?></span>
                                    </div>
                                </td>
                                <td class="actions-column" data-label="Actions">
                                    <div class="row-actions">
                                        <?php
                                        // Prepare view link
                                        $view_url = '';

                                        if (!empty($notif['link'])) {
                                            // Fix link paths
                                            $fixed_link = $notif['link'];

                                            // If it's an absolute URL, use it as is
                                            if (strpos($fixed_link, 'http://') === 0 || strpos($fixed_link, 'https://') === 0) {
                                                // Extract the path part if it's pointing to this admin panel
                                                $admin_url_pattern = '/^https?:\/\/[^\/]+\/manageinc-html\/admin\//i';
                                                if (preg_match($admin_url_pattern, $fixed_link, $matches)) {
                                                    // Extract just the file and query string
                                                    $fixed_link = preg_replace($admin_url_pattern, '', $fixed_link);
                                                }
                                            }

                                            // Remove any 'admin/' prefix
                                            if (strpos($fixed_link, 'admin/') === 0) {
                                                $fixed_link = substr($fixed_link, 6); // Remove 'admin/'
                                            }

                                            // Fix double admin path in links
                                            if (strpos($fixed_link, 'admin/admin/') !== false) {
                                                $fixed_link = str_replace('admin/admin/', 'admin/', $fixed_link);
                                            }

                                            $view_url = $fixed_link;
                                        } elseif (strpos(strtolower($notif['message']), 'contact') !== false) {
                                            // Extract submission ID if available
                                            $submission_id = null;
                                            if (preg_match('/ID: (\d+)/', $notif['message'], $matches)) {
                                                $submission_id = $matches[1];
                                            } elseif (preg_match('/submission #(\d+)/', $notif['message'], $matches)) {
                                                $submission_id = $matches[1];
                                            } elseif (preg_match('/submission (\d+)/', $notif['message'], $matches)) {
                                                $submission_id = $matches[1];
                                            } elseif (preg_match('/ID (\d+)/', $notif['message'], $matches)) {
                                                $submission_id = $matches[1];
                                            } elseif (preg_match('/id (\d+)/', $notif['message'], $matches)) {
                                                $submission_id = $matches[1];
                                            } elseif (preg_match('/\#(\d+)/', $notif['message'], $matches)) {
                                                $submission_id = $matches[1];
                                            } elseif (preg_match('/(\d+)/', $notif['message'], $matches)) {
                                                // Last resort - try to find any number
                                                $submission_id = $matches[1];
                                            }

                                            // Always link to inbox page
                                            $view_url = "inbox.php";
                                            if ($submission_id) {
                                                $view_url .= "?id={$submission_id}";
                                            }
                                        }
                                        ?>

                                        <?php if (!empty($view_url)): ?>
                                            <a href="<?php echo $view_url; ?>" class="admin-btn view-btn" title="View">
                                                <i class="fas fa-external-link-alt"></i>
                                            </a>
                                        <?php endif; ?>

                                        <?php if ($notif['is_read'] == 0): ?>
                                            <a href="all_notifications.php?action=mark_read&id=<?php echo $notif['id']; ?>" class="admin-btn secondary" title="Mark as Read">
                                                <i class="fas fa-check"></i>
                                            </a>
                                        <?php endif; ?>

                                        <a href="all_notifications.php?action=delete&id=<?php echo $notif['id']; ?>" class="admin-btn delete-btn" title="Delete" onclick="return confirm('Are you sure you want to delete this notification?')">
                                            <i class="fas fa-trash-alt"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        <?php else: ?>
            <div class="empty-state">
                <div class="empty-state-icon">
                    <i class="fas fa-bell-slash"></i>
                </div>
                <h3 class="empty-state-title">No Notifications</h3>
                <p class="empty-state-text">You don't have any notifications at the moment.</p>
                <a href="add_notification.php" class="admin-btn primary">
                    <i class="fas fa-plus"></i> Create Notification
                </a>
            </div>
        <?php endif; ?>
    </div>
</div>

<style>
/* Notification specific styles */
.notification-type {
    display: inline-flex;
    align-items: center;
    gap: 5px;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 600;
}

.notification-type.info {
    background-color: #e3f2fd;
    color: #0d47a1;
}

.notification-type.success {
    background-color: #e8f5e9;
    color: #1b5e20;
}

.notification-type.warning {
    background-color: #fff3e0;
    color: #e65100;
}

.notification-type.danger {
    background-color: #ffebee;
    color: #b71c1c;
}

.status-badge {
    display: inline-block;
    width: 24px;
    height: 24px;
    line-height: 24px;
    text-align: center;
}

.status-badge.unread i {
    color: #f1ca2f;
    font-size: 10px;
}

.status-badge.read i {
    color: #aaa;
    font-size: 10px;
}

.unread-row {
    background-color: #fffdf5;
}

/* Table column widths */
.admin-table .status-column {
    width: 5%;
    text-align: center;
}

.admin-table .type-column {
    width: 15%;
}

.admin-table .title-column {
    width: 20%;
}

.admin-table .message-column {
    width: 35%;
}

.admin-table .date-column {
    width: 15%;
}

.admin-table .actions-column {
    width: 10%;
    text-align: right;
}

/* Date and time display */
.date-time {
    display: flex;
    flex-direction: column;
}

.date-time .date {
    font-weight: 600;
}

.date-time .time {
    font-size: 12px;
    color: #666;
}

/* Row actions */
.row-actions {
    display: flex;
    gap: 5px;
    justify-content: flex-end;
}

/* Empty state styling */
.empty-state {
    text-align: center;
    padding: 50px 20px;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
    margin: 20px 0;
}

.empty-state-icon {
    font-size: 48px;
    color: #ccc;
    margin-bottom: 20px;
}

.empty-state-title {
    margin: 0 0 10px;
    font-size: 20px;
    color: #333;
}

.empty-state-text {
    margin: 0 0 20px;
    color: #666;
    max-width: 400px;
    margin-left: auto;
    margin-right: auto;
}

/* Responsive styles */
@media (max-width: 768px) {
    .admin-table .message-column {
        display: none;
    }

    .admin-table .type-column {
        width: 25%;
    }

    .admin-table .title-column {
        width: 40%;
    }
}
</style>

<?php include 'includes/footer.php'; ?>
