/**
 * Admin Content Header CSS
 *
 * This file contains styles for the content header component.
 * The content header is used at the top of each admin page.
 * Updated to match the implementation in users.php.
 */

/* Content Header Base */
.admin-content-header {
  margin-bottom: var(--spacing-6);
  position: relative;
  display: flex;
  align-items: flex-start; /* Align to top for better layout with subtitle */
  justify-content: space-between;
  flex-wrap: nowrap;
  gap: var(--spacing-4);
  padding-bottom: var(--spacing-4);
  border-bottom: 1px solid var(--border-light);
}

/* Title Group - Left Side */
.admin-content-title-group {
  display: flex;
  flex-direction: column;
  flex: 1;
  min-width: 0; /* Ensure text truncation works */
  padding-top: var(--spacing-1); /* Add slight padding to align with action buttons */
}

/* Main Title */
.admin-content-title {
  margin: 0;
  font-size: var(--font-size-xl); /* Slightly smaller for better proportions */
  font-weight: var(--font-weight-semibold); /* Match users.php */
  color: var(--text-dark);
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  line-height: 1.2;
}

/* Title Icon */
.admin-content-title i {
  color: var(--primary-color);
  font-size: 1em; /* Match icon size with text */
  margin-right: var(--spacing-1);
  flex-shrink: 0;
}

/* Subtitle */
.admin-content-subtitle {
  margin: var(--spacing-1) 0 0 0; /* Align with title */
  font-size: var(--font-size-sm);
  color: var(--text-light);
  line-height: 1.5;
  max-width: 80%;
  padding-bottom: var(--spacing-2);
}

/* Actions - Right Side */
.admin-content-actions {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  margin-left: auto;
  flex-shrink: 0;
}

/* Primary Action Button */
.admin-content-actions .admin-btn.primary {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  padding: var(--spacing-2) var(--spacing-3);
  height: auto;
  font-weight: var(--font-weight-medium);
  transition: all var(--transition-fast) ease;
}

.admin-content-actions .admin-btn.primary i {
  font-size: var(--font-size-sm);
}

.admin-content-actions .admin-btn.primary:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.admin-content-actions .admin-btn.primary:active {
  transform: translateY(0);
}

/* Secondary Actions */
.admin-content-secondary-actions {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: var(--spacing-2);
  margin-top: var(--spacing-3);
  padding-top: var(--spacing-3);
  border-top: 1px solid var(--border-light);
  width: 100%;
}

/* Back Link */
.admin-content-title-group a.admin-btn.secondary {
  margin-top: var(--spacing-2);
  align-self: flex-start;
  font-size: var(--font-size-sm);
  padding: var(--spacing-1) var(--spacing-3);
}

/* Responsive Styles */
@media (max-width: 992px) {
  .admin-content-header {
    margin-bottom: var(--spacing-6);
    padding-bottom: var(--spacing-3);
  }

  .admin-content-title {
    font-size: var(--font-size-xl);
  }

  .admin-content-subtitle {
    font-size: var(--font-size-sm);
  }
}

@media (max-width: 768px) {
  .admin-content-header {
    margin-bottom: var(--spacing-4);
    flex-wrap: wrap;
    gap: var(--spacing-3);
  }

  .admin-content-title-group {
    flex: 1 0 100%;
    margin-bottom: var(--spacing-2);
  }

  .admin-content-actions {
    margin-left: 0; /* Reset margin */
    margin-top: var(--spacing-2);
    width: 100%;
    justify-content: flex-start;
  }
}

@media (max-width: 576px) {
  .admin-content-header {
    margin-bottom: var(--spacing-3);
    padding-bottom: var(--spacing-2);
    gap: var(--spacing-2);
  }

  .admin-content-title {
    font-size: var(--font-size-lg);
  }

  .admin-content-subtitle {
    font-size: var(--font-size-xs);
    margin-left: 0;
  }

  .admin-content-actions {
    margin-left: 0;
    width: 100%;
    justify-content: flex-start;
  }

  .admin-content-actions .admin-btn.primary {
    width: 100%;
    justify-content: center;
  }

  .admin-content-secondary-actions {
    margin-top: var(--spacing-2);
    padding-top: var(--spacing-2);
  }
}

/* Dark Mode Styles */
.dark-mode .admin-content-header {
  border-bottom-color: rgba(255, 255, 255, 0.1);
}

.dark-mode .admin-content-title {
  color: var(--white);
}

.dark-mode .admin-content-title i {
  background-color: rgba(var(--primary-rgb), 0.2);
}

.dark-mode .admin-content-subtitle {
  color: rgba(255, 255, 255, 0.7);
}

.dark-mode .admin-content-secondary-actions {
  border-top-color: rgba(255, 255, 255, 0.1);
}
