/**
 * Settings Module JavaScript
 * Functionality for the settings module
 * Consolidated from multiple JS files
 */

// Initialize settings module when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    // Initialize settings module
    SettingsModule.init();
});

// Settings Module Namespace
const SettingsModule = {
    // Initialize settings module
    init: function() {
        this.initSettingsTabs();
        this.initSettingsForm();
        this.initColorPickers();
        this.initLogoUpload();
        this.initEmailSettings();
        this.initTestEmail();
        this.initEmailTemplates();
        this.initTemplateEditor();
        this.initBackupRestore();
        this.initSystemInfo();
        this.initAdminLogoPath();
    },

    // Initialize settings tabs
    initSettingsTabs: function() {
        const tabLinks = document.querySelectorAll('.settings-tab-button');
        const tabContents = document.querySelectorAll('.settings-tab-content');
        
        tabLinks.forEach(function(link) {
            link.addEventListener('click', function(e) {
                e.preventDefault();
                
                // Get target tab
                const target = this.getAttribute('data-target');
                
                // Hide all tabs
                tabContents.forEach(function(content) {
                    content.classList.remove('active');
                });
                
                // Deactivate all links
                tabLinks.forEach(function(link) {
                    link.classList.remove('active');
                });
                
                // Show target tab
                document.querySelector(target).classList.add('active');
                
                // Activate current link
                this.classList.add('active');
                
                // Save active tab to localStorage
                localStorage.setItem('activeSettingsTab', target);
                
                // Update URL hash
                window.location.hash = target.substring(1);
            });
        });
        
        // Set active tab from URL hash or localStorage
        const hash = window.location.hash;
        const savedTab = localStorage.getItem('activeSettingsTab');
        
        if (hash) {
            const hashTab = document.querySelector(`[data-target="${hash}"]`);
            if (hashTab) {
                hashTab.click();
            }
        } else if (savedTab) {
            const savedTabLink = document.querySelector(`[data-target="${savedTab}"]`);
            if (savedTabLink) {
                savedTabLink.click();
            }
        } else if (tabLinks.length > 0) {
            // Default to first tab
            tabLinks[0].click();
        }
    },

    // Initialize settings form
    initSettingsForm: function() {
        const settingsForm = document.querySelector('#settings-form');
        
        if (settingsForm) {
            settingsForm.addEventListener('submit', function(e) {
                e.preventDefault();
                
                const formData = new FormData(this);
                const submitButton = this.querySelector('[type="submit"]');
                
                // Disable submit button and show loading state
                if (submitButton) {
                    submitButton.disabled = true;
                    submitButton.classList.add('loading');
                    
                    const originalText = submitButton.innerHTML;
                    submitButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Saving...';
                }
                
                // Send AJAX request
                fetch('ajax/save_settings.php', {
                    method: 'POST',
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    // Re-enable submit button
                    if (submitButton) {
                        submitButton.disabled = false;
                        submitButton.classList.remove('loading');
                        submitButton.innerHTML = originalText;
                    }
                    
                    if (data.success) {
                        AdminCore.showNotification('Settings saved successfully.', 'success');
                    } else {
                        AdminCore.showNotification(data.message || 'Error saving settings.', 'error');
                    }
                })
                .catch(error => {
                    console.error('Error saving settings:', error);
                    
                    // Re-enable submit button
                    if (submitButton) {
                        submitButton.disabled = false;
                        submitButton.classList.remove('loading');
                        submitButton.innerHTML = originalText;
                    }
                    
                    AdminCore.showNotification('Error saving settings.', 'error');
                });
            });
        }
    },

    // Initialize color pickers
    initColorPickers: function() {
        const colorPickers = document.querySelectorAll('.color-picker');
        
        if (colorPickers.length > 0 && typeof $.fn.spectrum !== 'undefined') {
            colorPickers.forEach(function(picker) {
                $(picker).spectrum({
                    preferredFormat: "hex",
                    showInput: true,
                    showInitial: true,
                    allowEmpty: true,
                    showPalette: true,
                    palette: [
                        ["#000", "#444", "#666", "#999", "#ccc", "#eee", "#f3f3f3", "#fff"],
                        ["#f00", "#f90", "#ff0", "#0f0", "#0ff", "#00f", "#90f", "#f0f"],
                        ["#f4cccc", "#fce5cd", "#fff2cc", "#d9ead3", "#d0e0e3", "#cfe2f3", "#d9d2e9", "#ead1dc"],
                        ["#ea9999", "#f9cb9c", "#ffe599", "#b6d7a8", "#a2c4c9", "#9fc5e8", "#b4a7d6", "#d5a6bd"],
                        ["#e06666", "#f6b26b", "#ffd966", "#93c47d", "#76a5af", "#6fa8dc", "#8e7cc3", "#c27ba0"],
                        ["#c00", "#e69138", "#f1c232", "#6aa84f", "#45818e", "#3d85c6", "#674ea7", "#a64d79"],
                        ["#900", "#b45f06", "#bf9000", "#38761d", "#134f5c", "#0b5394", "#351c75", "#741b47"],
                        ["#600", "#783f04", "#7f6000", "#274e13", "#0c343d", "#073763", "#20124d", "#4c1130"]
                    ],
                    change: function(color) {
                        // Update preview
                        const previewId = this.getAttribute('data-preview');
                        if (previewId) {
                            const preview = document.querySelector(previewId);
                            if (preview) {
                                preview.style.backgroundColor = color ? color.toHexString() : '';
                            }
                        }
                    }
                });
            });
        }
    },

    // Initialize logo upload
    initLogoUpload: function() {
        const logoUpload = document.querySelector('#logo-upload');
        const logoPreview = document.querySelector('#logo-preview');
        const logoInput = document.querySelector('#logo');
        
        if (logoUpload && logoPreview) {
            logoUpload.addEventListener('change', function() {
                if (this.files && this.files[0]) {
                    const reader = new FileReader();
                    
                    reader.onload = function(e) {
                        logoPreview.src = e.target.result;
                        logoPreview.style.display = 'block';
                        
                        if (logoInput) {
                            logoInput.value = e.target.result;
                        }
                    };
                    
                    reader.readAsDataURL(this.files[0]);
                }
            });
        }
    },

    // Initialize email settings
    initEmailSettings: function() {
        const smtpToggle = document.querySelector('#use-smtp');
        const smtpSettings = document.querySelector('#smtp-settings');
        
        if (smtpToggle && smtpSettings) {
            // Initial state
            updateSmtpSettings();
            
            // Update on change
            smtpToggle.addEventListener('change', updateSmtpSettings);
            
            function updateSmtpSettings() {
                smtpSettings.style.display = smtpToggle.checked ? 'block' : 'none';
            }
        }
    },

    // Initialize test email
    initTestEmail: function() {
        const testEmailButton = document.querySelector('#test-email');
        
        if (testEmailButton) {
            testEmailButton.addEventListener('click', function(e) {
                e.preventDefault();
                
                // Get email settings
                const useSmtp = document.querySelector('#use-smtp').checked;
                const smtpHost = document.querySelector('#smtp-host').value;
                const smtpPort = document.querySelector('#smtp-port').value;
                const smtpUsername = document.querySelector('#smtp-username').value;
                const smtpPassword = document.querySelector('#smtp-password').value;
                const smtpEncryption = document.querySelector('#smtp-encryption').value;
                const adminEmail = document.querySelector('#admin-email').value;
                
                // Validate email settings
                if (useSmtp) {
                    if (!smtpHost) {
                        AdminCore.showNotification('SMTP Host is required.', 'error');
                        return;
                    }
                    
                    if (!smtpPort) {
                        AdminCore.showNotification('SMTP Port is required.', 'error');
                        return;
                    }
                }
                
                if (!adminEmail) {
                    AdminCore.showNotification('Admin Email is required.', 'error');
                    return;
                }
                
                // Show loading state
                this.disabled = true;
                this.classList.add('loading');
                
                const originalText = this.innerHTML;
                this.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Sending...';
                
                // Send test email
                fetch('ajax/test_email.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: `use_smtp=${useSmtp ? 1 : 0}&smtp_host=${encodeURIComponent(smtpHost)}&smtp_port=${encodeURIComponent(smtpPort)}&smtp_username=${encodeURIComponent(smtpUsername)}&smtp_password=${encodeURIComponent(smtpPassword)}&smtp_encryption=${encodeURIComponent(smtpEncryption)}&admin_email=${encodeURIComponent(adminEmail)}`
                })
                .then(response => response.json())
                .then(data => {
                    // Reset button
                    this.disabled = false;
                    this.classList.remove('loading');
                    this.innerHTML = originalText;
                    
                    if (data.success) {
                        AdminCore.showNotification('Test email sent successfully. Please check your inbox.', 'success');
                    } else {
                        AdminCore.showNotification(data.message || 'Error sending test email.', 'error');
                    }
                })
                .catch(error => {
                    console.error('Error sending test email:', error);
                    
                    // Reset button
                    this.disabled = false;
                    this.classList.remove('loading');
                    this.innerHTML = originalText;
                    
                    AdminCore.showNotification('Error sending test email.', 'error');
                });
            });
        }
    },

    // Initialize email templates
    initEmailTemplates: function() {
        const editButtons = document.querySelectorAll('.edit-template');
        
        editButtons.forEach(function(button) {
            button.addEventListener('click', function(e) {
                e.preventDefault();
                
                const templateId = this.getAttribute('data-id');
                const templateName = this.getAttribute('data-name');
                
                // Redirect to template editor
                window.location.href = `template_editor.php?id=${templateId}&name=${encodeURIComponent(templateName)}`;
            });
        });
    },

    // Initialize template editor
    initTemplateEditor: function() {
        const templateEditor = document.querySelector('#template-editor');
        
        if (templateEditor) {
            // Initialize editor tabs
            const editorTabs = templateEditor.querySelectorAll('.template-editor-tab');
            const editorContents = templateEditor.querySelectorAll('.template-editor-content');
            
            editorTabs.forEach(function(tab) {
                tab.addEventListener('click', function(e) {
                    e.preventDefault();
                    
                    // Get target content
                    const target = this.getAttribute('data-target');
                    
                    // Hide all contents
                    editorContents.forEach(function(content) {
                        content.classList.remove('active');
                    });
                    
                    // Deactivate all tabs
                    editorTabs.forEach(function(tab) {
                        tab.classList.remove('active');
                    });
                    
                    // Show target content
                    document.querySelector(target).classList.add('active');
                    
                    // Activate current tab
                    this.classList.add('active');
                });
            });
            
            // Initialize code editor
            const codeEditor = templateEditor.querySelector('#code-editor');
            const htmlEditor = templateEditor.querySelector('#html-editor');
            const previewFrame = templateEditor.querySelector('#preview-frame');
            
            if (codeEditor && htmlEditor && previewFrame) {
                // Initialize CodeMirror if available
                if (typeof CodeMirror !== 'undefined') {
                    const editor = CodeMirror.fromTextArea(htmlEditor, {
                        mode: 'htmlmixed',
                        lineNumbers: true,
                        lineWrapping: true,
                        theme: 'default',
                        extraKeys: {
                            'Ctrl-Space': 'autocomplete'
                        }
                    });
                    
                    // Update preview on change
                    editor.on('change', function() {
                        updatePreview(editor.getValue());
                    });
                    
                    // Initial preview
                    updatePreview(editor.getValue());
                } else {
                    // Fallback to textarea
                    htmlEditor.addEventListener('input', function() {
                        updatePreview(this.value);
                    });
                    
                    // Initial preview
                    updatePreview(htmlEditor.value);
                }
                
                function updatePreview(html) {
                    // Replace variables with sample values
                    const sampleData = {
                        '{{site_name}}': 'Your Website',
                        '{{site_url}}': 'https://example.com',
                        '{{admin_email}}': '<EMAIL>',
                        '{{user_name}}': 'John Doe',
                        '{{user_email}}': '<EMAIL>',
                        '{{verification_link}}': 'https://example.com/verify?token=sample',
                        '{{reset_link}}': 'https://example.com/reset?token=sample',
                        '{{logo}}': 'https://example.com/logo.png',
                        '{{current_year}}': new Date().getFullYear()
                    };
                    
                    let previewHtml = html;
                    
                    for (const [variable, value] of Object.entries(sampleData)) {
                        previewHtml = previewHtml.replace(new RegExp(variable, 'g'), value);
                    }
                    
                    // Update preview
                    const previewDocument = previewFrame.contentDocument || previewFrame.contentWindow.document;
                    previewDocument.open();
                    previewDocument.write(previewHtml);
                    previewDocument.close();
                }
            }
            
            // Initialize variables dropdown
            const variablesDropdown = templateEditor.querySelector('.variables-dropdown');
            const variablesToggle = templateEditor.querySelector('.variables-dropdown-toggle');
            const variablesItems = templateEditor.querySelectorAll('.variables-dropdown-item');
            
            if (variablesDropdown && variablesToggle) {
                variablesToggle.addEventListener('click', function(e) {
                    e.preventDefault();
                    variablesDropdown.classList.toggle('active');
                });
                
                // Close dropdown when clicking outside
                document.addEventListener('click', function(e) {
                    if (!variablesDropdown.contains(e.target)) {
                        variablesDropdown.classList.remove('active');
                    }
                });
                
                // Insert variable
                variablesItems.forEach(function(item) {
                    item.addEventListener('click', function() {
                        const variable = this.getAttribute('data-variable');
                        
                        // Insert into CodeMirror if available
                        if (typeof CodeMirror !== 'undefined' && window.editor) {
                            window.editor.replaceSelection(variable);
                            window.editor.focus();
                        } else {
                            // Fallback to textarea
                            const textarea = document.querySelector('#html-editor');
                            if (textarea) {
                                const start = textarea.selectionStart;
                                const end = textarea.selectionEnd;
                                const text = textarea.value;
                                
                                textarea.value = text.substring(0, start) + variable + text.substring(end);
                                textarea.selectionStart = textarea.selectionEnd = start + variable.length;
                                textarea.focus();
                                
                                // Trigger input event to update preview
                                const event = new Event('input');
                                textarea.dispatchEvent(event);
                            }
                        }
                        
                        // Close dropdown
                        variablesDropdown.classList.remove('active');
                    });
                });
            }
            
            // Save template
            const saveButton = templateEditor.querySelector('#save-template');
            const templateForm = templateEditor.querySelector('#template-form');
            
            if (saveButton && templateForm) {
                saveButton.addEventListener('click', function(e) {
                    e.preventDefault();
                    
                    // Update textarea value from CodeMirror if available
                    if (typeof CodeMirror !== 'undefined' && window.editor) {
                        window.editor.save();
                    }
                    
                    const formData = new FormData(templateForm);
                    
                    // Show loading state
                    this.disabled = true;
                    this.classList.add('loading');
                    
                    const originalText = this.innerHTML;
                    this.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Saving...';
                    
                    // Save template
                    fetch('ajax/save_template.php', {
                        method: 'POST',
                        body: formData
                    })
                    .then(response => response.json())
                    .then(data => {
                        // Reset button
                        this.disabled = false;
                        this.classList.remove('loading');
                        this.innerHTML = originalText;
                        
                        if (data.success) {
                            AdminCore.showNotification('Template saved successfully.', 'success');
                        } else {
                            AdminCore.showNotification(data.message || 'Error saving template.', 'error');
                        }
                    })
                    .catch(error => {
                        console.error('Error saving template:', error);
                        
                        // Reset button
                        this.disabled = false;
                        this.classList.remove('loading');
                        this.innerHTML = originalText;
                        
                        AdminCore.showNotification('Error saving template.', 'error');
                    });
                });
            }
        }
    },

    // Initialize backup and restore
    initBackupRestore: function() {
        const backupButton = document.querySelector('#create-backup');
        const restoreForm = document.querySelector('#restore-form');
        
        if (backupButton) {
            backupButton.addEventListener('click', function(e) {
                e.preventDefault();
                
                // Show loading state
                this.disabled = true;
                this.classList.add('loading');
                
                const originalText = this.innerHTML;
                this.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Creating Backup...';
                
                // Create backup
                fetch('ajax/create_backup.php')
                    .then(response => response.json())
                    .then(data => {
                        // Reset button
                        this.disabled = false;
                        this.classList.remove('loading');
                        this.innerHTML = originalText;
                        
                        if (data.success) {
                            AdminCore.showNotification('Backup created successfully.', 'success');
                            
                            // Reload page to show new backup
                            window.location.reload();
                        } else {
                            AdminCore.showNotification(data.message || 'Error creating backup.', 'error');
                        }
                    })
                    .catch(error => {
                        console.error('Error creating backup:', error);
                        
                        // Reset button
                        this.disabled = false;
                        this.classList.remove('loading');
                        this.innerHTML = originalText;
                        
                        AdminCore.showNotification('Error creating backup.', 'error');
                    });
            });
        }
        
        if (restoreForm) {
            restoreForm.addEventListener('submit', function(e) {
                e.preventDefault();
                
                if (!confirm('Are you sure you want to restore this backup? This will overwrite your current settings.')) {
                    return;
                }
                
                const formData = new FormData(this);
                const submitButton = this.querySelector('[type="submit"]');
                
                // Disable submit button and show loading state
                if (submitButton) {
                    submitButton.disabled = true;
                    submitButton.classList.add('loading');
                    
                    const originalText = submitButton.innerHTML;
                    submitButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Restoring...';
                }
                
                // Restore backup
                fetch('ajax/restore_backup.php', {
                    method: 'POST',
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    // Re-enable submit button
                    if (submitButton) {
                        submitButton.disabled = false;
                        submitButton.classList.remove('loading');
                        submitButton.innerHTML = originalText;
                    }
                    
                    if (data.success) {
                        AdminCore.showNotification('Backup restored successfully.', 'success');
                        
                        // Reload page
                        window.location.reload();
                    } else {
                        AdminCore.showNotification(data.message || 'Error restoring backup.', 'error');
                    }
                })
                .catch(error => {
                    console.error('Error restoring backup:', error);
                    
                    // Re-enable submit button
                    if (submitButton) {
                        submitButton.disabled = false;
                        submitButton.classList.remove('loading');
                        submitButton.innerHTML = originalText;
                    }
                    
                    AdminCore.showNotification('Error restoring backup.', 'error');
                });
            });
        }
    },

    // Initialize system info
    initSystemInfo: function() {
        const refreshButton = document.querySelector('#refresh-system-info');
        const systemInfoContainer = document.querySelector('#system-info');
        
        if (refreshButton && systemInfoContainer) {
            refreshButton.addEventListener('click', function(e) {
                e.preventDefault();
                
                // Show loading state
                this.disabled = true;
                this.classList.add('loading');
                
                const originalText = this.innerHTML;
                this.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Refreshing...';
                
                // Refresh system info
                fetch('ajax/get_system_info.php')
                    .then(response => response.json())
                    .then(data => {
                        // Reset button
                        this.disabled = false;
                        this.classList.remove('loading');
                        this.innerHTML = originalText;
                        
                        if (data.success) {
                            systemInfoContainer.innerHTML = data.html;
                        } else {
                            AdminCore.showNotification(data.message || 'Error refreshing system info.', 'error');
                        }
                    })
                    .catch(error => {
                        console.error('Error refreshing system info:', error);
                        
                        // Reset button
                        this.disabled = false;
                        this.classList.remove('loading');
                        this.innerHTML = originalText;
                        
                        AdminCore.showNotification('Error refreshing system info.', 'error');
                    });
            });
        }
    },

    // Initialize admin logo path
    initAdminLogoPath: function() {
        const logoPathInput = document.querySelector('#admin-logo-path');
        const logoPreview = document.querySelector('#admin-logo-preview');
        
        if (logoPathInput && logoPreview) {
            // Update preview on input
            logoPathInput.addEventListener('input', function() {
                logoPreview.src = this.value;
            });
        }
    }
};
