/**
 * Settings Tabs JavaScript
 *
 * Handles tab functionality for the settings page
 */

document.addEventListener('DOMContentLoaded', function() {
    // Initialize secondary tabs
    initSecondaryTabs();
});

/**
 * Initialize secondary tabs functionality
 */
function initSecondaryTabs() {
    const tabButtons = document.querySelectorAll('.secondary-tab-button');
    
    if (tabButtons.length === 0) {
        return; // No tabs found
    }
    
    // Create tab content containers if they don't exist
    const tabContents = {};
    
    // First, find all settings cells and organize them
    const allCells = document.querySelectorAll('.settings-cell');
    
    // Create tab content sections
    tabButtons.forEach(button => {
        const targetId = button.getAttribute('data-target');
        
        // Create a container for this tab's content if it doesn't exist
        if (!document.getElementById(targetId + '-content')) {
            const tabContent = document.createElement('div');
            tabContent.id = targetId + '-content';
            tabContent.className = 'tab-content';
            tabContent.style.display = button.classList.contains('active') ? 'block' : 'none';
            
            // Insert after the secondary tabs container
            const tabsContainer = document.querySelector('.secondary-tabs-container');
            if (tabsContainer) {
                tabsContainer.parentNode.insertBefore(tabContent, tabsContainer.nextSibling);
            }
            
            tabContents[targetId] = tabContent;
        }
    });
    
    // Organize settings into tabs
    if (tabButtons[0].getAttribute('data-target') === 'general-email-settings') {
        // Email settings specific organization
        
        // General email settings
        const generalEmailSettings = ['from_email', 'from_name', 'reply_to', 'use_smtp'];
        
        // SMTP settings
        const smtpSettings = ['smtp_host', 'smtp_port', 'smtp_security', 'smtp_username', 'smtp_password'];
        
        // Move cells to appropriate tab content
        allCells.forEach(cell => {
            // Find the input element to determine which setting this is
            const inputs = cell.querySelectorAll('input, select, textarea');
            if (inputs.length === 0) {
                // Check if this is a template cell
                const templateItem = cell.querySelector('.template-item');
                if (templateItem) {
                    // This is a template cell, move to template settings tab
                    if (tabContents['template-settings']) {
                        tabContents['template-settings'].appendChild(cell.cloneNode(true));
                        cell.style.display = 'none';
                    }
                }
                return;
            }
            
            const input = inputs[0];
            const name = input.getAttribute('name');
            
            if (!name) return;
            
            // Determine which tab this setting belongs to
            if (generalEmailSettings.includes(name)) {
                if (tabContents['general-email-settings']) {
                    tabContents['general-email-settings'].appendChild(cell.cloneNode(true));
                    cell.style.display = 'none';
                }
            } else if (smtpSettings.includes(name) || name.startsWith('smtp_')) {
                if (tabContents['smtp-settings']) {
                    tabContents['smtp-settings'].appendChild(cell.cloneNode(true));
                    cell.style.display = 'none';
                    
                    // Add SMTP class for visibility toggling
                    cell.classList.add('smtp-setting');
                }
            } else if (name.startsWith('template_')) {
                if (tabContents['template-settings']) {
                    tabContents['template-settings'].appendChild(cell.cloneNode(true));
                    cell.style.display = 'none';
                }
            }
        });
    }
    
    // Add click event to tab buttons
    tabButtons.forEach(button => {
        button.addEventListener('click', function() {
            const targetId = this.getAttribute('data-target');
            
            // Hide all tab contents
            document.querySelectorAll('.tab-content').forEach(content => {
                content.style.display = 'none';
            });
            
            // Show the selected tab content
            const targetContent = document.getElementById(targetId + '-content');
            if (targetContent) {
                targetContent.style.display = 'block';
            }
            
            // Update active state of buttons
            tabButtons.forEach(btn => {
                btn.classList.remove('active');
            });
            this.classList.add('active');
            
            // Special handling for SMTP settings
            if (targetId === 'smtp-settings') {
                const useSmtpCheckbox = document.querySelector('input[name="use_smtp"]');
                if (useSmtpCheckbox) {
                    // Toggle visibility of SMTP settings based on checkbox
                    const smtpSettings = document.querySelectorAll('.smtp-setting');
                    smtpSettings.forEach(setting => {
                        setting.style.display = useSmtpCheckbox.checked ? 'block' : 'none';
                    });
                }
            }
        });
    });
    
    // Handle SMTP checkbox toggle
    const useSmtpCheckbox = document.querySelector('input[name="use_smtp"]');
    if (useSmtpCheckbox) {
        useSmtpCheckbox.addEventListener('change', function() {
            const smtpSettings = document.querySelectorAll('.smtp-setting');
            smtpSettings.forEach(setting => {
                setting.style.display = this.checked ? 'block' : 'none';
            });
        });
        
        // Initial state
        const smtpSettings = document.querySelectorAll('.smtp-setting');
        smtpSettings.forEach(setting => {
            setting.style.display = useSmtpCheckbox.checked ? 'block' : 'none';
        });
    }
}
