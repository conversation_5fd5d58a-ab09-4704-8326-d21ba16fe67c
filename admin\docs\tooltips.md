# Tooltips Documentation

This document provides guidance on how to use the enhanced tooltip system in the admin panel.

## Basic Usage

To add a tooltip to any element, simply add the `data-tooltip` attribute with the tooltip text:

```html
<button data-tooltip="This is a tooltip">Hover me</button>
```

## Tooltip Positioning

You can control the position of the tooltip using the `data-tooltip-position` attribute:

```html
<button data-tooltip="Top tooltip" data-tooltip-position="top">Top</button>
<button data-tooltip="Bottom tooltip" data-tooltip-position="bottom">Bottom</button>
<button data-tooltip="Left tooltip" data-tooltip-position="left">Left</button>
<button data-tooltip="Right tooltip" data-tooltip-position="right">Right</button>
<button data-tooltip="Auto-positioned tooltip" data-tooltip-position="auto">Auto</button>
```

The default position is `top`. The `auto` position will automatically choose the best position based on available space.

## Tooltip Types

You can use different tooltip types for different contexts:

```html
<button data-tooltip="Default tooltip">Default</button>
<button data-tooltip="Information tooltip" data-tooltip-type="info">Info</button>
<button data-tooltip="Success tooltip" data-tooltip-type="success">Success</button>
<button data-tooltip="Warning tooltip" data-tooltip-type="warning">Warning</button>
<button data-tooltip="Danger tooltip" data-tooltip-type="danger">Danger</button>
<button data-tooltip="Light tooltip" data-tooltip-type="light">Light</button>
```

## Custom Width

You can set a custom width for the tooltip:

```html
<button data-tooltip="This tooltip has a custom width" data-tooltip-width="300px">Custom Width</button>
```

## Tooltip Icons

You can add an icon to the element with the tooltip:

```html
<span data-tooltip="Help text" data-tooltip-icon="fas fa-question-circle">Help</span>
```

This will add a question mark icon after the text "Help". You can use any Font Awesome icon class.

## Standalone Tooltip Icons

For standalone tooltip icons (common in form labels):

```html
<label>
    Field Name
    <i class="fas fa-info-circle" data-tooltip="This is help text for the field"></i>
</label>
```

## Multiline Tooltips

You can create multiline tooltips using several methods:

### Using Line Breaks

```html
<button data-tooltip="Line 1\nLine 2\nLine 3">Multiline with \n</button>
```

### Using HTML Entities

```html
<button data-tooltip="Line 1&lt;br&gt;Line 2&lt;br&gt;Line 3">Multiline with HTML entities</button>
```

### Using HTML Tags (automatically converted)

```html
<button data-tooltip="Line 1<br>Line 2<br>Line 3">Multiline with HTML tags</button>
```

### Using the Multiline Attribute

```html
<button data-tooltip="This is a long tooltip that will be displayed as multiline" data-tooltip-multiline="true">Multiline Attribute</button>
```

All of these methods will produce a tooltip with multiple lines of text. Choose the method that works best for your context.

## Advanced Tooltip Features

### Tooltip with Title

```html
<button data-tooltip="Detailed explanation of the feature" data-tooltip-title="Feature Name">Tooltip with Title</button>
```

### Tooltip with Icon

```html
<button data-tooltip="This tooltip includes an icon" data-tooltip-content-icon="fas fa-info-circle">Tooltip with Icon</button>
```

### Tooltip with Title and Icon

```html
<button
  data-tooltip="Detailed explanation of the feature"
  data-tooltip-title="Feature Name"
  data-tooltip-content-icon="fas fa-info-circle">
  Tooltip with Title and Icon
</button>
```

### Custom Width Tooltip

```html
<button data-tooltip="This tooltip has a custom width" data-tooltip-width="400px">Custom Width</button>
```

### Tooltip Types

```html
<button data-tooltip="Information tooltip" data-tooltip-type="info">Info</button>
<button data-tooltip="Success tooltip" data-tooltip-type="success">Success</button>
<button data-tooltip="Warning tooltip" data-tooltip-type="warning">Warning</button>
<button data-tooltip="Danger tooltip" data-tooltip-type="danger">Danger</button>
<button data-tooltip="Light tooltip" data-tooltip-type="light">Light</button>
```

### Disable Animation

```html
<button data-tooltip="This tooltip appears without animation" data-tooltip-no-animation="true">No Animation</button>
```

## Common Use Cases

### Form Field Help

```html
<div class="form-group">
    <label for="field-name">
        Field Name
        <i class="fas fa-info-circle" data-tooltip="Help text for this field" data-tooltip-position="right"></i>
    </label>
    <input type="text" id="field-name" name="field-name">
</div>
```

### Feature Explanation

```html
<h3>
    Feature Name
    <span class="tooltip-container" data-tooltip="This feature allows you to..." data-tooltip-icon="fas fa-info-circle"></span>
</h3>
```

### Warning About Actions

```html
<button class="admin-btn danger">
    Delete Item
    <i class="fas fa-exclamation-triangle" data-tooltip="This action cannot be undone" data-tooltip-type="warning"></i>
</button>
```

### Table Column Headers

```html
<th>
    Column Name
    <i class="fas fa-info-circle" data-tooltip="Explanation of this column" data-tooltip-position="bottom"></i>
</th>
```

## Accessibility

The tooltip system is designed to be accessible:

- Tooltips can be triggered by focus events (for keyboard navigation)
- Tooltips can be triggered by click events (for touch devices)
- Tooltip content is available to screen readers

## Mobile Support

On mobile devices:
- Tooltips are triggered by tapping the element
- Tooltips are positioned to avoid going off-screen
- Tooltip text is sized appropriately for small screens

## Best Practices

1. **Keep tooltips concise**: Tooltips should provide brief, helpful information.
2. **Use appropriate positioning**: Choose a position that won't obscure important content.
3. **Use appropriate types**: Use different tooltip types to convey different kinds of information.
4. **Don't rely solely on tooltips**: Critical information should be visible without requiring a tooltip.
5. **Be consistent**: Use tooltips consistently throughout the interface.

## Examples

### Settings Page

```html
<div class="form-group">
    <label for="smtp-host">
        SMTP Host
        <i class="fas fa-info-circle" data-tooltip="The hostname of your SMTP server (e.g., smtp.gmail.com)" data-tooltip-position="right"></i>
    </label>
    <input type="text" id="smtp-host" name="smtp_host">
</div>
```

### Dashboard Widgets

```html
<div class="widget-header">
    <h3>Analytics</h3>
    <i class="fas fa-info-circle" data-tooltip="Shows visitor statistics for the last 30 days" data-tooltip-position="left"></i>
</div>
```

### Feature Toggles

```html
<div class="feature-toggle">
    <label for="feature-toggle">
        Enable Feature
        <i class="fas fa-info-circle" data-tooltip="Enabling this feature will..." data-tooltip-position="top"></i>
    </label>
    <input type="checkbox" id="feature-toggle" name="feature_toggle">
</div>
```
