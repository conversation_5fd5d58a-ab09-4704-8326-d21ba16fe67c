/**
 * Appearance Settings JavaScript
 *
 * This file enhances the application of appearance settings to the admin panel.
 * It loads system appearance settings and applies them to the admin panel.
 */

document.addEventListener('DOMContentLoaded', function() {
    // Load system appearance settings
    loadSystemAppearanceSettings();

    // Apply user settings (these take precedence over system settings)
    const settingsContainer = document.getElementById('user-settings-data');
    if (settingsContainer) {
        try {
            const settings = JSON.parse(settingsContainer.getAttribute('data-settings'));
            console.log('User settings loaded:', settings);

            // Apply theme (user settings take precedence)
            if (settings.theme) {
                applyTheme(settings.theme);
            }
        } catch (error) {
            console.error('Error parsing user settings:', error);
        }
    }
});

/**
 * Load system appearance settings from the database
 */
function loadSystemAppearanceSettings() {
    // Fetch appearance settings from the server with absolute path
    fetch(window.location.pathname.substring(0, window.location.pathname.lastIndexOf('/') + 1) + 'ajax/get_admin_appearance_settings.php')
        .then(response => {
            // Check if response is valid JSON
            const contentType = response.headers.get('content-type');
            if (contentType && contentType.includes('application/json')) {
                return response.json();
            } else {
                throw new Error('Invalid response format');
            }
        })
        .then(data => {
            if (data.success) {
                console.log('Admin appearance settings loaded:', data.settings);
                applySystemAppearanceSettings(data.settings);
            } else {
                // Fallback to old appearance settings endpoint if new one fails
                console.log('Falling back to legacy appearance settings');
                return fetchLegacySettings();
            }
        })
        .catch(error => {
            console.error('Error fetching admin appearance settings:', error);
            // Fallback to old appearance settings endpoint
            return fetchLegacySettings();
        });

    // Function to fetch legacy settings
    function fetchLegacySettings() {
        return fetch(window.location.pathname.substring(0, window.location.pathname.lastIndexOf('/') + 1) + 'ajax/get_appearance_settings.php')
            .then(response => {
                // Check if response is valid JSON
                const contentType = response.headers.get('content-type');
                if (contentType && contentType.includes('application/json')) {
                    return response.json();
                } else {
                    throw new Error('Invalid response format');
                }
            })
            .then(data => {
                if (data.success) {
                    console.log('Legacy appearance settings loaded:', data.settings);
                    applySystemAppearanceSettings(data.settings);
                } else {
                    console.error('Error loading appearance settings:', data.message);
                    // Apply default settings if both endpoints fail
                    applyDefaultSettings();
                }
            })
            .catch(error => {
                console.error('Error fetching legacy appearance settings:', error);
                // Apply default settings if both endpoints fail
                applyDefaultSettings();
            });
    }

    // Function to apply default settings if all else fails
    function applyDefaultSettings() {
        console.log('Applying default appearance settings');
        const defaultSettings = {
            'admin_primary_color': '#f1ca2f',
            'admin_secondary_color': '#3c3c45',
            'admin_success_color': '#28a745',
            'admin_warning_color': '#ffc107',
            'admin_danger_color': '#dc3545',
            'admin_heading_font': 'Arial, sans-serif',
            'admin_body_font': 'Arial, sans-serif'
        };
        applySystemAppearanceSettings(defaultSettings);
    }
}

/**
 * Apply system appearance settings
 * @param {Object} settings - The system appearance settings
 */
function applySystemAppearanceSettings(settings) {
    // Check for admin_appearance settings first, then fall back to legacy settings

    // Apply primary color
    if (settings.admin_primary_color) {
        document.documentElement.style.setProperty('--primary-color', settings.admin_primary_color);
    } else if (settings.primary_color) {
        document.documentElement.style.setProperty('--primary-color', settings.primary_color);
    }

    // Apply secondary color
    if (settings.admin_secondary_color) {
        document.documentElement.style.setProperty('--secondary-color', settings.admin_secondary_color);
    } else if (settings.secondary_color) {
        document.documentElement.style.setProperty('--secondary-color', settings.secondary_color);
    }

    // Apply success color
    if (settings.admin_success_color) {
        document.documentElement.style.setProperty('--success-color', settings.admin_success_color);
    } else if (settings.success_color) {
        document.documentElement.style.setProperty('--success-color', settings.success_color);
    }

    // Apply warning color
    if (settings.admin_warning_color) {
        document.documentElement.style.setProperty('--warning-color', settings.admin_warning_color);
    } else if (settings.warning_color) {
        document.documentElement.style.setProperty('--warning-color', settings.warning_color);
    }

    // Apply danger color
    if (settings.admin_danger_color) {
        document.documentElement.style.setProperty('--danger-color', settings.admin_danger_color);
    } else if (settings.danger_color) {
        document.documentElement.style.setProperty('--danger-color', settings.danger_color);
    }

    // Apply accent color (legacy)
    if (settings.accent_color) {
        document.documentElement.style.setProperty('--accent-color', settings.accent_color);
    }

    // Apply text color (legacy)
    if (settings.text_color) {
        document.documentElement.style.setProperty('--text-color', settings.text_color);
    }

    // Apply background color (legacy)
    if (settings.background_color) {
        document.documentElement.style.setProperty('--background-color', settings.background_color);
    }

    // Apply header background color (legacy)
    if (settings.header_background_color) {
        document.documentElement.style.setProperty('--header-bg-color', settings.header_background_color);
    }

    // Apply sidebar background color (legacy)
    if (settings.sidebar_background_color) {
        document.documentElement.style.setProperty('--sidebar-bg-color', settings.sidebar_background_color);
    }

    // Apply button color (legacy)
    if (settings.button_color) {
        document.documentElement.style.setProperty('--button-color', settings.button_color);
    }

    // Apply button text color (legacy)
    if (settings.button_text_color) {
        document.documentElement.style.setProperty('--button-text-color', settings.button_text_color);
    }

    // Apply link color (legacy)
    if (settings.link_color) {
        document.documentElement.style.setProperty('--link-color', settings.link_color);
    }

    // Apply border radius (legacy)
    if (settings.border_radius) {
        document.documentElement.style.setProperty('--border-radius', settings.border_radius + 'px');
    }

    // Apply font family
    if (settings.admin_heading_font) {
        document.documentElement.style.setProperty('--heading-font', settings.admin_heading_font);
    }

    if (settings.admin_body_font) {
        document.documentElement.style.setProperty('--body-font', settings.admin_body_font);
    } else if (settings.font_family) {
        // Legacy font setting
        document.documentElement.style.setProperty('--body-font', settings.font_family);
        document.documentElement.style.setProperty('--font-family', settings.font_family);

        // Load Google Font if needed
        if (settings.font_family !== 'Open Sans' &&
            settings.font_family !== 'Arial' &&
            settings.font_family !== 'Helvetica' &&
            settings.font_family !== 'sans-serif') {

            // Create link element for Google Font
            const link = document.createElement('link');
            link.rel = 'stylesheet';
            link.href = `https://fonts.googleapis.com/css2?family=${settings.font_family.replace(' ', '+')}:wght@300;400;600;700&display=swap`;
            document.head.appendChild(link);
        }
    }

    // Apply font weights
    if (settings.admin_heading_font_weight) {
        document.documentElement.style.setProperty('--heading-font-weight', settings.admin_heading_font_weight);
    }

    if (settings.admin_body_font_weight) {
        document.documentElement.style.setProperty('--body-font-weight', settings.admin_body_font_weight);
    }

    // Apply font size (legacy)
    if (settings.font_size) {
        document.documentElement.style.setProperty('--base-font-size', settings.font_size + 'px');
    }

    // Apply custom CSS if available (legacy)
    if (settings.custom_css) {
        const style = document.createElement('style');
        style.textContent = settings.custom_css;
        document.head.appendChild(style);
    }

    // Apply logo path if available
    if (settings.admin_logo_path) {
        const logoElements = document.querySelectorAll('.sidebar-logo img');
        logoElements.forEach(logo => {
            logo.src = '../' + settings.admin_logo_path;
        });
    }
}

/**
 * Apply theme setting
 * @param {string} theme - The theme to apply (light, dark, auto)
 */
function applyTheme(theme) {
    if (!theme) return;

    const body = document.body;

    // Remove existing theme classes
    body.classList.remove('theme-light', 'theme-dark');

    if (theme === 'auto') {
        // Check system preference
        if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {
            body.classList.add('theme-dark');
            document.documentElement.setAttribute('data-theme', 'dark');
        } else {
            body.classList.add('theme-light');
            document.documentElement.setAttribute('data-theme', 'light');
        }

        // Listen for changes in system preference
        window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', e => {
            if (e.matches) {
                body.classList.remove('theme-light');
                body.classList.add('theme-dark');
                document.documentElement.setAttribute('data-theme', 'dark');
            } else {
                body.classList.remove('theme-dark');
                body.classList.add('theme-light');
                document.documentElement.setAttribute('data-theme', 'light');
            }
        });
    } else {
        // Apply specific theme
        body.classList.add('theme-' + theme);
        document.documentElement.setAttribute('data-theme', theme);
    }

    console.log('Theme applied:', theme);
}
