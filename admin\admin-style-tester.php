<?php
// Include header
$page_title = 'Admin Style Tester';
include 'includes/header.php';
?>

<div class="admin-main-content">
    <div class="content-header">
        <h1>🎨 Admin Style Tester</h1>
        <p>This page tests all critical admin panel components to ensure styles are working correctly.</p>
    </div>

    <!-- Test Controls -->
    <div class="card mb-4">
        <div class="card-header">
            <h3>Test Controls</h3>
        </div>
        <div class="card-body">
            <button class="btn btn-primary" onclick="runAllTests()">Run All Tests</button>
            <button class="btn btn-secondary" onclick="testButtons()">Test Buttons</button>
            <button class="btn btn-success" onclick="testForms()">Test Forms</button>
            <button class="btn btn-warning" onclick="testTables()">Test Tables</button>
            <button class="btn btn-info" onclick="testCards()">Test Cards</button>
            <button class="btn btn-danger" onclick="testModals()">Test Modals</button>
            
            <div id="test-results" class="mt-3"></div>
        </div>
    </div>

    <!-- Button Tests -->
    <div class="card mb-4">
        <div class="card-header">
            <h3>Button Tests</h3>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <h5>Primary Buttons</h5>
                    <button class="btn btn-primary me-2 mb-2">Primary Button</button>
                    <button class="btn btn-primary btn-sm me-2 mb-2">Small Primary</button>
                    <button class="btn btn-primary btn-lg me-2 mb-2">Large Primary</button>
                    
                    <h5 class="mt-3">Secondary Buttons</h5>
                    <button class="btn btn-secondary me-2 mb-2">Secondary</button>
                    <button class="btn btn-success me-2 mb-2">Success</button>
                    <button class="btn btn-danger me-2 mb-2">Danger</button>
                    <button class="btn btn-warning me-2 mb-2">Warning</button>
                    <button class="btn btn-info me-2 mb-2">Info</button>
                </div>
                <div class="col-md-6">
                    <h5>Button States</h5>
                    <button class="btn btn-primary me-2 mb-2" disabled>Disabled</button>
                    <button class="btn btn-outline-primary me-2 mb-2">Outline</button>
                    
                    <h5 class="mt-3">Icon Buttons</h5>
                    <button class="btn btn-primary me-2 mb-2">
                        <i class="fas fa-plus"></i> Add New
                    </button>
                    <button class="btn btn-danger me-2 mb-2">
                        <i class="fas fa-trash"></i> Delete
                    </button>
                    <button class="btn btn-success me-2 mb-2">
                        <i class="fas fa-save"></i> Save
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Form Tests -->
    <div class="card mb-4">
        <div class="card-header">
            <h3>Form Tests</h3>
        </div>
        <div class="card-body">
            <form>
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="form-label required">Text Input</label>
                            <input type="text" class="form-control" placeholder="Enter text">
                        </div>
                        
                        <div class="form-group">
                            <label class="form-label">Email Input</label>
                            <input type="email" class="form-control" placeholder="Enter email">
                        </div>
                        
                        <div class="form-group">
                            <label class="form-label">Select Dropdown</label>
                            <select class="form-control">
                                <option>Option 1</option>
                                <option>Option 2</option>
                                <option>Option 3</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="form-label">Textarea</label>
                            <textarea class="form-control" rows="3" placeholder="Enter description"></textarea>
                        </div>
                        
                        <div class="form-group">
                            <label class="form-label">File Input</label>
                            <input type="file" class="form-control">
                        </div>
                        
                        <div class="form-group">
                            <div class="form-check">
                                <input type="checkbox" class="form-check-input" id="check1">
                                <label class="form-check-label" for="check1">Checkbox Option</label>
                            </div>
                            <div class="form-check">
                                <input type="radio" class="form-check-input" name="radio" id="radio1">
                                <label class="form-check-label" for="radio1">Radio Option 1</label>
                            </div>
                            <div class="form-check">
                                <input type="radio" class="form-check-input" name="radio" id="radio2">
                                <label class="form-check-label" for="radio2">Radio Option 2</label>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Table Tests -->
    <div class="card mb-4">
        <div class="card-header">
            <h3>Table Tests</h3>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Name</th>
                            <th>Email</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>1</td>
                            <td>John Doe</td>
                            <td><EMAIL></td>
                            <td><span class="badge bg-success">Active</span></td>
                            <td>
                                <button class="btn btn-sm btn-primary">Edit</button>
                                <button class="btn btn-sm btn-danger">Delete</button>
                            </td>
                        </tr>
                        <tr>
                            <td>2</td>
                            <td>Jane Smith</td>
                            <td><EMAIL></td>
                            <td><span class="badge bg-warning">Pending</span></td>
                            <td>
                                <button class="btn btn-sm btn-primary">Edit</button>
                                <button class="btn btn-sm btn-danger">Delete</button>
                            </td>
                        </tr>
                        <tr>
                            <td>3</td>
                            <td>Bob Johnson</td>
                            <td><EMAIL></td>
                            <td><span class="badge bg-danger">Inactive</span></td>
                            <td>
                                <button class="btn btn-sm btn-primary">Edit</button>
                                <button class="btn btn-sm btn-danger">Delete</button>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Alert Tests -->
    <div class="card mb-4">
        <div class="card-header">
            <h3>Alert Tests</h3>
        </div>
        <div class="card-body">
            <div class="alert alert-success">
                <strong>Success!</strong> This is a success alert.
            </div>
            <div class="alert alert-info">
                <strong>Info!</strong> This is an info alert.
            </div>
            <div class="alert alert-warning">
                <strong>Warning!</strong> This is a warning alert.
            </div>
            <div class="alert alert-danger">
                <strong>Error!</strong> This is an error alert.
            </div>
        </div>
    </div>

    <!-- Navigation Tests -->
    <div class="card mb-4">
        <div class="card-header">
            <h3>Navigation Tests</h3>
        </div>
        <div class="card-body">
            <ul class="nav nav-tabs">
                <li class="nav-item">
                    <a class="nav-link active" href="#">Active Tab</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="#">Tab 2</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="#">Tab 3</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link disabled" href="#">Disabled</a>
                </li>
            </ul>
            
            <div class="mt-3">
                <nav>
                    <ul class="pagination">
                        <li class="page-item disabled">
                            <a class="page-link" href="#">Previous</a>
                        </li>
                        <li class="page-item active">
                            <a class="page-link" href="#">1</a>
                        </li>
                        <li class="page-item">
                            <a class="page-link" href="#">2</a>
                        </li>
                        <li class="page-item">
                            <a class="page-link" href="#">3</a>
                        </li>
                        <li class="page-item">
                            <a class="page-link" href="#">Next</a>
                        </li>
                    </ul>
                </nav>
            </div>
        </div>
    </div>

    <!-- User Dropdown Test -->
    <div class="card mb-4">
        <div class="card-header">
            <h3>User Dropdown Test</h3>
        </div>
        <div class="card-body">
            <p>The user dropdown should be visible in the top-right corner of the admin header.</p>
            <button class="btn btn-primary" onclick="testUserDropdown()">Test User Dropdown</button>
            <div id="dropdown-test-result" class="mt-2"></div>
        </div>
    </div>
</div>

<script>
function runAllTests() {
    const results = document.getElementById('test-results');
    let html = '<div class="alert alert-info"><h5>🧪 Running Comprehensive Style Tests...</h5>';
    
    // Test critical elements
    const tests = [
        { selector: '.btn-primary', name: 'Primary Buttons', expected: 'rgb(241, 202, 47)' },
        { selector: '.form-control', name: 'Form Controls', expected: 'rgb(255, 255, 255)' },
        { selector: '.table', name: 'Tables', expected: 'separate' },
        { selector: '.card', name: 'Cards', expected: 'rgb(255, 255, 255)' },
        { selector: '.alert-success', name: 'Success Alerts', expected: 'rgb(212, 237, 218)' },
        { selector: '.nav-link', name: 'Navigation Links', expected: 'block' }
    ];
    
    let passedTests = 0;
    
    tests.forEach(test => {
        const element = document.querySelector(test.selector);
        if (element) {
            const styles = window.getComputedStyle(element);
            let property = 'background-color';
            
            if (test.name === 'Tables') property = 'border-collapse';
            if (test.name === 'Navigation Links') property = 'display';
            
            const value = styles.getPropertyValue(property);
            const passed = value.includes(test.expected) || value === test.expected;
            
            if (passed) passedTests++;
            
            html += `<p><strong>${test.name}:</strong> ${passed ? '✅ PASS' : '❌ FAIL'} (${property}: ${value})</p>`;
        } else {
            html += `<p><strong>${test.name}:</strong> ❌ ELEMENT NOT FOUND</p>`;
        }
    });
    
    html += `<hr><p><strong>Results:</strong> ${passedTests}/${tests.length} tests passed</p>`;
    
    if (passedTests === tests.length) {
        html += '<p class="text-success"><strong>🎉 All styles are working correctly!</strong></p>';
    } else {
        html += '<p class="text-warning"><strong>⚠️ Some styles may have override issues.</strong></p>';
    }
    
    html += '</div>';
    results.innerHTML = html;
}

function testUserDropdown() {
    const result = document.getElementById('dropdown-test-result');
    const dropdown = document.querySelector('.user-dropdown');
    const toggle = document.querySelector('.user-menu-toggle');
    
    if (!dropdown || !toggle) {
        result.innerHTML = '<div class="alert alert-danger">❌ User dropdown elements not found</div>';
        return;
    }
    
    const styles = window.getComputedStyle(dropdown);
    const position = styles.position;
    const zIndex = styles.zIndex;
    const background = styles.backgroundColor;
    
    let html = '<div class="alert alert-info">';
    html += `<p><strong>Position:</strong> ${position} ${position === 'absolute' ? '✅' : '❌'}</p>`;
    html += `<p><strong>Z-Index:</strong> ${zIndex} ${zIndex === '9999' ? '✅' : '❌'}</p>`;
    html += `<p><strong>Background:</strong> ${background} ${background.includes('255, 255, 255') ? '✅' : '❌'}</p>`;
    
    // Test toggle functionality
    toggle.click();
    setTimeout(() => {
        const isVisible = dropdown.classList.contains('show');
        html += `<p><strong>Toggle Function:</strong> ${isVisible ? '✅ Working' : '❌ Not working'}</p>`;
        
        if (isVisible) {
            toggle.click(); // Close it
        }
        
        html += '</div>';
        result.innerHTML = html;
    }, 100);
}

// Auto-run tests on page load
window.addEventListener('load', function() {
    setTimeout(runAllTests, 1000);
});
</script>

<?php include 'includes/footer.php'; ?>
