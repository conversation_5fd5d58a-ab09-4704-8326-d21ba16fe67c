<?php
/**
 * Process Bulk Actions for News Posts
 *
 * This file handles bulk actions for news posts, including:
 * - Deleting multiple posts
 * - Updating category for multiple posts
 *
 * It includes CSRF protection and proper validation.
 */

session_start();
require_once 'config.php';
require_once 'lib/Permissions.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    redirect('index.php');
}

// Initialize permission system
$permissions = new Permissions($conn, $_SESSION['user_id']);

// Check if user has permission to manage news
if (!$permissions->hasPermission('manage_news')) {
    $_SESSION['error_message'] = "You do not have permission to manage news posts.";
    redirect('all_news.php');
}

// Check if the form was submitted
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    $_SESSION['error_message'] = "Invalid request method.";
    redirect('all_news.php');
}

// Validate CSRF token
if (!isset($_POST['csrf_token']) || !validate_csrf_token($_POST['csrf_token'])) {
    $_SESSION['error_message'] = "Security validation failed. Please try again.";
    redirect('all_news.php');
}

// Check if required parameters are provided
if (!isset($_POST['post_ids']) || empty($_POST['post_ids']) || !isset($_POST['action_type']) || empty($_POST['action_type'])) {
    $_SESSION['error_message'] = "Missing required parameters.";
    redirect('all_news.php');
}

// Get the action type and post IDs
$action_type = $_POST['action_type'];
$post_ids = $_POST['post_ids'];
$id_array = explode(',', $post_ids);

// Validate IDs (ensure they are all integers)
$valid_ids = [];
foreach ($id_array as $id) {
    $id = (int)$id;
    if ($id > 0) {
        $valid_ids[] = $id;
    }
}

if (empty($valid_ids)) {
    $_SESSION['error_message'] = "Invalid post IDs.";
    redirect('all_news.php');
}

// Process based on action type
if ($action_type === 'delete') {
    // Process delete action

    // Create a comma-separated list of placeholders for the SQL query
    $placeholders = implode(',', array_fill(0, count($valid_ids), '?'));

    // Get the image filenames before deleting the posts
    $stmt = $conn->prepare("SELECT id, image FROM news WHERE id IN ($placeholders)");
    $types = str_repeat('i', count($valid_ids));
    $stmt->bind_param($types, ...$valid_ids);
    $stmt->execute();
    $result = $stmt->get_result();

    $images_to_delete = [];
    while ($row = $result->fetch_assoc()) {
        if (!empty($row['image'])) {
            $images_to_delete[] = $row['image'];
        }
    }

    // Delete the posts
    $stmt = $conn->prepare("DELETE FROM news WHERE id IN ($placeholders)");
    $stmt->bind_param($types, ...$valid_ids);

    if ($stmt->execute()) {
        // Delete the image files
        foreach ($images_to_delete as $image) {
            $image_path = "../images/news/" . $image;
            if (file_exists($image_path)) {
                unlink($image_path);
            }
        }

        // Log the activity
        require_once 'includes/admin-functions.php';
        $count = count($valid_ids);
        log_activity('delete', 'Bulk deleted ' . $count . ' news post' . ($count > 1 ? 's' : ''), $_SESSION['user_id']);

        $_SESSION['success_message'] = "$count post" . ($count > 1 ? "s" : "") . " deleted successfully.";
    } else {
        $_SESSION['error_message'] = "Error deleting posts: " . $conn->error;
    }

} elseif ($action_type === 'category') {
    // Process category update action

    // Check if category_id is provided
    if (!isset($_POST['category_id']) || empty($_POST['category_id'])) {
        $_SESSION['error_message'] = "Missing category ID.";
        redirect('all_news.php');
    }

    $category_id = (int)$_POST['category_id'];

    // Validate category ID
    $category_stmt = $conn->prepare("SELECT id FROM categories WHERE id = ?");
    $category_stmt->bind_param("i", $category_id);
    $category_stmt->execute();
    $category_result = $category_stmt->get_result();

    if ($category_result->num_rows === 0) {
        $_SESSION['error_message'] = "Invalid category ID.";
        redirect('all_news.php');
    }

    // Create a comma-separated list of placeholders for the SQL query
    $placeholders = implode(',', array_fill(0, count($valid_ids), '?'));

    // Update the posts with the new category
    $stmt = $conn->prepare("UPDATE news SET category_id = ? WHERE id IN ($placeholders)");
    $types = "i" . str_repeat('i', count($valid_ids));
    $params = array_merge([$category_id], $valid_ids);
    $stmt->bind_param($types, ...$params);

    if ($stmt->execute()) {
        // Log the activity
        require_once 'includes/admin-functions.php';
        $count = count($valid_ids);

        // Get category name for logging
        $cat_stmt = $conn->prepare("SELECT name FROM categories WHERE id = ?");
        $cat_stmt->bind_param("i", $category_id);
        $cat_stmt->execute();
        $cat_result = $cat_stmt->get_result();
        $category_name = 'Unknown Category';
        if ($cat_result && $cat_result->num_rows > 0) {
            $cat_row = $cat_result->fetch_assoc();
            $category_name = $cat_row['name'];
        }

        log_activity('update', 'Bulk updated category to "' . $category_name . '" for ' . $count . ' news post' . ($count > 1 ? 's' : ''), $_SESSION['user_id']);

        $_SESSION['success_message'] = "Category updated for $count post" . ($count > 1 ? "s" : "") . ".";
    } else {
        $_SESSION['error_message'] = "Error updating category: " . $conn->error;
    }

} else {
    $_SESSION['error_message'] = "Invalid action type.";
}

// Redirect back to the news list with appropriate parameters
$redirect_params = [];

// Preserve any existing filter, sort, and pagination parameters
if (isset($_SERVER['HTTP_REFERER'])) {
    $referer_parts = parse_url($_SERVER['HTTP_REFERER']);
    if (isset($referer_parts['query'])) {
        parse_str($referer_parts['query'], $query_params);

        // Preserve these parameters if they exist
        $preserve_params = ['page', 'sort', 'order', 'category', 'per_page'];
        foreach ($preserve_params as $param) {
            if (isset($query_params[$param])) {
                $redirect_params[$param] = $query_params[$param];
            }
        }
    }
}

redirect('all_news.php', $redirect_params);
?>
