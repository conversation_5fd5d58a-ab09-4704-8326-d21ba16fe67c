<?php
/**
 * Admin Login Page
 *
 * This is the main entry point for the admin panel.
 * It handles user authentication and redirects to the dashboard upon successful login.
 */

// Start session
session_start();

// Include necessary files
require_once 'config.php';
require_once 'includes/security.php';
require_once 'includes/helpers.php';
require_once 'lib/LogoHelper.php';

// Check if installation is needed
if (!defined('DB_INSTALLED') || DB_INSTALLED !== true) {
    header('Location: install.php');
    exit;
}

// Check if user is already logged in
if (isset($_SESSION['user_id'])) {
    redirect('inbox.php');
}

// Initialize variables
$error = '';
$success = '';
$resend_link = false;
$user_email = '';

// Process login form
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Verify CSRF token
    if (!isset($_POST['csrf_token']) || !validate_csrf_token($_POST['csrf_token'])) {
        $error = "Security validation failed. Please try again.";
    }
    // Check if form fields exist
    else if (!isset($_POST['username']) || !isset($_POST['password'])) {
        $error = "Please enter both username and password";
    } else {
        $username = sanitize($_POST['username']);
        $password = $_POST['password'];

        // Validate input
        if (empty($username) || empty($password)) {
            $error = "Please enter both username and password";
        } else {
            try {
                // Use prepared statement to prevent SQL injection
                $sql = "SELECT id, username, password, email, is_verified, is_admin FROM users WHERE username = ?";
                $stmt = $conn->prepare($sql);

                if (!$stmt) {
                    throw new Exception("Database error. Please try again later.");
                }

                $stmt->bind_param("s", $username);
                $stmt->execute();
                $result = $stmt->get_result();

                if ($result->num_rows == 1) {
                    $user = $result->fetch_assoc();

                    // Verify password
                    if (password_verify($password, $user['password'])) {
                        // Check if email is verified
                        if (isset($user['is_verified']) && $user['is_verified'] == 0) {
                            $error = "Your account is not verified. Please check your email for the verification link.";
                            $resend_link = true;
                            $user_email = $user['email'];
                        } else {
                            // Password is correct and account is verified, create session
                            $_SESSION['user_id'] = $user['id'];
                            $_SESSION['username'] = $user['username'];
                            $_SESSION['is_admin'] = $user['is_admin'];

                            // Set a flag to indicate successful login
                            $_SESSION['login_success'] = true;

                            // Redirect to inbox
                            redirect('inbox.php');
                        }
                    } else {
                        $error = "Invalid username or password";
                    }
                } else {
                    $error = "Invalid username or password";
                }
            } catch (Exception $e) {
                $error = $e->getMessage();
            }
        }
    }
}

// Set page title
$page_title = "Admin Login";
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Admin login for Manage Inc. content management system">
    <title><?php echo $page_title; ?> | Manage Inc.</title>

    <!-- Stylesheets -->
    <link rel="stylesheet" href="assets/css/main.css?v=<?php echo time(); ?>">
    <link rel="stylesheet" href="assets/css/pages/login.css?v=<?php echo time(); ?>">

    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Open+Sans:wght@300;400;600;700&display=swap" rel="stylesheet">

    <!-- Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">

    <!-- Favicon -->
    <link rel="shortcut icon" href="favicon.ico" type="image/x-icon">
</head>
<body class="login-page">
    <div class="login-container">
        <div class="login-card">
            <div class="login-header">
                <div class="login-logo">
                    <?php
                    // Get login logo using LogoHelper
                    if ($conn) {
                        echo get_logo_html('login', 'Manage Incorporated', 'login-logo-img');
                    } else {
                        echo '<img src="images/login-logo.jpg" alt="Manage Incorporated" class="login-logo-img">';
                    }
                    ?>
                </div>
                <h1 class="login-title">Welcome Back</h1>
                <p class="login-subtitle">Sign in to your admin account</p>
            </div>

            <div class="login-body">
                <?php if (!empty($error)): ?>
                    <div class="login-alert error">
                        <i class="fas fa-exclamation-circle"></i>
                        <div class="alert-message">
                            <?php echo $error; ?>
                            <?php if (isset($resend_link) && $resend_link): ?>
                                <div class="alert-action">
                                    <a href="resend-verification.php?email=<?php echo urlencode($user_email); ?>">
                                        Resend verification email
                                    </a>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                <?php endif; ?>

                <?php if (isset($_SESSION['installation_message'])): ?>
                    <div class="login-alert success">
                        <i class="fas fa-check-circle"></i>
                        <div class="alert-message">
                            <?php echo $_SESSION['installation_message']; ?>
                        </div>
                    </div>
                    <?php unset($_SESSION['installation_message']); ?>
                <?php endif; ?>

                <form class="login-form" method="post" action="" id="loginForm">
                    <!-- CSRF Protection -->
                    <input type="hidden" name="csrf_token" value="<?php echo generate_csrf_token(); ?>">

                    <div class="form-group">
                        <label for="username">Username</label>
                        <div class="input-wrapper">
                            <i class="fas fa-user"></i>
                            <input type="text" id="username" name="username" placeholder="Enter your username" required autofocus>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="password">Password</label>
                        <div class="input-wrapper">
                            <i class="fas fa-lock"></i>
                            <input type="password" id="password" name="password" placeholder="Enter your password" required>
                        </div>
                    </div>

                    <div class="form-options">
                        <div class="remember-me">
                            <input type="checkbox" id="remember" name="remember">
                            <label for="remember">Remember me</label>
                        </div>
                        <a href="forgot-password.php" class="forgot-password">Forgot password?</a>
                    </div>

                    <button type="submit" class="login-button" id="loginButton">
                        <i class="fas fa-sign-in-alt"></i> Sign In
                    </button>
                </form>
            </div>

            <div class="login-footer">
                <p>&copy; <?php echo date('Y'); ?> Manage Inc. All Rights Reserved.</p>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const loginForm = document.getElementById('loginForm');
            const loginButton = document.getElementById('loginButton');

            loginForm.addEventListener('submit', function(e) {
                // Validate form
                const username = document.getElementById('username').value.trim();
                const password = document.getElementById('password').value.trim();

                if (!username || !password) {
                    e.preventDefault();
                    showError('Please enter both username and password');
                    return false;
                }

                // Show loading state
                loginButton.disabled = true;
                loginButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Signing in...';

                return true;
            });

            // Function to show error message
            function showError(message) {
                // Remove existing alerts
                const existingAlerts = document.querySelectorAll('.login-alert');
                existingAlerts.forEach(alert => alert.remove());

                // Create new alert
                const alertDiv = document.createElement('div');
                alertDiv.className = 'login-alert error';
                alertDiv.innerHTML = `
                    <i class="fas fa-exclamation-circle"></i>
                    <div class="alert-message">${message}</div>
                `;

                // Insert at the top of the form
                const loginBody = document.querySelector('.login-body');
                loginBody.insertBefore(alertDiv, loginBody.firstChild);
            }
        });
    </script>
</body>
</html>
