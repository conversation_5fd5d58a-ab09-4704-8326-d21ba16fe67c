/**
 * Admin Variables CSS
 * 
 * This file contains CSS variables for the admin panel.
 * The color scheme is based on black and yellow brand colors.
 */

:root {
    /* Brand Colors */
    --primary-color: #f1ca2f;         /* Yellow */
    --primary-light: #f7e07c;
    --primary-dark: #d9b429;
    --primary-very-light: #fdf8e3;
    
    --secondary-color: #2c3e50;       /* Dark Blue/Black */
    --secondary-light: #34495e;
    --secondary-dark: #1a252f;
    --secondary-very-light: #eef2f5;
    
    /* Neutral Colors */
    --white: #ffffff;
    --gray-50: #f9fafb;
    --gray-100: #f3f4f6;
    --gray-200: #e5e7eb;
    --gray-300: #d1d5db;
    --gray-400: #9ca3af;
    --gray-500: #6b7280;
    --gray-600: #4b5563;
    --gray-700: #374151;
    --gray-800: #1f2937;
    --gray-900: #111827;
    --black: #000000;
    
    /* Semantic Colors */
    --success-color: #10b981;         /* Green */
    --success-light: #34d399;
    --success-dark: #059669;
    
    --danger-color: #ef4444;          /* Red */
    --danger-light: #f87171;
    --danger-dark: #dc2626;
    
    --warning-color: #f59e0b;         /* Orange */
    --warning-light: #fbbf24;
    --warning-dark: #d97706;
    
    --info-color: #3b82f6;            /* Blue */
    --info-light: #60a5fa;
    --info-dark: #2563eb;
    
    /* Text Colors */
    --text-color: var(--gray-700);
    --text-light: var(--gray-500);
    --text-dark: var(--gray-900);
    --text-muted: var(--gray-400);
    
    /* Border Colors */
    --border-color: var(--gray-200);
    --border-dark: var(--gray-300);
    --border-light: var(--gray-100);
    
    /* Background Colors */
    --background-color: #f8f9fa;
    --background-light: var(--white);
    --background-dark: var(--gray-100);
    
    /* Component Colors */
    --sidebar-bg: var(--secondary-color);
    --sidebar-text: var(--white);
    --sidebar-active: var(--primary-color);
    
    --header-bg: var(--white);
    --header-text: var(--text-color);
    
    --footer-bg: var(--white);
    --footer-text: var(--text-color);
    
    /* Typography */
    --font-family-sans: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
    --font-family-mono: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, monospace;
    
    --font-size-xs: 0.75rem;   /* 12px */
    --font-size-sm: 0.875rem;  /* 14px */
    --font-size-base: 1rem;    /* 16px */
    --font-size-lg: 1.125rem;  /* 18px */
    --font-size-xl: 1.25rem;   /* 20px */
    --font-size-2xl: 1.5rem;   /* 24px */
    --font-size-3xl: 1.875rem; /* 30px */
    --font-size-4xl: 2.25rem;  /* 36px */
    
    --font-weight-light: 300;
    --font-weight-normal: 400;
    --font-weight-medium: 500;
    --font-weight-semibold: 600;
    --font-weight-bold: 700;
    
    --line-height-tight: 1.25;
    --line-height-normal: 1.5;
    --line-height-relaxed: 1.75;
    
    /* Spacing */
    --spacing-0: 0;
    --spacing-1: 0.25rem;  /* 4px */
    --spacing-2: 0.5rem;   /* 8px */
    --spacing-3: 0.75rem;  /* 12px */
    --spacing-4: 1rem;     /* 16px */
    --spacing-5: 1.25rem;  /* 20px */
    --spacing-6: 1.5rem;   /* 24px */
    --spacing-8: 2rem;     /* 32px */
    --spacing-10: 2.5rem;  /* 40px */
    --spacing-12: 3rem;    /* 48px */
    --spacing-16: 4rem;    /* 64px */
    --spacing-20: 5rem;    /* 80px */
    
    /* Borders */
    --radius-sm: 0.25rem;  /* 4px */
    --radius-md: 0.375rem; /* 6px */
    --radius-lg: 0.5rem;   /* 8px */
    --radius-xl: 0.75rem;  /* 12px */
    --radius-full: 9999px;
    
    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    
    /* Transitions */
    --transition-fast: 150ms;
    --transition-normal: 300ms;
    --transition-slow: 500ms;
    
    /* Z-index */
    --z-index-dropdown: 1000;
    --z-index-sticky: 1020;
    --z-index-fixed: 1030;
    --z-index-modal-backdrop: 1040;
    --z-index-modal: 1050;
    --z-index-popover: 1060;
    --z-index-tooltip: 1070;
    
    /* Layout */
    --sidebar-width: 260px;
    --sidebar-collapsed-width: 70px;
    --topbar-height: 60px;
    --content-max-width: 1200px;
    --card-border-radius: 8px;
    --input-border-radius: 6px;
    --button-border-radius: 6px;
}
