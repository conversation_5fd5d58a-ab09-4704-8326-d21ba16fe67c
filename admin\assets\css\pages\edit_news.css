/**
 * Edit News Page CSS
 *
 * This file contains styles specific to the edit news page.
 */

/* Edit News Page Specific Styles */
.page-edit_news .admin-content-title {
  color: var(--text-dark);
}

.page-edit_news .admin-content-title i {
  color: var(--info-color);
  margin-right: 8px;
}

.page-edit_news .admin-content-subtitle {
  font-weight: var(--font-weight-medium);
  color: var(--text-dark);
}

/* Enhanced Form Styling for Edit News */
.page-edit_news .form-control {
  border-width: 2px;
  background-color: var(--white);
}

.page-edit_news .form-control:focus {
  border-color: var(--info-color);
  box-shadow: 0 0 0 4px rgba(var(--info-rgb), 0.15);
}

/* Enhanced Card Styling */
.page-edit_news .admin-card {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

.page-edit_news .admin-card-header {
  background-color: var(--bg-light);
  border-bottom: 1px solid var(--border-light);
}

.page-edit_news .admin-card-meta {
  background-color: rgba(var(--info-rgb), 0.05);
  padding: var(--spacing-2) var(--spacing-3);
  border-radius: var(--radius-md);
  margin-top: var(--spacing-2);
}

.page-edit_news .admin-card-meta-item {
  padding: var(--spacing-1) var(--spacing-2);
  background-color: rgba(var(--info-rgb), 0.1);
  border-radius: var(--radius-sm);
}

/* Enhanced Button Styling */
.page-edit_news .admin-btn.primary {
  background-color: var(--info-color);
  border-color: var(--info-color);
  font-weight: var(--font-weight-semibold);
  padding: var(--spacing-3) var(--spacing-5);
  transition: all var(--transition-fast) ease;
}

.page-edit_news .admin-btn.primary:hover {
  background-color: var(--info-dark);
  border-color: var(--info-dark);
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(var(--info-rgb), 0.3);
}

.page-edit_news .admin-btn.primary:active {
  transform: translateY(0);
  box-shadow: 0 2px 4px rgba(var(--info-rgb), 0.3);
}

.page-edit_news .admin-btn.outline {
  border: 2px solid var(--info-color);
  color: var(--info-color);
  background-color: transparent;
  font-weight: var(--font-weight-medium);
}

.page-edit_news .admin-btn.outline:hover {
  background-color: rgba(var(--info-rgb), 0.1);
}

/* Enhanced Image Upload Area */
.page-edit_news .image-preview {
  border: 2px solid var(--info-color);
  background-color: rgba(var(--info-rgb), 0.05);
  box-shadow: 0 4px 12px rgba(var(--info-rgb), 0.1);
}

.page-edit_news .image-preview img {
  transition: transform var(--transition-normal) ease;
}

.page-edit_news .image-preview:hover img {
  transform: scale(1.05);
}

.page-edit_news .custom-file-label {
  background-color: rgba(var(--info-rgb), 0.1);
  border-color: var(--info-color);
  color: var(--info-dark);
  font-weight: var(--font-weight-medium);
}

.page-edit_news .custom-file-label:hover {
  background-color: rgba(var(--info-rgb), 0.15);
}

/* Enhanced WYSIWYG Editor */
.page-edit_news .wysiwyg-editor-container {
  border-color: var(--info-color);
  border-radius: var(--radius-md);
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(var(--info-rgb), 0.1);
}

.page-edit_news .wysiwyg-toolbar {
  background-color: rgba(var(--info-rgb), 0.05);
}

.page-edit_news .wysiwyg-toolbar button.active {
  background-color: var(--info-color);
}

/* Fix for sidebar toggle visibility */
#sidebarToggleContainer,
.sidebar-toggle,
body.sidebar-collapsed #sidebarToggleContainer,
body.sidebar-collapsed .sidebar-toggle {
  display: flex;
  visibility: visible;
  opacity: 1;
  z-index: 9999;
}

/* Responsive Adjustments */
@media (max-width: 992px) {
  .page-edit_news .admin-content-grid {
    gap: var(--spacing-4);
  }

  .page-edit_news .admin-card {
    margin-bottom: var(--spacing-4);
  }
}

@media (max-width: 768px) {
  .page-edit_news .admin-content-title {
    font-size: var(--font-size-xl);
  }

  .page-edit_news .admin-content-subtitle {
    font-size: var(--font-size-sm);
  }

  .page-edit_news .admin-btn.primary {
    width: 100%;
    justify-content: center;
  }

  .page-edit_news .admin-content-actions {
    flex-direction: column;
    width: 100%;
  }

  .page-edit_news .admin-content-actions .admin-btn {
    width: 100%;
    justify-content: center;
    margin-bottom: var(--spacing-2);
  }
}

/* These styles are now in news_editor.css */

/* These responsive styles are now in news_editor.css */
