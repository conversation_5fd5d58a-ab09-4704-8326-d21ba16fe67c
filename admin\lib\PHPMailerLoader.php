<?php
/**
 * Centralized PHPMailer Loader
 * Prevents multiple inclusions and class redeclaration errors
 */

class PHPMailerLoader {
    private static $loaded = false;
    private static $available = false;
    private static $version = null;
    private static $path = null;
    
    /**
     * Load PHPMailer classes if not already loaded
     * @return bool True if PHPMailer is available, false otherwise
     */
    public static function load() {
        // Return cached result if already checked
        if (self::$loaded) {
            return self::$available;
        }
        
        self::$loaded = true;
        
        // Check if PHPMailer is already loaded
        if (class_exists('PHPMailer\PHPMailer\PHPMailer')) {
            self::$available = true;
            self::$version = 'namespaced';
            error_log("PHPMailer already loaded (namespaced)");
            return true;
        }
        
        if (class_exists('PHPMailer')) {
            self::$available = true;
            self::$version = 'legacy';
            error_log("PHPMailer already loaded (legacy)");
            return true;
        }
        
        // Try to load PHPMailer from various locations
        $phpmailer_paths = [
            // Composer vendor directory
            [
                'base' => $_SERVER['DOCUMENT_ROOT'] . '/vendor/phpmailer/phpmailer/src',
                'files' => ['PHPMailer.php', 'SMTP.php', 'Exception.php'],
                'type' => 'composer'
            ],
            // Project vendor directory
            [
                'base' => __DIR__ . '/../../vendor/phpmailer/phpmailer/src',
                'files' => ['PHPMailer.php', 'SMTP.php', 'Exception.php'],
                'type' => 'vendor'
            ],
            // Real PHPMailer in lib directory
            [
                'base' => __DIR__ . '/PHPMailer/real/PHPMailer-6.8.1/src',
                'files' => ['PHPMailer.php', 'SMTP.php', 'Exception.php'],
                'type' => 'real'
            ],
            // Simplified PHPMailer in lib directory
            [
                'base' => __DIR__ . '/PHPMailer',
                'files' => ['PHPMailer.php', 'SMTP.php', 'Exception.php'],
                'type' => 'simplified'
            ]
        ];
        
        foreach ($phpmailer_paths as $config) {
            if (self::tryLoadFromPath($config)) {
                self::$available = true;
                self::$path = $config['base'];
                error_log("PHPMailer loaded successfully from: " . $config['type'] . " (" . $config['base'] . ")");
                return true;
            }
        }
        
        // If all else fails, try composer autoload
        if (self::tryComposerAutoload()) {
            self::$available = true;
            self::$version = 'composer';
            error_log("PHPMailer loaded via composer autoload");
            return true;
        }
        
        error_log("PHPMailer could not be loaded from any location");
        return false;
    }
    
    /**
     * Try to load PHPMailer from a specific path configuration
     */
    private static function tryLoadFromPath($config) {
        $base = $config['base'];
        $files = $config['files'];
        
        // Check if all required files exist
        foreach ($files as $file) {
            if (!file_exists($base . '/' . $file)) {
                return false;
            }
        }
        
        try {
            // Include files only if classes don't exist
            if (!class_exists('PHPMailer\PHPMailer\PHPMailer')) {
                require_once $base . '/PHPMailer.php';
            }
            if (!class_exists('PHPMailer\PHPMailer\SMTP')) {
                require_once $base . '/SMTP.php';
            }
            if (!class_exists('PHPMailer\PHPMailer\Exception')) {
                require_once $base . '/Exception.php';
            }
            
            // Verify classes are now available
            if (class_exists('PHPMailer\PHPMailer\PHPMailer')) {
                self::$version = 'namespaced';
                return true;
            }
            
            // Check for legacy (non-namespaced) version
            if (class_exists('PHPMailer')) {
                self::$version = 'legacy';
                return true;
            }
            
        } catch (Exception $e) {
            error_log("Error loading PHPMailer from {$base}: " . $e->getMessage());
        } catch (Error $e) {
            error_log("Fatal error loading PHPMailer from {$base}: " . $e->getMessage());
        }
        
        return false;
    }
    
    /**
     * Try to load PHPMailer via composer autoload
     */
    private static function tryComposerAutoload() {
        $autoload_paths = [
            $_SERVER['DOCUMENT_ROOT'] . '/vendor/autoload.php',
            __DIR__ . '/../../vendor/autoload.php',
            __DIR__ . '/../../../vendor/autoload.php'
        ];
        
        foreach ($autoload_paths as $autoload_path) {
            if (file_exists($autoload_path)) {
                try {
                    require_once $autoload_path;
                    
                    if (class_exists('PHPMailer\PHPMailer\PHPMailer')) {
                        self::$version = 'composer';
                        return true;
                    }
                } catch (Exception $e) {
                    error_log("Error loading composer autoload from {$autoload_path}: " . $e->getMessage());
                }
            }
        }
        
        return false;
    }
    
    /**
     * Create a new PHPMailer instance
     * @param bool $exceptions Whether to throw exceptions
     * @return PHPMailer|null
     */
    public static function createInstance($exceptions = true) {
        if (!self::load()) {
            return null;
        }
        
        try {
            if (self::$version === 'namespaced' || self::$version === 'composer') {
                return new \PHPMailer\PHPMailer\PHPMailer($exceptions);
            } elseif (self::$version === 'legacy') {
                return new PHPMailer($exceptions);
            }
        } catch (Exception $e) {
            error_log("Error creating PHPMailer instance: " . $e->getMessage());
        }
        
        return null;
    }
    
    /**
     * Check if PHPMailer is available
     * @return bool
     */
    public static function isAvailable() {
        return self::load();
    }
    
    /**
     * Get PHPMailer version info
     * @return array
     */
    public static function getInfo() {
        self::load();
        return [
            'available' => self::$available,
            'version' => self::$version,
            'path' => self::$path,
            'class_exists_namespaced' => class_exists('PHPMailer\PHPMailer\PHPMailer'),
            'class_exists_legacy' => class_exists('PHPMailer')
        ];
    }
    
    /**
     * Reset the loader state (for testing purposes)
     */
    public static function reset() {
        self::$loaded = false;
        self::$available = false;
        self::$version = null;
        self::$path = null;
    }
}
