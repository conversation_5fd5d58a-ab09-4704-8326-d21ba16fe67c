<?php
session_start();
require_once 'config.php';
require_once 'lib/Mailer.php';

$message = '';
$status = '';

// Check if email is provided
if (isset($_GET['email']) && !empty($_GET['email'])) {
    $email = sanitize($_GET['email']);

    // Check if user exists
    $sql = "SELECT id, username, email, is_verified FROM users WHERE email = ?";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("s", $email);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result && $result->num_rows > 0) {
        $user = $result->fetch_assoc();

        // Check if user is already verified
        if (isset($user['is_verified']) && $user['is_verified'] == 1) {
            $message = "Your account is already verified. You can login now.";
            $status = "info";
        } else {
            // Generate verification token
            $token = bin2hex(random_bytes(32)); // More secure token generation
            $expiry = date('Y-m-d H:i:s', strtotime('+24 hours'));

            // Check if tokens table exists
            $check_table = $conn->query("SHOW TABLES LIKE 'verification_tokens'");
            if ($check_table->num_rows == 0) {
                // Create tokens table
                $create_table = "CREATE TABLE verification_tokens (
                    id INT(11) NOT NULL AUTO_INCREMENT,
                    user_id INT(11) NOT NULL,
                    token VARCHAR(255) NOT NULL,
                    expiry DATETIME NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    PRIMARY KEY (id),
                    UNIQUE KEY token (token)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4";

                $conn->query($create_table);
            }

            // Start transaction
            $conn->begin_transaction();

            try {
                // Delete any existing tokens for this user
                $delete_tokens = "DELETE FROM verification_tokens WHERE user_id = ?";
                $delete_stmt = $conn->prepare($delete_tokens);
                $delete_stmt->bind_param("i", $user['id']);
                $delete_stmt->execute();

                // Save new token
                $insert_token = "INSERT INTO verification_tokens (user_id, token, expiry) VALUES (?, ?, ?)";
                $insert_stmt = $conn->prepare($insert_token);
                $insert_stmt->bind_param("iss", $user['id'], $token, $expiry);
                $insert_stmt->execute();

                // Initialize mailer
                $mailer = new Mailer($conn);

                // Send verification email
                $result = $mailer->sendVerificationEmail($user['email'], $user['username'], $token);

                if ($result['success']) {
                    $message = "Verification email has been sent to your email address. Please check your inbox.";
                    $status = "success";

                    // Commit transaction
                    $conn->commit();

                    // Log successful email sending
                    error_log("Verification email sent to: " . $user['email']);
                } else {
                    // Rollback transaction
                    $conn->rollback();

                    $message = "Failed to send verification email: " . $result['message'];
                    $status = "error";

                    // Log error
                    error_log("Failed to send verification email: " . $result['message']);
                }
            } catch (Exception $e) {
                // Rollback transaction
                $conn->rollback();

                $message = "An error occurred. Please try again later.";
                $status = "error";

                // Log error
                error_log("Error in resend-verification.php: " . $e->getMessage());
            }
        }
    } else {
        $message = "No account found with this email address.";
        $status = "error";
    }
} else {
    $message = "Email address is required.";
    $status = "error";
}

// Set page title
$page_title = "Resend Verification";
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> | Manage Inc.</title>
    <link rel="stylesheet" href="../css/style.css?v=<?php echo time(); ?>">
    <link rel="stylesheet" href="css/admin-style.css?v=<?php echo time(); ?>">
    <link href="https://fonts.googleapis.com/css2?family=Open+Sans:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <style>
        body {
            background-color: #f5f5f5;
            background-image: linear-gradient(135deg, #f5f5f5 0%, #e0e0e0 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            min-height: 100vh;
            padding: 20px;
            margin: 0;
            font-family: 'Open Sans', sans-serif;
        }

        .verification-container {
            max-width: 500px;
            width: 100%;
            padding: 40px;
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            position: relative;
            overflow: hidden;
        }

        .verification-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 5px;
            background: #f1ca2f;
        }

        .verification-header {
            text-align: center;
            margin-bottom: 35px;
        }

        .verification-header img {
            max-width: 200px;
            margin-bottom: 25px;
        }

        .verification-header h2 {
            color: #3c3c45;
            font-weight: 600;
            margin-bottom: 10px;
            font-size: 24px;
        }

        .admin-alert {
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 25px;
            font-size: 14px;
        }

        .admin-alert.error {
            background-color: #ffebee;
            color: #c62828;
            border-left: 4px solid #c62828;
        }

        .admin-alert.success {
            background-color: #e8f5e9;
            color: #2e7d32;
            border-left: 4px solid #2e7d32;
        }

        .admin-alert.info {
            background-color: #e3f2fd;
            color: #1565c0;
            border-left: 4px solid #1565c0;
        }

        .verification-actions {
            text-align: center;
            margin-top: 30px;
        }

        .verification-actions a {
            display: inline-block;
            padding: 12px 24px;
            background-color: #3c3c45;
            color: #fff;
            border-radius: 4px;
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .verification-actions a:hover {
            background-color: #4a4a52;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }

        .verification-footer {
            text-align: center;
            margin-top: 35px;
            color: #777;
            font-size: 13px;
            border-top: 1px solid #eee;
            padding-top: 20px;
        }
    </style>
</head>
<body>
    <div class="verification-container">
        <div class="verification-header">
            <img src="../images/logo.png" alt="Manage Incorporated">
            <h2>Email Verification</h2>
        </div>

        <div class="admin-alert <?php echo $status; ?>">
            <?php if ($status == 'error'): ?>
                <i class="fas fa-exclamation-circle"></i>
            <?php elseif ($status == 'success'): ?>
                <i class="fas fa-check-circle"></i>
            <?php else: ?>
                <i class="fas fa-info-circle"></i>
            <?php endif; ?>
            <?php echo $message; ?>
        </div>

        <div class="verification-actions">
            <a href="index.php">Back to Login</a>
        </div>

        <div class="verification-footer">
            <p>&copy; <?php echo date('Y'); ?> Manage Inc. All Rights Reserved.</p>
        </div>
    </div>
</body>
</html>
