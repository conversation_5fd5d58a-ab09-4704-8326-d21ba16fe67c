<?php
/**
 * Dynamic CSS for Font Settings
 *
 * This file generates CSS based on the font settings in the database
 */

// Disable error reporting to prevent output before headers
error_reporting(0);
ini_set('display_errors', 0);

// Set content type to CSS
header('Content-Type: text/css');

// Include database configuration
try {
    require_once '../../../config.php';

// Default font settings
$default_font_family = 'Open Sans, sans-serif';
$heading_font_family = 'Montserrat, sans-serif';
$body_font_family = 'Open Sans, sans-serif';
$editor_font_family = 'Open Sans, sans-serif';
$font_size_base = '14px';
$line_height_base = '1.5';

// Get font settings from database
$font_settings_query = "SELECT setting_key, setting_value FROM system_settings WHERE category = 'fonts'";
$font_settings_result = $conn->query($font_settings_query);

if ($font_settings_result && $font_settings_result->num_rows > 0) {
    while ($row = $font_settings_result->fetch_assoc()) {
        switch ($row['setting_key']) {
            case 'default_font_family':
                $default_font_family = $row['setting_value'];
                break;
            case 'heading_font_family':
                $heading_font_family = $row['setting_value'];
                break;
            case 'body_font_family':
                $body_font_family = $row['setting_value'];
                break;
            case 'editor_font_family':
                $editor_font_family = $row['setting_value'];
                break;
            case 'font_size_base':
                $font_size_base = $row['setting_value'];
                break;
            case 'line_height_base':
                $line_height_base = $row['setting_value'];
                break;
        }
    }
}

// Make sure font values have fallbacks
if (empty($default_font_family)) {
    $default_font_family = 'Open Sans, sans-serif';
}
if (empty($heading_font_family)) {
    $heading_font_family = 'Montserrat, sans-serif';
}
if (empty($body_font_family)) {
    $body_font_family = 'Open Sans, sans-serif';
}
if (empty($editor_font_family)) {
    $editor_font_family = 'Open Sans, sans-serif';
}
if (empty($font_size_base)) {
    $font_size_base = '14px';
}
if (empty($line_height_base)) {
    $line_height_base = '1.5';
}

// Generate CSS
?>
/* Dynamic Font Settings - Generated <?php echo date('Y-m-d H:i:s'); ?> */

/* Default Font */
.form-control,
.admin-btn,
.admin-alert,
.admin-card,
.admin-table,
.admin-sidebar-menu ul li a span,
.admin-topbar-actions,
.admin-search-input,
.admin-user-menu-toggle span,
.admin-user-dropdown ul li a,
.admin-tabs-header button,
.admin-tab-content,
.admin-form label,
.admin-form input,
.admin-form textarea,
.admin-form select,
.admin-form .form-help-text,
.template-variables span,
.notifications-dropdown,
.notification-item,
.admin-modal-content,
.admin-modal-header h3,
.admin-modal-body,
.admin-modal-footer button {
    font-family: <?php echo $default_font_family; ?>;
}

/* Body Font */
body,
p,
li,
td,
th,
div,
span,
.template-help p,
.template-help li {
    font-family: <?php echo $body_font_family; ?>;
}

/* Heading Font */
h1, h2, h3, h4, h5, h6,
.admin-header h2,
.admin-card-header h3,
.admin-sidebar-header,
.admin-modal-header h2,
.template-help h4,
.template-help h5,
.template-help h6,
.notifications-header h3 {
    font-family: <?php echo $heading_font_family; ?>;
}

/* Editor Font */
.simple-editor-toolbar button,
.simple-editor-content {
    font-family: <?php echo $editor_font_family; ?>;
}

/* Base Font Size */
body {
    font-size: <?php echo $font_size_base; ?>;
    line-height: <?php echo $line_height_base; ?>;
}

/* Font Size Scale */
h1 { font-size: calc(<?php echo $font_size_base; ?> * 2.5); }
h2 { font-size: calc(<?php echo $font_size_base; ?> * 2); }
h3 { font-size: calc(<?php echo $font_size_base; ?> * 1.75); }
h4 { font-size: calc(<?php echo $font_size_base; ?> * 1.5); }
h5 { font-size: calc(<?php echo $font_size_base; ?> * 1.25); }
h6 { font-size: calc(<?php echo $font_size_base; ?> * 1.1); }

/* Editor Content */
.simple-editor-content {
    font-size: <?php echo $font_size_base; ?>;
    line-height: <?php echo $line_height_base; ?>;
}

.simple-editor-content h1,
.simple-editor-content h2,
.simple-editor-content h3,
.simple-editor-content h4,
.simple-editor-content h5,
.simple-editor-content h6 {
    font-family: <?php echo $heading_font_family; ?>;
}

/* Form elements */
.form-control,
input[type="text"],
input[type="email"],
input[type="password"],
input[type="number"],
textarea,
select {
    font-family: <?php echo $body_font_family; ?>;
    font-size: <?php echo $font_size_base; ?>;
}

/* Buttons */
.btn,
.admin-btn {
    font-family: <?php echo $body_font_family; ?>;
    font-size: <?php echo $font_size_base; ?>;
}
<?php
} catch (Exception $e) {
    // Log error but don't display it
    error_log('Error in font-settings.css.php: ' . $e->getMessage());

    // Output default CSS if there's an error
    echo "/* Error loading custom font settings - using defaults */\n";
    echo "body, p, div, span { font-family: 'Open Sans', Arial, sans-serif; }\n";
    echo "h1, h2, h3, h4, h5, h6 { font-family: 'Montserrat', Arial, sans-serif; }\n";
}
?>
