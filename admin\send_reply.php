<?php
// Start session
session_start();

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header('Location: login.php');
    exit;
}

// Include database configuration
require_once 'config.php';

// Include security functions
require_once 'includes/security.php';

// Include Mailer class
require_once 'lib/Mailer.php';

// Initialize variables
$success = false;
$error = '';

// Process form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Get CSRF token if available
    $csrf_token = isset($_POST['csrf_token']) ? $_POST['csrf_token'] : '';

    // Verify CSRF token if provided, otherwise proceed (for backward compatibility)
    if (!empty($csrf_token) && !validate_csrf_token($csrf_token)) {
        $error = "Security validation failed. Please try again.";
        error_log("CSRF token validation failed during reply submission");
        $_SESSION['reply_error'] = $error;
        header("Location: inbox.php");
        exit;
    }

    // Get form data
    $submission_id = isset($_POST['submission_id']) ? (int)$_POST['submission_id'] : 0;
    $recipient_email = isset($_POST['recipient_email']) ? trim($_POST['recipient_email']) : '';
    $recipient_name = isset($_POST['recipient_name']) ? trim($_POST['recipient_name']) : '';
    $subject = isset($_POST['reply_subject']) ? trim($_POST['reply_subject']) : '';
    $message = isset($_POST['reply_message']) ? trim($_POST['reply_message']) : '';

    // Validate form data
    if (empty($recipient_email) || empty($subject) || empty($message)) {
        $error = "Please fill in all required fields.";
    } elseif (!filter_var($recipient_email, FILTER_VALIDATE_EMAIL)) {
        $error = "Invalid email address.";
    } else {
        try {
            // Try EmailSender first (preferred), fallback to Mailer
            $emailSender = null;
            $mailer = null;
            $emailSystem = null;

            try {
                // Try EmailSender first
                require_once 'lib/EmailSender.php';
                $emailSender = new EmailSender($conn);
                $emailSystem = $emailSender;
                error_log("Using EmailSender for reply email");
            } catch (Exception $e) {
                error_log("EmailSender failed to initialize for reply: " . $e->getMessage());
                // Fallback to Mailer
                try {
                    require_once 'lib/Mailer.php';
                    $mailer = new Mailer($conn);
                    $emailSystem = $mailer;
                    error_log("Falling back to Mailer for reply email");
                } catch (Exception $e2) {
                    error_log("Both EmailSender and Mailer failed to initialize for reply: " . $e2->getMessage());
                    throw new Exception('Email system is not properly configured. Please contact the administrator.');
                }
            }

            // Format HTML message
            $html_message = "
            <html>
            <head>
                <title>$subject</title>
                <style>
                    body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
                    .container { max-width: 600px; margin: 0 auto; padding: 20px; }
                    h2 { color: #3c3c45; border-bottom: 2px solid #f1ca2f; padding-bottom: 10px; }
                    p { margin-bottom: 15px; }
                    .footer { margin-top: 30px; font-size: 12px; color: #777; border-top: 1px solid #ddd; padding-top: 10px; }
                </style>
            </head>
            <body>
                <div class='container'>
                    <h2>$subject</h2>
                    <p>Dear $recipient_name,</p>
                    " . nl2br(htmlspecialchars($message)) . "
                    <div class='footer'>
                        <p>This email was sent in response to your contact form submission.</p>
                    </div>
                </div>
            </body>
            </html>
            ";

            // Send email using the appropriate email system
            if ($emailSender) {
                $result = $emailSender->sendEmail($recipient_email, $subject, $html_message);
            } else {
                $result = $mailer->sendEmail($recipient_email, $subject, $html_message, $recipient_name);
            }

            if ($result['success']) {
                // Update submission in database to mark as replied using prepared statement
                $update_sql = "UPDATE `contact_submissions` SET `is_read` = 1 WHERE `id` = ?";
                $update_stmt = $conn->prepare($update_sql);
                if ($update_stmt) {
                    $update_stmt->bind_param("i", $submission_id);
                    $update_stmt->execute();
                    $update_stmt->close();
                } else {
                    error_log("Failed to prepare update statement: " . $conn->error);
                }

                // Log the reply in the database
                $admin_id = $_SESSION['user_id'];
                $admin_sql = "SELECT username FROM users WHERE id = ?";
                $admin_stmt = $conn->prepare($admin_sql);
                if ($admin_stmt) {
                    $admin_stmt->bind_param("i", $admin_id);
                    $admin_stmt->execute();
                    $admin_result = $admin_stmt->get_result();
                    $admin_stmt->close();
                } else {
                    error_log("Failed to prepare admin query: " . $conn->error);
                    $admin_result = false;
                }
                $admin_username = ($admin_result && $admin_result->num_rows > 0) ?
                                  $admin_result->fetch_assoc()['username'] : 'Unknown';

                // Check if contact_replies table exists
                $check_table = "SHOW TABLES LIKE 'contact_replies'";
                $table_result = $conn->query($check_table);

                if ($table_result->num_rows == 0) {
                    // Create table if it doesn't exist
                    $create_table = "CREATE TABLE `contact_replies` (
                        `id` int(11) NOT NULL AUTO_INCREMENT,
                        `submission_id` int(11) NOT NULL,
                        `user_id` int(11) NOT NULL,
                        `subject` varchar(255) NOT NULL,
                        `message` text NOT NULL,
                        `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
                        PRIMARY KEY (`id`),
                        KEY `submission_id` (`submission_id`),
                        KEY `user_id` (`user_id`)
                    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;";

                    $conn->query($create_table);
                }

                // Insert reply using prepared statement
                $insert_sql = "INSERT INTO `contact_replies`
                              (`submission_id`, `user_id`, `subject`, `message`)
                              VALUES (?, ?, ?, ?)";

                $insert_stmt = $conn->prepare($insert_sql);
                if ($insert_stmt) {
                    $insert_stmt->bind_param("iiss", $submission_id, $admin_id, $subject, $message);
                    $insert_stmt->execute();
                    $insert_stmt->close();
                } else {
                    error_log("Failed to prepare insert statement: " . $conn->error);
                }

                $success = true;
                $_SESSION['reply_success'] = "Your reply has been sent successfully to $recipient_name.";
            } else {
                $error = "Failed to send email: " . $result['message'];
            }
        } catch (Exception $e) {
            $error = "An error occurred: " . $e->getMessage();
        }
    }
}

// Redirect back to the submission page
if ($success) {
    header("Location: inbox.php?id=$submission_id");
} else {
    $_SESSION['reply_error'] = $error;
    header("Location: inbox.php?id=$submission_id");
}
exit;
