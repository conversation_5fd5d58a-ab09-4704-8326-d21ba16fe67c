<?php
/**
 * Article handler for clean URLs
 * This file handles URLs in the format: slug.html
 */

// Get the requested URI
$request_uri = $_SERVER['REQUEST_URI'];

// Extract the filename from the URL
$path = parse_url($request_uri, PHP_URL_PATH);
$filename = basename($path);

// Remove the .html extension to get the slug
$slug = str_replace('.html', '', $filename);

// If we have a slug, include the news-detail.php file
if (!empty($slug)) {
    // Set the slug as a GET parameter for news-detail.php
    $_GET['slug'] = $slug;
    
    // Include the news-detail.php file
    if (file_exists('news-detail.php')) {
        include 'news-detail.php';
        exit;
    } else {
        // Redirect to news-detail.html as a fallback
        header('Location: news-detail.html?slug=' . urlencode($slug));
        exit;
    }
} else {
    // If no slug is provided, redirect to the news page
    header('Location: news.html');
    exit;
}
?>
