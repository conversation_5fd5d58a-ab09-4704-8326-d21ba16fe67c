/**
 * Manage Inc. Website JavaScript
 * Handles carousel functionality and interactive elements
 */
document.addEventListener('DOMContentLoaded', function() {
    // Partners carousel functionality
    const partnersContainer = document.querySelector('.partners-container');
    const prevBtn = document.querySelector('.carousel-prev');
    const nextBtn = document.querySelector('.carousel-next');

    if (partnersContainer && prevBtn && nextBtn) {
        // All partner logos
        const allPartnerLogos = [
            'images/partners/Picture4.png',
            'images/partners/Picture5.png',
            'images/partners/Picture6.png',
            'images/partners/Picture7.png',
            'images/partners/Picture8.png',
            'images/partners/Picture9.png',
            'images/partners/Picture10.png',
            'images/partners/Picture11.png',
            'images/partners/Picture12.png',
            'images/partners/Picture13.png',
            'images/partners/Picture14.png',
            'images/partners/Picture15.png',
            'images/partners/Picture16.png',
            'images/partners/Picture17.png',
            'images/partners/Picture18.png',
            'images/partners/Picture19.png',
            'images/partners/Picture20.png',
            'images/partners/Picture21.png',
            'images/partners/Picture22.png',
            'images/partners/Picture23.png',
            'images/partners/Picture24.png',
            'images/partners/Picture25.png',
            'images/partners/Picture26.png'
        ];

        // Configuration
        const config = {
            slidesToShow: {
                desktop: 6,
                tablet: 3,
                mobile: 1 // Changed from 2 to 1 to show only one image at a time on mobile
            },
            autoplay: true,
            autoplaySpeed: 3000,
            infinite: true
        };

        // State
        let currentIndex = 0;
        let autoplayInterval = null;

        // Get the number of slides to show based on screen width
        function getSlidesToShow() {
            const width = window.innerWidth;
            if (width < 768) {
                return config.slidesToShow.mobile;
            } else if (width < 992) {
                return config.slidesToShow.tablet;
            } else {
                return config.slidesToShow.desktop;
            }
        }

        // Update the carousel display
        function updateCarousel() {
            const slidesToShow = getSlidesToShow();

            // Clear the container
            partnersContainer.innerHTML = '';

            // Add the visible partners
            for (let i = 0; i < slidesToShow; i++) {
                const index = (currentIndex + i) % allPartnerLogos.length;

                const partner = document.createElement('div');
                partner.className = 'partner';

                const img = document.createElement('img');
                img.src = allPartnerLogos[index];
                img.alt = 'Partner Logo';
                img.style.filter = 'grayscale(0)';
                img.style.opacity = '1';

                partner.appendChild(img);
                partnersContainer.appendChild(partner);
            }
        }

        // Move the carousel by a certain number of slides
        function moveSlide(direction) {
            currentIndex = (currentIndex + direction + allPartnerLogos.length) % allPartnerLogos.length;
            updateCarousel();
        }

        // Set up navigation
        prevBtn.addEventListener('click', function() {
            moveSlide(-1);
            stopAutoplay();
        });

        nextBtn.addEventListener('click', function() {
            moveSlide(1);
            stopAutoplay();
        });

        // Start autoplay
        function startAutoplay() {
            if (config.autoplay && !autoplayInterval) {
                autoplayInterval = setInterval(function() {
                    moveSlide(1);
                }, config.autoplaySpeed);
            }
        }

        // Stop autoplay
        function stopAutoplay() {
            if (autoplayInterval) {
                clearInterval(autoplayInterval);
                autoplayInterval = null;
            }
        }

        // Pause on hover
        partnersContainer.parentElement.addEventListener('mouseenter', stopAutoplay);
        partnersContainer.parentElement.addEventListener('mouseleave', startAutoplay);

        // Handle window resize
        window.addEventListener('resize', updateCarousel);

        // Initialize
        updateCarousel();
        startAutoplay();
    }

    // Hero section slider functionality
    const controllers = document.querySelectorAll('.et-pb-controllers a');
    const mobileIndicators = document.querySelectorAll('.mobile-slider-indicators a');
    const slides = document.querySelectorAll('.et_pb_slide');
    let currentSlide = 0;
    let slideInterval;

    // Function to show a specific slide with smooth fade transition
    function showSlide(index) {
        // If we're already on this slide, do nothing
        if (currentSlide === index) return;

        // Remove active class from all controllers
        controllers.forEach(controller => controller.classList.remove('et-pb-active-control'));

        // Remove active class from all mobile indicators
        mobileIndicators.forEach(indicator => indicator.classList.remove('active'));

        // Add active class to current controller
        controllers[index].classList.add('et-pb-active-control');

        // Add active class to current mobile indicator
        if (mobileIndicators[index]) {
            mobileIndicators[index].classList.add('active');
        }

        // Get the current and next slides
        const currentSlideElement = slides[currentSlide];
        const nextSlideElement = slides[index];

        // Set up the transition
        const isMobile = window.innerWidth <= 768;
        const transitionDuration = isMobile ? 1000 : 600; // Longer, smoother transition for mobile

        // Prepare the next slide for transition
        nextSlideElement.style.opacity = '0';
        nextSlideElement.classList.add('active');
        nextSlideElement.style.display = 'block';
        nextSlideElement.style.transition = `opacity ${transitionDuration}ms ease-in-out`;

        // Force a reflow before starting the transition
        nextSlideElement.offsetHeight;

        // Start fade out of current slide
        currentSlideElement.style.opacity = '0';
        currentSlideElement.style.transition = `opacity ${transitionDuration}ms ease-in-out`;

        // After fade out completes
        setTimeout(() => {
            // Hide the old slide
            currentSlideElement.classList.remove('active');
            currentSlideElement.style.display = 'none';

            // Fade in the new slide
            nextSlideElement.style.opacity = '1';

            // Reset animations
            const titles = nextSlideElement.querySelectorAll('.et_pb_slide_title, .et_pb_slide_content h2');
            const content = nextSlideElement.querySelectorAll('.et_pb_slide_content p, .et_pb_button_wrapper');

            // Apply animations with a slight delay for smoother appearance
            titles.forEach(title => {
                title.style.animation = 'none';
                setTimeout(() => {
                    title.style.animation = 'fadeLeft 1.2s ease-in-out';
                    title.style.opacity = '1';
                }, 100);
            });

            content.forEach(item => {
                item.style.animation = 'none';
                setTimeout(() => {
                    item.style.animation = 'fadeLeft 1.2s ease-in-out';
                    item.style.opacity = '1';
                }, 500);
            });

            // Update current slide index
            currentSlide = index;
        }, transitionDuration / 2);
    }

    // Function to show the next slide
    function nextSlide() {
        let nextIndex = (currentSlide + 1) % slides.length;
        showSlide(nextIndex);
    }

    // Start automatic slideshow
    function startSlideshow() {
        slideInterval = setInterval(nextSlide, 8000); // Slightly longer to allow for smoother transitions
    }

    // Stop automatic slideshow
    function stopSlideshow() {
        clearInterval(slideInterval);
    }

    // Add click event to controllers
    if (controllers.length > 0) {
        controllers.forEach((controller, index) => {
            controller.addEventListener('click', function(e) {
                e.preventDefault();
                stopSlideshow();
                showSlide(index);
                startSlideshow();
            });
        });

        // Start the slideshow
        startSlideshow();
    }

    // Add click event to mobile indicators
    if (mobileIndicators.length > 0) {
        mobileIndicators.forEach((indicator, index) => {
            indicator.addEventListener('click', function(e) {
                e.preventDefault();
                stopSlideshow();
                showSlide(index);
                startSlideshow();
            });
        });
    }

    // Mobile menu toggle (for responsive design)
    const mobileMenuToggle = document.querySelector('.mobile-menu-toggle');
    const mainNav = document.querySelector('.main-nav');

    if (mobileMenuToggle && mainNav) {
        mobileMenuToggle.addEventListener('click', function() {
            mainNav.classList.toggle('active');
            this.classList.toggle('active');
        });
    }
});
