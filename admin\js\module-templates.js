/**
 * Templates Module JavaScript
 * Functionality for the templates module
 * Consolidated from multiple JS files
 */

// Initialize templates module when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    // Initialize templates module
    TemplatesModule.init();
});

// Templates Module Namespace
const TemplatesModule = {
    // Initialize templates module
    init: function() {
        this.initTemplateEditor();
        this.initVariablesDropdown();
        this.initTemplatePreview();
        this.initTemplateSave();
        this.initTemplateDelete();
        this.initTemplateExport();
        this.initTemplateImport();
        this.initTemplateSearch();
        this.initTemplateFilter();
        this.initTemplateSort();
    },

    // Initialize template editor
    initTemplateEditor: function() {
        const templateEditor = document.querySelector('#template-editor');
        
        if (templateEditor) {
            // Initialize editor tabs
            const editorTabs = templateEditor.querySelectorAll('.template-editor-tab');
            const editorContents = templateEditor.querySelectorAll('.template-editor-content');
            
            editorTabs.forEach(function(tab) {
                tab.addEventListener('click', function(e) {
                    e.preventDefault();
                    
                    // Get target content
                    const target = this.getAttribute('data-target');
                    
                    // Hide all contents
                    editorContents.forEach(function(content) {
                        content.classList.remove('active');
                    });
                    
                    // Deactivate all tabs
                    editorTabs.forEach(function(tab) {
                        tab.classList.remove('active');
                    });
                    
                    // Show target content
                    document.querySelector(target).classList.add('active');
                    
                    // Activate current tab
                    this.classList.add('active');
                });
            });
            
            // Initialize code editor
            const codeEditor = templateEditor.querySelector('#code-editor');
            const htmlEditor = templateEditor.querySelector('#html-editor');
            
            if (codeEditor && htmlEditor) {
                // Initialize CodeMirror if available
                if (typeof CodeMirror !== 'undefined') {
                    window.editor = CodeMirror.fromTextArea(htmlEditor, {
                        mode: 'htmlmixed',
                        lineNumbers: true,
                        lineWrapping: true,
                        theme: 'default',
                        extraKeys: {
                            'Ctrl-Space': 'autocomplete'
                        }
                    });
                    
                    // Set editor mode
                    const modeButtons = document.querySelectorAll('.code-editor-mode-button');
                    modeButtons.forEach(function(button) {
                        button.addEventListener('click', function(e) {
                            e.preventDefault();
                            
                            const mode = this.getAttribute('data-mode');
                            
                            // Deactivate all buttons
                            modeButtons.forEach(function(btn) {
                                btn.classList.remove('active');
                            });
                            
                            // Activate current button
                            this.classList.add('active');
                            
                            // Set editor mode
                            if (mode === 'html') {
                                window.editor.setOption('mode', 'htmlmixed');
                            } else if (mode === 'css') {
                                window.editor.setOption('mode', 'css');
                            } else if (mode === 'js') {
                                window.editor.setOption('mode', 'javascript');
                            }
                        });
                    });
                    
                    // Set HTML mode as default
                    const htmlModeButton = document.querySelector('[data-mode="html"]');
                    if (htmlModeButton) {
                        htmlModeButton.classList.add('active');
                    }
                    
                    // Update preview on change
                    window.editor.on('change', function() {
                        TemplatesModule.updatePreview(window.editor.getValue());
                    });
                    
                    // Initial preview
                    TemplatesModule.updatePreview(window.editor.getValue());
                } else {
                    // Fallback to textarea
                    htmlEditor.addEventListener('input', function() {
                        TemplatesModule.updatePreview(this.value);
                    });
                    
                    // Initial preview
                    TemplatesModule.updatePreview(htmlEditor.value);
                }
            }
        }
    },

    // Update template preview
    updatePreview: function(html) {
        const previewFrame = document.querySelector('#preview-frame');
        
        if (previewFrame) {
            // Replace variables with sample values
            const sampleData = {
                '{{site_name}}': 'Your Website',
                '{{site_url}}': 'https://example.com',
                '{{admin_email}}': '<EMAIL>',
                '{{user_name}}': 'John Doe',
                '{{user_email}}': '<EMAIL>',
                '{{verification_link}}': 'https://example.com/verify?token=sample',
                '{{reset_link}}': 'https://example.com/reset?token=sample',
                '{{logo}}': 'https://example.com/logo.png',
                '{{current_year}}': new Date().getFullYear(),
                '{{admin_logo}}': '../admin/images/logo.png',
                '{{primary_color}}': '#3c3c45',
                '{{secondary_color}}': '#f1ca2f'
            };
            
            let previewHtml = html;
            
            for (const [variable, value] of Object.entries(sampleData)) {
                previewHtml = previewHtml.replace(new RegExp(variable, 'g'), value);
            }
            
            // Update preview
            const previewDocument = previewFrame.contentDocument || previewFrame.contentWindow.document;
            previewDocument.open();
            previewDocument.write(previewHtml);
            previewDocument.close();
        }
    },

    // Initialize variables dropdown
    initVariablesDropdown: function() {
        const variablesDropdown = document.querySelector('.variables-dropdown');
        const variablesToggle = document.querySelector('.variables-dropdown-toggle');
        const variablesItems = document.querySelectorAll('.variables-dropdown-item');
        
        if (variablesDropdown && variablesToggle) {
            variablesToggle.addEventListener('click', function(e) {
                e.preventDefault();
                variablesDropdown.classList.toggle('active');
            });
            
            // Close dropdown when clicking outside
            document.addEventListener('click', function(e) {
                if (variablesDropdown && !variablesDropdown.contains(e.target)) {
                    variablesDropdown.classList.remove('active');
                }
            });
            
            // Insert variable
            variablesItems.forEach(function(item) {
                item.addEventListener('click', function() {
                    const variable = this.getAttribute('data-variable');
                    
                    // Insert into CodeMirror if available
                    if (typeof CodeMirror !== 'undefined' && window.editor) {
                        window.editor.replaceSelection(variable);
                        window.editor.focus();
                    } else {
                        // Fallback to textarea
                        const textarea = document.querySelector('#html-editor');
                        if (textarea) {
                            const start = textarea.selectionStart;
                            const end = textarea.selectionEnd;
                            const text = textarea.value;
                            
                            textarea.value = text.substring(0, start) + variable + text.substring(end);
                            textarea.selectionStart = textarea.selectionEnd = start + variable.length;
                            textarea.focus();
                            
                            // Trigger input event to update preview
                            const event = new Event('input');
                            textarea.dispatchEvent(event);
                        }
                    }
                    
                    // Close dropdown
                    variablesDropdown.classList.remove('active');
                });
            });
            
            // Filter variables
            const variablesSearch = document.querySelector('.variables-panel-search input');
            if (variablesSearch) {
                variablesSearch.addEventListener('input', function() {
                    const query = this.value.toLowerCase();
                    
                    variablesItems.forEach(function(item) {
                        const variable = item.getAttribute('data-variable').toLowerCase();
                        const description = item.querySelector('.variable-description').textContent.toLowerCase();
                        
                        if (variable.includes(query) || description.includes(query)) {
                            item.style.display = '';
                        } else {
                            item.style.display = 'none';
                        }
                    });
                    
                    // Show/hide groups based on visible items
                    const variablesGroups = document.querySelectorAll('.variables-group');
                    variablesGroups.forEach(function(group) {
                        const visibleItems = group.querySelectorAll('.variable-item[style=""]').length;
                        group.style.display = visibleItems > 0 ? '' : 'none';
                    });
                });
            }
        }
    },

    // Initialize template preview
    initTemplatePreview: function() {
        const previewButton = document.querySelector('#preview-template');
        const previewFrame = document.querySelector('#preview-frame');
        
        if (previewButton && previewFrame) {
            previewButton.addEventListener('click', function(e) {
                e.preventDefault();
                
                // Get template content
                let html;
                
                if (typeof CodeMirror !== 'undefined' && window.editor) {
                    html = window.editor.getValue();
                } else {
                    const textarea = document.querySelector('#html-editor');
                    if (textarea) {
                        html = textarea.value;
                    }
                }
                
                if (html) {
                    // Open preview in new window
                    const previewWindow = window.open('', 'templatePreview', 'width=1024,height=768');
                    
                    // Replace variables with sample values
                    const sampleData = {
                        '{{site_name}}': 'Your Website',
                        '{{site_url}}': 'https://example.com',
                        '{{admin_email}}': '<EMAIL>',
                        '{{user_name}}': 'John Doe',
                        '{{user_email}}': '<EMAIL>',
                        '{{verification_link}}': 'https://example.com/verify?token=sample',
                        '{{reset_link}}': 'https://example.com/reset?token=sample',
                        '{{logo}}': 'https://example.com/logo.png',
                        '{{current_year}}': new Date().getFullYear(),
                        '{{admin_logo}}': '../admin/images/logo.png',
                        '{{primary_color}}': '#3c3c45',
                        '{{secondary_color}}': '#f1ca2f'
                    };
                    
                    let previewHtml = html;
                    
                    for (const [variable, value] of Object.entries(sampleData)) {
                        previewHtml = previewHtml.replace(new RegExp(variable, 'g'), value);
                    }
                    
                    // Write to preview window
                    previewWindow.document.open();
                    previewWindow.document.write(previewHtml);
                    previewWindow.document.close();
                }
            });
        }
    },

    // Initialize template save
    initTemplateSave: function() {
        const saveButton = document.querySelector('#save-template');
        const templateForm = document.querySelector('#template-form');
        
        if (saveButton && templateForm) {
            saveButton.addEventListener('click', function(e) {
                e.preventDefault();
                
                // Update textarea value from CodeMirror if available
                if (typeof CodeMirror !== 'undefined' && window.editor) {
                    window.editor.save();
                }
                
                const formData = new FormData(templateForm);
                
                // Show loading state
                this.disabled = true;
                this.classList.add('loading');
                
                const originalText = this.innerHTML;
                this.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Saving...';
                
                // Save template
                fetch('ajax/save_template.php', {
                    method: 'POST',
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    // Reset button
                    this.disabled = false;
                    this.classList.remove('loading');
                    this.innerHTML = originalText;
                    
                    if (data.success) {
                        AdminCore.showNotification('Template saved successfully.', 'success');
                    } else {
                        AdminCore.showNotification(data.message || 'Error saving template.', 'error');
                    }
                })
                .catch(error => {
                    console.error('Error saving template:', error);
                    
                    // Reset button
                    this.disabled = false;
                    this.classList.remove('loading');
                    this.innerHTML = originalText;
                    
                    AdminCore.showNotification('Error saving template.', 'error');
                });
            });
        }
    },

    // Initialize template delete
    initTemplateDelete: function() {
        const deleteButtons = document.querySelectorAll('.delete-template');
        
        deleteButtons.forEach(function(button) {
            button.addEventListener('click', function(e) {
                e.preventDefault();
                
                const templateId = this.getAttribute('data-id');
                const templateName = this.getAttribute('data-name');
                
                if (confirm(`Are you sure you want to delete the template "${templateName}"? This action cannot be undone.`)) {
                    // Delete template via AJAX
                    fetch('ajax/delete_template.php', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/x-www-form-urlencoded',
                        },
                        body: `id=${templateId}`
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            // Remove template from list
                            const templateCard = this.closest('.template-card');
                            if (templateCard) {
                                templateCard.remove();
                            }
                            
                            AdminCore.showNotification('Template deleted successfully.', 'success');
                        } else {
                            AdminCore.showNotification(data.message || 'Error deleting template.', 'error');
                        }
                    })
                    .catch(error => {
                        console.error('Error deleting template:', error);
                        AdminCore.showNotification('Error deleting template.', 'error');
                    });
                }
            });
        });
    },

    // Initialize template export
    initTemplateExport: function() {
        const exportButtons = document.querySelectorAll('.export-template');
        
        exportButtons.forEach(function(button) {
            button.addEventListener('click', function(e) {
                e.preventDefault();
                
                const templateId = this.getAttribute('data-id');
                
                // Export template
                window.location.href = `ajax/export_template.php?id=${templateId}`;
            });
        });
    },

    // Initialize template import
    initTemplateImport: function() {
        const importForm = document.querySelector('#import-template-form');
        
        if (importForm) {
            importForm.addEventListener('submit', function(e) {
                e.preventDefault();
                
                const formData = new FormData(this);
                const submitButton = this.querySelector('[type="submit"]');
                
                // Validate file
                const fileInput = this.querySelector('input[type="file"]');
                if (!fileInput.files.length) {
                    AdminCore.showNotification('Please select a template file to import.', 'error');
                    return;
                }
                
                // Disable submit button and show loading state
                if (submitButton) {
                    submitButton.disabled = true;
                    submitButton.classList.add('loading');
                    
                    const originalText = submitButton.innerHTML;
                    submitButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Importing...';
                }
                
                // Import template
                fetch('ajax/import_template.php', {
                    method: 'POST',
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    // Re-enable submit button
                    if (submitButton) {
                        submitButton.disabled = false;
                        submitButton.classList.remove('loading');
                        submitButton.innerHTML = originalText;
                    }
                    
                    if (data.success) {
                        AdminCore.showNotification('Template imported successfully.', 'success');
                        
                        // Reload page
                        window.location.reload();
                    } else {
                        AdminCore.showNotification(data.message || 'Error importing template.', 'error');
                    }
                })
                .catch(error => {
                    console.error('Error importing template:', error);
                    
                    // Re-enable submit button
                    if (submitButton) {
                        submitButton.disabled = false;
                        submitButton.classList.remove('loading');
                        submitButton.innerHTML = originalText;
                    }
                    
                    AdminCore.showNotification('Error importing template.', 'error');
                });
            });
        }
    },

    // Initialize template search
    initTemplateSearch: function() {
        const searchInput = document.querySelector('#template-search');
        const templateCards = document.querySelectorAll('.template-card');
        
        if (searchInput && templateCards.length) {
            searchInput.addEventListener('input', function() {
                const query = this.value.toLowerCase();
                
                templateCards.forEach(function(card) {
                    const title = card.querySelector('.template-card-title').textContent.toLowerCase();
                    const description = card.querySelector('.template-card-description').textContent.toLowerCase();
                    
                    if (title.includes(query) || description.includes(query)) {
                        card.style.display = '';
                    } else {
                        card.style.display = 'none';
                    }
                });
            });
        }
    },

    // Initialize template filter
    initTemplateFilter: function() {
        const filterSelect = document.querySelector('#template-filter');
        const templateCards = document.querySelectorAll('.template-card');
        
        if (filterSelect && templateCards.length) {
            filterSelect.addEventListener('change', function() {
                const filter = this.value;
                
                templateCards.forEach(function(card) {
                    if (filter === 'all' || card.getAttribute('data-type') === filter) {
                        card.style.display = '';
                    } else {
                        card.style.display = 'none';
                    }
                });
            });
        }
    },

    // Initialize template sort
    initTemplateSort: function() {
        const sortSelect = document.querySelector('#template-sort');
        const templatesContainer = document.querySelector('.templates-list');
        const templateCards = document.querySelectorAll('.template-card');
        
        if (sortSelect && templatesContainer && templateCards.length) {
            sortSelect.addEventListener('change', function() {
                const sort = this.value;
                const cards = Array.from(templateCards);
                
                cards.sort(function(a, b) {
                    if (sort === 'name-asc') {
                        return a.querySelector('.template-card-title').textContent.localeCompare(b.querySelector('.template-card-title').textContent);
                    } else if (sort === 'name-desc') {
                        return b.querySelector('.template-card-title').textContent.localeCompare(a.querySelector('.template-card-title').textContent);
                    } else if (sort === 'type') {
                        return a.getAttribute('data-type').localeCompare(b.getAttribute('data-type'));
                    }
                });
                
                // Reorder cards
                cards.forEach(function(card) {
                    templatesContainer.appendChild(card);
                });
            });
        }
    }
};
