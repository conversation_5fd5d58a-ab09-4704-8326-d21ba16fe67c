/**
 * Admin Dashboard CSS
 *
 * This file contains styles specific to the admin dashboard page.
 */

/* Dashboard Stats */
.dashboard-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--spacing-4);
  margin-bottom: var(--spacing-6);
}

.dashboard-stat-card {
  background-color: var(--white);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
  padding: var(--spacing-4);
  border: 1px solid var(--border-color);
  transition: all var(--transition-fast) ease;
  position: relative;
  overflow: hidden;
  height: 100%; /* Ensure consistent height */
  display: flex;
  flex-direction: column;
}

.dashboard-stat-card:hover {
  transform: translateY(-3px);
  box-shadow: var(--shadow-md);
}

.dashboard-stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 3px;
  background: linear-gradient(to right, var(--primary-color), var(--primary-light));
  opacity: 0;
  transition: opacity var(--transition-fast) ease;
}

.dashboard-stat-card:hover::before {
  opacity: 1;
}

.dashboard-stat-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-3);
}

.dashboard-stat-title {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--text-light);
  margin: 0;
}

.dashboard-stat-icon {
  width: 40px;
  height: 40px;
  border-radius: var(--radius-md);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--font-size-lg);
}

.dashboard-stat-icon.primary {
  background-color: var(--primary-very-light);
  color: var(--primary-color);
}

.dashboard-stat-icon.success {
  background-color: rgba(16, 185, 129, 0.1);
  color: var(--success-color);
}

.dashboard-stat-icon.info {
  background-color: rgba(59, 130, 246, 0.1);
  color: var(--info-color);
}

.dashboard-stat-icon.warning {
  background-color: rgba(245, 158, 11, 0.1);
  color: var(--warning-color);
}

.dashboard-stat-icon.danger {
  background-color: rgba(239, 68, 68, 0.1);
  color: var(--danger-color);
}

.dashboard-stat-value {
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-bold);
  color: var(--text-dark);
  margin-bottom: var(--spacing-2);
  line-height: 1.2;
  margin-top: auto; /* Push to bottom of flex container */
}

.dashboard-stat-change {
  font-size: var(--font-size-xs);
  color: var(--text-light);
  display: flex;
  align-items: center;
  gap: var(--spacing-1);
  margin-top: auto; /* Push to bottom of flex container */
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.dashboard-stat-change i {
  flex-shrink: 0;
}

.dashboard-stat-change.positive {
  color: var(--success-color);
}

.dashboard-stat-change.negative {
  color: var(--danger-color);
}

/* Dashboard Sections */
.dashboard-section {
  margin-bottom: var(--spacing-6);
}

.dashboard-section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--spacing-4);
}

.dashboard-section-title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--text-dark);
  margin: 0;
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
}

.dashboard-section-title i {
  color: var(--primary-color);
  font-size: var(--font-size-base);
}

/* Quick Actions */
.dashboard-quick-actions {
  display: grid;
  grid-template-columns: repeat(6, 1fr); /* Fixed 6 columns for the 6 quick action cards */
  gap: var(--spacing-2);
}

.quick-action-card {
  background-color: var(--white);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
  padding: var(--spacing-2);
  border: 1px solid var(--border-color);
  text-align: center;
  text-decoration: none;
  transition: all var(--transition-fast) ease;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  min-height: 120px; /* Reduced height */
}

.quick-action-card:hover {
  transform: translateY(-3px);
  box-shadow: var(--shadow-md);
  border-color: var(--primary-color);
}

.quick-action-icon {
  width: 40px;
  height: 40px;
  border-radius: var(--radius-full);
  background-color: var(--primary-very-light);
  color: var(--primary-color);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--font-size-base);
  margin-bottom: var(--spacing-1);
  transition: all var(--transition-fast) ease;
}

.quick-action-card:hover .quick-action-icon {
  background-color: var(--primary-color);
  color: var(--white);
  transform: scale(1.05);
}

.quick-action-title {
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-semibold);
  color: var(--text-dark);
  margin: 0 0 var(--spacing-1);
  line-height: 1.2;
}

.quick-action-desc {
  font-size: 10px;
  color: var(--text-light);
  margin: 0;
  line-height: 1.2;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  max-height: 2.4em;
}

/* Dashboard Grid */
.dashboard-grid-2 {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--spacing-4);
  margin-bottom: var(--spacing-6);
}

/* Dashboard Card */
.dashboard-card {
  background-color: var(--white);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--border-color);
  overflow: hidden;
}

.dashboard-card-header {
  padding: var(--spacing-4);
  border-bottom: 1px solid var(--border-color);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.dashboard-card-title {
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-semibold);
  color: var(--text-dark);
  margin: 0;
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
}

.dashboard-card-title i {
  color: var(--primary-color);
}

.dashboard-card-body {
  padding: var(--spacing-4);
}

.dashboard-card-footer {
  padding: var(--spacing-3) var(--spacing-4);
  border-top: 1px solid var(--border-color);
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: var(--spacing-2);
}

/* Activity List */
.activity-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-3);
}

.activity-item {
  display: flex;
  align-items: flex-start;
  gap: var(--spacing-3);
  padding-bottom: var(--spacing-3);
  border-bottom: 1px solid var(--border-light);
}

.activity-item:last-child {
  padding-bottom: 0;
  border-bottom: none;
}

.activity-icon {
  width: 36px;
  height: 36px;
  border-radius: var(--radius-md);
  background-color: var(--primary-very-light);
  color: var(--primary-color);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--font-size-base);
  flex-shrink: 0;
}

.activity-icon.success {
  background-color: rgba(16, 185, 129, 0.1);
  color: var(--success-color);
}

.activity-icon.info {
  background-color: rgba(59, 130, 246, 0.1);
  color: var(--info-color);
}

.activity-icon.warning {
  background-color: rgba(245, 158, 11, 0.1);
  color: var(--warning-color);
}

.activity-icon.danger {
  background-color: rgba(239, 68, 68, 0.1);
  color: var(--danger-color);
}

.activity-content {
  flex: 1;
}

.activity-message {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--text-dark);
  margin-bottom: var(--spacing-1);
}

.activity-time {
  font-size: var(--font-size-xs);
  color: var(--text-light);
}

/* Status List */
.status-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-3);
}

.status-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-bottom: var(--spacing-3);
  border-bottom: 1px solid var(--border-light);
}

.status-item:last-child {
  padding-bottom: 0;
  border-bottom: none;
}

.status-label {
  font-size: var(--font-size-sm);
  color: var(--text-dark);
  font-weight: var(--font-weight-medium);
}

.status-value {
  font-size: var(--font-size-sm);
  color: var(--text-color);
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  text-align: right;
  max-width: 50%;
  word-break: break-word;
}

.badge {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-1) var(--spacing-2);
  border-radius: var(--radius-full);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
  line-height: 1;
}

.badge-success {
  background-color: rgba(16, 185, 129, 0.1);
  color: var(--success-color);
}

.badge-warning {
  background-color: rgba(245, 158, 11, 0.1);
  color: var(--warning-color);
}

.badge-danger {
  background-color: rgba(239, 68, 68, 0.1);
  color: var(--danger-color);
}

.status-indicator {
  width: 8px;
  height: 8px;
  border-radius: var(--radius-full);
  display: inline-block;
  margin-left: var(--spacing-1);
}

.status-indicator.success {
  background-color: var(--success-color);
}

.status-indicator.warning {
  background-color: var(--warning-color);
}

.status-indicator.danger {
  background-color: var(--danger-color);
}

/* Responsive */
@media (max-width: 992px) {
  .dashboard-quick-actions {
    grid-template-columns: repeat(6, 1fr); /* Keep 6 columns on medium screens */
    gap: var(--spacing-2);
  }

  .quick-action-card {
    min-height: 110px;
    padding: var(--spacing-1);
  }

  .quick-action-icon {
    width: 36px;
    height: 36px;
    font-size: var(--font-size-sm);
    margin-bottom: var(--spacing-1);
  }
}

@media (max-width: 768px) {
  .dashboard-stats {
    grid-template-columns: repeat(2, 1fr);
    gap: var(--spacing-3);
  }

  .dashboard-quick-actions {
    grid-template-columns: repeat(3, 1fr); /* 3 columns on tablet */
    gap: var(--spacing-2);
  }

  .dashboard-grid-2 {
    grid-template-columns: 1fr;
    gap: var(--spacing-4);
  }

  .dashboard-section {
    margin-bottom: var(--spacing-4);
  }

  .dashboard-section-header {
    margin-bottom: var(--spacing-3);
  }

  .dashboard-section-title {
    font-size: var(--font-size-base);
  }

  .dashboard-card-header {
    padding: var(--spacing-3);
  }

  .dashboard-card-body {
    padding: var(--spacing-3);
  }

  .dashboard-card-footer {
    padding: var(--spacing-2) var(--spacing-3);
  }
}

@media (max-width: 576px) {
  .dashboard-stats {
    grid-template-columns: 1fr;
  }

  .dashboard-quick-actions {
    grid-template-columns: repeat(2, 1fr); /* 2 columns on mobile */
  }

  .quick-action-card {
    min-height: 100px;
  }

  .dashboard-stat-value {
    font-size: var(--font-size-2xl);
  }
}
