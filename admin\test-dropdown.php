<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>User Dropdown Test</title>
    
    <!-- Test different CSS loading methods -->
    <style>
        /* Inline CSS Variables */
        :root {
            --primary-color: #f1ca2f;
            --secondary-color: #3c3c45;
            --text-color: #333333;
            --text-dark: #212529;
            --text-light: #6c757d;
            --text-muted: #868e96;
            --white: #ffffff;
            --background-light: #f8f9fa;
            --border-color: #dee2e6;
            --border-light: #e9ecef;
            --spacing-1: 0.25rem;
            --spacing-2: 0.5rem;
            --spacing-3: 1rem;
            --spacing-4: 1.5rem;
            --radius-sm: 0.25rem;
            --radius-md: 0.375rem;
            --radius-lg: 0.5rem;
            --radius-full: 50rem;
            --font-size-xs: 0.75rem;
            --font-size-sm: 0.875rem;
            --font-size-base: 1rem;
            --font-size-xl: 1.25rem;
            --font-weight-medium: 500;
            --font-weight-bold: 700;
            --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
            --transition-fast: 0.15s;
            --topbar-height: 60px;
        }
        
        /* Reset */
        * { box-sizing: border-box; }
        
        body {
            margin: 0;
            padding: 0;
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
            background-color: var(--background-light);
        }
        
        /* Test Header */
        .test-header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: var(--topbar-height);
            background-color: var(--white);
            border-bottom: 1px solid var(--border-color);
            z-index: 1000;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 var(--spacing-4);
        }
        
        .test-content {
            margin-top: var(--topbar-height);
            padding: var(--spacing-4);
        }
        
        /* INLINE USER DROPDOWN STYLES */
        .topbar-user-menu {
            position: relative;
            height: 100%;
            display: flex;
            align-items: center;
        }
        
        .user-menu-toggle {
            display: flex;
            align-items: center;
            gap: var(--spacing-2);
            padding: var(--spacing-1) var(--spacing-2);
            background-color: transparent;
            border: none;
            border-radius: var(--radius-md);
            color: var(--text-color);
            cursor: pointer;
            transition: all var(--transition-fast) ease;
            height: 40px;
        }
        
        .user-menu-toggle:hover {
            background-color: rgba(0, 0, 0, 0.05);
        }
        
        .user-avatar {
            width: 36px;
            height: 36px;
            border-radius: var(--radius-full);
            overflow: hidden;
            flex-shrink: 0;
            background-color: var(--background-light);
            display: flex;
            align-items: center;
            justify-content: center;
            border: 2px solid var(--white);
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        
        .avatar-placeholder {
            width: 100%;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: var(--primary-color);
            color: var(--white);
            font-weight: var(--font-weight-bold);
            text-transform: uppercase;
        }
        
        .user-name {
            font-weight: var(--font-weight-medium);
            display: none;
            margin-right: var(--spacing-2);
        }
        
        @media (min-width: 768px) {
            .user-name { display: inline-block; }
        }
        
        .dropdown-icon {
            font-size: var(--font-size-xs);
            transition: transform var(--transition-fast) ease;
            color: var(--text-light);
        }
        
        .user-menu-toggle.active .dropdown-icon {
            transform: rotate(180deg);
            color: var(--primary-color);
        }
        
        /* User Dropdown */
        .user-dropdown {
            position: absolute;
            top: calc(100% + 5px);
            right: 0;
            z-index: 9999;
            width: 320px;
            background-color: var(--white);
            border-radius: var(--radius-lg);
            box-shadow: var(--shadow-xl);
            border: 1px solid var(--border-color);
            display: none;
            transform: translateY(10px);
            opacity: 0;
            transition: all var(--transition-fast) ease;
        }
        
        .user-dropdown.show {
            display: block;
            transform: translateY(0);
            opacity: 1;
        }
        
        .user-dropdown-header {
            padding: var(--spacing-4);
            display: flex;
            align-items: center;
            gap: var(--spacing-3);
            border-bottom: 1px solid var(--border-light);
            background-color: var(--background-light);
        }
        
        .user-dropdown-avatar {
            width: 60px;
            height: 60px;
            border-radius: var(--radius-full);
            overflow: hidden;
            flex-shrink: 0;
            border: 3px solid var(--white);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .avatar-placeholder-large {
            width: 100%;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: var(--primary-color);
            color: var(--white);
            font-weight: var(--font-weight-bold);
            font-size: var(--font-size-xl);
            text-transform: uppercase;
        }
        
        .user-dropdown-info {
            flex: 1;
            min-width: 0;
        }
        
        .user-dropdown-name {
            margin: 0 0 4px 0;
            font-weight: var(--font-weight-bold);
            color: var(--text-color);
        }
        
        .user-dropdown-email {
            margin: 0 0 8px 0;
            font-size: var(--font-size-xs);
            color: var(--text-light);
        }
        
        .user-dropdown-badge {
            display: inline-block;
            padding: 2px 8px;
            background-color: var(--primary-color);
            color: var(--text-dark);
            font-size: 10px;
            font-weight: var(--font-weight-medium);
            border-radius: 12px;
            text-transform: uppercase;
        }
        
        .dropdown-menu {
            padding: 0;
        }
        
        .dropdown-section {
            margin-bottom: var(--spacing-3);
            border-bottom: 1px solid var(--border-color);
            padding-bottom: var(--spacing-3);
        }
        
        .dropdown-section:last-child {
            border-bottom: none;
            margin-bottom: 0;
            padding-bottom: 0;
        }
        
        .dropdown-item {
            margin: 0;
            padding: 0;
        }
        
        .dropdown-link {
            padding: var(--spacing-3) var(--spacing-4);
            display: flex;
            align-items: center;
            gap: var(--spacing-3);
            color: var(--text-dark);
            text-decoration: none;
            transition: all var(--transition-fast) ease;
            font-size: var(--font-size-sm);
            border-left: 3px solid transparent;
        }
        
        .dropdown-link:hover {
            background-color: var(--background-light);
            color: var(--primary-color);
            border-left-color: var(--primary-color);
        }
        
        .dropdown-link-icon {
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--text-light);
            background-color: var(--background-light);
            border-radius: var(--radius-full);
            flex-shrink: 0;
        }
        
        .dropdown-link:hover .dropdown-link-icon {
            color: var(--primary-color);
            background-color: rgba(241, 202, 47, 0.1);
        }
        
        .dropdown-link-content {
            flex: 1;
        }
        
        .dropdown-link-title {
            display: block;
            font-weight: var(--font-weight-medium);
            margin-bottom: 2px;
        }
        
        .dropdown-link-description {
            display: block;
            font-size: var(--font-size-xs);
            color: var(--text-muted);
        }
        
        .logout-link {
            color: #dc3545 !important;
        }
        
        .logout-link:hover {
            background-color: rgba(220, 53, 69, 0.1);
            color: #dc3545 !important;
            border-left-color: #dc3545;
        }
        
        /* Debug styles */
        .debug-info {
            background: #fff;
            border: 1px solid #ddd;
            padding: 15px;
            margin: 20px 0;
            border-radius: 5px;
        }
        
        .test-button {
            background: #007cba;
            color: white;
            padding: 10px 15px;
            border: none;
            border-radius: 3px;
            cursor: pointer;
            margin: 5px;
        }
    </style>
    
    <!-- Try loading external CSS as well -->
    <link rel="stylesheet" href="assets/css/consolidated.css?v=<?php echo time(); ?>">
    <link rel="stylesheet" href="assets/css/main.css?v=<?php echo time(); ?>">
</head>
<body>
    <div class="test-header">
        <div>
            <h2>User Dropdown Test</h2>
        </div>
        
        <div class="topbar-user-menu">
            <button class="user-menu-toggle" id="userMenuToggle">
                <div class="user-avatar">
                    <div class="avatar-placeholder">A</div>
                </div>
                <span class="user-name">Admin</span>
                <span class="dropdown-icon">▼</span>
            </button>
            
            <div class="user-dropdown" id="userDropdown">
                <div class="user-dropdown-header">
                    <div class="user-dropdown-avatar">
                        <div class="avatar-placeholder-large">A</div>
                    </div>
                    <div class="user-dropdown-info">
                        <div class="user-dropdown-name">Admin User</div>
                        <div class="user-dropdown-email"><EMAIL></div>
                        <div class="user-dropdown-badge">Administrator</div>
                    </div>
                </div>
                
                <div class="dropdown-menu">
                    <div class="dropdown-section">
                        <div class="dropdown-item">
                            <a href="#" class="dropdown-link">
                                <div class="dropdown-link-icon">👤</div>
                                <div class="dropdown-link-content">
                                    <span class="dropdown-link-title">Profile</span>
                                    <span class="dropdown-link-description">Manage your account</span>
                                </div>
                            </a>
                        </div>
                        <div class="dropdown-item">
                            <a href="#" class="dropdown-link">
                                <div class="dropdown-link-icon">⚙️</div>
                                <div class="dropdown-link-content">
                                    <span class="dropdown-link-title">Settings</span>
                                    <span class="dropdown-link-description">Configure preferences</span>
                                </div>
                            </a>
                        </div>
                    </div>
                    
                    <div class="dropdown-section">
                        <div class="dropdown-item">
                            <a href="#" class="dropdown-link logout-link">
                                <div class="dropdown-link-icon">🚪</div>
                                <div class="dropdown-link-content">
                                    <span class="dropdown-link-title">Logout</span>
                                    <span class="dropdown-link-description">Sign out of your account</span>
                                </div>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="test-content">
        <div class="debug-info">
            <h3>CSS Loading Test Results</h3>
            <p>This page tests the user dropdown with inline CSS to isolate the issue.</p>
            
            <button class="test-button" onclick="testStyles()">Test Applied Styles</button>
            <button class="test-button" onclick="showComputedStyles()">Show Computed Styles</button>
            <button class="test-button" onclick="testCSSFiles()">Test CSS File Loading</button>
            
            <div id="test-results"></div>
        </div>
        
        <div class="debug-info">
            <h3>Instructions</h3>
            <ol>
                <li>Click the user avatar in the top-right corner</li>
                <li>The dropdown should appear with proper styling</li>
                <li>If it works here but not in the main admin panel, the issue is with CSS loading</li>
                <li>If it doesn't work here either, the issue is with the HTML structure or JavaScript</li>
            </ol>
        </div>
    </div>
    
    <script>
        // Dropdown functionality
        document.getElementById('userMenuToggle').addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            
            const dropdown = document.getElementById('userDropdown');
            const toggle = document.getElementById('userMenuToggle');
            
            if (dropdown.classList.contains('show')) {
                dropdown.classList.remove('show');
                toggle.classList.remove('active');
            } else {
                dropdown.classList.add('show');
                toggle.classList.add('active');
            }
        });
        
        // Close dropdown when clicking outside
        document.addEventListener('click', function(e) {
            const dropdown = document.getElementById('userDropdown');
            const toggle = document.getElementById('userMenuToggle');
            
            if (!toggle.contains(e.target) && !dropdown.contains(e.target)) {
                dropdown.classList.remove('show');
                toggle.classList.remove('active');
            }
        });
        
        // Test functions
        function testStyles() {
            const results = document.getElementById('test-results');
            const dropdown = document.getElementById('userDropdown');
            const toggle = document.getElementById('userMenuToggle');
            
            const dropdownStyles = window.getComputedStyle(dropdown);
            const toggleStyles = window.getComputedStyle(toggle);
            
            let html = '<h4>Style Test Results:</h4>';
            html += `<p><strong>Dropdown position:</strong> ${dropdownStyles.position}</p>`;
            html += `<p><strong>Dropdown background:</strong> ${dropdownStyles.backgroundColor}</p>`;
            html += `<p><strong>Dropdown z-index:</strong> ${dropdownStyles.zIndex}</p>`;
            html += `<p><strong>Toggle display:</strong> ${toggleStyles.display}</p>`;
            html += `<p><strong>Toggle align-items:</strong> ${toggleStyles.alignItems}</p>`;
            
            results.innerHTML = html;
        }
        
        function showComputedStyles() {
            const results = document.getElementById('test-results');
            const dropdown = document.getElementById('userDropdown');
            
            const styles = window.getComputedStyle(dropdown);
            let html = '<h4>All Computed Styles for Dropdown:</h4><div style="max-height:300px;overflow:auto;font-family:monospace;font-size:12px;">';
            
            for (let i = 0; i < styles.length; i++) {
                const property = styles[i];
                const value = styles.getPropertyValue(property);
                html += `<div>${property}: ${value}</div>`;
            }
            
            html += '</div>';
            results.innerHTML = html;
        }
        
        function testCSSFiles() {
            const results = document.getElementById('test-results');
            results.innerHTML = '<p>Testing CSS file loading...</p>';
            
            const cssFiles = [
                'assets/css/consolidated.css',
                'assets/css/main.css',
                'assets/css/components/admin_user-dropdown.css'
            ];
            
            let testResults = [];
            let completed = 0;
            
            cssFiles.forEach((file, index) => {
                fetch(file + '?test=' + Date.now())
                    .then(response => {
                        if (response.ok) {
                            return response.text();
                        }
                        throw new Error(`HTTP ${response.status}`);
                    })
                    .then(content => {
                        testResults[index] = `<div style="color:green;">✅ ${file} loaded (${content.length} bytes)</div>`;
                        completed++;
                        if (completed === cssFiles.length) showCSSResults();
                    })
                    .catch(error => {
                        testResults[index] = `<div style="color:red;">❌ ${file} failed: ${error.message}</div>`;
                        completed++;
                        if (completed === cssFiles.length) showCSSResults();
                    });
            });
            
            function showCSSResults() {
                results.innerHTML = '<h4>CSS File Loading Results:</h4>' + testResults.join('');
            }
        }
    </script>
</body>
</html>
