/**
 * Admin Avatars CSS
 * 
 * This file contains styles for avatar components in the admin panel.
 */

/* Base Avatar */
.avatar {
  position: relative;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: var(--gray-200);
  color: var(--text-dark);
  font-weight: var(--font-weight-medium);
  overflow: hidden;
  flex-shrink: 0;
}

.avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* Avatar Sizes */
.avatar-xs {
  width: 24px;
  height: 24px;
  font-size: var(--font-size-xs);
}

.avatar-sm {
  width: 32px;
  height: 32px;
  font-size: var(--font-size-sm);
}

.avatar-md {
  width: 40px;
  height: 40px;
  font-size: var(--font-size-base);
}

.avatar-lg {
  width: 48px;
  height: 48px;
  font-size: var(--font-size-lg);
}

.avatar-xl {
  width: 64px;
  height: 64px;
  font-size: var(--font-size-xl);
}

.avatar-xxl {
  width: 96px;
  height: 96px;
  font-size: var(--font-size-2xl);
}

/* Avatar Shapes */
.avatar-square {
  border-radius: var(--radius-md);
}

.avatar-rounded {
  border-radius: var(--radius-lg);
}

/* Avatar with Border */
.avatar-border {
  border: 2px solid var(--white);
  box-shadow: var(--shadow-sm);
}

.avatar-border-primary {
  border-color: var(--primary-color);
}

.avatar-border-secondary {
  border-color: var(--secondary-color);
}

.avatar-border-success {
  border-color: var(--success-color);
}

.avatar-border-danger {
  border-color: var(--danger-color);
}

.avatar-border-warning {
  border-color: var(--warning-color);
}

.avatar-border-info {
  border-color: var(--info-color);
}

/* Avatar Colors */
.avatar-primary {
  background-color: var(--primary-very-light);
  color: var(--primary-color);
}

.avatar-secondary {
  background-color: rgba(44, 62, 80, 0.1);
  color: var(--secondary-color);
}

.avatar-success {
  background-color: rgba(16, 185, 129, 0.1);
  color: var(--success-color);
}

.avatar-danger {
  background-color: rgba(239, 68, 68, 0.1);
  color: var(--danger-color);
}

.avatar-warning {
  background-color: rgba(245, 158, 11, 0.1);
  color: var(--warning-color);
}

.avatar-info {
  background-color: rgba(59, 130, 246, 0.1);
  color: var(--info-color);
}

.avatar-light {
  background-color: var(--gray-100);
  color: var(--gray-600);
}

.avatar-dark {
  background-color: var(--gray-800);
  color: var(--white);
}

/* Avatar with Status */
.avatar-status {
  position: relative;
}

.avatar-status::after {
  content: "";
  position: absolute;
  bottom: 0;
  right: 0;
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background-color: var(--gray-400);
  border: 2px solid var(--white);
}

.avatar-status.online::after {
  background-color: var(--success-color);
}

.avatar-status.offline::after {
  background-color: var(--gray-400);
}

.avatar-status.busy::after {
  background-color: var(--danger-color);
}

.avatar-status.away::after {
  background-color: var(--warning-color);
}

/* Avatar with Badge */
.avatar-badge {
  position: relative;
}

.avatar-badge .badge {
  position: absolute;
  top: 0;
  right: 0;
  transform: translate(25%, -25%);
  min-width: 18px;
  height: 18px;
  padding: 0 var(--spacing-1);
  font-size: 10px;
  font-weight: var(--font-weight-bold);
  line-height: 1;
  border-radius: var(--radius-full);
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Avatar with Icon */
.avatar-icon {
  position: relative;
}

.avatar-icon-badge {
  position: absolute;
  bottom: 0;
  right: 0;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background-color: var(--white);
  border: 2px solid var(--white);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 8px;
}

.avatar-icon-badge i {
  color: var(--primary-color);
}

/* Avatar Group */
.avatar-group {
  display: flex;
  align-items: center;
}

.avatar-group .avatar {
  margin-right: -10px;
  transition: transform var(--transition-fast) ease;
}

.avatar-group .avatar:hover {
  transform: translateY(-5px);
  z-index: 1;
}

.avatar-group .avatar:last-child {
  margin-right: 0;
}

.avatar-group-count {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: var(--gray-100);
  color: var(--text-dark);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: var(--spacing-1);
}

/* Avatar with Name */
.avatar-with-name {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
}

.avatar-name {
  display: flex;
  flex-direction: column;
}

.avatar-name-title {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--text-dark);
  line-height: 1.2;
}

.avatar-name-subtitle {
  font-size: var(--font-size-xs);
  color: var(--text-light);
  line-height: 1.2;
}

/* Avatar with Tooltip */
.avatar-tooltip {
  position: relative;
}

.avatar-tooltip-text {
  visibility: hidden;
  width: auto;
  min-width: 120px;
  background-color: var(--gray-800);
  color: var(--white);
  text-align: center;
  border-radius: var(--radius-md);
  padding: var(--spacing-2) var(--spacing-3);
  position: absolute;
  z-index: var(--z-index-tooltip);
  bottom: 125%;
  left: 50%;
  transform: translateX(-50%);
  opacity: 0;
  transition: opacity var(--transition-fast) ease;
  font-size: var(--font-size-xs);
  white-space: nowrap;
}

.avatar-tooltip-text::after {
  content: "";
  position: absolute;
  top: 100%;
  left: 50%;
  margin-left: -5px;
  border-width: 5px;
  border-style: solid;
  border-color: var(--gray-800) transparent transparent transparent;
}

.avatar-tooltip:hover .avatar-tooltip-text {
  visibility: visible;
  opacity: 1;
}

/* Avatar with Dropdown */
.avatar-dropdown {
  cursor: pointer;
  position: relative;
}

.avatar-dropdown-menu {
  position: absolute;
  top: 100%;
  right: 0;
  z-index: var(--z-index-dropdown);
  display: none;
  min-width: 180px;
  padding: var(--spacing-1) 0;
  margin-top: var(--spacing-1);
  background-color: var(--white);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  box-shadow: var(--shadow-md);
}

.avatar-dropdown.show .avatar-dropdown-menu {
  display: block;
}

.avatar-dropdown-item {
  display: block;
  padding: var(--spacing-2) var(--spacing-4);
  font-size: var(--font-size-sm);
  color: var(--text-color);
  text-decoration: none;
  transition: all var(--transition-fast) ease;
  white-space: nowrap;
}

.avatar-dropdown-item:hover {
  background-color: var(--gray-50);
  color: var(--text-dark);
}

.avatar-dropdown-divider {
  height: 1px;
  margin: var(--spacing-1) 0;
  background-color: var(--border-color);
}

/* Avatar Placeholder */
.avatar-placeholder {
  background-color: var(--gray-200);
  color: var(--gray-500);
  font-weight: var(--font-weight-bold);
  text-transform: uppercase;
}

/* Avatar Initials */
.avatar-initials {
  text-transform: uppercase;
  font-weight: var(--font-weight-bold);
}

/* Avatar Upload */
.avatar-upload {
  position: relative;
  cursor: pointer;
}

.avatar-upload-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity var(--transition-fast) ease;
}

.avatar-upload:hover .avatar-upload-overlay {
  opacity: 1;
}

.avatar-upload-icon {
  color: var(--white);
  font-size: var(--font-size-lg);
}

.avatar-upload input[type="file"] {
  position: absolute;
  width: 100%;
  height: 100%;
  opacity: 0;
  cursor: pointer;
}

/* Responsive Styles */
@media (max-width: 576px) {
  .avatar-group .avatar {
    margin-right: -5px;
  }
}
