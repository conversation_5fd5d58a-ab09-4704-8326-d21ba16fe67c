!function(e){"object"==typeof exports&&"object"==typeof module?e(require("../../lib/codemirror"),require("../../mode/css/css")):"function"==typeof define&&define.amd?define(["../../lib/codemirror","../../mode/css/css"],e):e(CodeMirror)}(function(l){"use strict";var c={active:1,after:1,before:1,checked:1,default:1,disabled:1,empty:1,enabled:1,"first-child":1,"first-letter":1,"first-line":1,"first-of-type":1,focus:1,hover:1,"in-range":1,indeterminate:1,invalid:1,lang:1,"last-child":1,"last-of-type":1,link:1,not:1,"nth-child":1,"nth-last-child":1,"nth-last-of-type":1,"nth-of-type":1,"only-of-type":1,"only-child":1,optional:1,"out-of-range":1,placeholder:1,"read-only":1,"read-write":1,required:1,root:1,selection:1,target:1,valid:1,visited:1};l.registerHelper("hint","css",function(e){var t=e.getCursor(),r=e.getTokenAt(t),e=l.innerMode(e.getMode(),r.state);if("css"==e.mode.name){if("keyword"==r.type&&0=="!important".indexOf(r.string))return{list:["!important"],from:l.Pos(t.line,r.start),to:l.Pos(t.line,r.end)};var o=r.start,i=t.ch,s=r.string.slice(0,i-o),n=(/[^\w$_-]/.test(s)&&(s="",o=i=t.ch),l.resolveMode("text/css")),a=[],e=e.state.state;return"pseudo"==e||"variable-3"==r.type?d(c):"block"==e||"maybeprop"==e?d(n.propertyKeywords):"prop"==e||"parens"==e||"at"==e||"params"==e?(d(n.valueKeywords),d(n.colorKeywords)):"media"!=e&&"media_parens"!=e||(d(n.mediaTypes),d(n.mediaFeatures)),a.length?{list:a,from:l.Pos(t.line,o),to:l.Pos(t.line,i)}:void 0}function d(e){for(var t in e)s&&0!=t.lastIndexOf(s,0)||a.push(t)}})});