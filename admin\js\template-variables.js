/**
 * Template Variables Handler
 *
 * This script handles the insertion of template variables into the WYSIWYG editor
 * or plain textarea in the email template editor.
 */
document.addEventListener('DOMContentLoaded', function() {
    // Track editor state
    window.templateEditorState = {
        activeEditor: null,
        isHtmlMode: true, // Default to HTML mode
        lastFocusedElement: null,
        editorInitialized: false
    };

    // Initialize template variables when the template modal is opened
    document.addEventListener('modal:opened', function(e) {
        if (e.detail.modalId === 'template-modal') {
            initTemplateVariables();
            setupEditorTracking();
        }
    });

    // Track when the modal is closed to reset state
    document.addEventListener('modal:closed', function(e) {
        if (e.detail.modalId === 'template-modal') {
            window.templateEditorState = {
                activeEditor: null,
                isHtmlMode: true, // Default to HTML mode
                lastFocusedElement: null,
                editorInitialized: false
            };
        }
    });

    function initTemplateVariables() {
        // Preload editor components to reduce initialization delay
        preloadEditorComponents();

        // Get all template variable elements
        const templateVariables = document.querySelectorAll('.template-variable');

        // Add click event to each variable
        templateVariables.forEach(variable => {
            variable.addEventListener('click', function(e) {
                // Prevent default behavior
                e.preventDefault();
                e.stopPropagation();

                // Get the variable text
                const variableText = this.getAttribute('data-variable');

                // Format the variable with double curly braces: {{variable_name}}
                // First, remove any existing curly braces to avoid triple braces
                const cleanVariable = variableText.replace(/[{}]/g, '');
                const formattedVariable = '{{' + cleanVariable + '}}';

                // Insert the variable immediately
                insertVariableIntoEditor(formattedVariable);

                // Add visual feedback that the variable was clicked
                this.classList.add('clicked');
                setTimeout(() => {
                    this.classList.remove('clicked');
                }, 300);
            });
        });

        // Initialize search functionality
        initVariableSearch();

        // Add styles for clicked variables and enhanced visual feedback
        const style = document.createElement('style');
        style.textContent = `
            .template-variable.clicked {
                background-color: #f1ca2f;
                transform: translateY(-2px);
                box-shadow: 0 2px 5px rgba(0,0,0,0.2);
                transition: all 0.3s ease;
            }
            .template-variable {
                transition: all 0.2s ease;
            }
            .template-variable:hover {
                transform: translateY(-1px);
                box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            }
            .variable-inserted-notification {
                position: fixed;
                bottom: 20px;
                right: 20px;
                background-color: #4CAF50;
                color: white;
                padding: 10px 15px;
                border-radius: 4px;
                box-shadow: 0 2px 10px rgba(0,0,0,0.2);
                z-index: 9999;
                opacity: 0;
                transform: translateY(20px);
                transition: all 0.3s ease;
            }
            .variable-inserted-notification.show {
                opacity: 1;
                transform: translateY(0);
            }
            .variable-category-filter {
                position: relative;
                display: inline-block;
            }
            .active-filter-indicator {
                position: absolute;
                top: -8px;
                right: -8px;
                background-color: #f1ca2f;
                color: #333;
                border-radius: 50%;
                width: 18px;
                height: 18px;
                font-size: 10px;
                display: flex;
                align-items: center;
                justify-content: center;
                font-weight: bold;
                opacity: 0;
                transform: scale(0);
                transition: all 0.3s ease;
            }
            .active-filter-indicator.show {
                opacity: 1;
                transform: scale(1);
            }
        `;
        document.head.appendChild(style);

        // Create notification element for variable insertion
        const notification = document.createElement('div');
        notification.className = 'variable-inserted-notification';
        notification.style.display = 'none';
        document.body.appendChild(notification);

        // Handle variable category filter with enhanced UI
        const variableCategorySelect = document.getElementById('variable-category');
        if (variableCategorySelect) {
            // Create filter indicator
            const filterContainer = variableCategorySelect.parentElement;
            if (filterContainer) {
                filterContainer.classList.add('variable-category-filter');

                const indicator = document.createElement('span');
                indicator.className = 'active-filter-indicator';
                indicator.textContent = '0';
                filterContainer.appendChild(indicator);

                // Add event listener for filter changes
                variableCategorySelect.addEventListener('change', function() {
                    const category = this.value;
                    filterVariablesByCategory(category);

                    // Update indicator
                    if (category !== 'all') {
                        // Count visible variables
                        const visibleCount = document.querySelectorAll(`.template-variable[data-category="${category}"]:not([style*="display: none"])`).length;
                        indicator.textContent = visibleCount;
                        indicator.classList.add('show');
                    } else {
                        indicator.classList.remove('show');
                    }
                });

                // Initialize with current value
                if (variableCategorySelect.value !== 'all') {
                    const category = variableCategorySelect.value;
                    const visibleCount = document.querySelectorAll(`.template-variable[data-category="${category}"]:not([style*="display: none"])`).length;
                    indicator.textContent = visibleCount;
                    indicator.classList.add('show');
                }
            }
        }
    }

    /**
     * Insert a variable into the editor
     * Works with both WYSIWYG editor and plain textarea
     *
     * @param {string} variableText - The variable text to insert
     */
    function insertVariableIntoEditor(variableText) {
        // Use the tracked editor state to determine where to insert
        const editorState = window.templateEditorState || {
            activeEditor: null,
            isHtmlMode: false,
            lastFocusedElement: null
        };

        // Get all possible editor elements
        const editorContent = document.querySelector('.simple-editor-content');
        const htmlTextarea = document.querySelector('.simple-editor-html');
        const templateContent = document.getElementById('template_content');

        // Determine which editor to use based on state and visibility
        let targetEditor;
        let editorType;

        if (editorState.activeEditor) {
            // Use the tracked active editor if available
            switch (editorState.activeEditor) {
                case 'wysiwyg':
                    targetEditor = editorContent;
                    editorType = 'wysiwyg';
                    break;
                case 'html':
                    targetEditor = htmlTextarea;
                    editorType = 'html';
                    break;
                case 'plain':
                    targetEditor = templateContent;
                    editorType = 'plain';
                    break;
            }
        } else {
            // Fallback detection based on visibility and focus
            if (editorContent && editorContent.isContentEditable && editorContent.offsetParent !== null) {
                targetEditor = editorContent;
                editorType = 'wysiwyg';
            } else if (htmlTextarea && htmlTextarea.style.display !== 'none') {
                targetEditor = htmlTextarea;
                editorType = 'html';
            } else if (templateContent) {
                targetEditor = templateContent;
                editorType = 'plain';
            }
        }

        // If no editor is found, try to use the last focused element
        if (!targetEditor && editorState.lastFocusedElement) {
            targetEditor = editorState.lastFocusedElement;

            if (targetEditor.isContentEditable) {
                editorType = 'wysiwyg';
            } else if (targetEditor.tagName === 'TEXTAREA') {
                if (targetEditor.className.includes('simple-editor-html')) {
                    editorType = 'html';
                } else {
                    editorType = 'plain';
                }
            }
        }

        // If we still don't have a target, use the first available editor
        if (!targetEditor) {
            if (editorContent && editorContent.offsetParent !== null) {
                targetEditor = editorContent;
                editorType = 'wysiwyg';
            } else if (htmlTextarea) {
                targetEditor = htmlTextarea;
                editorType = 'html';
            } else if (templateContent) {
                targetEditor = templateContent;
                editorType = 'plain';
            } else {
                console.error('No editor found for variable insertion');
                return;
            }
        }

        // Make sure the target editor has focus
        targetEditor.focus();

        // Insert the variable based on editor type
        switch (editorType) {
            case 'wysiwyg':
                // Insert into WYSIWYG editor
                try {
                    // Use a small delay to ensure focus is established
                    setTimeout(() => {
                        insertAtCursorPosition(targetEditor, variableText);

                        // Update state
                        window.templateEditorState.activeEditor = 'wysiwyg';
                        window.templateEditorState.lastFocusedElement = targetEditor;
                        window.templateEditorState.isHtmlMode = false;

                        // Focus again to ensure it maintains focus
                        targetEditor.focus();
                    }, 10);
                } catch (error) {
                    console.error("Error inserting variable into WYSIWYG editor:", error);

                    // Fallback method - append to the end of content
                    const currentContent = targetEditor.innerHTML;
                    targetEditor.innerHTML = currentContent + variableText;

                    // Trigger input event
                    const inputEvent = new Event('input', {
                        bubbles: true,
                        cancelable: true
                    });
                    targetEditor.dispatchEvent(inputEvent);
                }
                break;

            case 'html':
            case 'plain':
                // Insert into textarea (HTML mode or plain textarea)
                try {
                    // Get cursor position
                    const startPos = targetEditor.selectionStart || 0;
                    const endPos = targetEditor.selectionEnd || 0;
                    const currentValue = targetEditor.value;

                    // Check if there's selected text
                    const selectedText = currentValue.substring(startPos, endPos);

                    // Get the text around the cursor (20 characters before and after)
                    const surroundingStart = Math.max(0, startPos - 20);
                    const surroundingEnd = Math.min(currentValue.length, endPos + 20);
                    const surroundingText = currentValue.substring(surroundingStart, surroundingEnd);

                    // Check if we're trying to insert the same variable that's already at the cursor
                    // This prevents duplicate variables like {{admin_name}}{{admin_name}}
                    const variableName = variableText.replace(/[{}]/g, '');
                    const variablePattern = new RegExp('\\{\\{' + variableName + '\\}\\}\\{\\{' + variableName + '\\}\\}');

                    if (surroundingText.match(variablePattern)) {
                        console.log('Prevented duplicate variable insertion in textarea');
                        return; // Don't insert the variable again
                    }

                    // Check if the cursor is right after a variable of the same name
                    const beforeCursor = currentValue.substring(Math.max(0, startPos - 20), startPos);
                    const afterVariablePattern = new RegExp('\\{\\{' + variableName + '\\}\\}$');

                    let newValue;
                    let newCursorPos;

                    // If the selected text is already a variable, replace it
                    if (selectedText.startsWith('{{') && selectedText.endsWith('}}')) {
                        // Already have a variable selected, replace it with the new one
                        newValue = currentValue.substring(0, startPos) +
                                   variableText +
                                   currentValue.substring(endPos);
                        newCursorPos = startPos + variableText.length;
                    } else if (beforeCursor.match(afterVariablePattern)) {
                        // If cursor is right after the same variable, add a space first
                        newValue = currentValue.substring(0, startPos) +
                                   ' ' + variableText +
                                   currentValue.substring(endPos);
                        newCursorPos = startPos + variableText.length + 1; // +1 for the space
                    } else {
                        // No variable selected, just insert at cursor position
                        newValue = currentValue.substring(0, startPos) +
                                   variableText +
                                   currentValue.substring(endPos);
                        newCursorPos = startPos + variableText.length;
                    }

                    // Update the textarea value
                    targetEditor.value = newValue;

                    // Set cursor position after the inserted variable
                    targetEditor.selectionStart = newCursorPos;
                    targetEditor.selectionEnd = newCursorPos;

                    // Update state
                    window.templateEditorState.activeEditor = editorType;
                    window.templateEditorState.lastFocusedElement = targetEditor;
                    window.templateEditorState.isHtmlMode = (editorType === 'html');

                    // Trigger change event
                    const changeEvent = new Event('input', {
                        bubbles: true,
                        cancelable: true
                    });
                    targetEditor.dispatchEvent(changeEvent);
                } catch (error) {
                    console.error(`Error inserting variable into ${editorType} textarea:`, error);

                    // Fallback - append to the end
                    targetEditor.value += variableText;

                    // Trigger change event
                    const changeEvent = new Event('input', {
                        bubbles: true,
                        cancelable: true
                    });
                    targetEditor.dispatchEvent(changeEvent);
                }
                break;
        }

        // Show insertion notification
        const notification = document.querySelector('.variable-inserted-notification');
        if (notification) {
            // Get variable name without braces
            const variableName = variableText.replace(/[{}]/g, '');

            // Set notification text
            notification.textContent = `Variable "${variableName}" inserted`;
            notification.style.display = 'block';

            // Show notification with animation
            setTimeout(() => {
                notification.classList.add('show');
            }, 10);

            // Hide notification after delay
            setTimeout(() => {
                notification.classList.remove('show');
                setTimeout(() => {
                    notification.style.display = 'none';
                }, 300);
            }, 2000);
        }

        // Update the preview if available
        if (typeof updatePreview === 'function') {
            setTimeout(updatePreview, 50);
        }

        // Ensure editor maintains focus
        setTimeout(() => {
            if (targetEditor) {
                targetEditor.focus();
            }
        }, 100);
    }

    /**
     * Filter variables by category
     *
     * @param {string} category - The category to filter by ('all', 'contact', 'user', 'system')
     */
    function filterVariablesByCategory(category) {
        const variables = document.querySelectorAll('.template-variable');
        const searchInput = document.getElementById('variable-search');
        const searchTerm = searchInput ? searchInput.value.toLowerCase() : '';

        variables.forEach(variable => {
            const matchesCategory = category === 'all' || variable.getAttribute('data-category') === category;
            const matchesSearch = !searchTerm || variable.textContent.toLowerCase().includes(searchTerm) ||
                                 variable.getAttribute('data-variable').toLowerCase().includes(searchTerm);

            if (matchesCategory && matchesSearch) {
                variable.style.display = '';
            } else {
                variable.style.display = 'none';
            }
        });
    }

    /**
     * Initialize variable search functionality
     */
    function initVariableSearch() {
        const searchInput = document.getElementById('variable-search');
        const clearButton = document.getElementById('clear-search');
        const categorySelect = document.getElementById('variable-category');

        if (searchInput) {
            // Add input event listener
            searchInput.addEventListener('input', function() {
                const category = categorySelect ? categorySelect.value : 'all';
                filterVariablesByCategory(category);

                // Show/hide clear button based on input value
                if (clearButton) {
                    clearButton.style.display = this.value ? 'flex' : 'none';
                }
            });

            // Initial state - hide clear button
            if (clearButton) {
                clearButton.style.display = 'none';

                // Add click event to clear button
                clearButton.addEventListener('click', function() {
                    searchInput.value = '';
                    searchInput.focus();
                    this.style.display = 'none';

                    // Reapply category filter
                    const category = categorySelect ? categorySelect.value : 'all';
                    filterVariablesByCategory(category);
                });
            }
        }
    }

    /**
     * Preload editor components to reduce initialization delay
     */
    function preloadEditorComponents() {
        // Create a hidden container for preloading
        const preloadContainer = document.createElement('div');
        preloadContainer.style.position = 'absolute';
        preloadContainer.style.width = '0';
        preloadContainer.style.height = '0';
        preloadContainer.style.overflow = 'hidden';
        preloadContainer.style.visibility = 'hidden';
        document.body.appendChild(preloadContainer);

        // Preload editor icons
        const editorIcons = [
            'bold', 'italic', 'underline', 'strikethrough',
            'heading1', 'heading2', 'paragraph',
            'alignLeft', 'alignCenter', 'alignRight',
            'orderedList', 'unorderedList',
            'link', 'image', 'table',
            'foreColor', 'backColor',
            'undo', 'redo', 'html'
        ];

        // Create preload elements for each icon
        editorIcons.forEach(icon => {
            const img = document.createElement('div');
            img.className = `simple-editor-button-${icon}`;
            preloadContainer.appendChild(img);
        });

        // Remove preload container after a delay
        setTimeout(() => {
            if (preloadContainer && preloadContainer.parentNode) {
                preloadContainer.parentNode.removeChild(preloadContainer);
            }
        }, 2000);
    }

    /**
     * Setup tracking for editor focus and mode changes
     * This helps maintain proper state for variable insertion
     */
    function setupEditorTracking() {
        // Wait for editor to be initialized
        const checkEditorInterval = setInterval(() => {
            const editorContent = document.querySelector('.simple-editor-content');
            const htmlTextarea = document.querySelector('.simple-editor-html');
            const plainTextarea = document.getElementById('template_content');

            if (editorContent) {
                clearInterval(checkEditorInterval);
                window.templateEditorState.editorInitialized = true;

                // Track focus on WYSIWYG editor
                editorContent.addEventListener('focus', () => {
                    window.templateEditorState.activeEditor = 'wysiwyg';
                    window.templateEditorState.lastFocusedElement = editorContent;
                    window.templateEditorState.isHtmlMode = false;
                });

                // Track focus on HTML textarea
                if (htmlTextarea) {
                    htmlTextarea.addEventListener('focus', () => {
                        window.templateEditorState.activeEditor = 'html';
                        window.templateEditorState.lastFocusedElement = htmlTextarea;
                        window.templateEditorState.isHtmlMode = true;
                    });
                }

                // Track focus on plain textarea
                if (plainTextarea) {
                    plainTextarea.addEventListener('focus', () => {
                        window.templateEditorState.activeEditor = 'plain';
                        window.templateEditorState.lastFocusedElement = plainTextarea;
                        window.templateEditorState.isHtmlMode = false;
                    });
                }

                // Track HTML mode toggle
                const htmlButton = document.querySelector('.simple-editor-button[title="HTML Mode"]');
                if (htmlButton) {
                    htmlButton.addEventListener('click', () => {
                        // Wait for mode change to complete
                        setTimeout(() => {
                            const htmlTextarea = document.querySelector('.simple-editor-html');
                            if (htmlTextarea && htmlTextarea.style.display !== 'none') {
                                window.templateEditorState.activeEditor = 'html';
                                window.templateEditorState.lastFocusedElement = htmlTextarea;
                                window.templateEditorState.isHtmlMode = true;
                            } else {
                                window.templateEditorState.activeEditor = 'wysiwyg';
                                window.templateEditorState.lastFocusedElement = editorContent;
                                window.templateEditorState.isHtmlMode = false;
                            }
                        }, 50);
                    });
                }

                // Track tab changes to maintain focus
                document.addEventListener('modal:tabChanged', function(e) {
                    if (e.detail.modalId === 'template-modal') {
                        // When returning to content tab, restore focus to the last active editor
                        if (e.detail.tabId === 'content' && window.templateEditorState.lastFocusedElement) {
                            setTimeout(() => {
                                window.templateEditorState.lastFocusedElement.focus();
                            }, 50);
                        }
                    }
                });
            }
        }, 100);

        // Set a timeout to avoid infinite checking
        setTimeout(() => {
            clearInterval(checkEditorInterval);
        }, 5000);
    }

    /**
     * Helper function to insert text at cursor position in the editor
     *
     * @param {HTMLElement} editorContent - The editor element
     * @param {string} text - The text to insert
     */
    function insertAtCursorPosition(editorContent, text) {
        try {
            // Save the current selection
            const selection = window.getSelection();

            // Make sure the editor has focus
            editorContent.focus();

            // Check if we have a valid selection within the editor
            let isSelectionInEditor = false;
            if (selection.rangeCount > 0) {
                const range = selection.getRangeAt(0);
                isSelectionInEditor = editorContent.contains(range.commonAncestorContainer);
            }

            // If no valid selection in editor, create one at the end
            if (!isSelectionInEditor) {
                // Create a new range at the end of the editor content
                const range = document.createRange();

                // If editor is empty, just select the editor itself
                if (editorContent.childNodes.length === 0) {
                    range.selectNode(editorContent);
                    range.collapse(true); // Collapse to start
                } else {
                    // Otherwise, select the last position
                    range.selectNodeContents(editorContent);
                    range.collapse(false); // Collapse to end
                }

                // Apply the new selection
                selection.removeAllRanges();
                selection.addRange(range);
            }

            // Now we should have a valid selection in the editor
            const range = selection.getRangeAt(0);

            // Check if we're at the end of a variable or between variables
            // This prevents inserting a variable right after another variable without space
            const currentPosition = range.startOffset;
            const containerNode = range.startContainer;

            // Get the text around the cursor (10 characters before and after)
            let surroundingText = '';
            if (containerNode.nodeType === Node.TEXT_NODE) {
                const start = Math.max(0, currentPosition - 20);
                const end = Math.min(containerNode.textContent.length, currentPosition + 20);
                surroundingText = containerNode.textContent.substring(start, end);
            } else {
                // If not in a text node, get the HTML around the cursor
                const tempRange = range.cloneRange();
                tempRange.setStart(range.startContainer, Math.max(0, range.startOffset - 20));
                tempRange.setEnd(range.startContainer, Math.min(range.startContainer.childNodes.length, range.startOffset + 20));
                surroundingText = tempRange.toString();
            }

            // Check if we're trying to insert the same variable that's already at the cursor
            // This prevents duplicate variables like {{admin_name}}{{admin_name}}
            const variableName = text.replace(/[{}]/g, '');
            const variablePattern = new RegExp('\\{\\{' + variableName + '\\}\\}\\{\\{' + variableName + '\\}\\}');

            if (surroundingText.match(variablePattern)) {
                console.log('Prevented duplicate variable insertion');
                return; // Don't insert the variable again
            }

            // Check if the cursor is right after a variable of the same name
            const afterVariablePattern = new RegExp('\\{\\{' + variableName + '\\}\\}$');
            if (surroundingText.substring(0, currentPosition).match(afterVariablePattern)) {
                // Insert a space before adding another variable
                document.execCommand('insertText', false, ' ');
            }

            // Use execCommand to insert the text at the current cursor position
            document.execCommand('insertText', false, text);

            // If execCommand failed (which can happen in some browsers), use the fallback method
            if (!editorContent.innerHTML.includes(text)) {
                // Get the current range
                const range = selection.getRangeAt(0);

                // Create a text node with the variable
                const textNode = document.createTextNode(text);

                // Insert the text node at the cursor position
                range.insertNode(textNode);

                // Move the cursor after the inserted variable
                range.setStartAfter(textNode);
                range.setEndAfter(textNode);
                selection.removeAllRanges();
                selection.addRange(range);
            }

            // Trigger input event to update the underlying textarea
            const inputEvent = new Event('input', {
                bubbles: true,
                cancelable: true
            });
            editorContent.dispatchEvent(inputEvent);
        } catch (error) {
            console.error("Error inserting at cursor position:", error);

            // Fallback method - append to the end of content
            const currentContent = editorContent.innerHTML;
            editorContent.innerHTML = currentContent + text;

            // Trigger input event
            const inputEvent = new Event('input', {
                bubbles: true,
                cancelable: true
            });
            editorContent.dispatchEvent(inputEvent);
        }
    }
});
