<?php
/**
 * Authentication Class
 *
 * Handles user authentication and permissions
 */
class Auth {
    private $conn;
    private $user_id = null;
    private $user_data = null;
    private $permissions = [];

    /**
     * Constructor
     *
     * @param object $conn Database connection
     */
    public function __construct($conn) {
        $this->conn = $conn;
        $this->initSession();
    }

    /**
     * Initialize session and load user data
     */
    private function initSession() {
        if (session_status() === PHP_SESSION_NONE) {
            session_start();
        }

        if (isset($_SESSION['user_id'])) {
            $this->user_id = $_SESSION['user_id'];
            $this->loadUserData();
            $this->loadPermissions();
        }
    }

    /**
     * Load user data from database
     */
    private function loadUserData() {
        if (!$this->user_id) return;

        $sql = "SELECT * FROM users WHERE id = ?";
        $stmt = $this->conn->prepare($sql);
        $stmt->bind_param("i", $this->user_id);
        $stmt->execute();
        $result = $stmt->get_result();

        if ($result && $result->num_rows > 0) {
            $this->user_data = $result->fetch_assoc();
        }
    }

    /**
     * Load user permissions
     */
    private function loadPermissions() {
        if (!$this->user_id || !$this->user_data) return;

        // Default permissions based on role
        $role = $this->user_data['role'] ?? 'viewer';

        // Define role-based permissions
        $role_permissions = [
            'admin' => [
                'manage_users',
                'manage_settings',
                'manage_email_templates',
                'manage_content',
                'view_dashboard',
                'manage_news'
            ],
            'editor' => [
                'manage_content',
                'view_dashboard',
                'manage_news',
                'manage_email_templates'
            ],
            'viewer' => [
                'view_dashboard'
            ]
        ];

        // Assign permissions based on role
        if (isset($role_permissions[$role])) {
            $this->permissions = $role_permissions[$role];
        }

        // Super admin has all permissions
        if ($role === 'admin' && isset($this->user_data['is_superadmin']) && $this->user_data['is_superadmin'] == 1) {
            $this->permissions = array_merge($this->permissions, [
                'manage_roles',
                'system_settings',
                'view_logs'
            ]);
        }
    }

    /**
     * Check if user is logged in
     *
     * @return bool True if logged in, false otherwise
     */
    public function isLoggedIn() {
        return $this->user_id !== null && $this->user_data !== null;
    }

    /**
     * Check if user has a specific permission
     *
     * @param string $permission Permission to check
     * @return bool True if user has permission, false otherwise
     */
    public function hasPermission($permission) {
        // Check if user is admin (temporary solution until proper role-based permissions are implemented)
        if (isset($this->user_data['is_admin']) && $this->user_data['is_admin'] == 1) {
            return true;
        }

        // Check specific permission
        return in_array($permission, $this->permissions);
    }

    /**
     * Get user data
     *
     * @return array|null User data or null if not logged in
     */
    public function getUserData() {
        return $this->user_data;
    }

    /**
     * Get user ID
     *
     * @return int|null User ID or null if not logged in
     */
    public function getUserId() {
        return $this->user_id;
    }

    /**
     * Get user permissions
     *
     * @return array User permissions
     */
    public function getPermissions() {
        return $this->permissions;
    }
}
?>
