<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CSS Conflict Detector</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; }
        .test-btn { background: #007cba; color: white; padding: 10px 15px; border: none; border-radius: 4px; cursor: pointer; margin: 5px; }
        .result { margin: 15px 0; padding: 15px; border-radius: 4px; font-family: monospace; font-size: 14px; }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .warning { background: #fff3cd; color: #856404; }
        .info { background: #d1ecf1; color: #0c5460; }
        .css-rule { margin: 10px 0; padding: 10px; background: #f8f9fa; border-left: 4px solid #007cba; }
        .override { border-left-color: #dc3545; background: #fff5f5; }
        table { width: 100%; border-collapse: collapse; margin: 15px 0; }
        th, td { padding: 8px; border: 1px solid #ddd; text-align: left; }
        th { background: #f8f9fa; }
        .conflict { background: #fff5f5; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 CSS Conflict Detector</h1>
        <p>This tool analyzes CSS loading order and detects conflicts that might be affecting the dropdown.</p>
        
        <button class="test-btn" onclick="analyzeCSS()">Analyze CSS Loading</button>
        <button class="test-btn" onclick="detectConflicts()">Detect Conflicts</button>
        <button class="test-btn" onclick="testDropdownStyles()">Test Dropdown Styles</button>
        <button class="test-btn" onclick="showLoadOrder()">Show Load Order</button>
        
        <div id="results"></div>
        
        <!-- Test dropdown for analysis -->
        <div style="position: relative; margin: 20px 0;">
            <div class="topbar-user-menu">
                <button class="user-menu-toggle" id="testToggle">
                    <span class="user-name">Test User</span>
                    <div class="user-avatar">
                        <div class="avatar-placeholder">T</div>
                    </div>
                    <span class="dropdown-icon">▼</span>
                </button>
                
                <div class="user-dropdown" id="testDropdown">
                    <div class="user-dropdown-header">
                        <div class="user-dropdown-avatar">
                            <div class="avatar-placeholder-large">T</div>
                        </div>
                        <div class="user-dropdown-info">
                            <div class="user-dropdown-name">Test User</div>
                            <div class="user-dropdown-email"><EMAIL></div>
                            <div class="user-dropdown-badge">Admin</div>
                        </div>
                    </div>
                    <div class="dropdown-menu">
                        <div class="dropdown-section">
                            <div class="dropdown-item">
                                <a href="#" class="dropdown-link">
                                    <div class="dropdown-icon">👤</div>
                                    <div class="dropdown-link-content">
                                        <span class="dropdown-link-title">Profile</span>
                                        <span class="dropdown-link-description">Manage account</span>
                                    </div>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        function analyzeCSS() {
            const results = document.getElementById('results');
            let html = '<div class="result info"><h3>📊 CSS Analysis Results</h3>';
            
            // Get all stylesheets
            const stylesheets = Array.from(document.styleSheets);
            html += `<p><strong>Total Stylesheets:</strong> ${stylesheets.length}</p>`;
            
            html += '<table><tr><th>#</th><th>Source</th><th>Rules</th><th>Status</th></tr>';
            
            stylesheets.forEach((sheet, index) => {
                let source = 'Inline';
                let ruleCount = 0;
                let status = 'Unknown';
                
                try {
                    if (sheet.href) {
                        source = sheet.href.split('/').pop();
                    }
                    ruleCount = sheet.cssRules ? sheet.cssRules.length : 0;
                    status = sheet.disabled ? 'Disabled' : 'Active';
                } catch (e) {
                    status = 'CORS Error';
                }
                
                const isConflict = source.includes('dropdown') || source.includes('emergency') || source.includes('override');
                const rowClass = isConflict ? 'conflict' : '';
                
                html += `<tr class="${rowClass}">
                    <td>${index + 1}</td>
                    <td>${source}</td>
                    <td>${ruleCount}</td>
                    <td>${status}</td>
                </tr>`;
            });
            
            html += '</table></div>';
            results.innerHTML = html;
        }
        
        function detectConflicts() {
            const results = document.getElementById('results');
            const dropdown = document.getElementById('testDropdown');
            
            if (!dropdown) {
                results.innerHTML = '<div class="result error">❌ Test dropdown not found</div>';
                return;
            }
            
            let html = '<div class="result warning"><h3>⚠️ CSS Conflict Detection</h3>';
            
            // Check computed styles
            const computedStyles = window.getComputedStyle(dropdown);
            
            // Critical properties to check
            const criticalProps = {
                'position': 'absolute',
                'z-index': '9999',
                'display': 'none',
                'background-color': 'rgb(255, 255, 255)',
                'border-radius': '0.5rem'
            };
            
            html += '<h4>Critical Property Check:</h4>';
            for (const [prop, expected] of Object.entries(criticalProps)) {
                const actual = computedStyles.getPropertyValue(prop);
                const isCorrect = actual.includes(expected) || actual === expected;
                
                html += `<div class="css-rule ${isCorrect ? '' : 'override'}">
                    <strong>${prop}:</strong> Expected "${expected}", Got "${actual}" 
                    ${isCorrect ? '✅' : '❌'}
                </div>`;
            }
            
            // Check for overriding stylesheets
            html += '<h4>Potential Conflicts:</h4>';
            const stylesheets = Array.from(document.styleSheets);
            
            stylesheets.forEach((sheet, index) => {
                try {
                    if (sheet.cssRules) {
                        for (let rule of sheet.cssRules) {
                            if (rule.selectorText && (
                                rule.selectorText.includes('.user-dropdown') ||
                                rule.selectorText.includes('.dropdown-menu') ||
                                rule.selectorText.includes('.topbar-user-menu')
                            )) {
                                const source = sheet.href ? sheet.href.split('/').pop() : 'Inline';
                                html += `<div class="css-rule">
                                    <strong>Source:</strong> ${source}<br>
                                    <strong>Selector:</strong> ${rule.selectorText}<br>
                                    <strong>Properties:</strong> ${rule.style.cssText || 'Multiple'}
                                </div>`;
                            }
                        }
                    }
                } catch (e) {
                    // CORS or other error
                }
            });
            
            html += '</div>';
            results.innerHTML = html;
        }
        
        function testDropdownStyles() {
            const results = document.getElementById('results');
            const dropdown = document.getElementById('testDropdown');
            const toggle = document.getElementById('testToggle');
            
            let html = '<div class="result info"><h3>🧪 Dropdown Style Test</h3>';
            
            if (!dropdown || !toggle) {
                html += '<p class="error">❌ Test elements not found</p></div>';
                results.innerHTML = html;
                return;
            }
            
            // Test visibility toggle
            html += '<h4>Visibility Test:</h4>';
            
            // Show dropdown
            dropdown.classList.add('show');
            const showStyles = window.getComputedStyle(dropdown);
            html += `<p><strong>With .show class:</strong></p>`;
            html += `<p>Display: ${showStyles.display}</p>`;
            html += `<p>Opacity: ${showStyles.opacity}</p>`;
            html += `<p>Transform: ${showStyles.transform}</p>`;
            
            // Hide dropdown
            dropdown.classList.remove('show');
            const hideStyles = window.getComputedStyle(dropdown);
            html += `<p><strong>Without .show class:</strong></p>`;
            html += `<p>Display: ${hideStyles.display}</p>`;
            html += `<p>Opacity: ${hideStyles.opacity}</p>`;
            html += `<p>Transform: ${hideStyles.transform}</p>`;
            
            // Test positioning
            html += '<h4>Positioning Test:</h4>';
            const rect = dropdown.getBoundingClientRect();
            html += `<p>Position: ${showStyles.position}</p>`;
            html += `<p>Z-index: ${showStyles.zIndex}</p>`;
            html += `<p>Top: ${showStyles.top}</p>`;
            html += `<p>Right: ${showStyles.right}</p>`;
            html += `<p>Bounding Rect: ${rect.width}x${rect.height} at (${rect.left}, ${rect.top})</p>`;
            
            html += '</div>';
            results.innerHTML = html;
        }
        
        function showLoadOrder() {
            const results = document.getElementById('results');
            let html = '<div class="result info"><h3>📋 CSS Load Order Analysis</h3>';
            
            // Get all link elements
            const links = Array.from(document.querySelectorAll('link[rel="stylesheet"]'));
            
            html += '<table><tr><th>Order</th><th>File</th><th>Version</th><th>Status</th></tr>';
            
            links.forEach((link, index) => {
                const href = link.href;
                const filename = href.split('/').pop().split('?')[0];
                const version = href.includes('?v=') ? href.split('?v=')[1] : 'None';
                
                // Check if loaded
                let status = 'Loading...';
                if (link.sheet) {
                    try {
                        const ruleCount = link.sheet.cssRules ? link.sheet.cssRules.length : 0;
                        status = `Loaded (${ruleCount} rules)`;
                    } catch (e) {
                        status = 'CORS/Error';
                    }
                }
                
                const isDropdownRelated = filename.includes('dropdown') || filename.includes('emergency') || filename.includes('override');
                const rowClass = isDropdownRelated ? 'conflict' : '';
                
                html += `<tr class="${rowClass}">
                    <td>${index + 1}</td>
                    <td>${filename}</td>
                    <td>${version}</td>
                    <td>${status}</td>
                </tr>`;
            });
            
            html += '</table>';
            
            // Show recommendations
            html += '<h4>Recommendations:</h4>';
            html += '<ul>';
            html += '<li>Dropdown-related CSS should load LAST to avoid conflicts</li>';
            html += '<li>Reset CSS should load FIRST</li>';
            html += '<li>Override CSS should be the final stylesheet</li>';
            html += '<li>External CSS (Font Awesome) can override local styles</li>';
            html += '</ul>';
            
            html += '</div>';
            results.innerHTML = html;
        }
        
        // Auto-run analysis on load
        window.addEventListener('load', function() {
            setTimeout(analyzeCSS, 1000);
        });
    </script>
</body>
</html>
