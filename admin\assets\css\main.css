/**
 * Main Admin CSS
 *
 * This file imports all CSS partials in a logical order.
 * The admin panel design follows a black and yellow color scheme.
 */

/* Base Styles */
@import url('base/admin_reset.css');
@import url('base/admin_variables.css');
@import url('base/admin_typography.css');
@import url('base/admin_helpers.css');

/* Layout */
@import url('layout/admin_grid.css');
@import url('layout/admin_header.css');
@import url('layout/admin_navbar.css');
@import url('layout/admin_sidebar.css');
@import url('layout/admin_footer.css');
@import url('layout/admin_main-content.css');
@import url('layout/admin_breadcrumb.css');
@import url('layout/admin_top-actions.css');
@import url('layout/admin_layout-auth.css');
@import url('layout/admin_layout-dashboard.css');
/* Removed admin_layout-responsive.css to avoid conflicts */

/* Components */
@import url('components/admin_buttons.css');
@import url('components/admin_cards.css');
@import url('components/admin_forms.css');
@import url('components/admin_tables.css');
@import url('components/admin_modals.css');
@import url('components/admin_alerts.css');
@import url('components/admin_badges.css');
@import url('components/admin_widgets.css');
@import url('components/admin_tabs.css');
@import url('components/admin_pagination.css');
@import url('components/admin_dropdowns.css');
@import url('components/admin_tooltips.css');
@import url('components/admin_accordions.css');
@import url('components/admin_avatars.css');
@import url('components/admin_progress-bars.css');
@import url('components/admin_notifications.css');
@import url('components/admin_editor.css');
@import url('components/enhanced-editor.css');
/* Removed non-existent CSS files to avoid conflicts */
@import url('components/admin_content-header.css');
@import url('components/admin_user-dropdown.css');

/* Pages */
@import url('pages/dashboard.css');
@import url('pages/all_news.css');
@import url('pages/inbox.css');
@import url('pages/login.css');
@import url('pages/profile.css');
@import url('pages/settings.css');
@import url('pages/admin_settings_enhanced.css');
/* Removed non-existent page CSS files to avoid conflicts */

/**
 * Utilities
 * These are utility classes that can be used to quickly style elements.
 */

/* Display */
.d-none { display: none !important; }
.d-inline { display: inline !important; }
.d-inline-block { display: inline-block !important; }
.d-block { display: block !important; }
.d-flex { display: flex !important; }
.d-inline-flex { display: inline-flex !important; }
.d-grid { display: grid !important; }

/* Flex */
.flex-row { flex-direction: row !important; }
.flex-column { flex-direction: column !important; }
.flex-wrap { flex-wrap: wrap !important; }
.flex-nowrap { flex-wrap: nowrap !important; }
.justify-content-start { justify-content: flex-start !important; }
.justify-content-end { justify-content: flex-end !important; }
.justify-content-center { justify-content: center !important; }
.justify-content-between { justify-content: space-between !important; }
.justify-content-around { justify-content: space-around !important; }
.align-items-start { align-items: flex-start !important; }
.align-items-end { align-items: flex-end !important; }
.align-items-center { align-items: center !important; }
.align-items-baseline { align-items: baseline !important; }
.align-items-stretch { align-items: stretch !important; }
.flex-grow-0 { flex-grow: 0 !important; }
.flex-grow-1 { flex-grow: 1 !important; }
.flex-shrink-0 { flex-shrink: 0 !important; }
.flex-shrink-1 { flex-shrink: 1 !important; }

/* Spacing */
.m-0 { margin: 0 !important; }
.mt-0 { margin-top: 0 !important; }
.mr-0 { margin-right: 0 !important; }
.mb-0 { margin-bottom: 0 !important; }
.ml-0 { margin-left: 0 !important; }
.mx-0 { margin-left: 0 !important; margin-right: 0 !important; }
.my-0 { margin-top: 0 !important; margin-bottom: 0 !important; }

.m-1 { margin: var(--spacing-1) !important; }
.mt-1 { margin-top: var(--spacing-1) !important; }
.mr-1 { margin-right: var(--spacing-1) !important; }
.mb-1 { margin-bottom: var(--spacing-1) !important; }
.ml-1 { margin-left: var(--spacing-1) !important; }
.mx-1 { margin-left: var(--spacing-1) !important; margin-right: var(--spacing-1) !important; }
.my-1 { margin-top: var(--spacing-1) !important; margin-bottom: var(--spacing-1) !important; }

.m-2 { margin: var(--spacing-2) !important; }
.mt-2 { margin-top: var(--spacing-2) !important; }
.mr-2 { margin-right: var(--spacing-2) !important; }
.mb-2 { margin-bottom: var(--spacing-2) !important; }
.ml-2 { margin-left: var(--spacing-2) !important; }
.mx-2 { margin-left: var(--spacing-2) !important; margin-right: var(--spacing-2) !important; }
.my-2 { margin-top: var(--spacing-2) !important; margin-bottom: var(--spacing-2) !important; }

.m-3 { margin: var(--spacing-3) !important; }
.mt-3 { margin-top: var(--spacing-3) !important; }
.mr-3 { margin-right: var(--spacing-3) !important; }
.mb-3 { margin-bottom: var(--spacing-3) !important; }
.ml-3 { margin-left: var(--spacing-3) !important; }
.mx-3 { margin-left: var(--spacing-3) !important; margin-right: var(--spacing-3) !important; }
.my-3 { margin-top: var(--spacing-3) !important; margin-bottom: var(--spacing-3) !important; }

.m-4 { margin: var(--spacing-4) !important; }
.mt-4 { margin-top: var(--spacing-4) !important; }
.mr-4 { margin-right: var(--spacing-4) !important; }
.mb-4 { margin-bottom: var(--spacing-4) !important; }
.ml-4 { margin-left: var(--spacing-4) !important; }
.mx-4 { margin-left: var(--spacing-4) !important; margin-right: var(--spacing-4) !important; }
.my-4 { margin-top: var(--spacing-4) !important; margin-bottom: var(--spacing-4) !important; }

.m-5 { margin: var(--spacing-5) !important; }
.mt-5 { margin-top: var(--spacing-5) !important; }
.mr-5 { margin-right: var(--spacing-5) !important; }
.mb-5 { margin-bottom: var(--spacing-5) !important; }
.ml-5 { margin-left: var(--spacing-5) !important; }
.mx-5 { margin-left: var(--spacing-5) !important; margin-right: var(--spacing-5) !important; }
.my-5 { margin-top: var(--spacing-5) !important; margin-bottom: var(--spacing-5) !important; }

.p-0 { padding: 0 !important; }
.pt-0 { padding-top: 0 !important; }
.pr-0 { padding-right: 0 !important; }
.pb-0 { padding-bottom: 0 !important; }
.pl-0 { padding-left: 0 !important; }
.px-0 { padding-left: 0 !important; padding-right: 0 !important; }
.py-0 { padding-top: 0 !important; padding-bottom: 0 !important; }

.p-1 { padding: var(--spacing-1) !important; }
.pt-1 { padding-top: var(--spacing-1) !important; }
.pr-1 { padding-right: var(--spacing-1) !important; }
.pb-1 { padding-bottom: var(--spacing-1) !important; }
.pl-1 { padding-left: var(--spacing-1) !important; }
.px-1 { padding-left: var(--spacing-1) !important; padding-right: var(--spacing-1) !important; }
.py-1 { padding-top: var(--spacing-1) !important; padding-bottom: var(--spacing-1) !important; }

.p-2 { padding: var(--spacing-2) !important; }
.pt-2 { padding-top: var(--spacing-2) !important; }
.pr-2 { padding-right: var(--spacing-2) !important; }
.pb-2 { padding-bottom: var(--spacing-2) !important; }
.pl-2 { padding-left: var(--spacing-2) !important; }
.px-2 { padding-left: var(--spacing-2) !important; padding-right: var(--spacing-2) !important; }
.py-2 { padding-top: var(--spacing-2) !important; padding-bottom: var(--spacing-2) !important; }

.p-3 { padding: var(--spacing-3) !important; }
.pt-3 { padding-top: var(--spacing-3) !important; }
.pr-3 { padding-right: var(--spacing-3) !important; }
.pb-3 { padding-bottom: var(--spacing-3) !important; }
.pl-3 { padding-left: var(--spacing-3) !important; }
.px-3 { padding-left: var(--spacing-3) !important; padding-right: var(--spacing-3) !important; }
.py-3 { padding-top: var(--spacing-3) !important; padding-bottom: var(--spacing-3) !important; }

.p-4 { padding: var(--spacing-4) !important; }
.pt-4 { padding-top: var(--spacing-4) !important; }
.pr-4 { padding-right: var(--spacing-4) !important; }
.pb-4 { padding-bottom: var(--spacing-4) !important; }
.pl-4 { padding-left: var(--spacing-4) !important; }
.px-4 { padding-left: var(--spacing-4) !important; padding-right: var(--spacing-4) !important; }
.py-4 { padding-top: var(--spacing-4) !important; padding-bottom: var(--spacing-4) !important; }

.p-5 { padding: var(--spacing-5) !important; }
.pt-5 { padding-top: var(--spacing-5) !important; }
.pr-5 { padding-right: var(--spacing-5) !important; }
.pb-5 { padding-bottom: var(--spacing-5) !important; }
.pl-5 { padding-left: var(--spacing-5) !important; }
.px-5 { padding-left: var(--spacing-5) !important; padding-right: var(--spacing-5) !important; }
.py-5 { padding-top: var(--spacing-5) !important; padding-bottom: var(--spacing-5) !important; }

/* Text */
.text-left { text-align: left !important; }
.text-center { text-align: center !important; }
.text-right { text-align: right !important; }
.text-justify { text-align: justify !important; }

.text-primary { color: var(--primary-color) !important; }
.text-secondary { color: var(--secondary-color) !important; }
.text-success { color: var(--success-color) !important; }
.text-danger { color: var(--danger-color) !important; }
.text-warning { color: var(--warning-color) !important; }
.text-info { color: var(--info-color) !important; }
.text-light { color: var(--text-light) !important; }
.text-dark { color: var(--text-dark) !important; }
.text-muted { color: var(--text-muted) !important; }
.text-white { color: var(--white) !important; }

.font-weight-light { font-weight: var(--font-weight-light) !important; }
.font-weight-normal { font-weight: var(--font-weight-normal) !important; }
.font-weight-medium { font-weight: var(--font-weight-medium) !important; }
.font-weight-semibold { font-weight: var(--font-weight-semibold) !important; }
.font-weight-bold { font-weight: var(--font-weight-bold) !important; }

.font-size-xs { font-size: var(--font-size-xs) !important; }
.font-size-sm { font-size: var(--font-size-sm) !important; }
.font-size-base { font-size: var(--font-size-base) !important; }
.font-size-lg { font-size: var(--font-size-lg) !important; }
.font-size-xl { font-size: var(--font-size-xl) !important; }
.font-size-2xl { font-size: var(--font-size-2xl) !important; }
.font-size-3xl { font-size: var(--font-size-3xl) !important; }

/* Background */
.bg-primary { background-color: var(--primary-color) !important; }
.bg-secondary { background-color: var(--secondary-color) !important; }
.bg-success { background-color: var(--success-color) !important; }
.bg-danger { background-color: var(--danger-color) !important; }
.bg-warning { background-color: var(--warning-color) !important; }
.bg-info { background-color: var(--info-color) !important; }
.bg-light { background-color: var(--gray-100) !important; }
.bg-dark { background-color: var(--gray-800) !important; }
.bg-white { background-color: var(--white) !important; }
.bg-transparent { background-color: transparent !important; }

/* Border */
.border { border: 1px solid var(--border-color) !important; }
.border-top { border-top: 1px solid var(--border-color) !important; }
.border-right { border-right: 1px solid var(--border-color) !important; }
.border-bottom { border-bottom: 1px solid var(--border-color) !important; }
.border-left { border-left: 1px solid var(--border-color) !important; }
.border-0 { border: 0 !important; }
.border-top-0 { border-top: 0 !important; }
.border-right-0 { border-right: 0 !important; }
.border-bottom-0 { border-bottom: 0 !important; }
.border-left-0 { border-left: 0 !important; }

.border-primary { border-color: var(--primary-color) !important; }
.border-secondary { border-color: var(--secondary-color) !important; }
.border-success { border-color: var(--success-color) !important; }
.border-danger { border-color: var(--danger-color) !important; }
.border-warning { border-color: var(--warning-color) !important; }
.border-info { border-color: var(--info-color) !important; }
.border-light { border-color: var(--gray-100) !important; }
.border-dark { border-color: var(--gray-800) !important; }
.border-white { border-color: var(--white) !important; }

/* Rounded */
.rounded { border-radius: var(--radius-md) !important; }
.rounded-sm { border-radius: var(--radius-sm) !important; }
.rounded-lg { border-radius: var(--radius-lg) !important; }
.rounded-circle { border-radius: 50% !important; }
.rounded-pill { border-radius: var(--radius-full) !important; }
.rounded-0 { border-radius: 0 !important; }

/* Visibility */
.visible { visibility: visible !important; }
.invisible { visibility: hidden !important; }

/* Position */
.position-static { position: static !important; }
.position-relative { position: relative !important; }
.position-absolute { position: absolute !important; }
.position-fixed { position: fixed !important; }
.position-sticky { position: sticky !important; }

/* Width & Height */
.w-25 { width: 25% !important; }
.w-50 { width: 50% !important; }
.w-75 { width: 75% !important; }
.w-100 { width: 100% !important; }
.w-auto { width: auto !important; }

.h-25 { height: 25% !important; }
.h-50 { height: 50% !important; }
.h-75 { height: 75% !important; }
.h-100 { height: 100% !important; }
.h-auto { height: auto !important; }

/* Overflow */
.overflow-auto { overflow: auto !important; }
.overflow-hidden { overflow: hidden !important; }
.overflow-visible { overflow: visible !important; }
.overflow-scroll { overflow: scroll !important; }

/* Shadow */
.shadow-none { box-shadow: none !important; }
.shadow-sm { box-shadow: var(--shadow-sm) !important; }
.shadow { box-shadow: var(--shadow-md) !important; }
.shadow-lg { box-shadow: var(--shadow-lg) !important; }
.shadow-xl { box-shadow: var(--shadow-xl) !important; }
