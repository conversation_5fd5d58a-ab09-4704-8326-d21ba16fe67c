<?php
/**
 * FileVersions Class
 * 
 * Manages file versioning for the frontend editor
 */
class FileVersions {
    private $conn;
    private $user_id;
    
    /**
     * Constructor
     * 
     * @param mysqli $conn Database connection
     * @param int $user_id User ID
     */
    public function __construct($conn, $user_id) {
        $this->conn = $conn;
        $this->user_id = $user_id;
    }
    
    /**
     * Save a new version of a file
     * 
     * @param string $file_path File path
     * @param string $content File content
     * @param string $comment Optional comment about the changes
     * @return bool True if successful, false otherwise
     */
    public function saveVersion($file_path, $content, $comment = '') {
        // Get the latest version number
        $version = $this->getLatestVersionNumber($file_path) + 1;
        
        // Insert new version
        $sql = "INSERT INTO file_versions (file_path, content, version, created_by, comment) 
                VALUES (?, ?, ?, ?, ?)";
        
        $stmt = $this->conn->prepare($sql);
        $stmt->bind_param("ssiss", $file_path, $content, $version, $this->user_id, $comment);
        
        return $stmt->execute();
    }
    
    /**
     * Get the latest version number for a file
     * 
     * @param string $file_path File path
     * @return int Latest version number (0 if no versions exist)
     */
    public function getLatestVersionNumber($file_path) {
        $sql = "SELECT MAX(version) as max_version FROM file_versions WHERE file_path = ?";
        
        $stmt = $this->conn->prepare($sql);
        $stmt->bind_param("s", $file_path);
        $stmt->execute();
        $result = $stmt->get_result();
        
        if ($result->num_rows > 0) {
            $row = $result->fetch_assoc();
            return (int)$row['max_version'];
        }
        
        return 0;
    }
    
    /**
     * Get a specific version of a file
     * 
     * @param string $file_path File path
     * @param int $version Version number (0 for latest)
     * @return array|null Version data or null if not found
     */
    public function getVersion($file_path, $version = 0) {
        if ($version === 0) {
            $version = $this->getLatestVersionNumber($file_path);
        }
        
        $sql = "SELECT fv.*, u.username as username
                FROM file_versions fv
                JOIN users u ON fv.created_by = u.id
                WHERE fv.file_path = ? AND fv.version = ?";
        
        $stmt = $this->conn->prepare($sql);
        $stmt->bind_param("si", $file_path, $version);
        $stmt->execute();
        $result = $stmt->get_result();
        
        if ($result->num_rows > 0) {
            return $result->fetch_assoc();
        }
        
        return null;
    }
    
    /**
     * Get all versions of a file
     * 
     * @param string $file_path File path
     * @return array Array of version data
     */
    public function getAllVersions($file_path) {
        $sql = "SELECT fv.*, u.username as username
                FROM file_versions fv
                JOIN users u ON fv.created_by = u.id
                WHERE fv.file_path = ?
                ORDER BY fv.version DESC";
        
        $stmt = $this->conn->prepare($sql);
        $stmt->bind_param("s", $file_path);
        $stmt->execute();
        $result = $stmt->get_result();
        
        $versions = [];
        while ($row = $result->fetch_assoc()) {
            $versions[] = $row;
        }
        
        return $versions;
    }
    
    /**
     * Restore a specific version of a file
     * 
     * @param string $file_path File path
     * @param int $version Version number
     * @return bool True if successful, false otherwise
     */
    public function restoreVersion($file_path, $version) {
        // Get the version data
        $version_data = $this->getVersion($file_path, $version);
        
        if (!$version_data) {
            return false;
        }
        
        // Save the current content as a new version with a comment
        $current_content = file_get_contents($file_path);
        $comment = "Automatic backup before restoring version $version";
        $this->saveVersion($file_path, $current_content, $comment);
        
        // Write the restored content to the file
        if (file_put_contents($file_path, $version_data['content']) !== false) {
            // Save a new version indicating the restoration
            $comment = "Restored from version $version";
            $this->saveVersion($file_path, $version_data['content'], $comment);
            return true;
        }
        
        return false;
    }
    
    /**
     * Delete a specific version of a file
     * 
     * @param string $file_path File path
     * @param int $version Version number
     * @return bool True if successful, false otherwise
     */
    public function deleteVersion($file_path, $version) {
        // Don't allow deleting the latest version
        $latest_version = $this->getLatestVersionNumber($file_path);
        if ($version == $latest_version) {
            return false;
        }
        
        $sql = "DELETE FROM file_versions WHERE file_path = ? AND version = ?";
        
        $stmt = $this->conn->prepare($sql);
        $stmt->bind_param("si", $file_path, $version);
        
        return $stmt->execute();
    }
    
    /**
     * Compare two versions of a file
     * 
     * @param string $file_path File path
     * @param int $version1 First version number
     * @param int $version2 Second version number (0 for latest)
     * @return array Comparison data
     */
    public function compareVersions($file_path, $version1, $version2 = 0) {
        $v1 = $this->getVersion($file_path, $version1);
        $v2 = $this->getVersion($file_path, $version2);
        
        if (!$v1 || !$v2) {
            return [
                'success' => false,
                'message' => 'One or both versions not found'
            ];
        }
        
        // Simple line-by-line diff
        $lines1 = explode("\n", $v1['content']);
        $lines2 = explode("\n", $v2['content']);
        
        $diff = [];
        $max_lines = max(count($lines1), count($lines2));
        
        for ($i = 0; $i < $max_lines; $i++) {
            $line1 = isset($lines1[$i]) ? $lines1[$i] : '';
            $line2 = isset($lines2[$i]) ? $lines2[$i] : '';
            
            if ($line1 !== $line2) {
                $diff[] = [
                    'line' => $i + 1,
                    'v1' => $line1,
                    'v2' => $line2
                ];
            }
        }
        
        return [
            'success' => true,
            'v1' => [
                'version' => $v1['version'],
                'created_at' => $v1['created_at'],
                'username' => $v1['username']
            ],
            'v2' => [
                'version' => $v2['version'],
                'created_at' => $v2['created_at'],
                'username' => $v2['username']
            ],
            'diff' => $diff
        ];
    }
}
?>
