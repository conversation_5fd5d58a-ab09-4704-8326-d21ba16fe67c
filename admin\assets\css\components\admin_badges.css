/**
 * Admin Badges CSS
 * 
 * This file contains styles for badges and status indicators in the admin panel.
 */

/* Base Badge */
.badge {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-1) var(--spacing-2);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
  line-height: 1;
  text-align: center;
  white-space: nowrap;
  vertical-align: baseline;
  border-radius: var(--radius-full);
  transition: color var(--transition-fast) ease, background-color var(--transition-fast) ease;
}

/* Badge Sizes */
.badge-sm {
  padding: 0.125rem 0.375rem;
  font-size: 0.625rem;
}

.badge-lg {
  padding: var(--spacing-1) var(--spacing-3);
  font-size: var(--font-size-sm);
}

/* Badge Variants */
.badge-primary {
  color: var(--secondary-dark);
  background-color: var(--primary-color);
}

.badge-secondary {
  color: var(--white);
  background-color: var(--secondary-color);
}

.badge-success {
  color: var(--white);
  background-color: var(--success-color);
}

.badge-danger {
  color: var(--white);
  background-color: var(--danger-color);
}

.badge-warning {
  color: var(--secondary-dark);
  background-color: var(--warning-color);
}

.badge-info {
  color: var(--white);
  background-color: var(--info-color);
}

.badge-light {
  color: var(--text-dark);
  background-color: var(--gray-100);
}

.badge-dark {
  color: var(--white);
  background-color: var(--gray-800);
}

/* Soft Badge Variants */
.badge-soft-primary {
  color: var(--primary-dark);
  background-color: var(--primary-very-light);
}

.badge-soft-secondary {
  color: var(--secondary-color);
  background-color: rgba(44, 62, 80, 0.1);
}

.badge-soft-success {
  color: var(--success-color);
  background-color: rgba(16, 185, 129, 0.1);
}

.badge-soft-danger {
  color: var(--danger-color);
  background-color: rgba(239, 68, 68, 0.1);
}

.badge-soft-warning {
  color: var(--warning-color);
  background-color: rgba(245, 158, 11, 0.1);
}

.badge-soft-info {
  color: var(--info-color);
  background-color: rgba(59, 130, 246, 0.1);
}

.badge-soft-light {
  color: var(--gray-700);
  background-color: var(--gray-100);
}

.badge-soft-dark {
  color: var(--gray-800);
  background-color: rgba(31, 41, 55, 0.1);
}

/* Outline Badge Variants */
.badge-outline-primary {
  color: var(--primary-color);
  background-color: transparent;
  border: 1px solid var(--primary-color);
}

.badge-outline-secondary {
  color: var(--secondary-color);
  background-color: transparent;
  border: 1px solid var(--secondary-color);
}

.badge-outline-success {
  color: var(--success-color);
  background-color: transparent;
  border: 1px solid var(--success-color);
}

.badge-outline-danger {
  color: var(--danger-color);
  background-color: transparent;
  border: 1px solid var(--danger-color);
}

.badge-outline-warning {
  color: var(--warning-color);
  background-color: transparent;
  border: 1px solid var(--warning-color);
}

.badge-outline-info {
  color: var(--info-color);
  background-color: transparent;
  border: 1px solid var(--info-color);
}

.badge-outline-light {
  color: var(--gray-500);
  background-color: transparent;
  border: 1px solid var(--gray-300);
}

.badge-outline-dark {
  color: var(--gray-800);
  background-color: transparent;
  border: 1px solid var(--gray-800);
}

/* Badge with Icon */
.badge-with-icon {
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-1);
}

.badge-icon {
  font-size: 0.75em;
}

/* Badge Pill */
.badge-pill {
  padding-right: var(--spacing-3);
  padding-left: var(--spacing-3);
  border-radius: var(--radius-full);
}

/* Status Badges */
.status-badge {
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-1);
  padding: var(--spacing-1) var(--spacing-2);
  border-radius: var(--radius-full);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
  line-height: 1;
}

.status-badge::before {
  content: "";
  display: block;
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background-color: currentColor;
}

.status-active {
  color: var(--success-color);
  background-color: rgba(16, 185, 129, 0.1);
}

.status-inactive {
  color: var(--danger-color);
  background-color: rgba(239, 68, 68, 0.1);
}

.status-pending {
  color: var(--warning-color);
  background-color: rgba(245, 158, 11, 0.1);
}

.status-draft {
  color: var(--gray-500);
  background-color: rgba(107, 114, 128, 0.1);
}

.status-published {
  color: var(--success-color);
  background-color: rgba(16, 185, 129, 0.1);
}

.status-scheduled {
  color: var(--info-color);
  background-color: rgba(59, 130, 246, 0.1);
}

.status-archived {
  color: var(--gray-500);
  background-color: rgba(107, 114, 128, 0.1);
}

/* Badge Positioned */
.badge-positioned {
  position: absolute;
  top: 0;
  right: 0;
  transform: translate(50%, -50%);
}

/* Badge Counter */
.badge-counter {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-width: 18px;
  height: 18px;
  padding: 0 var(--spacing-1);
  font-size: 10px;
  font-weight: var(--font-weight-bold);
  line-height: 1;
  border-radius: var(--radius-full);
}

/* Badge in Button */
.btn .badge {
  position: relative;
  top: -1px;
}

/* Badge in Notification */
.notification-badge {
  position: absolute;
  top: 0;
  right: 0;
  min-width: 18px;
  height: 18px;
  padding: 0 var(--spacing-1);
  font-size: 10px;
  font-weight: var(--font-weight-bold);
  line-height: 1;
  border-radius: var(--radius-full);
  background-color: var(--danger-color);
  color: var(--white);
  display: flex;
  align-items: center;
  justify-content: center;
  transform: translate(50%, -50%);
}

/* Badge in List */
.list-item-badge {
  margin-left: auto;
}

/* Badge in Table */
.table-badge {
  margin: -2px 0;
}

/* Badge in Card */
.card-badge {
  position: absolute;
  top: var(--spacing-3);
  right: var(--spacing-3);
}

/* Badge in Avatar */
.avatar-badge {
  position: absolute;
  bottom: 0;
  right: 0;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background-color: var(--success-color);
  border: 2px solid var(--white);
}

.avatar-badge.online {
  background-color: var(--success-color);
}

.avatar-badge.offline {
  background-color: var(--gray-400);
}

.avatar-badge.busy {
  background-color: var(--danger-color);
}

.avatar-badge.away {
  background-color: var(--warning-color);
}
