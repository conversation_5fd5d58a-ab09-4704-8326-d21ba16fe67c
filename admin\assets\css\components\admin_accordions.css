/**
 * Admin Accordions CSS
 * 
 * This file contains styles for accordion components in the admin panel.
 */

/* Accordion Container */
.accordion {
  background-color: var(--white);
  border-radius: var(--card-border-radius);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--border-color);
  overflow: hidden;
}

/* Accordion Item */
.accordion-item {
  border-bottom: 1px solid var(--border-color);
}

.accordion-item:last-child {
  border-bottom: none;
}

/* Accordion Header */
.accordion-header {
  margin: 0;
}

/* Accordion Button */
.accordion-button {
  position: relative;
  display: flex;
  align-items: center;
  width: 100%;
  padding: var(--spacing-4);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  color: var(--text-dark);
  text-align: left;
  background-color: var(--white);
  border: 0;
  border-radius: 0;
  overflow-anchor: none;
  transition: color var(--transition-fast) ease, background-color var(--transition-fast) ease;
  cursor: pointer;
}

.accordion-button:not(.collapsed) {
  color: var(--primary-color);
  background-color: var(--primary-very-light);
}

.accordion-button:focus {
  z-index: 1;
  outline: 0;
  box-shadow: none;
}

.accordion-button:hover {
  background-color: var(--gray-50);
}

.accordion-button:not(.collapsed):hover {
  background-color: var(--primary-very-light);
}

/* Accordion Button Icon */
.accordion-button::after {
  flex-shrink: 0;
  width: 16px;
  height: 16px;
  margin-left: auto;
  content: "";
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%23212529'%3E%3Cpath fill-rule='evenodd' d='M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708z'/%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-size: 16px;
  transition: transform var(--transition-fast) ease;
}

.accordion-button:not(.collapsed)::after {
  transform: rotate(-180deg);
}

/* Accordion Body */
.accordion-collapse {
  overflow: hidden;
  transition: height var(--transition-normal) ease;
}

.accordion-body {
  padding: var(--spacing-4);
  background-color: var(--white);
}

/* Accordion Flush */
.accordion-flush {
  border-radius: 0;
  border-left: 0;
  border-right: 0;
}

.accordion-flush .accordion-item {
  border-right: 0;
  border-left: 0;
  border-radius: 0;
}

.accordion-flush .accordion-item:first-child {
  border-top: 0;
}

.accordion-flush .accordion-item:last-child {
  border-bottom: 0;
}

/* Accordion with Icons */
.accordion-icon {
  margin-right: var(--spacing-3);
  font-size: var(--font-size-lg);
  color: var(--text-light);
}

.accordion-button:not(.collapsed) .accordion-icon {
  color: var(--primary-color);
}

/* Accordion with Borders */
.accordion-bordered {
  border: none;
  box-shadow: none;
}

.accordion-bordered .accordion-item {
  border: 1px solid var(--border-color);
  border-radius: var(--card-border-radius);
  margin-bottom: var(--spacing-2);
  overflow: hidden;
}

.accordion-bordered .accordion-item:last-child {
  margin-bottom: 0;
}

/* Accordion with Left Border */
.accordion-border-left .accordion-button {
  border-left: 3px solid transparent;
}

.accordion-border-left .accordion-button:not(.collapsed) {
  border-left-color: var(--primary-color);
}

/* Accordion with Background */
.accordion-bg .accordion-button {
  background-color: var(--gray-50);
}

.accordion-bg .accordion-button:not(.collapsed) {
  background-color: var(--primary-very-light);
}

.accordion-bg .accordion-body {
  background-color: var(--white);
}

/* Accordion with Rounded Buttons */
.accordion-rounded .accordion-button {
  border-radius: var(--radius-md);
  margin: var(--spacing-1);
}

/* Accordion with Shadow */
.accordion-shadow .accordion-button:not(.collapsed) {
  box-shadow: 0 0 0 1px var(--primary-color);
}

/* Accordion with Badge */
.accordion-badge {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-width: 20px;
  height: 20px;
  padding: 0 var(--spacing-1);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-bold);
  line-height: 1;
  color: var(--white);
  background-color: var(--text-muted);
  border-radius: var(--radius-full);
  margin-left: var(--spacing-2);
}

.accordion-button:not(.collapsed) .accordion-badge {
  background-color: var(--primary-color);
  color: var(--secondary-dark);
}

/* Accordion with Plus/Minus Icons */
.accordion-plus-minus .accordion-button::after {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%23212529'%3E%3Cpath fill-rule='evenodd' d='M8 4a.5.5 0 0 1 .5.5v3h3a.5.5 0 0 1 0 1h-3v3a.5.5 0 0 1-1 0v-3h-3a.5.5 0 0 1 0-1h3v-3A.5.5 0 0 1 8 4z'/%3E%3C/svg%3E");
}

.accordion-plus-minus .accordion-button:not(.collapsed)::after {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%23212529'%3E%3Cpath fill-rule='evenodd' d='M4 8a.5.5 0 0 1 .5-.5h7a.5.5 0 0 1 0 1h-7A.5.5 0 0 1 4 8z'/%3E%3C/svg%3E");
  transform: rotate(0deg);
}

/* Accordion with Arrow Icons */
.accordion-arrow .accordion-button::after {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%23212529'%3E%3Cpath fill-rule='evenodd' d='M4.646 1.646a.5.5 0 0 1 .708 0l6 6a.5.5 0 0 1 0 .708l-6 6a.5.5 0 0 1-.708-.708L10.293 8 4.646 2.354a.5.5 0 0 1 0-.708z'/%3E%3C/svg%3E");
}

.accordion-arrow .accordion-button:not(.collapsed)::after {
  transform: rotate(90deg);
}

/* Accordion with Custom Colors */
.accordion-primary .accordion-button:not(.collapsed) {
  color: var(--primary-color);
  background-color: var(--primary-very-light);
}

.accordion-secondary .accordion-button:not(.collapsed) {
  color: var(--secondary-color);
  background-color: rgba(44, 62, 80, 0.1);
}

.accordion-success .accordion-button:not(.collapsed) {
  color: var(--success-color);
  background-color: rgba(16, 185, 129, 0.1);
}

.accordion-danger .accordion-button:not(.collapsed) {
  color: var(--danger-color);
  background-color: rgba(239, 68, 68, 0.1);
}

.accordion-warning .accordion-button:not(.collapsed) {
  color: var(--warning-color);
  background-color: rgba(245, 158, 11, 0.1);
}

.accordion-info .accordion-button:not(.collapsed) {
  color: var(--info-color);
  background-color: rgba(59, 130, 246, 0.1);
}

/* Accordion with Nested Items */
.accordion-nested .accordion-body {
  padding: 0;
}

.accordion-nested .accordion {
  border: none;
  box-shadow: none;
}

.accordion-nested .accordion-item {
  border: none;
  border-bottom: none;
}

.accordion-nested .accordion-button {
  padding-left: var(--spacing-6);
  font-size: var(--font-size-sm);
}

.accordion-nested .accordion-body {
  padding-left: var(--spacing-6);
}

/* Accordion with Hover Effect */
.accordion-hover .accordion-item:hover {
  background-color: var(--gray-50);
}

.accordion-hover .accordion-button:hover {
  color: var(--primary-color);
}

/* Responsive Styles */
@media (max-width: 576px) {
  .accordion-button {
    padding: var(--spacing-3);
  }
  
  .accordion-body {
    padding: var(--spacing-3);
  }
}
