/**
 * Settings Manager JavaScript
 *
 * Handles backup and restore functionality for all settings tabs
 */

document.addEventListener('DOMContentLoaded', function() {
    // Initialize backup and restore buttons
    initBackupRestore();

    // Initialize email template variables insertion
    initTemplateVariables();
});

/**
 * Initialize backup and restore functionality
 */
function initBackupRestore() {
    const backupBtn = document.getElementById('backup-settings-btn');
    const restoreBtn = document.getElementById('restore-settings-btn');

    if (backupBtn) {
        // Remove any existing event listeners
        backupBtn.replaceWith(backupBtn.cloneNode(true));

        // Get the fresh reference
        const freshBackupBtn = document.getElementById('backup-settings-btn');

        // Add event listener
        freshBackupBtn.addEventListener('click', function() {
            backupAllSettings();
        });
    }

    if (restoreBtn) {
        // Remove any existing event listeners
        restoreBtn.replaceWith(restoreBtn.cloneNode(true));

        // Get the fresh reference
        const freshRestoreBtn = document.getElementById('restore-settings-btn');

        // Add event listener
        freshRestoreBtn.addEventListener('click', function() {
            // Create file input element
            const fileInput = document.createElement('input');
            fileInput.type = 'file';
            fileInput.accept = '.json';
            fileInput.style.display = 'none';
            document.body.appendChild(fileInput);

            // Trigger file selection
            fileInput.click();

            // Handle file selection
            fileInput.addEventListener('change', function() {
                if (fileInput.files.length > 0) {
                    const file = fileInput.files[0];
                    const reader = new FileReader();

                    reader.onload = function(e) {
                        try {
                            const settings = JSON.parse(e.target.result);
                            restoreAllSettings(settings);
                        } catch (error) {
                            alert('Error parsing settings file: ' + error.message);
                        }
                    };

                    reader.readAsText(file);
                }

                // Clean up
                document.body.removeChild(fileInput);
            });
        });
    }
}

/**
 * Backup all settings from all categories
 */
function backupAllSettings() {
    // Show loading indicator
    showLoadingOverlay('Backing up settings...');

    // Fetch all settings from the server
    fetch('ajax/get_all_settings.php')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Create a JSON file with the settings
                const settingsJson = JSON.stringify(data.settings, null, 2);
                const blob = new Blob([settingsJson], { type: 'application/json' });
                const url = URL.createObjectURL(blob);

                // Create a download link
                const a = document.createElement('a');
                a.href = url;
                a.download = 'settings_backup_' + formatDate(new Date()) + '.json';
                document.body.appendChild(a);
                a.click();

                // Clean up
                setTimeout(function() {
                    document.body.removeChild(a);
                    URL.revokeObjectURL(url);
                }, 100);

                // Hide loading indicator
                hideLoadingOverlay();
            } else {
                hideLoadingOverlay();
                alert('Error backing up settings: ' + data.message);
            }
        })
        .catch(error => {
            hideLoadingOverlay();
            alert('Error backing up settings: ' + error.message);
        });
}

/**
 * Restore all settings from a backup file
 * @param {Object} settings - The settings object from the backup file
 */
function restoreAllSettings(settings) {
    // Confirm restore
    if (!confirm('Are you sure you want to restore all settings from this backup? This will overwrite all current settings.')) {
        return;
    }

    // Show loading indicator
    showLoadingOverlay('Restoring settings...');

    // Send settings to server
    fetch('ajax/restore_all_settings.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({ settings: settings })
    })
    .then(response => response.json())
    .then(data => {
        hideLoadingOverlay();

        if (data.success) {
            alert('Settings restored successfully. The page will now reload.');
            window.location.reload();
        } else {
            alert('Error restoring settings: ' + data.message);
        }
    })
    .catch(error => {
        hideLoadingOverlay();
        alert('Error restoring settings: ' + error.message);
    });
}

/**
 * Format date as YYYY-MM-DD_HH-MM-SS
 * @param {Date} date - The date to format
 * @returns {string} - Formatted date string
 */
function formatDate(date) {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    const seconds = String(date.getSeconds()).padStart(2, '0');

    return `${year}-${month}-${day}_${hours}-${minutes}-${seconds}`;
}

/**
 * Show loading overlay
 * @param {string} message - The message to display
 */
function showLoadingOverlay(message) {
    // Create overlay if it doesn't exist
    let overlay = document.getElementById('settings-loading-overlay');

    if (!overlay) {
        overlay = document.createElement('div');
        overlay.id = 'settings-loading-overlay';
        overlay.className = 'loading-overlay';
        overlay.innerHTML = `
            <div class="loading-spinner"></div>
            <div class="loading-message">${message || 'Loading...'}</div>
        `;
        document.body.appendChild(overlay);
    } else {
        overlay.querySelector('.loading-message').textContent = message || 'Loading...';
        overlay.style.display = 'flex';
    }
}

/**
 * Hide loading overlay
 */
function hideLoadingOverlay() {
    const overlay = document.getElementById('settings-loading-overlay');
    if (overlay) {
        overlay.style.display = 'none';
    }
}

/**
 * Initialize template variables insertion
 */
function initTemplateVariables() {
    // This function is intentionally left empty as it's handled by template-variables.js
    // It's included here for completeness and future expansion
}
