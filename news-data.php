<?php
// Prevent any output before JSO<PERSON>
ob_start();

// Disable error display
error_reporting(0);
ini_set('display_errors', 0);

// Include database connection
require_once 'admin/config.php';

// Include frontend settings helper
require_once 'includes/FrontendSettings.php';

// Set headers to allow AJAX requests and prevent caching
header('Content-Type: application/json');
header('Cache-Control: no-store, no-cache, must-revalidate, max-age=0');
header('Cache-Control: post-check=0, pre-check=0', false);
header('Pragma: no-cache');

// Initialize frontend settings
$frontendSettings = new FrontendSettings($conn);
$newsSettings = $frontendSettings->getNewsSettings();

// Function to safely escape HTML
function escapeHtml($text) {
    return htmlspecialchars($text, ENT_QUOTES, 'UTF-8');
}

// Function to truncate text
function truncateText($text, $length = 150) {
    if (!$text) return '';
    // Remove HTML tags
    $text = strip_tags($text);
    return (strlen($text) > $length) ? substr($text, 0, $length) . '...' : $text;
}

// Get news items from database with pagination
function getNewsItems($conn, $settings) {
    $items = [];
    $error = null;
    $total_items = 0;
    $total_pages = 0;

    // Get page number from query string, default to 1
    $page = isset($_GET['page']) ? intval($_GET['page']) : 1;
    if ($page < 1) $page = 1;

    // Get settings
    $items_per_page = $settings['news_per_page'];
    $excerpt_length = $settings['news_excerpt_length'];
    $sort_order = $settings['default_sort_order'];

    try {
        // First, get total count of news items
        $count_sql = "SELECT COUNT(*) as total FROM news";
        $count_result = $conn->query($count_sql);

        if (!$count_result) {
            throw new Exception("Count query failed: " . $conn->error);
        }

        $count_row = $count_result->fetch_assoc();
        $total_items = $count_row['total'];

        // If there are no news items, return early with empty items array
        if ($total_items === 0) {
            return [
                'items' => [],
                'count' => 0,
                'total_items' => 0,
                'total_pages' => 0,
                'current_page' => 1,
                'items_per_page' => $items_per_page,
                'pagination_position' => $settings['pagination_position'],
                'show_date' => $settings['show_news_date'],
                'show_category' => $settings['show_news_category'],
                'error' => null,
                'no_news' => true
            ];
        }

        $total_pages = ceil($total_items / $items_per_page);

        // Calculate offset for pagination
        $offset = ($page - 1) * $items_per_page;

        // Determine sort order
        $order_by = "n.created_at DESC"; // Default (newest)
        if ($sort_order === 'oldest') {
            $order_by = "n.created_at ASC";
        } elseif ($sort_order === 'title_asc') {
            $order_by = "n.title ASC";
        } elseif ($sort_order === 'title_desc') {
            $order_by = "n.title DESC";
        }

        // Check which image column exists (image or featured_image)
        $image_column = 'image'; // Default
        $column_check = $conn->query("SHOW COLUMNS FROM news LIKE 'featured_image'");
        if ($column_check && $column_check->num_rows > 0) {
            $image_column = 'featured_image';
        }

        // Get paginated news items
        $sql = "SELECT n.id, n.title, n.content, n.$image_column as image, n.slug, n.created_at,
                       IFNULL(c.name, 'News') as category_name
                FROM news n
                LEFT JOIN categories c ON n.category_id = c.id
                ORDER BY $order_by
                LIMIT $items_per_page OFFSET $offset";

        $result = $conn->query($sql);

        if (!$result) {
            throw new Exception("Query failed: " . $conn->error);
        }

        while ($row = $result->fetch_assoc()) {
            // Format the data for JSON output
            $items[] = [
                'id' => $row['id'],
                'title' => escapeHtml($row['title']),
                'content' => truncateText($row['content'], $excerpt_length),
                'image' => escapeHtml($row['image']),
                'slug' => $row['slug'] ? escapeHtml($row['slug']) : $row['id'],
                'category_name' => escapeHtml($row['category_name']),
                'created_at' => date('F j, Y', strtotime($row['created_at'])),
                'show_date' => $settings['show_news_date'],
                'show_category' => $settings['show_news_category']
            ];
        }

        $result->free();
    } catch (Exception $e) {
        $error = $e->getMessage();
    }

    return [
        'items' => $items,
        'count' => count($items),
        'total_items' => $total_items,
        'total_pages' => $total_pages,
        'current_page' => $page,
        'items_per_page' => $items_per_page,
        'pagination_position' => $settings['pagination_position'],
        'show_date' => $settings['show_news_date'],
        'show_category' => $settings['show_news_category'],
        'error' => $error,
        'debug' => [
            'file_location' => __FILE__,
            'php_version' => PHP_VERSION,
            'server' => $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown',
            'database_connected' => ($conn && !$conn->connect_error) ? 'Yes' : 'No',
            'database_error' => $conn->error ?? 'None',
            'settings' => $settings,
            'request_method' => $_SERVER['REQUEST_METHOD'],
            'request_uri' => $_SERVER['REQUEST_URI'],
            'query_string' => $_SERVER['QUERY_STRING'] ?? '',
            'http_referer' => $_SERVER['HTTP_REFERER'] ?? 'None'
        ]
    ];
}

// Get news data
$newsData = getNewsItems($conn, $newsSettings);

// Make sure there's no output before the JSON
ob_clean();

// Set proper JSON headers
header('Content-Type: application/json');
header('Cache-Control: no-store, no-cache, must-revalidate, max-age=0');
header('Cache-Control: post-check=0, pre-check=0', false);
header('Pragma: no-cache');

// Return JSON response with proper encoding options
echo json_encode($newsData, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);

// If JSON encoding failed, return a simplified error response
if (json_last_error() !== JSON_ERROR_NONE) {
    // Clear any previous output
    ob_clean();

    // Return a simplified error response
    echo json_encode([
        'error' => 'JSON encoding error: ' . json_last_error_msg(),
        'items' => [],
        'count' => 0,
        'total_items' => 0,
        'total_pages' => 0,
        'current_page' => 1
    ]);
}

ob_end_flush();
exit; // Stop execution to prevent any additional output
?>
