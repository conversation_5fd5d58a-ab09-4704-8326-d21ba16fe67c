<?php
/**
 * Deep Debug Tool for CSS and Styling Issues
 * This script performs comprehensive diagnostics to identify styling problems
 */

// Start session
session_start();

// Include config
require_once 'config.php';

echo "<!DOCTYPE html>";
echo "<html><head><title>Deep Debug Tool</title>";
echo "<style>
body{font-family:Arial,sans-serif;margin:20px;background:#f5f5f5;} 
.section{margin:20px 0;padding:15px;border:1px solid #ddd;background:white;border-radius:5px;} 
.good{color:green;font-weight:bold;} 
.bad{color:red;font-weight:bold;} 
.warning{color:orange;font-weight:bold;}
.code{background:#f8f8f8;padding:10px;border-radius:3px;font-family:monospace;margin:10px 0;}
.test-btn{background:#007cba;color:white;padding:10px 15px;border:none;border-radius:3px;cursor:pointer;margin:5px;}
.test-result{margin:10px 0;padding:10px;border-radius:3px;}
.success{background:#d4edda;border:1px solid #c3e6cb;color:#155724;}
.error{background:#f8d7da;border:1px solid #f5c6cb;color:#721c24;}
</style>";
echo "</head><body>";

echo "<h1>🔍 Deep CSS Debug Investigation</h1>";

// 1. HTTP Headers Analysis
echo "<div class='section'>";
echo "<h2>1. HTTP Response Headers Analysis</h2>";

$css_files = [
    'assets/css/consolidated.css',
    'assets/css/main.css',
    'assets/css/components/admin_user-dropdown.css'
];

foreach ($css_files as $css_file) {
    echo "<h3>Testing: $css_file</h3>";
    
    $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
    $host = $_SERVER['HTTP_HOST'];
    $admin_path = dirname($_SERVER['SCRIPT_NAME']);
    $css_url = $protocol . '://' . $host . $admin_path . '/' . $css_file;
    
    echo "<strong>URL:</strong> <a href='$css_url' target='_blank'>$css_url</a><br>";
    
    // Get headers
    $context = stream_context_create([
        'http' => [
            'method' => 'HEAD',
            'timeout' => 10
        ]
    ]);
    
    $headers = @get_headers($css_url, 1, $context);
    
    if ($headers) {
        echo "<strong>Status:</strong> " . $headers[0] . "<br>";
        
        if (isset($headers['Content-Type'])) {
            $content_type = is_array($headers['Content-Type']) ? end($headers['Content-Type']) : $headers['Content-Type'];
            echo "<strong>Content-Type:</strong> $content_type ";
            if (strpos($content_type, 'text/css') !== false) {
                echo "<span class='good'>✅ Correct</span>";
            } else {
                echo "<span class='bad'>❌ Wrong! Should be text/css</span>";
            }
            echo "<br>";
        }
        
        if (isset($headers['Content-Length'])) {
            $size = is_array($headers['Content-Length']) ? end($headers['Content-Length']) : $headers['Content-Length'];
            echo "<strong>Size:</strong> $size bytes<br>";
        }
        
        // Check for caching headers
        if (isset($headers['Cache-Control'])) {
            $cache = is_array($headers['Cache-Control']) ? end($headers['Cache-Control']) : $headers['Cache-Control'];
            echo "<strong>Cache-Control:</strong> $cache<br>";
        }
        
        if (isset($headers['ETag'])) {
            $etag = is_array($headers['ETag']) ? end($headers['ETag']) : $headers['ETag'];
            echo "<strong>ETag:</strong> $etag<br>";
        }
    } else {
        echo "<span class='bad'>❌ Failed to get headers</span><br>";
    }
    echo "<hr>";
}
echo "</div>";

// 2. CSS Content Analysis
echo "<div class='section'>";
echo "<h2>2. CSS Content Analysis</h2>";

foreach ($css_files as $css_file) {
    $full_path = __DIR__ . '/' . $css_file;
    echo "<h3>$css_file</h3>";
    
    if (file_exists($full_path)) {
        $content = file_get_contents($full_path);
        $size = strlen($content);
        echo "<span class='good'>✅ File exists</span> - Size: $size bytes<br>";
        
        // Check for specific CSS rules
        $checks = [
            '.user-dropdown' => 'User dropdown styles',
            '.topbar-user-menu' => 'User menu styles',
            '@import' => '@import statements (potential issue)',
            'var(--' => 'CSS variables',
            '.dropdown-menu' => 'Dropdown menu styles'
        ];
        
        foreach ($checks as $pattern => $description) {
            $count = substr_count($content, $pattern);
            if ($count > 0) {
                echo "<span class='good'>✅ $description found ($count occurrences)</span><br>";
            } else {
                echo "<span class='warning'>⚠️ $description not found</span><br>";
            }
        }
        
        // Show first 200 characters
        echo "<div class='code'>" . htmlspecialchars(substr($content, 0, 200)) . "...</div>";
    } else {
        echo "<span class='bad'>❌ File not found at: $full_path</span><br>";
    }
    echo "<hr>";
}
echo "</div>";

// 3. Browser CSS Loading Test
echo "<div class='section'>";
echo "<h2>3. Live CSS Loading Test</h2>";
echo "<p>This test loads CSS files dynamically and checks if they apply correctly:</p>";

echo "<div id='css-test-area'>";
echo "<div class='test-element' style='padding:10px;border:1px solid #ccc;margin:10px 0;'>Test Element (should change when CSS loads)</div>";
echo "</div>";

echo "<button class='test-btn' onclick='testCSSLoading()'>Test CSS Loading</button>";
echo "<div id='css-test-results'></div>";

echo "<script>
function testCSSLoading() {
    const results = document.getElementById('css-test-results');
    results.innerHTML = '<p>Testing CSS loading...</p>';
    
    const cssFiles = [
        'assets/css/consolidated.css',
        'assets/css/main.css',
        'assets/css/components/admin_user-dropdown.css'
    ];
    
    let testResults = [];
    let completed = 0;
    
    cssFiles.forEach((file, index) => {
        const link = document.createElement('link');
        link.rel = 'stylesheet';
        link.href = file + '?test=' + Date.now();
        
        const startTime = Date.now();
        
        link.onload = function() {
            const loadTime = Date.now() - startTime;
            testResults[index] = `<div class='success'>✅ ${file} loaded successfully (${loadTime}ms)</div>`;
            completed++;
            if (completed === cssFiles.length) showResults();
        };
        
        link.onerror = function() {
            const loadTime = Date.now() - startTime;
            testResults[index] = `<div class='error'>❌ ${file} failed to load (${loadTime}ms)</div>`;
            completed++;
            if (completed === cssFiles.length) showResults();
        };
        
        document.head.appendChild(link);
    });
    
    function showResults() {
        results.innerHTML = testResults.join('');
    }
}
</script>";
echo "</div>";

// 4. DOM and CSS Application Test
echo "<div class='section'>";
echo "<h2>4. DOM Structure Test</h2>";
echo "<p>Testing if the user dropdown HTML structure exists and CSS is applied:</p>";

echo "<div class='test-dropdown-container'>";
echo "<div class='topbar-user-menu'>";
echo "<button class='user-menu-toggle' id='test-toggle'>";
echo "<div class='user-avatar'><div class='avatar-placeholder'>A</div></div>";
echo "<span class='user-name'>Admin</span>";
echo "<i class='dropdown-icon'>▼</i>";
echo "</button>";
echo "<div class='user-dropdown' id='test-dropdown'>";
echo "<div class='user-dropdown-header'>";
echo "<div class='user-dropdown-avatar'><div class='avatar-placeholder-large'>A</div></div>";
echo "<div class='user-dropdown-info'>";
echo "<div class='user-dropdown-name'>Admin User</div>";
echo "<div class='user-dropdown-email'><EMAIL></div>";
echo "</div>";
echo "</div>";
echo "<div class='dropdown-menu'>";
echo "<div class='dropdown-section'>";
echo "<div class='dropdown-item'>";
echo "<a href='#' class='dropdown-link'>";
echo "<div class='dropdown-icon'>👤</div>";
echo "<div class='dropdown-link-content'>";
echo "<span class='dropdown-link-title'>Profile</span>";
echo "<span class='dropdown-link-description'>Manage your account</span>";
echo "</div>";
echo "</a>";
echo "</div>";
echo "</div>";
echo "</div>";
echo "</div>";
echo "</div>";
echo "</div>";

echo "<button class='test-btn' onclick='testDropdownStyles()'>Test Dropdown Styles</button>";
echo "<button class='test-btn' onclick='toggleTestDropdown()'>Toggle Test Dropdown</button>";
echo "<div id='style-test-results'></div>";

echo "<script>
function testDropdownStyles() {
    const results = document.getElementById('style-test-results');
    const dropdown = document.getElementById('test-dropdown');
    const toggle = document.getElementById('test-toggle');
    
    let testResults = [];
    
    // Test computed styles
    const dropdownStyles = window.getComputedStyle(dropdown);
    const toggleStyles = window.getComputedStyle(toggle);
    
    // Check key styles
    const tests = [
        {element: dropdown, property: 'position', expected: 'absolute', name: 'Dropdown position'},
        {element: dropdown, property: 'background-color', expected: 'rgb(255, 255, 255)', name: 'Dropdown background'},
        {element: dropdown, property: 'border-radius', expected: '0.5rem', name: 'Dropdown border radius'},
        {element: toggle, property: 'display', expected: 'flex', name: 'Toggle display'},
        {element: toggle, property: 'align-items', expected: 'center', name: 'Toggle alignment'}
    ];
    
    tests.forEach(test => {
        const computedValue = window.getComputedStyle(test.element)[test.property];
        if (computedValue && computedValue !== 'auto' && computedValue !== 'normal') {
            testResults.push(`<div class='success'>✅ ${test.name}: ${computedValue}</div>`);
        } else {
            testResults.push(`<div class='error'>❌ ${test.name}: ${computedValue} (CSS not applied)</div>`);
        }
    });
    
    results.innerHTML = testResults.join('');
}

function toggleTestDropdown() {
    const dropdown = document.getElementById('test-dropdown');
    const toggle = document.getElementById('test-toggle');
    
    if (dropdown.classList.contains('show')) {
        dropdown.classList.remove('show');
        toggle.classList.remove('active');
    } else {
        dropdown.classList.add('show');
        toggle.classList.add('active');
    }
}
</script>";
echo "</div>";

// 5. Network and Caching Analysis
echo "<div class='section'>";
echo "<h2>5. Server Configuration Analysis</h2>";

echo "<h3>PHP Configuration</h3>";
echo "<strong>PHP Version:</strong> " . PHP_VERSION . "<br>";
echo "<strong>Server Software:</strong> " . $_SERVER['SERVER_SOFTWARE'] . "<br>";
echo "<strong>Document Root:</strong> " . $_SERVER['DOCUMENT_ROOT'] . "<br>";

echo "<h3>File Permissions</h3>";
foreach ($css_files as $css_file) {
    $full_path = __DIR__ . '/' . $css_file;
    if (file_exists($full_path)) {
        $perms = fileperms($full_path);
        $perms_str = substr(sprintf('%o', $perms), -4);
        echo "<strong>$css_file:</strong> $perms_str ";
        if (is_readable($full_path)) {
            echo "<span class='good'>✅ Readable</span>";
        } else {
            echo "<span class='bad'>❌ Not readable</span>";
        }
        echo "<br>";
    }
}

echo "<h3>MIME Type Test</h3>";
if (function_exists('mime_content_type')) {
    foreach ($css_files as $css_file) {
        $full_path = __DIR__ . '/' . $css_file;
        if (file_exists($full_path)) {
            $mime = mime_content_type($full_path);
            echo "<strong>$css_file:</strong> $mime<br>";
        }
    }
} else {
    echo "mime_content_type function not available<br>";
}

echo "</div>";

echo "<div class='section'>";
echo "<h2>6. Quick Fix Generator</h2>";
echo "<p>Based on the analysis above, here are potential solutions:</p>";
echo "<button class='test-btn' onclick='generateFix()'>Generate Fix</button>";
echo "<div id='fix-suggestions'></div>";

echo "<script>
function generateFix() {
    const suggestions = document.getElementById('fix-suggestions');
    suggestions.innerHTML = `
        <h3>Potential Solutions:</h3>
        <div class='code'>
        1. Add to .htaccess file:<br>
        AddType text/css .css<br>
        <FilesMatch \"\\.css$\"><br>
        &nbsp;&nbsp;Header set Cache-Control \"max-age=31536000, public\"<br>
        </FilesMatch><br><br>
        
        2. Check server MIME types configuration<br><br>
        
        3. Try loading CSS with different method:<br>
        &lt;style&gt;@import url('assets/css/consolidated.css');&lt;/style&gt;<br><br>
        
        4. Inline critical CSS directly in HTML<br><br>
        
        5. Check for mod_rewrite conflicts
        </div>
    `;
}
</script>";
echo "</div>";

echo "</body></html>";
?>
