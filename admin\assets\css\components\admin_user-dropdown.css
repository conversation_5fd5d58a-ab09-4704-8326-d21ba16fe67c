/**
 * Admin User Dropdown CSS
 *
 * This file contains styles for the user dropdown menu in the admin panel.
 * Redesigned for a more modern and user-friendly experience.
 */

/* User Menu Container */
.topbar-user-menu {
  position: relative;
  height: 100%;
  display: flex;
  align-items: center;
  margin-left: 8px;
}

/* User Menu Toggle Button */
.user-menu-toggle {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  padding: var(--spacing-1) var(--spacing-2);
  background-color: transparent;
  border: none;
  border-radius: var(--radius-md);
  color: var(--text-color);
  font-size: var(--font-size-sm);
  cursor: pointer;
  transition: all var(--transition-fast) ease;
  height: 40px;
}

.user-menu-toggle:hover {
  background-color: rgba(0, 0, 0, 0.05);
}

.user-menu-toggle:focus {
  outline: none;
  box-shadow: 0 0 0 2px rgba(241, 202, 47, 0.25);
}

/* User Avatar */
.user-avatar {
  position: relative;
  width: 36px;
  height: 36px;
  border-radius: var(--radius-full);
  overflow: hidden;
  flex-shrink: 0;
  background-color: var(--background-light);
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2px solid var(--white);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.avatar-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.avatar-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--primary-color);
  color: var(--white);
  font-weight: var(--font-weight-bold);
  font-size: var(--font-size-base);
  text-transform: uppercase;
}

.avatar-status {
  position: absolute;
  bottom: 0;
  right: 0;
  width: 10px;
  height: 10px;
  border-radius: 50%;
  border: 2px solid var(--white);
}

.avatar-status.online {
  background-color: #4CAF50;
}

.avatar-status.away {
  background-color: #FFC107;
}

.avatar-status.offline {
  background-color: #9E9E9E;
}

.user-menu-toggle i {
  font-size: var(--font-size-base);
  flex-shrink: 0;
}

.user-menu-toggle .dropdown-icon {
  font-size: var(--font-size-xs);
  transition: transform var(--transition-fast) ease;
  margin-left: auto;
  color: var(--text-light);
}

.user-menu-toggle.active .dropdown-icon {
  transform: rotate(180deg);
  color: var(--primary-color);
}

/* User Name in Toggle */
.user-name {
  font-weight: var(--font-weight-medium);
  display: none;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 120px;
  margin-right: var(--spacing-2);
}

@media (min-width: 768px) {
  .user-name {
    display: inline-block;
  }
}

@media (min-width: 992px) {
  .user-name {
    max-width: 150px;
  }
}

/* User Dropdown */
.user-dropdown {
  position: absolute;
  top: calc(100% + 5px);
  right: 10px;
  z-index: 9999 !important; /* Higher z-index to ensure it's above everything */
  width: 320px;
  /* Remove max-height and overflow to allow natural height extension */
  background-color: var(--white);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-xl);
  border: 1px solid var(--border-color);
  display: none;
  transform: translateY(10px);
  opacity: 0;
  transition: transform var(--transition-fast) ease, opacity var(--transition-fast) ease;
  overflow: visible; /* Remove scrollbars */
  margin-top: 5px; /* Add margin to ensure it's below the header */
}

.user-dropdown.show {
  display: block !important;
  transform: translateY(0) !important;
  opacity: 1;
  animation: dropdownFadeIn 0.2s ease-out;
  z-index: 9999 !important;
}

@keyframes dropdownFadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* User Dropdown Header */
.user-dropdown-header {
  padding: var(--spacing-4);
  display: flex !important;
  align-items: center;
  gap: var(--spacing-3);
  border-bottom: 1px solid var(--border-light);
  background-color: var(--background-light);
  margin-bottom: 0; /* Remove any margin that might cause gaps */
  visibility: visible !important;
  opacity: 1 !important;
  position: relative;
  z-index: 1;
}

.user-dropdown-avatar {
  position: relative;
  width: 60px;
  height: 60px;
  border-radius: var(--radius-full);
  overflow: hidden;
  flex-shrink: 0;
  border: 3px solid var(--white);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  display: flex !important;
  visibility: visible !important;
  opacity: 1 !important;
}

.avatar-img-large {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.avatar-placeholder-large {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--primary-color);
  color: var(--white);
  font-weight: var(--font-weight-bold);
  font-size: var(--font-size-xl);
  text-transform: uppercase;
}

.user-dropdown-info {
  flex: 1;
  min-width: 0;
  display: block !important;
  visibility: visible !important;
  opacity: 1 !important;
}

.user-dropdown-name {
  margin: 0 0 4px 0;
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-bold);
  color: var(--text-color);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  display: block !important;
  visibility: visible !important;
  opacity: 1 !important;
}

.user-dropdown-email {
  margin: 0 0 8px 0;
  font-size: var(--font-size-xs);
  color: var(--text-light);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  display: block !important;
  visibility: visible !important;
  opacity: 1 !important;
}

.user-dropdown-badge {
  display: inline-block !important;
  padding: 2px 8px;
  background-color: var(--primary-color);
  color: var(--text-dark);
  font-size: 10px;
  font-weight: var(--font-weight-medium);
  border-radius: 12px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  visibility: visible !important;
  opacity: 1 !important;
}

/* Dropdown Menu */
.dropdown-menu {
  padding: 0; /* Remove padding that causes gaps */
  margin: 0;
  display: block !important; /* Force display */
  visibility: visible !important; /* Force visibility */
  opacity: 1 !important; /* Force opacity */
  position: relative; /* Ensure proper positioning */
  z-index: 1; /* Ensure it's above other elements */
}

/* Ensure all dropdown items are visible */
.dropdown-menu .dropdown-item {
  display: block !important;
  visibility: visible !important;
  opacity: 1 !important;
}

.dropdown-menu .dropdown-link {
  display: flex !important;
  visibility: visible !important;
  opacity: 1 !important;
}

/* Dropdown Section Title */
.dropdown-section-title {
  padding: var(--spacing-2) var(--spacing-4);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-bold);
  text-transform: uppercase;
  color: var(--text-muted) !important; /* Ensure proper muted text color */
  letter-spacing: 0.5px;
  margin-top: var(--spacing-2);
  display: none !important; /* Hidden by default on desktop */
  visibility: hidden !important; /* Hidden on desktop */
}

/* Dropdown Section */
.dropdown-section {
  margin-bottom: var(--spacing-3);
  border-bottom: 1px solid var(--border-color);
  padding-bottom: var(--spacing-3);
}

.dropdown-section:last-child {
  border-bottom: none;
  margin-bottom: 0;
  padding-bottom: 0;
}

.dropdown-section-title:first-child {
  margin-top: 0;
}

/* Dropdown Item */
.dropdown-item {
  margin: 0;
  padding: 0;
  display: block !important; /* Force display */
  visibility: visible !important; /* Force visibility */
  opacity: 1 !important; /* Force opacity */
  position: relative; /* Ensure proper positioning */
}

.dropdown-link {
  padding: var(--spacing-3) var(--spacing-4);
  display: flex !important; /* Force display */
  align-items: center;
  gap: var(--spacing-3);
  color: var(--text-dark) !important; /* Ensure proper text color */
  text-decoration: none;
  transition: all var(--transition-fast) ease;
  font-size: var(--font-size-sm);
  border-left: 3px solid transparent;
  visibility: visible !important; /* Force visibility */
}

.dropdown-link:hover {
  background-color: var(--background-light);
  color: var(--primary-color) !important;
  border-left-color: var(--primary-color);
}

/* Special styling for logout link */
.dropdown-link.logout-link {
  color: var(--danger-color) !important;
  border-left-color: transparent;
}

.dropdown-link.logout-link:hover {
  background-color: rgba(220, 53, 69, 0.1);
  color: var(--danger-dark) !important;
  border-left-color: var(--danger-color);
}

.dropdown-link.logout-link .dropdown-icon {
  background-color: rgba(220, 53, 69, 0.1);
  color: var(--danger-color);
}

.dropdown-link.logout-link:hover .dropdown-icon {
  background-color: rgba(220, 53, 69, 0.2);
  color: var(--danger-dark);
}

.dropdown-link.logout-link .dropdown-link-description {
  color: rgba(220, 53, 69, 0.7) !important;
}

.dropdown-link.logout-link:hover .dropdown-link-description {
  color: var(--danger-dark) !important;
}

button.dropdown-link {
  width: 100%;
  text-align: left;
  background: none;
  border: none;
  cursor: pointer;
  font-family: inherit;
}

.dropdown-icon {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-light);
  font-size: var(--font-size-base);
  background-color: var(--background-light);
  border-radius: var(--radius-full);
  flex-shrink: 0;
}

.dropdown-link:hover .dropdown-icon {
  color: var(--primary-color);
  background-color: rgba(241, 202, 47, 0.1);
}

.dropdown-link-content {
  flex: 1;
  min-width: 0;
}

.dropdown-link-title {
  display: block;
  font-weight: var(--font-weight-medium);
  margin-bottom: 2px;
  color: inherit; /* Inherit color from parent link */
}

.dropdown-link-description {
  display: block;
  font-size: var(--font-size-xs);
  color: var(--text-muted) !important; /* Ensure proper muted text color */
}

/* Toggle Switch for Dark Mode */
.toggle-switch {
  position: relative;
  display: inline-block;
  width: 40px;
  height: 20px;
  flex-shrink: 0;
}

.toggle-input {
  opacity: 0;
  width: 0;
  height: 0;
}

.toggle-label {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ccc;
  transition: .4s;
  border-radius: 34px;
}

.toggle-label:before {
  position: absolute;
  content: "";
  height: 16px;
  width: 16px;
  left: 2px;
  bottom: 2px;
  background-color: white;
  transition: .4s;
  border-radius: 50%;
}

.toggle-input:checked + .toggle-label {
  background-color: var(--primary-color);
}

.toggle-input:checked + .toggle-label:before {
  transform: translateX(20px);
}

/* Dropdown Footer */
.dropdown-footer {
  padding: var(--spacing-3) var(--spacing-4);
  border-top: 1px solid var(--border-light);
  display: flex;
  flex-direction: column;
  gap: var(--spacing-2);
}

.dropdown-footer-link {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  color: var(--text-color);
  text-decoration: none;
  font-weight: var(--font-weight-medium);
  padding: var(--spacing-2) 0;
  transition: color var(--transition-fast) ease;
}

.dropdown-footer-link:hover {
  color: var(--primary-color);
}

.dropdown-footer-link i {
  font-size: var(--font-size-base);
}

.dropdown-footer-info {
  font-size: var(--font-size-xs);
  color: var(--text-light);
  text-align: center;
}

/* Dark Mode Styles */
.dark-mode .user-dropdown {
  background-color: var(--secondary-dark);
  border-color: var(--secondary-light);
}

.dark-mode .user-dropdown-header {
  background-color: rgba(255, 255, 255, 0.05);
  border-color: rgba(255, 255, 255, 0.1);
}

.dark-mode .user-dropdown-name {
  color: rgba(255, 255, 255, 0.9);
}

.dark-mode .user-dropdown-email {
  color: rgba(255, 255, 255, 0.6);
}

.dark-mode .dropdown-link {
  color: rgba(255, 255, 255, 0.8);
}

.dark-mode .dropdown-link:hover {
  background-color: rgba(255, 255, 255, 0.05);
}

.dark-mode .dropdown-icon {
  background-color: rgba(255, 255, 255, 0.1);
  color: rgba(255, 255, 255, 0.6);
}

.dark-mode .dropdown-link-description {
  color: rgba(255, 255, 255, 0.5);
}

.dark-mode .dropdown-section-title {
  color: rgba(255, 255, 255, 0.6);
}

.dark-mode .dropdown-footer {
  border-color: rgba(255, 255, 255, 0.1);
}

.dark-mode .dropdown-footer-info {
  color: rgba(255, 255, 255, 0.5);
}

/* Responsive Styles */
@media (max-width: 768px) {
  .user-dropdown {
    position: fixed !important;
    top: calc(var(--topbar-height) + 10px) !important; /* Position just below the topbar */
    right: 10px !important;
    width: 320px !important; /* Match desktop width */
    max-width: calc(100vw - 20px) !important;
    max-height: calc(100vh - var(--topbar-height) - 20px) !important; /* Ensure it doesn't exceed viewport */
    overflow-y: auto !important; /* Allow scrolling if content is too tall */
    box-shadow: var(--shadow-xl) !important;
    z-index: 9999 !important;
    transform: none !important;
    margin: 0 !important; /* Remove all margins */
    padding: 0 !important; /* Remove all padding */
    /* Match desktop styling exactly */
    background-color: var(--white) !important;
    border-radius: var(--radius-lg) !important;
    border: 1px solid var(--border-color) !important;
  }

  .user-dropdown.show {
    display: block !important;
    opacity: 1 !important;
    transform: none !important;
    z-index: 9999 !important;
  }

  /* FORCE NO GAPS - Override any framework styles */
  .user-dropdown .dropdown-menu {
    padding: 0 !important; /* Match desktop - no padding */
    margin: 0 !important; /* Match desktop - no margin */
    margin-top: 0 !important; /* Explicitly remove top margin */
    margin-bottom: 0 !important; /* Explicitly remove bottom margin */
    padding-top: 0 !important; /* Explicitly remove top padding */
    padding-bottom: 0 !important; /* Explicitly remove bottom padding */
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
    position: relative !important;
    z-index: 1 !important;
    border: none !important; /* Remove all borders */
    background: transparent !important; /* Remove any background that might create visual gaps */
    box-shadow: none !important; /* Remove any shadows */
    transform: none !important; /* Remove any transforms */
  }

  /* Remove any gap between header and first section */
  .user-dropdown .dropdown-section:first-child {
    margin-top: 0 !important;
    padding-top: 0 !important;
  }

  /* Ensure first dropdown item has no top margin */
  .user-dropdown .dropdown-item:first-child {
    margin-top: 0 !important;
    padding-top: 0 !important;
  }

  /* Remove any potential gap from the first section in dropdown menu */
  .user-dropdown .dropdown-menu > .dropdown-section:first-child,
  .user-dropdown .dropdown-menu > .dropdown-item:first-child {
    margin-top: 0 !important;
    padding-top: 0 !important;
  }

  /* AGGRESSIVE TARGETING - Force first section to have NO spacing */
  .user-dropdown .profile-menu-section,
  .user-dropdown .dropdown-section:first-child,
  .user-dropdown .dropdown-menu > div:first-child {
    margin: 0 !important;
    margin-top: 0 !important;
    margin-bottom: var(--spacing-3) !important; /* Keep bottom margin for section separation */
    padding: 0 !important;
    padding-top: 0 !important;
    padding-bottom: var(--spacing-3) !important; /* Keep bottom padding for section separation */
    border-top: none !important;
    position: relative !important;
    top: 0 !important;
  }

  /* Force first dropdown item to have no spacing */
  .user-dropdown .profile-menu-section .dropdown-item:first-child,
  .user-dropdown .dropdown-section:first-child .dropdown-item:first-child {
    margin: 0 !important;
    padding: 0 !important;
    border-top: none !important;
    position: relative !important;
    top: 0 !important;
  }

  /* Force first dropdown link to have proper padding but no extra spacing */
  .user-dropdown .profile-menu-section .dropdown-item:first-child .dropdown-link,
  .user-dropdown .dropdown-section:first-child .dropdown-item:first-child .dropdown-link {
    margin: 0 !important;
    margin-top: 0 !important;
    padding-top: var(--spacing-3) !important; /* Normal top padding */
    border-top: none !important;
    position: relative !important;
    top: 0 !important;
  }

  /* FORCE HEADER TO CONNECT SEAMLESSLY */
  .user-dropdown-header {
    padding: var(--spacing-4) !important; /* Match desktop padding */
    margin: 0 !important;
    margin-bottom: 0 !important; /* Ensure no bottom margin */
    padding-bottom: var(--spacing-4) !important; /* Ensure proper bottom padding */
    display: flex !important;
    align-items: center !important;
    gap: var(--spacing-3) !important;
    border-bottom: 1px solid var(--border-light) !important; /* Match desktop border */
    background-color: var(--background-light) !important; /* Match desktop background */
    visibility: visible !important;
    opacity: 1 !important;
    position: relative !important;
    z-index: 2 !important; /* Higher z-index */
    /* Force the header to connect to the next element */
    border-bottom-left-radius: 0 !important;
    border-bottom-right-radius: 0 !important;
  }

  /* FORCE DROPDOWN MENU TO CONNECT TO HEADER - NEGATIVE MARGIN */
  .user-dropdown-header + .dropdown-menu {
    margin-top: -1px !important; /* Negative margin to overlap border */
    padding-top: 0 !important;
    border-top: none !important;
    position: relative !important;
    top: 0 !important;
    z-index: 1 !important;
  }

  /* ADD LARGER MARGIN between header and first section */
  .user-dropdown .dropdown-menu > .dropdown-section:first-child,
  .user-dropdown .dropdown-menu > .profile-menu-section {
    margin-top: var(--spacing-4) !important; /* Larger margin between header and first section */
    padding-top: 0 !important;
    border-top: none !important;
    position: relative !important;
    z-index: 1 !important;
  }

  /* Force the dropdown-menu itself to have minimal spacing */
  .user-dropdown .user-dropdown-header + .dropdown-menu {
    margin: 0 !important;
    padding: 0 !important;
    margin-top: 0 !important; /* No overlap - allow natural spacing */
    border-top: none !important;
  }

  /* Reset nuclear option - allow natural spacing */
  .user-dropdown * {
    /* Remove the nuclear margin reset to allow natural spacing */
  }

  /* Maintain proper spacing for non-first elements */
  .user-dropdown .dropdown-section:not(:first-child) {
    margin-top: var(--spacing-3) !important;
  }

  /* Hide section titles on mobile to match desktop */
  .user-dropdown .dropdown-section-title {
    display: none !important;
    visibility: hidden !important;
  }

  /* Match desktop section styling exactly */
  .user-dropdown .dropdown-section {
    margin-bottom: var(--spacing-3) !important; /* Match desktop margin */
    border-bottom: 1px solid var(--border-color) !important; /* Match desktop border */
    padding-bottom: var(--spacing-3) !important; /* Match desktop padding */
  }

  .user-dropdown .dropdown-section:last-child {
    border-bottom: none !important;
    margin-bottom: 0 !important;
    padding-bottom: 0 !important;
  }

  /* Match desktop dropdown items exactly */
  .user-dropdown .dropdown-item {
    margin: 0 !important;
    padding: 0 !important;
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
    position: relative !important;
  }

  /* Fix dropdown links spacing - match desktop styling */
  .user-dropdown .dropdown-link {
    padding: var(--spacing-3) var(--spacing-4) !important; /* Match desktop padding */
    margin: 0 !important; /* Remove any margin */
    min-height: auto !important;
    display: flex !important;
    align-items: center !important;
    gap: var(--spacing-3) !important; /* Match desktop gap */
    line-height: 1.4 !important; /* Consistent line height */
    box-sizing: border-box !important;
    border: none !important; /* Remove any borders */
    border-left: 3px solid transparent !important; /* Match desktop border style */
    width: 100% !important;
    text-decoration: none !important;
    color: var(--text-dark) !important; /* Match desktop text color */
    background-color: transparent !important;
    transition: all var(--transition-fast) ease !important; /* Match desktop transition */
    font-size: var(--font-size-sm) !important; /* Match desktop font size */
  }

  /* Hover effect for dropdown links - match desktop */
  .user-dropdown .dropdown-link:hover {
    background-color: var(--background-light) !important;
    color: var(--primary-color) !important;
    border-left-color: var(--primary-color) !important;
  }

  /* Match desktop footer styling exactly */
  .user-dropdown .dropdown-footer {
    padding: var(--spacing-3) var(--spacing-4) !important; /* Match desktop padding */
    border-top: 1px solid var(--border-light) !important; /* Match desktop border */
    display: flex !important;
    flex-direction: column !important;
    gap: var(--spacing-2) !important;
    margin: 0 !important;
  }

  /* Match desktop toggle switch container styling */
  .user-dropdown .dropdown-item button.dropdown-link {
    width: 100% !important;
    text-align: left !important;
    background: none !important;
    border: none !important;
    cursor: pointer !important;
    font-family: inherit !important;
    justify-content: space-between !important;
    padding: var(--spacing-3) var(--spacing-4) !important; /* Match desktop padding */
    margin: 0 !important;
    min-height: auto !important;
    display: flex !important;
    align-items: center !important;
    gap: var(--spacing-3) !important;
    line-height: 1.4 !important;
    box-sizing: border-box !important;
    border-left: 3px solid transparent !important;
    color: var(--text-dark) !important;
    transition: all var(--transition-fast) ease !important;
    font-size: var(--font-size-sm) !important;
  }

  /* Match desktop toggle switch positioning */
  .user-dropdown .toggle-switch {
    position: relative !important;
    display: inline-block !important;
    width: 40px !important;
    height: 20px !important;
    flex-shrink: 0 !important;
  }

  /* Remove any extra spacing from dropdown content areas - match desktop */
  .user-dropdown .dropdown-link-content {
    flex: 1 !important;
    min-width: 0 !important;
  }

  /* Match desktop styling for link titles and descriptions */
  .user-dropdown .dropdown-link-title {
    display: block !important;
    font-weight: var(--font-weight-medium) !important;
    margin-bottom: 2px !important;
    color: inherit !important; /* Inherit color from parent link */
  }

  .user-dropdown .dropdown-link-description {
    display: block !important;
    font-size: var(--font-size-xs) !important;
    color: var(--text-muted) !important; /* Match desktop muted text color */
  }

  /* Fix dropdown icon spacing - match desktop styling */
  .user-dropdown .dropdown-icon {
    width: 24px !important; /* Match desktop icon size */
    height: 24px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    color: var(--text-light) !important;
    font-size: var(--font-size-base) !important;
    background-color: var(--background-light) !important;
    border-radius: var(--radius-full) !important;
    flex-shrink: 0 !important;
  }

  /* Match desktop icon hover effect */
  .user-dropdown .dropdown-link:hover .dropdown-icon {
    color: var(--primary-color) !important;
    background-color: rgba(241, 202, 47, 0.1) !important;
  }

  /* Special styling for logout link - match desktop */
  .user-dropdown .dropdown-link.logout-link {
    color: var(--danger-color) !important;
    border-left-color: transparent !important;
  }

  .user-dropdown .dropdown-link.logout-link:hover {
    background-color: rgba(220, 53, 69, 0.1) !important;
    color: var(--danger-dark) !important;
    border-left-color: var(--danger-color) !important;
  }

  .user-dropdown .dropdown-link.logout-link .dropdown-icon {
    background-color: rgba(220, 53, 69, 0.1) !important;
    color: var(--danger-color) !important;
  }

  .user-dropdown .dropdown-link.logout-link:hover .dropdown-icon {
    background-color: rgba(220, 53, 69, 0.2) !important;
    color: var(--danger-dark) !important;
  }

  .user-dropdown .dropdown-link.logout-link .dropdown-link-description {
    color: rgba(220, 53, 69, 0.7) !important;
  }

  .user-dropdown .dropdown-link.logout-link:hover .dropdown-link-description {
    color: var(--danger-dark) !important;
  }

  .topbar-user-menu {
    margin-left: auto;
  }

  .user-menu-toggle {
    padding: var(--spacing-1) var(--spacing-2);
  }
}

@media (max-width: 576px) {
  .user-dropdown {
    width: 280px !important;
    right: 5px !important;
  }

  /* Keep desktop styling for very small screens - only adjust width */
  .user-dropdown-avatar {
    width: 50px !important;
    height: 50px !important;
  }
}
