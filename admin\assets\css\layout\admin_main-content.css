/**
 * Admin Main Content CSS
 *
 * This file contains styles for the main content area of the admin panel.
 */

/* Main Content Container */
.admin-main {
  margin-left: var(--sidebar-width);
  padding-top: var(--topbar-height);
  min-height: 100vh;
  background-color: var(--background-color);
  transition: margin-left var(--transition-normal) ease, padding-top var(--transition-normal) ease;
}

body.sidebar-collapsed .admin-main {
  margin-left: var(--sidebar-collapsed-width);
}

/* Expanded main content when sidebar is collapsed */
.admin-main.expanded {
  margin-left: var(--sidebar-collapsed-width);
}

/* Content Container */
.admin-content {
  padding: var(--spacing-6);
  max-width: var(--content-max-width);
  margin: 0 auto;
}

/* Admin Container */
.admin-container {
  padding: var(--spacing-4);
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
  margin-top: 0; /* Remove any top margin to eliminate gap */
}

/* Admin Content Grid */
.admin-content-grid {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: var(--spacing-4);
}

/* Admin Form Elements */
.admin-form .form-group {
  margin-bottom: var(--spacing-4);
}

.admin-form .form-label {
  display: block;
  margin-bottom: var(--spacing-2);
  font-weight: var(--font-weight-medium);
  color: var(--text-dark);
}

.admin-form .form-text {
  font-size: var(--font-size-xs);
  color: var(--text-light);
  margin-top: var(--spacing-1);
}

.admin-form .input-group {
  display: flex;
  align-items: stretch;
}

.admin-form .input-group-text {
  display: flex;
  align-items: center;
  padding: 0 var(--spacing-2);
  background-color: var(--background-light);
  border: 1px solid var(--border-color);
  border-right: none;
  border-radius: var(--radius-md) 0 0 var(--radius-md);
  color: var(--text-light);
  font-size: var(--font-size-sm);
}

.admin-form .input-group .form-control {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
  flex: 1;
}

/* Image Preview Container */
.image-preview-container {
  margin-bottom: var(--spacing-3);
}

.image-preview {
  width: 100%;
  height: 200px;
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  overflow: hidden;
  background-color: var(--background-light);
  margin-bottom: var(--spacing-2);
  display: flex;
  align-items: center;
  justify-content: center;
}

.image-preview img {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
}

.image-preview-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: var(--text-light);
  height: 100%;
  width: 100%;
  text-align: center;
}

.image-preview-empty i {
  font-size: 3rem;
  margin-bottom: var(--spacing-2);
  opacity: 0.5;
}

.image-preview-empty p {
  margin: 0;
  font-size: var(--font-size-sm);
}

/* Custom File Upload */
.custom-file-upload {
  position: relative;
  display: inline-block;
  width: 100%;
}

.custom-file-input {
  position: absolute;
  left: 0;
  top: 0;
  opacity: 0;
  width: 100%;
  height: 100%;
  cursor: pointer;
  z-index: 2;
}

.custom-file-label {
  display: inline-block;
  padding: var(--spacing-2) var(--spacing-3);
  background-color: var(--background-light);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  cursor: pointer;
  width: 100%;
  text-align: center;
  transition: all var(--transition-fast) ease;
}

.custom-file-input:hover + .custom-file-label {
  background-color: var(--background-hover);
  border-color: var(--border-hover);
}

/* Card Meta */
.admin-card-meta {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-3);
  font-size: var(--font-size-xs);
  color: var(--text-light);
}

.admin-card-meta-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-1);
}

.admin-card-meta-item i {
  font-size: var(--font-size-sm);
  color: var(--primary-color);
}

/* Dashboard Container */
.dashboard-container {
  padding: var(--spacing-4);
  max-width: 100%;
}

/* Content Header */
.admin-content-header {
  margin-bottom: var(--spacing-8); /* Increased margin below header */
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-wrap: wrap;
  gap: var(--spacing-4);
  position: relative; /* Added for positioning context */
}

.admin-content-title-group {
  display: flex;
  flex-direction: column;
}

.admin-content-title {
  margin: 0;
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-semibold);
  color: var(--text-dark);
  display: block;
  width: auto;
  max-width: 100%;
}

.admin-content-subtitle {
  margin: var(--spacing-1) 0 0;
  font-size: var(--font-size-base);
  color: var(--text-light);
}

.admin-content-actions {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  margin-left: auto; /* Push to the right */
}

/* Content Section */
.admin-content-section {
  margin-bottom: var(--spacing-6);
}

.admin-section-header {
  margin-bottom: var(--spacing-4);
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-wrap: wrap;
  gap: var(--spacing-2);
}

.admin-section-title {
  margin: 0;
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  color: var(--text-dark);
}

.admin-section-subtitle {
  margin: var(--spacing-1) 0 0;
  font-size: var(--font-size-sm);
  color: var(--text-light);
}

.admin-section-actions {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
}

/* Content Card */
.admin-card {
  background-color: var(--white);
  border-radius: var(--card-border-radius);
  box-shadow: var(--shadow-sm);
  margin-bottom: var(--spacing-4);
  overflow: hidden;
  transition: box-shadow var(--transition-fast) ease, transform var(--transition-fast) ease;
  border: 1px solid var(--border-color);
}

.admin-card:hover {
  box-shadow: var(--shadow-md);
}

.admin-card-header {
  padding: var(--spacing-4);
  border-bottom: 1px solid var(--border-color);
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-wrap: wrap;
  gap: var(--spacing-2);
}

.admin-card-title {
  margin: 0;
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--text-dark);
  display: block;
  width: auto;
  max-width: 100%;
}

.admin-card-subtitle {
  margin: var(--spacing-1) 0 0;
  font-size: var(--font-size-sm);
  color: var(--text-light);
}

.admin-card-actions {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
}

.admin-card-body {
  padding: var(--spacing-4);
}

.admin-card-footer {
  padding: var(--spacing-4);
  border-top: 1px solid var(--border-color);
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-wrap: wrap;
  gap: var(--spacing-2);
}

/* Content Grid */
.admin-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: var(--spacing-4);
}

/* Dashboard Stats */
.admin-stats {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: var(--spacing-4);
  margin-bottom: var(--spacing-6);
}

.admin-stat-card {
  background-color: var(--white);
  border-radius: var(--card-border-radius);
  box-shadow: var(--shadow-sm);
  padding: var(--spacing-4);
  display: flex;
  flex-direction: column;
  border: 1px solid var(--border-color);
  transition: box-shadow var(--transition-fast) ease, transform var(--transition-fast) ease;
}

.admin-stat-card:hover {
  box-shadow: var(--shadow-md);
  transform: translateY(-2px);
}

.admin-stat-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--spacing-2);
}

.admin-stat-title {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--text-light);
  margin: 0;
}

.admin-stat-icon {
  width: 40px;
  height: 40px;
  border-radius: var(--radius-md);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--font-size-xl);
}

.admin-stat-icon.primary {
  background-color: var(--primary-very-light);
  color: var(--primary-color);
}

.admin-stat-icon.success {
  background-color: rgba(16, 185, 129, 0.1);
  color: var(--success-color);
}

.admin-stat-icon.warning {
  background-color: rgba(245, 158, 11, 0.1);
  color: var(--warning-color);
}

.admin-stat-icon.danger {
  background-color: rgba(239, 68, 68, 0.1);
  color: var(--danger-color);
}

.admin-stat-value {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-bold);
  color: var(--text-dark);
  margin: var(--spacing-1) 0;
}

.admin-stat-change {
  font-size: var(--font-size-xs);
  display: flex;
  align-items: center;
  gap: var(--spacing-1);
}

.admin-stat-change.positive {
  color: var(--success-color);
}

.admin-stat-change.negative {
  color: var(--danger-color);
}

/* Quick Actions */
.admin-quick-actions {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: var(--spacing-4);
  margin-bottom: var(--spacing-6);
}

.admin-quick-action {
  background-color: var(--white);
  border-radius: var(--card-border-radius);
  box-shadow: var(--shadow-sm);
  padding: var(--spacing-4);
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  border: 1px solid var(--border-color);
  transition: all var(--transition-fast) ease;
  text-decoration: none;
}

.admin-quick-action:hover {
  box-shadow: var(--shadow-md);
  transform: translateY(-2px);
  border-color: var(--primary-color);
}

.admin-quick-action-icon {
  width: 48px;
  height: 48px;
  border-radius: var(--radius-md);
  background-color: var(--primary-very-light);
  color: var(--primary-color);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--font-size-xl);
  margin-bottom: var(--spacing-3);
}

.admin-quick-action-title {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--text-dark);
  margin: 0 0 var(--spacing-1);
}

.admin-quick-action-description {
  font-size: var(--font-size-xs);
  color: var(--text-light);
  margin: 0;
}

/* Recent Activity */
.admin-activity {
  margin-bottom: var(--spacing-6);
}

.admin-activity-item {
  display: flex;
  align-items: flex-start;
  gap: var(--spacing-3);
  padding: var(--spacing-3) 0;
  border-bottom: 1px solid var(--border-light);
}

.admin-activity-item:last-child {
  border-bottom: none;
}

.admin-activity-icon {
  width: 36px;
  height: 36px;
  border-radius: var(--radius-full);
  background-color: var(--primary-very-light);
  color: var(--primary-color);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--font-size-base);
  flex-shrink: 0;
}

.admin-activity-content {
  flex: 1;
}

.admin-activity-title {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  margin-bottom: var(--spacing-1);
  color: var(--text-color);
}

.admin-activity-text {
  font-size: var(--font-size-xs);
  color: var(--text-light);
  margin-bottom: var(--spacing-1);
}

.admin-activity-time {
  font-size: var(--font-size-xs);
  color: var(--text-muted);
}

/* Responsive Styles */
@media (max-width: 1200px) {
  .admin-stats {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 992px) {
  .admin-content {
    padding: var(--spacing-4);
  }
}

@media (max-width: 768px) {
  /* Main content container */
  .admin-main {
    margin-left: 0 !important;
    margin-right: 0 !important;
    padding-top: calc(var(--topbar-height) * 2); /* Double height for two-row topbar */
    margin-top: 0 !important; /* Remove any margin to eliminate gap */
    width: 100% !important;
    max-width: 100% !important;
    box-sizing: border-box !important;
    overflow-x: hidden !important; /* Prevent horizontal scrolling */
  }

  body.sidebar-collapsed .admin-main,
  .admin-main.expanded {
    margin-left: 0 !important;
    margin-right: 0 !important;
    width: 100% !important;
  }

  /* Content containers */
  .admin-content {
    padding: var(--spacing-3) !important;
    max-width: 100% !important;
    width: 100% !important;
    box-sizing: border-box !important;
    overflow-x: hidden !important;
  }

  .dashboard-container {
    padding: var(--spacing-2) !important;
    max-width: 100% !important;
    width: 100% !important;
    box-sizing: border-box !important;
  }

  /* Headers and action areas */
  .admin-content-header,
  .admin-section-header,
  .admin-card-header,
  .admin-card-footer {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-2);
  }

  .admin-content-actions,
  .admin-section-actions,
  .admin-card-actions {
    width: 100%;
    justify-content: flex-start;
    margin-top: var(--spacing-2);
    flex-wrap: wrap;
  }

  /* Make buttons in action areas take full width */
  .admin-content-actions .admin-btn,
  .admin-section-actions .admin-btn,
  .admin-card-actions .admin-btn {
    width: 100%;
    margin-bottom: var(--spacing-2);
    text-align: center;
  }

  /* Stats and grid layouts */
  .admin-stats,
  .admin-grid,
  .admin-content-grid {
    grid-template-columns: 1fr;
    gap: var(--spacing-3);
  }

  /* Quick actions */
  .admin-quick-actions {
    grid-template-columns: repeat(2, 1fr);
    gap: var(--spacing-3);
  }

  /* Form elements in mobile view */
  .admin-form .input-group {
    flex-direction: column;
  }

  .admin-form .input-group-text {
    width: 100%;
    border-right: 1px solid var(--border-color);
    border-bottom: none;
    border-radius: var(--radius-md) var(--radius-md) 0 0;
    padding: var(--spacing-2);
    justify-content: center;
  }

  .admin-form .input-group .form-control {
    border-radius: 0 0 var(--radius-md) var(--radius-md);
  }

  /* Card meta in mobile view */
  .admin-card-meta {
    margin-top: var(--spacing-2);
    width: 100%;
  }

  /* Content header adjustments */
  .admin-content-header {
    flex-direction: row;
    flex-wrap: wrap;
    align-items: center;
    justify-content: space-between;
    gap: var(--spacing-3);
  }

  .admin-content-title-group {
    flex: 1;
    min-width: 60%;
  }

  .admin-content-actions {
    flex-wrap: nowrap;
    justify-content: flex-end;
    margin-left: auto;
  }

  /* Typography adjustments */
  .admin-content-title {
    font-size: var(--font-size-xl);
    width: auto;
    max-width: 100%;
    display: block;
  }

  .admin-section-title {
    font-size: var(--font-size-lg);
    width: auto;
    max-width: 100%;
    display: block;
  }

  /* Activity items */
  .admin-activity-item {
    padding: var(--spacing-2) 0;
  }

  /* Card adjustments */
  .admin-card {
    margin-bottom: var(--spacing-3);
  }

  .admin-card-body {
    padding: var(--spacing-3);
  }
}

@media (max-width: 576px) {
  /* Reduce padding for small screens */
  .admin-content {
    padding: var(--spacing-2) !important;
  }

  .dashboard-container {
    padding: var(--spacing-1) !important;
  }

  /* Stack quick actions in a single column */
  .admin-quick-actions {
    grid-template-columns: 1fr !important;
  }

  /* Reduce font sizes */
  .admin-content-title {
    font-size: var(--font-size-lg) !important;
  }

  .admin-section-title {
    font-size: var(--font-size-base) !important;
  }

  .admin-content-subtitle,
  .admin-section-subtitle {
    font-size: var(--font-size-xs) !important;
  }

  /* Reduce card padding */
  .admin-card-header,
  .admin-card-body,
  .admin-card-footer {
    padding: var(--spacing-3) !important;
  }

  .admin-card-title {
    font-size: var(--font-size-base) !important;
  }

  /* Adjust stat cards */
  .admin-stat-card {
    padding: var(--spacing-3) !important;
  }

  .admin-stat-value {
    font-size: var(--font-size-xl) !important;
  }

  .admin-stat-icon {
    width: 32px !important;
    height: 32px !important;
    font-size: var(--font-size-lg) !important;
  }

  /* Adjust activity items */
  .admin-activity-icon {
    width: 32px !important;
    height: 32px !important;
  }

  .admin-activity-title {
    font-size: var(--font-size-xs) !important;
  }

  .admin-activity-text,
  .admin-activity-time {
    font-size: 11px !important;
  }
}

/* Extra small devices */
@media (max-width: 375px) {
  /* Further reduce padding */
  .admin-content {
    padding: var(--spacing-1) !important;
  }

  .admin-card-header,
  .admin-card-body,
  .admin-card-footer {
    padding: var(--spacing-2) !important;
  }

  /* Adjust spacing */
  .admin-content-header {
    margin-bottom: var(--spacing-4) !important;
    flex-direction: row !important;
    flex-wrap: wrap !important;
  }

  .admin-section-header {
    margin-bottom: var(--spacing-3) !important;
  }

  .admin-content-section {
    margin-bottom: var(--spacing-4) !important;
  }

  /* Reduce icon sizes */
  .admin-stat-icon,
  .admin-activity-icon,
  .admin-quick-action-icon {
    width: 28px !important;
    height: 28px !important;
    font-size: var(--font-size-base) !important;
  }
}
