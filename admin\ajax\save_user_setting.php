<?php
/**
 * Save User Setting AJAX Handler
 *
 * This script saves a user setting to the database.
 */

// Start output buffering to prevent any output before JSON
ob_start();

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Set proper JSON headers
header('Content-Type: application/json');
header('Cache-Control: no-store, no-cache, must-revalidate, max-age=0');
header('Cache-Control: post-check=0, pre-check=0', false);
header('Pragma: no-cache');

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    echo json_encode([
        'success' => false,
        'message' => 'You must be logged in to perform this action'
    ]);
    exit;
}

// Include database connection
require_once '../config.php';

// Get user ID from session
$user_id = $_SESSION['user_id'];

// Get setting name and value from POST data
$setting = isset($_POST['setting']) ? $_POST['setting'] : '';
$value = isset($_POST['value']) ? $_POST['value'] : '';

// Validate input
if (empty($setting)) {
    echo json_encode([
        'success' => false,
        'message' => 'Setting name is required'
    ]);
    exit;
}

// Sanitize input
$setting = $conn->real_escape_string($setting);
$value = $conn->real_escape_string($value);

// Check if user_settings table exists
$check_table_sql = "SHOW TABLES LIKE 'user_settings'";
$check_table_result = $conn->query($check_table_sql);

if ($check_table_result->num_rows === 0) {
    // Create user_settings table if it doesn't exist
    $create_table_sql = "CREATE TABLE user_settings (
        id INT(11) NOT NULL AUTO_INCREMENT,
        user_id INT(11) NOT NULL,
        setting_key VARCHAR(50) NOT NULL,
        setting_value TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        PRIMARY KEY (id),
        UNIQUE KEY user_setting (user_id, setting_key)
    )";

    if (!$conn->query($create_table_sql)) {
        echo json_encode([
            'success' => false,
            'message' => 'Failed to create user_settings table: ' . $conn->error
        ]);
        exit;
    }
}

// Special handling for theme setting
if ($setting === 'theme') {
    // Also update the theme column in users table if it exists
    $check_theme_column = $conn->query("SHOW COLUMNS FROM users LIKE 'theme'");
    $theme_column_exists = ($check_theme_column && $check_theme_column->num_rows > 0);

    if ($theme_column_exists) {
        $update_user_sql = "UPDATE users SET theme = '$value' WHERE id = $user_id";
        $conn->query($update_user_sql);
        // We don't check for errors here as this is just a secondary update
    }
}

// Check if setting already exists for this user
$check_sql = "SELECT id FROM user_settings WHERE user_id = $user_id AND setting_key = '$setting'";
$check_result = $conn->query($check_sql);

if ($check_result->num_rows > 0) {
    // Update existing setting
    $update_sql = "UPDATE user_settings SET setting_value = '$value' WHERE user_id = $user_id AND setting_key = '$setting'";

    if ($conn->query($update_sql)) {
        echo json_encode([
            'success' => true,
            'message' => 'Setting updated successfully',
            'setting' => $setting,
            'value' => $value
        ], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
    } else {
        echo json_encode([
            'success' => false,
            'message' => 'Failed to update setting: ' . $conn->error
        ], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
    }
} else {
    // Insert new setting
    $insert_sql = "INSERT INTO user_settings (user_id, setting_key, setting_value) VALUES ($user_id, '$setting', '$value')";

    if ($conn->query($insert_sql)) {
        echo json_encode([
            'success' => true,
            'message' => 'Setting saved successfully',
            'setting' => $setting,
            'value' => $value
        ], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
    } else {
        echo json_encode([
            'success' => false,
            'message' => 'Failed to save setting: ' . $conn->error
        ], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
    }
}

// Check for JSON encoding errors
if (json_last_error() !== JSON_ERROR_NONE) {
    // Clear any previous output
    ob_clean();

    // Return a simplified error response
    echo json_encode([
        'success' => false,
        'message' => 'JSON encoding error: ' . json_last_error_msg()
    ], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
}

// End output buffering and flush
ob_end_flush();
exit;
