<?php
/**
 * User Management Page
 *
 * This page handles both adding new users and editing existing ones.
 */

// Include necessary files
session_start();
require_once 'config.php';
require_once 'includes/admin-functions.php';

// Check if user is logged in and has permission
require_login();

// Initialize variables
$page_title = "Add New User";
$page_icon = "fas fa-user-plus";
$page_subtitle = "Create a new user account";
$user_id = 0;
$username = '';
$email = '';
$is_admin = 0;
$is_verified = 1;
$role_id = 0;
$error_message = '';
$success_message = '';
$is_edit_mode = false;

// Get available roles
$available_roles = [];
$roles_query = "SELECT id, name FROM roles ORDER BY name";
$roles_result = $conn->query($roles_query);
if ($roles_result && $roles_result->num_rows > 0) {
    while ($role = $roles_result->fetch_assoc()) {
        $available_roles[$role['id']] = $role['name'];
    }
}

// Check if we're in edit mode
if (isset($_GET['id']) && is_numeric($_GET['id'])) {
    $user_id = (int)$_GET['id'];
    $is_edit_mode = true;
    $page_title = "Edit User";
    $page_icon = "fas fa-user-edit";
    $page_subtitle = "Modify an existing user account";

    // Get user data
    $stmt = $conn->prepare("SELECT u.id, u.username, u.email, u.is_admin, u.is_verified FROM users u WHERE u.id = ?");
    $stmt->bind_param("i", $user_id);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result->num_rows === 0) {
        // User not found, redirect to users list
        header("Location: users.php?error=User+not+found");
        exit;
    }

    $user = $result->fetch_assoc();
    $username = $user['username'];
    $email = $user['email'];
    $is_admin = $user['is_admin'];
    $is_verified = $user['is_verified'];

    // Get user's current role
    $role_stmt = $conn->prepare("SELECT role_id FROM user_roles WHERE user_id = ?");
    $role_stmt->bind_param("i", $user_id);
    $role_stmt->execute();
    $role_result = $role_stmt->get_result();
    $role_id = 0;
    if ($role_result->num_rows > 0) {
        $role_row = $role_result->fetch_assoc();
        $role_id = $role_row['role_id'];
    }
}

// Process form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Get form data
    $username = trim($_POST['username']);
    $email = trim($_POST['email']);
    $password = trim($_POST['password']);
    $confirm_password = trim($_POST['confirm_password']);
    $is_admin = isset($_POST['is_admin']) ? 1 : 0;
    $is_verified = isset($_POST['is_verified']) ? 1 : 0;
    $role_id = isset($_POST['role_id']) ? (int)$_POST['role_id'] : 0;

    // Validate form data
    if (empty($username) || empty($email)) {
        $error_message = "Username and email are required";
    } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $error_message = "Invalid email format";
    } elseif (!$is_edit_mode && empty($password)) {
        $error_message = "Password is required for new users";
    } elseif (!empty($password) && $password !== $confirm_password) {
        $error_message = "Passwords do not match";
    } else {
        // Check if username is already in use by another user
        $check_sql = "SELECT id FROM users WHERE username = ? AND id != ?";
        $check_stmt = $conn->prepare($check_sql);
        $check_stmt->bind_param("si", $username, $user_id);
        $check_stmt->execute();
        $check_result = $check_stmt->get_result();

        if ($check_result->num_rows > 0) {
            $error_message = "Username is already in use. Please choose a different one.";
        } else {
            // Check if email is already in use by another user
            $check_sql = "SELECT id FROM users WHERE email = ? AND id != ?";
            $check_stmt = $conn->prepare($check_sql);
            $check_stmt->bind_param("si", $email, $user_id);
            $check_stmt->execute();
            $check_result = $check_stmt->get_result();

            if ($check_result->num_rows > 0) {
                $error_message = "Email is already in use. Please choose a different one.";
            } else {
                // Save user
                if ($is_edit_mode) {
                    // Start transaction
                    $conn->begin_transaction();

                    try {
                        // Update existing user
                        if (!empty($password)) {
                            // Update with new password
                            $password_hash = password_hash($password, PASSWORD_DEFAULT);
                            $update_sql = "UPDATE users SET username = ?, email = ?, password = ?, is_admin = ?, is_verified = ? WHERE id = ?";
                            $update_stmt = $conn->prepare($update_sql);
                            $update_stmt->bind_param("sssiii", $username, $email, $password_hash, $is_admin, $is_verified, $user_id);
                        } else {
                            // Update without changing password
                            $update_sql = "UPDATE users SET username = ?, email = ?, is_admin = ?, is_verified = ? WHERE id = ?";
                            $update_stmt = $conn->prepare($update_sql);
                            $update_stmt->bind_param("ssiii", $username, $email, $is_admin, $is_verified, $user_id);
                        }

                        if (!$update_stmt->execute()) {
                            throw new Exception("Error updating user: " . $conn->error);
                        }

                        // Update user role
                        if ($role_id > 0) {
                            // Delete existing roles
                            $delete_role_sql = "DELETE FROM user_roles WHERE user_id = ?";
                            $delete_stmt = $conn->prepare($delete_role_sql);
                            $delete_stmt->bind_param("i", $user_id);
                            $delete_stmt->execute();

                            // Insert new role
                            $insert_role_sql = "INSERT INTO user_roles (user_id, role_id) VALUES (?, ?)";
                            $insert_role_stmt = $conn->prepare($insert_role_sql);
                            $insert_role_stmt->bind_param("ii", $user_id, $role_id);
                            if (!$insert_role_stmt->execute()) {
                                throw new Exception("Error updating user role: " . $conn->error);
                            }
                        }

                        $conn->commit();
                        $success_message = "User updated successfully";
                    } catch (Exception $e) {
                        $conn->rollback();
                        $error_message = $e->getMessage();
                    }
                } else {
                    // Start transaction
                    $conn->begin_transaction();

                    try {
                        // Insert new user
                        $password_hash = password_hash($password, PASSWORD_DEFAULT);
                        $insert_sql = "INSERT INTO users (username, email, password, is_admin, is_verified, created_at) VALUES (?, ?, ?, ?, ?, NOW())";
                        $insert_stmt = $conn->prepare($insert_sql);
                        $insert_stmt->bind_param("sssii", $username, $email, $password_hash, $is_admin, $is_verified);

                        if (!$insert_stmt->execute()) {
                            throw new Exception("Error creating user: " . $conn->error);
                        }

                        $user_id = $conn->insert_id;

                        // Assign role to user
                        if ($role_id > 0) {
                            $role_sql = "INSERT INTO user_roles (user_id, role_id) VALUES (?, ?)";
                            $role_stmt = $conn->prepare($role_sql);
                            $role_stmt->bind_param("ii", $user_id, $role_id);
                            if (!$role_stmt->execute()) {
                                throw new Exception("Error assigning role to user: " . $conn->error);
                            }
                        }

                        $conn->commit();
                        $success_message = "User created successfully";

                        // Clear form for new entry
                        if (!isset($_POST['save_and_continue'])) {
                            $username = '';
                            $email = '';
                            $is_admin = 0;
                            $is_verified = 1;
                            $role_id = 0;
                        } else {
                            // Switch to edit mode
                            $is_edit_mode = true;
                            $page_title = "Edit User";
                            $page_icon = "fas fa-user-edit";
                            $page_subtitle = "Modify an existing user account";
                        }
                    } catch (Exception $e) {
                        $conn->rollback();
                        $error_message = $e->getMessage();
                    }
                }
            }
        }
    }
}

// Include header
include 'includes/header.php';
?>

<div class="admin-container">
    <div class="admin-content-header">
        <div class="admin-content-title-group">
            <h2 class="admin-content-title"><i class="<?php echo $page_icon; ?>"></i> <?php echo $page_title; ?></h2>
            <p class="admin-content-subtitle"><?php echo $page_subtitle; ?></p>
        </div>
        <div class="admin-content-actions">
            <a href="users.php" class="admin-btn">
                <i class="fas fa-arrow-left"></i> Back to Users
            </a>
        </div>
    </div>

    <?php if (!empty($success_message)): ?>
    <div class="admin-alert success">
        <i class="fas fa-check-circle"></i> <?php echo $success_message; ?>
    </div>
    <?php endif; ?>

    <?php if (!empty($error_message)): ?>
    <div class="admin-alert error">
        <i class="fas fa-exclamation-circle"></i> <?php echo $error_message; ?>
    </div>
    <?php endif; ?>

    <div class="admin-content-body">
        <div class="admin-card">
            <div class="admin-card-header">
                <h3 class="admin-card-title">User Information</h3>
            </div>
            <div class="admin-card-body">
                <form class="admin-form" method="post" action="">
                    <input type="hidden" name="id" value="<?php echo $user_id; ?>">

                    <div class="form-group">
                        <label for="username">Username <span class="required">*</span></label>
                        <input type="text" id="username" name="username" value="<?php echo htmlspecialchars($username); ?>" required>
                        <p class="form-hint">The username used to log in</p>
                    </div>

                    <div class="form-group">
                        <label for="email">Email <span class="required">*</span></label>
                        <input type="email" id="email" name="email" value="<?php echo htmlspecialchars($email); ?>" required>
                        <p class="form-hint">The user's email address</p>
                    </div>

                    <div class="form-group">
                        <label for="password"><?php echo $is_edit_mode ? 'New Password' : 'Password'; ?> <?php echo $is_edit_mode ? '' : '<span class="required">*</span>'; ?></label>
                        <input type="password" id="password" name="password" <?php echo $is_edit_mode ? '' : 'required'; ?>>
                        <p class="form-hint"><?php echo $is_edit_mode ? 'Leave blank to keep current password' : 'The password for the account'; ?></p>
                    </div>

                    <div class="form-group">
                        <label for="confirm_password">Confirm Password</label>
                        <input type="password" id="confirm_password" name="confirm_password">
                        <p class="form-hint">Confirm the password</p>
                    </div>

                    <div class="form-group">
                        <label for="role_id">User Role <span class="required">*</span></label>
                        <select id="role_id" name="role_id" required>
                            <option value="">-- Select a role --</option>
                            <?php foreach ($available_roles as $id => $name): ?>
                            <option value="<?php echo $id; ?>" <?php echo $role_id == $id ? 'selected' : ''; ?>>
                                <?php echo htmlspecialchars($name); ?>
                            </option>
                            <?php endforeach; ?>
                        </select>
                        <p class="form-hint">Select the appropriate role for this user</p>
                    </div>

                    <div class="form-group">
                        <label class="checkbox-label">
                            <input type="checkbox" name="is_admin" value="1" <?php echo $is_admin ? 'checked' : ''; ?>>
                            Administrator
                        </label>
                        <p class="form-hint">Grant administrative privileges to this user</p>
                    </div>

                    <div class="form-group">
                        <label class="checkbox-label">
                            <input type="checkbox" name="is_verified" value="1" <?php echo $is_verified ? 'checked' : ''; ?>>
                            Verified
                        </label>
                        <p class="form-hint">Mark the account as verified (can log in immediately)</p>
                    </div>

                    <div class="form-actions">
                        <?php if ($is_edit_mode): ?>
                        <button type="submit" class="admin-btn">
                            <i class="fas fa-save"></i> Update User
                        </button>
                        <?php else: ?>
                        <button type="submit" class="admin-btn">
                            <i class="fas fa-user-plus"></i> Add User
                        </button>
                        <button type="submit" name="save_and_continue" class="admin-btn">
                            <i class="fas fa-save"></i> Save & Continue Editing
                        </button>
                        <?php endif; ?>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Password validation
        const passwordInput = document.getElementById('password');
        const confirmPasswordInput = document.getElementById('confirm_password');

        if (passwordInput && confirmPasswordInput) {
            confirmPasswordInput.addEventListener('input', function() {
                if (passwordInput.value !== this.value) {
                    this.setCustomValidity('Passwords do not match');
                } else {
                    this.setCustomValidity('');
                }
            });

            passwordInput.addEventListener('input', function() {
                if (confirmPasswordInput.value && confirmPasswordInput.value !== this.value) {
                    confirmPasswordInput.setCustomValidity('Passwords do not match');
                } else {
                    confirmPasswordInput.setCustomValidity('');
                }
            });
        }

        // Auto-dismiss alerts after 5 seconds
        const alerts = document.querySelectorAll('.admin-alert');
        alerts.forEach(function(alert) {
            setTimeout(function() {
                alert.style.opacity = '0';
                setTimeout(function() {
                    alert.style.display = 'none';
                }, 500);
            }, 5000);
        });
    });
</script>

<?php include 'includes/footer.php'; ?>
