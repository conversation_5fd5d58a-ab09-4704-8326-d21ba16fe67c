<?php
/**
 * FrontendSettings class
 *
 * Helper class to retrieve and apply settings in the frontend
 */
class FrontendSettings {
    private $conn;
    private $settings = [];
    private $cache_enabled = true;
    private $cache_duration = 3600; // 1 hour

    /**
     * Constructor
     *
     * @param object $conn Database connection
     */
    public function __construct($conn) {
        $this->conn = $conn;
        $this->loadSettings();
    }

    /**
     * Load all settings from database
     */
    private function loadSettings() {
        // Check if we have cached settings
        if ($this->cache_enabled && isset($_SESSION['frontend_settings_cache']) && isset($_SESSION['frontend_settings_cache_time'])) {
            $cache_age = time() - $_SESSION['frontend_settings_cache_time'];
            if ($cache_age < $this->cache_duration) {
                $this->settings = $_SESSION['frontend_settings_cache'];
                return;
            }
        }

        // Load settings from database
        $sql = "SELECT * FROM system_settings ORDER BY category, order_index";
        $result = $this->conn->query($sql);

        if ($result && $result->num_rows > 0) {
            while ($row = $result->fetch_assoc()) {
                $category = $row['category'];
                $key = $row['setting_key'];

                if (!isset($this->settings[$category])) {
                    $this->settings[$category] = [];
                }

                $this->settings[$category][$key] = $row;
            }

            // Cache settings
            if ($this->cache_enabled) {
                $_SESSION['frontend_settings_cache'] = $this->settings;
                $_SESSION['frontend_settings_cache_time'] = time();
            }
        }
    }

    /**
     * Get a specific setting value
     *
     * @param string $category Category name
     * @param string $key Setting key
     * @param mixed $default Default value if setting not found
     * @return mixed Setting value
     */
    public function getSetting($category, $key, $default = null) {
        if (isset($this->settings[$category][$key])) {
            return $this->settings[$category][$key]['setting_value'];
        }

        return $default;
    }

    /**
     * Get all settings for a category
     *
     * @param string $category Category name
     * @return array Settings for the category
     */
    public function getCategorySettings($category) {
        if (isset($this->settings[$category])) {
            return $this->settings[$category];
        }

        return [];
    }

    /**
     * Get news settings for frontend display
     *
     * @return array News settings
     */
    public function getNewsSettings() {
        // First check if frontend_news category exists and has settings
        if (isset($this->settings['frontend_news']) && !empty($this->settings['frontend_news'])) {
            $news_settings = [
                'news_per_page' => (int)$this->getSetting('frontend_news', 'frontend_news_per_page', 9),
                'show_news_date' => (bool)$this->getSetting('frontend_news', 'frontend_show_news_date', true),
                'show_news_category' => (bool)$this->getSetting('frontend_news', 'frontend_show_news_category', true),
                'news_excerpt_length' => (int)$this->getSetting('frontend_news', 'frontend_news_excerpt_length', 150),
                'news_image_width' => (int)$this->getSetting('frontend_news', 'frontend_news_image_width', 400),
                'pagination_position' => $this->getSetting('frontend_news', 'frontend_pagination_position', 'bottom'),
                'default_sort_order' => $this->getSetting('frontend_news', 'frontend_default_sort_order', 'newest'),
                'news_meta_title' => $this->getSetting('frontend_news', 'news_meta_title', 'Latest News'),
                'news_meta_description' => $this->getSetting('frontend_news', 'news_meta_description', 'Read the latest news and updates')
            ];

            // Parse items_per_page_options
            $items_per_page_options = $this->getSetting('frontend_news', 'frontend_items_per_page_options', '9,18,27,36');
            $news_settings['items_per_page_options'] = explode(',', $items_per_page_options);

            return $news_settings;
        }

        // Fallback to legacy news category
        $news_settings = [
            'news_per_page' => (int)$this->getSetting('news', 'news_per_page', 9),
            'show_news_date' => (bool)$this->getSetting('news', 'show_news_date', true),
            'show_news_category' => (bool)$this->getSetting('news', 'show_news_category', true),
            'news_excerpt_length' => (int)$this->getSetting('news', 'news_excerpt_length', 150),
            'news_image_width' => (int)$this->getSetting('news', 'news_image_width', 400),
            'pagination_position' => $this->getSetting('news', 'pagination_position', 'bottom'),
            'default_sort_order' => $this->getSetting('news', 'default_sort_order', 'newest'),
            'news_meta_title' => $this->getSetting('news', 'news_meta_title', 'Latest News'),
            'news_meta_description' => $this->getSetting('news', 'news_meta_description', 'Read the latest news and updates')
        ];

        // Parse items_per_page_options
        $items_per_page_options = $this->getSetting('news', 'items_per_page_options', '9,18,27,36');
        $news_settings['items_per_page_options'] = explode(',', $items_per_page_options);

        return $news_settings;
    }

    /**
     * Get meta tags for news page
     *
     * @return array Meta tags
     */
    public function getNewsMetaTags() {
        $site_name = $this->getSetting('general', 'site_name', 'Manage Inc.');

        // First check frontend_news category
        if (isset($this->settings['frontend_news']) && !empty($this->settings['frontend_news'])) {
            $meta_title = $this->getSetting('frontend_news', 'news_meta_title', 'Latest News | {site_name}');
            $meta_description = $this->getSetting('frontend_news', 'news_meta_description', 'Read the latest news and updates from {site_name}');
        } else {
            // Fallback to legacy news category
            $meta_title = $this->getSetting('news', 'news_meta_title', 'Latest News | {site_name}');
            $meta_description = $this->getSetting('news', 'news_meta_description', 'Read the latest news and updates from {site_name}');
        }

        // Replace placeholders
        $meta_title = str_replace('{site_name}', $site_name, $meta_title);
        $meta_description = str_replace('{site_name}', $site_name, $meta_description);

        return [
            'title' => $meta_title,
            'description' => $meta_description
        ];
    }
}
?>
