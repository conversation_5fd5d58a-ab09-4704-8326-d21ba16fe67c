<?php
/**
 * Create Inbox Tables
 * 
 * This script creates the necessary tables for the inbox functionality
 * without foreign key constraints to avoid dependency issues.
 */

session_start();
require_once 'config.php';

// Check if user is logged in and is admin
if (!isset($_SESSION['user_id'])) {
    die('Access denied. Please log in as admin.');
}

// Set content type
header('Content-Type: text/html; charset=utf-8');
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Create Inbox Tables</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 40px auto;
            padding: 20px;
            background: #f8f9fa;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2c3e50;
            margin-bottom: 20px;
        }
        .success {
            background: #d4edda;
            color: #155724;
            padding: 12px 16px;
            border-radius: 8px;
            margin: 10px 0;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            padding: 12px 16px;
            border-radius: 8px;
            margin: 10px 0;
            border: 1px solid #f5c6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            padding: 12px 16px;
            border-radius: 8px;
            margin: 10px 0;
            border: 1px solid #bee5eb;
        }
        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            text-decoration: none;
            display: inline-block;
            margin: 10px 5px;
            cursor: pointer;
            font-weight: 500;
        }
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
        }
        .btn-secondary {
            background: #6c757d;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🗂️ Create Inbox Tables</h1>
        
        <?php
        if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['create_tables'])) {
            echo "<h2>📋 Creating Tables...</h2>";
            
            try {
                // Create contact_submissions table
                $contact_submissions_sql = "CREATE TABLE IF NOT EXISTS `contact_submissions` (
                    `id` int(11) NOT NULL AUTO_INCREMENT,
                    `name` varchar(255) NOT NULL,
                    `email` varchar(255) NOT NULL,
                    `phone` varchar(50) DEFAULT NULL,
                    `message` text NOT NULL,
                    `source` varchar(50) NOT NULL DEFAULT 'Unknown',
                    `is_read` tinyint(1) NOT NULL DEFAULT 0,
                    `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
                    PRIMARY KEY (`id`),
                    KEY `idx_email` (`email`),
                    KEY `idx_is_read` (`is_read`),
                    KEY `idx_created_at` (`created_at`)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
                
                if ($conn->query($contact_submissions_sql)) {
                    echo "<div class='success'>✅ contact_submissions table created successfully</div>";
                } else {
                    echo "<div class='error'>❌ Error creating contact_submissions table: " . $conn->error . "</div>";
                }
                
                // Create contact_replies table (without foreign key constraint)
                $contact_replies_sql = "CREATE TABLE IF NOT EXISTS `contact_replies` (
                    `id` int(11) NOT NULL AUTO_INCREMENT,
                    `submission_id` int(11) NOT NULL,
                    `user_id` int(11) NOT NULL,
                    `username` varchar(255) NOT NULL DEFAULT 'Admin',
                    `subject` varchar(255) NOT NULL,
                    `message` text NOT NULL,
                    `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
                    PRIMARY KEY (`id`),
                    KEY `submission_id` (`submission_id`),
                    KEY `user_id` (`user_id`)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
                
                if ($conn->query($contact_replies_sql)) {
                    echo "<div class='success'>✅ contact_replies table created successfully</div>";
                } else {
                    echo "<div class='error'>❌ Error creating contact_replies table: " . $conn->error . "</div>";
                }
                
                echo "<div class='success'>";
                echo "<h3>🎉 Tables Created Successfully!</h3>";
                echo "<p>Both tables are now ready for the inbox functionality.</p>";
                echo "</div>";
                
                echo "<a href='insert_sample_data.php' class='btn'>📥 Insert Sample Data</a>";
                echo "<a href='inbox.php' class='btn btn-secondary'>📧 View Inbox</a>";
                
            } catch (Exception $e) {
                echo "<div class='error'>";
                echo "<h3>❌ Error occurred:</h3>";
                echo "<p>" . htmlspecialchars($e->getMessage()) . "</p>";
                echo "</div>";
            }
            
        } else {
            ?>
            
            <div class="info">
                <strong>Purpose:</strong> This script will create the necessary database tables for the inbox functionality.
            </div>
            
            <h2>📋 Tables to Create:</h2>
            
            <h3>1. contact_submissions</h3>
            <ul>
                <li>Stores contact form submissions</li>
                <li>Includes name, email, phone, message, source</li>
                <li>Tracks read/unread status</li>
                <li>Timestamps for creation</li>
            </ul>
            
            <h3>2. contact_replies</h3>
            <ul>
                <li>Stores admin replies to contact submissions</li>
                <li>Links to original submission</li>
                <li>Includes subject, message, username</li>
                <li>Timestamps for reply tracking</li>
            </ul>
            
            <div class="info">
                <strong>Note:</strong> This script creates tables without foreign key constraints to avoid dependency issues.
                The tables will work perfectly for the inbox functionality.
            </div>
            
            <form method="post">
                <button type="submit" name="create_tables" class="btn">
                    🗂️ Create Tables
                </button>
            </form>
            
            <a href="inbox.php" class="btn btn-secondary">📧 View Current Inbox</a>
            
            <?php
        }
        ?>
    </div>
</body>
</html>
