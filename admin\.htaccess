# Admin Directory Configuration
<IfModule mod_rewrite.c>
    RewriteEngine On
    RewriteBase /admin/

    # Allow direct access to install.php
    RewriteRule ^install\.php$ - [L]

    # Redirect admin directory root to index.php
    # When someone accesses /admin/ or /admin, redirect to index.php
    RewriteCond %{REQUEST_FILENAME} !-f
    RewriteCond %{REQUEST_FILENAME} !-d
    RewriteCond %{REQUEST_URI} ^/admin/?$
    RewriteRule ^$ index.php [L,R=301]

    # Alternative directory index handling
    DirectoryIndex index.php
</IfModule>

# Security: Block access to sensitive files
<Files "config.php">
    Order allow,deny
    Deny from all
</Files>

<Files "database.sql">
    Order allow,deny
    Deny from all
</Files>

# Block access to backup and log files
<FilesMatch "\.(bak|backup|log)$">
    Order allow,deny
    Deny from all
</FilesMatch>

# Block access to includes directory from direct access
<IfModule mod_rewrite.c>
    RewriteRule ^includes/ - [F,L]
    RewriteRule ^sql/ - [F,L]
    RewriteRule ^database/ - [F,L]
</IfModule>


# Set proper PHP settings
<IfModule mod_php7.c>
    php_flag display_errors on
    php_value error_reporting E_ALL
</IfModule>

# Allow access to the admin directory
<Files "install.php">
    Order Allow,Deny
    Allow from all
    Satisfy Any
</Files>

<Files "index.php">
    Order Allow,Deny
    Allow from all
    Satisfy Any
</Files>

# Prevent directory listing
Options -Indexes

# Set default character set
AddDefaultCharset UTF-8

# Security and content headers
<IfModule mod_headers.c>
    # Prevent clickjacking
    Header always append X-Frame-Options SAMEORIGIN

    # Prevent MIME type sniffing
    Header always set X-Content-Type-Options nosniff

    # Enable XSS protection
    Header always set X-XSS-Protection "1; mode=block"

    # Referrer policy
    Header always set Referrer-Policy "strict-origin-when-cross-origin"

    # Force the correct content type and charset for PHP files
    <FilesMatch "\.php$">
        Header set X-UA-Compatible "IE=edge"
    </FilesMatch>

    # Exclude CSS PHP files from text/html content type
    <FilesMatch "\.css\.php$">
        Header set Content-Type "text/css; charset=UTF-8"
    </FilesMatch>
</IfModule>

# Set default content type for PHP files
AddType text/html .php

# Override content type for CSS PHP files
AddType text/css .css.php

# Disable output buffering
php_flag output_buffering off

# Disable zlib compression
php_flag zlib.output_compression off

# Disable implicit flush
php_flag implicit_flush on

# CSS MIME Type Fix for Hosting Providers
AddType text/css .css

# Cache Control for CSS Files
<FilesMatch "\.(css)$">
    Header set Cache-Control "max-age=31536000, public"
    Header set Content-Type "text/css; charset=UTF-8"
</FilesMatch>

# Ensure CSS files are served with correct MIME type
<Files "*.css">
    ForceType text/css
</Files>

# Enable compression for CSS files
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/css
</IfModule>

# Fix for CSS @import issues
<IfModule mod_headers.c>
    <FilesMatch "\.(css)$">
        Header set Access-Control-Allow-Origin "*"
        Header set Access-Control-Allow-Methods "GET, POST, OPTIONS"
        Header set Access-Control-Allow-Headers "Content-Type"
    </FilesMatch>
</IfModule>

# Prevent caching issues for CSS
<IfModule mod_expires.c>
    ExpiresActive On
    ExpiresByType text/css "access plus 1 month"
</IfModule>
