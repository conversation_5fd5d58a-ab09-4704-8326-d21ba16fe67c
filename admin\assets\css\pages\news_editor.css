/**
 * News Editor Shared CSS
 *
 * This file contains shared styles for create_news.php and edit_news.php pages.
 * Complete overhaul based on the clean, modern design of the contact_submissions page.
 */

/* News Editor Wrapper */
.news-editor-wrapper {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

/* News Editor Layout */
.news-editor-form {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.admin-content-grid {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 20px;
}

/* Content Header */
.admin-content-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;
  flex-wrap: wrap;
  gap: 15px;
  background-color: #fff;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.admin-content-title-group {
  flex: 1;
  min-width: 0;
}

.admin-content-title {
  display: block;
  width: auto;
  max-width: 100%;
  font-size: 22px;
  font-weight: 600;
  margin: 0;
  padding: 0;
  color: var(--text-dark);
  line-height: 1.2;
}

.admin-content-subtitle {
  color: var(--text-muted);
  margin-top: 8px;
  font-size: 14px;
}

.admin-content-actions {
  display: flex;
  gap: 10px;
  align-items: center;
}

/* Card Styles */
.admin-card {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  overflow: hidden;
  margin-bottom: 20px;
  transition: box-shadow 0.2s ease;
}

.admin-card:hover {
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.admin-card-header {
  padding: 15px 20px;
  border-bottom: 1px solid var(--border-light);
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 10px;
  background-color: var(--bg-light);
}

.admin-card-title {
  display: flex;
  align-items: center;
  width: auto;
  max-width: 100%;
  font-size: 16px;
  font-weight: 600;
  margin: 0;
  padding: 0;
  color: var(--text-dark);
  line-height: 1.2;
  gap: 8px;
}

.admin-card-title i {
  color: var(--text-muted);
}

.admin-card-meta {
  display: flex;
  gap: 15px;
  flex-wrap: wrap;
  font-size: 14px;
  color: #666;
}

.admin-card-meta-item {
  display: flex;
  align-items: center;
  gap: 8px;
  background-color: #f0f0f0;
  padding: 5px 10px;
  border-radius: 20px;
}

.admin-card-meta-item i {
  font-size: 14px;
  color: #666;
}

.admin-card-body {
  padding: 20px;
}

/* Form Elements */
.form-group {
  margin-bottom: 20px;
}

.form-label {
  display: block;
  margin-bottom: 8px;
  font-weight: var(--font-weight-medium);
  color: var(--text-dark);
}

.form-control {
  width: 100%;
  padding: 12px 15px;
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  background-color: var(--bg-input);
  font-size: 14px;
  color: var(--text-dark);
  transition: all 0.2s ease;
}

.form-control:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(var(--primary-rgb), 0.1);
  outline: none;
}

.form-select {
  width: 100%;
  padding: 12px 15px;
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  background-color: var(--bg-input);
  font-size: 14px;
  color: var(--text-dark);
  appearance: none;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%23333' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: right 12px center;
  padding-right: 40px;
  transition: all 0.2s ease;
  cursor: pointer;
}

.form-select:hover {
  border-color: var(--border-hover);
}

.form-select:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(var(--primary-rgb), 0.1);
  outline: none;
}

.form-text {
  font-size: 13px;
  color: var(--text-muted);
  margin-top: 8px;
}

.required {
  color: var(--danger-color);
  margin-left: 3px;
}

/* Input Group */
.input-group {
  display: flex;
  align-items: stretch;
  width: 100%;
}

.input-group-text {
  display: flex;
  align-items: center;
  padding: 12px 15px;
  background-color: var(--bg-light);
  border: 1px solid var(--border-color);
  border-right: none;
  border-radius: var(--radius-md) 0 0 var(--radius-md);
  font-size: 14px;
  color: var(--text-muted);
}

.input-group .form-control {
  border-radius: 0 var(--radius-md) var(--radius-md) 0;
  flex: 1;
}

/* Image Upload */
.image-preview-container {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.image-preview {
  width: 100%;
  height: 200px;
  border: 2px dashed #e0e0e0;
  border-radius: 8px;
  overflow: hidden;
  background-color: #f9f9f9;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: border-color 0.2s ease;
}

.image-preview:hover {
  border-color: var(--primary-color);
}

.image-preview img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.image-preview:hover img {
  transform: scale(1.05);
}

.image-preview-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #999;
  padding: 20px;
  text-align: center;
}

.image-preview-empty i {
  font-size: 48px;
  margin-bottom: 15px;
  color: #ccc;
}

.image-preview-empty p {
  margin: 0;
  font-size: 14px;
}

.custom-file-upload {
  position: relative;
  width: 100%;
}

.custom-file-input {
  position: absolute;
  width: 100%;
  height: 100%;
  opacity: 0;
  cursor: pointer;
  z-index: 2;
}

.custom-file-label {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 12px 15px;
  background-color: #f5f7fa;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 14px;
  color: #555;
}

.custom-file-label i {
  margin-right: 8px;
}

.custom-file-label:hover {
  background-color: #e9e9e9;
}

/* WYSIWYG Editor */
.wysiwyg-editor-container {
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.wysiwyg-toolbar {
  display: flex;
  flex-wrap: wrap;
  gap: 5px;
  padding: 10px;
  background-color: #f5f7fa;
  border-bottom: 1px solid #e0e0e0;
}

.wysiwyg-toolbar button {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: transparent;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  color: #555;
  transition: all 0.2s ease;
}

.wysiwyg-toolbar button:hover {
  background-color: #e0e0e0;
  color: #333;
}

.wysiwyg-toolbar button.active {
  background-color: var(--primary-color);
  color: white;
}

.wysiwyg-editor {
  padding: 15px;
  min-height: 200px;
  background-color: white;
}

.wysiwyg-editor:focus {
  outline: none;
}

/* Button Styles */
.admin-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 10px 20px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  border: 1px solid transparent;
  text-decoration: none;
}

.admin-btn i {
  margin-right: 8px;
}

.admin-btn.primary {
  background-color: var(--primary-color);
  color: white;
  border-color: var(--primary-color);
}

.admin-btn.primary:hover {
  background-color: var(--primary-hover);
  border-color: var(--primary-hover);
}

.admin-btn.outline {
  background-color: transparent;
  border-color: #e0e0e0;
  color: #555;
}

.admin-btn.outline:hover {
  background-color: #f5f5f5;
  border-color: #ccc;
}

.admin-btn.success {
  background-color: #28a745;
  color: white;
  border-color: #28a745;
}

.admin-btn.success:hover {
  background-color: #218838;
  border-color: #1e7e34;
}

.admin-btn.danger {
  background-color: #dc3545;
  color: white;
  border-color: #dc3545;
}

.admin-btn.danger:hover {
  background-color: #c82333;
  border-color: #bd2130;
}

.admin-btn.small {
  padding: 6px 12px;
  font-size: 13px;
}

.admin-btn.full-width {
  width: 100%;
}

/* Status Badges */
.status-badge {
  display: inline-block;
  padding: 5px 10px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  text-align: center;
}

.status-badge.published {
  background-color: #e8f5e9;
  color: #2e7d32;
}

.status-badge.draft {
  background-color: #fff3e0;
  color: #e65100;
}

.status-badge.scheduled {
  background-color: #e3f2fd;
  color: #0d47a1;
}

/* Responsive Styles */
@media (max-width: 992px) {
  .admin-content-grid {
    grid-template-columns: 1fr;
  }

  .admin-content-title {
    font-size: 20px;
  }

  .admin-card-title {
    font-size: 16px;
  }

  .image-preview {
    height: 180px;
  }
}

@media (max-width: 768px) {
  .admin-content-header {
    flex-direction: column;
    align-items: flex-start;
  }

  .admin-content-actions {
    width: 100%;
    justify-content: flex-start;
    margin-top: 15px;
  }

  .admin-card-header {
    flex-direction: column;
    align-items: flex-start;
  }

  .admin-card-meta {
    margin-top: 10px;
    width: 100%;
  }

  .form-group {
    margin-bottom: 15px;
  }

  .wysiwyg-toolbar {
    flex-wrap: wrap;
    gap: 2px;
  }

  .wysiwyg-toolbar button {
    width: 32px;
    height: 32px;
  }

  .admin-btn {
    padding: 8px 16px;
  }

  .admin-content-actions .admin-btn {
    width: 100%;
    margin-bottom: 8px;
  }
}
