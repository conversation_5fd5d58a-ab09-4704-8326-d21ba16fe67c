<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Help Center | Manage Inc.</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        :root {
            --primary: #f1ca2f;
            --primary-dark: #e0b929;
            --secondary: #333;
            --text: #333;
            --text-light: #666;
            --bg: #f8f9fa;
            --bg-card: #fff;
            --border: #e9ecef;
            --shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
            --radius: 8px;
            --transition: all 0.3s ease;
            --header-height: 70px;
            --sidebar-width: 280px;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', sans-serif;
            line-height: 1.6;
            color: var(--text);
            background-color: var(--bg);
            overflow-x: hidden;
        }

        /* Header */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: var(--header-height);
            background-color: var(--bg-card);
            box-shadow: var(--shadow);
            z-index: 100;
            display: flex;
            align-items: center;
            padding: 0 30px;
            justify-content: space-between;
        }

        .header-logo {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .header-logo h1 {
            font-size: 20px;
            font-weight: 600;
            color: var(--secondary);
        }

        .header-logo i {
            font-size: 24px;
            color: var(--primary);
        }

        .header-actions {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .btn {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            padding: 10px 16px;
            border-radius: var(--radius);
            font-weight: 500;
            font-size: 14px;
            text-decoration: none;
            transition: var(--transition);
            cursor: pointer;
            border: none;
        }

        .btn-primary {
            background-color: var(--primary);
            color: var(--secondary);
        }

        .btn-primary:hover {
            background-color: var(--primary-dark);
        }

        .btn-secondary {
            background-color: var(--bg);
            color: var(--text);
            border: 1px solid var(--border);
        }

        .btn-secondary:hover {
            background-color: var(--border);
        }

        .btn-icon {
            padding: 10px;
            border-radius: 50%;
        }

        /* Mobile menu toggle */
        .menu-toggle {
            display: none;
            background: none;
            border: none;
            font-size: 24px;
            color: var(--text);
            cursor: pointer;
        }

        /* Main layout */
        .main {
            display: flex;
            margin-top: var(--header-height);
            min-height: calc(100vh - var(--header-height));
        }

        /* Sidebar */
        .sidebar {
            width: var(--sidebar-width);
            background-color: var(--bg-card);
            border-right: 1px solid var(--border);
            padding: 30px 0;
            height: calc(100vh - var(--header-height));
            position: fixed;
            overflow-y: auto;
            transition: var(--transition);
        }

        .sidebar-header {
            padding: 0 20px 20px;
            border-bottom: 1px solid var(--border);
            margin-bottom: 20px;
        }

        .sidebar-header h2 {
            font-size: 16px;
            font-weight: 600;
            color: var(--text-light);
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .nav-section {
            margin-bottom: 30px;
        }

        .nav-section-title {
            padding: 0 20px 10px;
            font-size: 14px;
            font-weight: 600;
            color: var(--text-light);
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .nav-items {
            list-style: none;
        }

        .nav-item {
            margin-bottom: 2px;
        }

        .nav-link {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 12px 20px;
            color: var(--text);
            text-decoration: none;
            font-weight: 500;
            transition: var(--transition);
            border-left: 3px solid transparent;
        }

        .nav-link:hover {
            background-color: rgba(241, 202, 47, 0.1);
            color: var(--primary-dark);
        }

        .nav-link.active {
            background-color: rgba(241, 202, 47, 0.15);
            color: var(--primary-dark);
            border-left-color: var(--primary);
        }

        .nav-link i {
            font-size: 18px;
            width: 24px;
            text-align: center;
        }

        /* Content area */
        .content {
            flex: 1;
            margin-left: var(--sidebar-width);
            padding: 30px;
            max-width: 100%;
        }

        .content-header {
            margin-bottom: 30px;
        }

        .content-title {
            font-size: 28px;
            font-weight: 700;
            color: var(--secondary);
            margin-bottom: 10px;
        }

        .content-subtitle {
            font-size: 16px;
            color: var(--text-light);
        }

        .breadcrumbs {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 15px;
            font-size: 14px;
        }

        .breadcrumbs a {
            color: var(--text-light);
            text-decoration: none;
        }

        .breadcrumbs a:hover {
            color: var(--primary);
        }

        .breadcrumbs .separator {
            color: var(--text-light);
        }

        /* Cards */
        .card {
            background-color: var(--bg-card);
            border-radius: var(--radius);
            box-shadow: var(--shadow);
            margin-bottom: 30px;
            overflow: hidden;
        }

        .card-header {
            padding: 20px;
            border-bottom: 1px solid var(--border);
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .card-title {
            font-size: 18px;
            font-weight: 600;
            color: var(--secondary);
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .card-title i {
            color: var(--primary);
        }

        .card-body {
            padding: 20px;
        }

        /* Tab navigation */
        .tab-nav {
            display: flex;
            border-bottom: 1px solid var(--border);
            margin-bottom: 20px;
            overflow-x: auto;
            scrollbar-width: none; /* Firefox */
        }

        .tab-nav::-webkit-scrollbar {
            display: none; /* Chrome, Safari, Edge */
        }

        .tab-btn {
            padding: 12px 20px;
            background: none;
            border: none;
            font-size: 14px;
            font-weight: 500;
            color: var(--text-light);
            cursor: pointer;
            border-bottom: 2px solid transparent;
            white-space: nowrap;
        }

        .tab-btn:hover {
            color: var(--primary);
        }

        .tab-btn.active {
            color: var(--primary);
            border-bottom-color: var(--primary);
        }

        /* Tab content */
        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
            animation: fadeIn 0.3s ease;
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        /* Documentation content styles */
        .doc-content h1 {
            font-size: 24px;
            font-weight: 700;
            color: var(--secondary);
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 1px solid var(--border);
        }

        .doc-content h2 {
            font-size: 20px;
            font-weight: 600;
            color: var(--secondary);
            margin: 30px 0 15px;
        }

        .doc-content h3 {
            font-size: 18px;
            font-weight: 600;
            color: var(--secondary);
            margin: 25px 0 10px;
        }

        .doc-content p {
            margin-bottom: 15px;
            line-height: 1.7;
        }

        .doc-content ul,
        .doc-content ol {
            margin-bottom: 20px;
            padding-left: 25px;
        }

        .doc-content li {
            margin-bottom: 8px;
        }

        .doc-content a {
            color: var(--primary);
            text-decoration: none;
            transition: var(--transition);
        }

        .doc-content a:hover {
            color: var(--primary-dark);
            text-decoration: underline;
        }

        .doc-content code {
            font-family: monospace;
            background-color: var(--bg);
            padding: 2px 5px;
            border-radius: 4px;
            font-size: 14px;
        }

        .doc-content blockquote {
            border-left: 4px solid var(--primary);
            padding: 10px 15px;
            margin: 0 0 20px;
            background-color: rgba(241, 202, 47, 0.1);
        }

        /* Search */
        .search-container {
            position: relative;
            margin-bottom: 20px;
        }

        .search-input {
            width: 100%;
            padding: 12px 20px 12px 45px;
            border: 1px solid var(--border);
            border-radius: var(--radius);
            font-size: 14px;
            transition: var(--transition);
        }

        .search-input:focus {
            outline: none;
            border-color: var(--primary);
            box-shadow: 0 0 0 3px rgba(241, 202, 47, 0.2);
        }

        .search-icon {
            position: absolute;
            left: 15px;
            top: 50%;
            transform: translateY(-50%);
            color: var(--text-light);
        }

        /* Feature cards */
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .feature-card {
            background-color: var(--bg-card);
            border-radius: var(--radius);
            box-shadow: var(--shadow);
            padding: 20px;
            transition: var(--transition);
            border: 1px solid var(--border);
        }

        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
        }

        .feature-icon {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            width: 50px;
            height: 50px;
            background-color: rgba(241, 202, 47, 0.15);
            color: var(--primary);
            border-radius: 12px;
            margin-bottom: 15px;
            font-size: 20px;
        }

        .feature-title {
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 10px;
        }

        .feature-desc {
            font-size: 14px;
            color: var(--text-light);
            margin-bottom: 15px;
        }

        .feature-link {
            font-size: 14px;
            font-weight: 500;
            color: var(--primary);
            text-decoration: none;
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .feature-link:hover {
            color: var(--primary-dark);
        }

        /* Visualization elements */
        .info-box {
            background-color: rgba(241, 202, 47, 0.1);
            border-left: 4px solid var(--primary);
            padding: 15px;
            margin-bottom: 20px;
            border-radius: 4px;
        }

        .info-box-title {
            font-weight: 600;
            margin-bottom: 8px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .info-box-title i {
            color: var(--primary);
        }

        .step-list {
            counter-reset: step-counter;
            list-style-type: none;
            padding-left: 0;
        }

        .step-list li {
            counter-increment: step-counter;
            position: relative;
            padding-left: 50px;
            margin-bottom: 25px;
        }

        .step-list li::before {
            content: counter(step-counter);
            position: absolute;
            left: 0;
            top: 0;
            width: 35px;
            height: 35px;
            background-color: var(--primary);
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
        }

        .image-preview {
            border: 1px solid var(--border);
            border-radius: var(--radius);
            padding: 10px;
            margin-bottom: 20px;
            background-color: #f9f9f9;
            text-align: center;
        }

        .image-preview img {
            max-width: 100%;
            height: auto;
            border-radius: 4px;
        }

        .image-preview figcaption {
            margin-top: 10px;
            font-size: 14px;
            color: var(--text-light);
            font-style: italic;
        }

        .comparison-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }

        .comparison-table th {
            background-color: var(--primary);
            color: white;
            text-align: left;
            padding: 12px 15px;
        }

        .comparison-table td {
            padding: 10px 15px;
            border-bottom: 1px solid var(--border);
        }

        .comparison-table tr:nth-child(even) {
            background-color: rgba(241, 202, 47, 0.05);
        }

        .tag {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
            margin-right: 5px;
        }

        .tag-primary {
            background-color: rgba(241, 202, 47, 0.2);
            color: var(--primary-dark);
        }

        .tag-secondary {
            background-color: rgba(51, 51, 51, 0.1);
            color: var(--secondary);
        }

        .tag-success {
            background-color: rgba(40, 167, 69, 0.2);
            color: #28a745;
        }

        .tag-danger {
            background-color: rgba(220, 53, 69, 0.2);
            color: #dc3545;
        }

        .progress-steps {
            display: flex;
            margin-bottom: 30px;
            position: relative;
        }

        .progress-steps::before {
            content: '';
            position: absolute;
            top: 15px;
            left: 0;
            right: 0;
            height: 2px;
            background-color: var(--border);
            z-index: 1;
        }

        .progress-step {
            flex: 1;
            text-align: center;
            position: relative;
            z-index: 2;
        }

        .progress-step-icon {
            width: 30px;
            height: 30px;
            background-color: white;
            border: 2px solid var(--border);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 10px;
        }

        .progress-step.active .progress-step-icon {
            background-color: var(--primary);
            border-color: var(--primary);
            color: white;
        }

        .progress-step-label {
            font-size: 14px;
            font-weight: 500;
        }

        .accordion {
            margin-bottom: 20px;
        }

        .accordion-item {
            border: 1px solid var(--border);
            border-radius: var(--radius);
            margin-bottom: 10px;
            overflow: hidden;
        }

        .accordion-header {
            padding: 15px;
            background-color: var(--bg);
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: space-between;
            font-weight: 500;
        }

        .accordion-header:hover {
            background-color: rgba(241, 202, 47, 0.05);
        }

        .accordion-content {
            padding: 0 15px;
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.3s ease, padding 0.3s ease;
        }

        .accordion-item.active .accordion-content {
            padding: 15px;
            max-height: 1000px;
        }

        .accordion-item.active .accordion-header {
            background-color: rgba(241, 202, 47, 0.1);
        }

        /* Responsive styles */
        @media (max-width: 992px) {
            .sidebar {
                left: -280px;
                z-index: 99;
            }

            .sidebar.active {
                left: 0;
            }

            .content {
                margin-left: 0;
            }

            .menu-toggle {
                display: block;
            }

            .overlay {
                display: none;
                position: fixed;
                top: var(--header-height);
                left: 0;
                right: 0;
                bottom: 0;
                background-color: rgba(0, 0, 0, 0.5);
                z-index: 98;
            }

            .overlay.active {
                display: block;
            }

            .feature-grid {
                grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
            }
        }

        @media (max-width: 768px) {
            .header {
                padding: 0 20px;
            }

            .content {
                padding: 20px;
            }

            .content-title {
                font-size: 24px;
            }

            .card-header {
                flex-direction: column;
                align-items: flex-start;
                gap: 10px;
            }

            .feature-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <button class="menu-toggle" id="menuToggle">
            <i class="fas fa-bars"></i>
        </button>
        <div class="header-logo">
            <i class="fas fa-question-circle"></i>
            <h1>Manage Inc Help Center</h1>
        </div>
        <div class="header-actions">
            <a href="../index.html" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i>
                <span>Back to Site</span>
            </a>
        </div>
    </header>

    <!-- Main content -->
    <main class="main">
        <!-- Sidebar -->
        <aside class="sidebar" id="sidebar">
            <div class="sidebar-header">
                <h2>Documentation</h2>
            </div>

            <div class="search-container">
                <i class="fas fa-search search-icon"></i>
                <input type="text" class="search-input" placeholder="Search documentation..." id="searchInput">
            </div>

            <div class="nav-section">
                <h3 class="nav-section-title">Getting Started</h3>
                <ul class="nav-items">
                    <li class="nav-item">
                        <a href="#overview" class="nav-link active" data-tab="overview">
                            <i class="fas fa-home"></i>
                            <span>Overview</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#user-guide" class="nav-link" data-tab="user-guide">
                            <i class="fas fa-book"></i>
                            <span>User Guide</span>
                        </a>
                    </li>
                </ul>
            </div>

            <div class="nav-section">
                <h3 class="nav-section-title">Features</h3>
                <ul class="nav-items">
                    <li class="nav-item">
                        <a href="#news" class="nav-link" data-tab="news">
                            <i class="fas fa-newspaper"></i>
                            <span>News Management</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#users" class="nav-link" data-tab="users">
                            <i class="fas fa-users"></i>
                            <span>User Management</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#inbox" class="nav-link" data-tab="inbox">
                            <i class="fas fa-inbox"></i>
                            <span>Inbox</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#editor" class="nav-link" data-tab="editor">
                            <i class="fas fa-code"></i>
                            <span>HTML Editor</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#settings" class="nav-link" data-tab="settings">
                            <i class="fas fa-cog"></i>
                            <span>Settings</span>
                        </a>
                    </li>
                </ul>
            </div>

            <div class="nav-section">
                <h3 class="nav-section-title">Resources</h3>
                <ul class="nav-items">
                    <li class="nav-item">
                        <a href="#tutorials" class="nav-link" data-tab="tutorials">
                            <i class="fas fa-graduation-cap"></i>
                            <span>Tutorials</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#faq" class="nav-link" data-tab="faq">
                            <i class="fas fa-question"></i>
                            <span>FAQ</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#glossary" class="nav-link" data-tab="glossary">
                            <i class="fas fa-book-open"></i>
                            <span>Glossary</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#security-features" class="nav-link" data-tab="security-features">
                            <i class="fas fa-shield-alt"></i>
                            <span>Security Features</span>
                        </a>
                    </li>
                </ul>
            </div>
        </aside>

        <!-- Overlay for mobile -->
        <div class="overlay" id="overlay"></div>

        <!-- Content area -->
        <div class="content">
            <!-- Overview tab -->
            <div id="overview" class="tab-content active">
                <div class="content-header">
                    <div class="breadcrumbs">
                        <a href="#overview">Documentation</a>
                        <span class="separator">/</span>
                        <span>Overview</span>
                    </div>
                    <h1 class="content-title">Manage Inc Admin Panel</h1>
                    <p class="content-subtitle">Welcome to the comprehensive documentation for the Manage Inc Admin Panel. This guide will help you understand and effectively use all features of the admin system.</p>
                </div>

                <div class="feature-grid">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-newspaper"></i>
                        </div>
                        <h3 class="feature-title">News Management</h3>
                        <p class="feature-desc">Create, edit, and organize news posts and categories for your website.</p>
                        <a href="#news" class="feature-link" data-tab="news">
                            Learn more <i class="fas fa-arrow-right"></i>
                        </a>
                    </div>

                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-users"></i>
                        </div>
                        <h3 class="feature-title">User Management</h3>
                        <p class="feature-desc">Manage user accounts, roles, and permissions for the admin panel.</p>
                        <a href="#users" class="feature-link" data-tab="users">
                            Learn more <i class="fas fa-arrow-right"></i>
                        </a>
                    </div>

                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-inbox"></i>
                        </div>
                        <h3 class="feature-title">Inbox</h3>
                        <p class="feature-desc">View and respond to contact form submissions from your website visitors.</p>
                        <a href="#inbox" class="feature-link" data-tab="inbox">
                            Learn more <i class="fas fa-arrow-right"></i>
                        </a>
                    </div>

                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-code"></i>
                        </div>
                        <h3 class="feature-title">HTML Editor</h3>
                        <p class="feature-desc">Edit website content directly through a visual HTML editor with code mode.</p>
                        <a href="#editor" class="feature-link" data-tab="editor">
                            Learn more <i class="fas fa-arrow-right"></i>
                        </a>
                    </div>

                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-cog"></i>
                        </div>
                        <h3 class="feature-title">Settings</h3>
                        <p class="feature-desc">Configure system settings including email, appearance, and fonts.</p>
                        <a href="#settings" class="feature-link" data-tab="settings">
                            Learn more <i class="fas fa-arrow-right"></i>
                        </a>
                    </div>

                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-graduation-cap"></i>
                        </div>
                        <h3 class="feature-title">Tutorials</h3>
                        <p class="feature-desc">Step-by-step guides for common tasks in the admin panel.</p>
                        <a href="#tutorials" class="feature-link" data-tab="tutorials">
                            Learn more <i class="fas fa-arrow-right"></i>
                        </a>
                    </div>
                </div>

                <div class="card">
                    <div class="card-header">
                        <h2 class="card-title">
                            <i class="fas fa-lightbulb"></i>
                            How to Use This Documentation
                        </h2>
                    </div>
                    <div class="card-body doc-content">
                        <ol>
                            <li><strong>New Users</strong>: Start with the <a href="#user-guide" data-tab="user-guide">User Guide</a> to understand the basic concepts and features</li>
                            <li><strong>Task-Oriented Users</strong>: Go directly to the <a href="#tutorials" data-tab="tutorials">Tutorials</a> for step-by-step instructions</li>
                            <li><strong>Reference Needs</strong>: Use the <a href="#glossary" data-tab="glossary">Glossary</a> to look up specific terms</li>
                            <li><strong>Optimization</strong>: Review the Best Practices section to improve your workflow and content quality</li>
                        </ol>

                        <h3>Getting Help</h3>
                        <p>If you can't find the information you need in this documentation:</p>
                        <ol>
                            <li>Look for contextual help icons (?) throughout the admin interface</li>
                            <li>Check the <a href="#faq" data-tab="faq">FAQ section</a> for answers to common questions</li>
                            <li>Contact your system administrator for assistance with technical issues</li>
                        </ol>
                    </div>
                </div>
            </div>

            <!-- User Guide tab -->
            <div id="user-guide" class="tab-content">
                <div class="content-header">
                    <div class="breadcrumbs">
                        <a href="#overview">Documentation</a>
                        <span class="separator">/</span>
                        <span>User Guide</span>
                    </div>
                    <h1 class="content-title">User Guide</h1>
                    <p class="content-subtitle">A comprehensive overview of the admin panel and its features.</p>
                </div>

                <div class="card">
                    <div class="card-body doc-content">
                        <h2>Overview</h2>
                        <p>The Manage Inc Admin Panel is a comprehensive content management system designed for website administrators and content managers. This powerful tool allows you to manage your website content, user accounts, system settings, and communication with site visitors through a user-friendly interface.</p>

                        <h3>Key Features</h3>
                        <ul>
                            <li><strong>Dashboard</strong>: Get a quick overview of your website statistics and recent activity</li>
                            <li><strong>News Management</strong>: Create, edit, and organize news posts and categories with dynamic content loading</li>
                            <li><strong>User Management</strong>: Manage user accounts and permissions</li>
                            <li><strong>Inbox</strong>: View and respond to contact form submissions</li>
                            <li><strong>HTML Editor</strong>: Edit website content directly through a visual HTML editor</li>
                            <li><strong>Settings</strong>: Configure system settings including email and news settings</li>
                            <li><strong>Email Templates</strong>: Customize automated email responses with a dedicated template editor</li>
                            <li><strong>Categories</strong>: Manage content categories with an elegant and simplified interface</li>
                        </ul>

                        <h2>Getting Started</h2>

                        <h3>Accessing the Admin Panel</h3>
                        <ol>
                            <li>Navigate to your website's admin URL (typically yourdomain.com/admin)</li>
                            <li>Enter your username and password in the login form</li>
                            <li>Click "Sign In" to access the dashboard</li>
                        </ol>

                        <h3>Dashboard Overview</h3>
                        <p>The dashboard provides a snapshot of your website's activity and quick access to common tasks:</p>
                        <ul>
                            <li><strong>Statistics Cards</strong>: View total news posts, categories, and active users</li>
                            <li><strong>Quick Actions</strong>: Access frequently used features with a single click</li>
                            <li><strong>Recent Activity</strong>: See the latest actions taken in the admin panel</li>
                            <li><strong>Recent News</strong>: View and manage your most recent news posts</li>
                        </ul>
                    </div>
                </div>
            </div>

            <!-- Tutorials tab -->
            <div id="tutorials" class="tab-content">
                <div class="content-header">
                    <div class="breadcrumbs">
                        <a href="#overview">Documentation</a>
                        <span class="separator">/</span>
                        <span>Tutorials</span>
                    </div>
                    <h1 class="content-title">Tutorials</h1>
                    <p class="content-subtitle">Step-by-step guides for common tasks in the admin panel.</p>
                </div>

                <div class="card">
                    <div class="card-header">
                        <h2 class="card-title">
                            <i class="fas fa-newspaper"></i>
                            Creating and Publishing a News Post
                        </h2>
                    </div>
                    <div class="card-body doc-content">
                        <ol>
                            <li><strong>Access the News Creation Page</strong>
                                <ul>
                                    <li>Log in to the admin panel</li>
                                    <li>Navigate to <strong>News > Add News</strong> in the sidebar menu</li>
                                </ul>
                            </li>

                            <li><strong>Enter Basic Information</strong>
                                <ul>
                                    <li>Enter a descriptive title for your news post</li>
                                    <li>Select an appropriate category from the dropdown menu</li>
                                    <li>If needed, create a new category by clicking the "+" icon</li>
                                </ul>
                            </li>

                            <li><strong>Add Content Using the WYSIWYG Editor</strong>
                                <ul>
                                    <li>Use the formatting toolbar to style your text</li>
                                    <li>Add headings, lists, and links as needed</li>
                                    <li>Insert images by clicking the image icon and uploading or selecting from library</li>
                                    <li>Format text using the font selector for consistent styling</li>
                                </ul>
                            </li>

                            <li><strong>Upload a Featured Image</strong>
                                <ul>
                                    <li>Click the "Choose File" button in the Featured Image section</li>
                                    <li>Select an image from your computer (recommended size: 1200x800px)</li>
                                    <li>The image will be displayed as a thumbnail once uploaded</li>
                                </ul>
                            </li>

                            <li><strong>Set SEO-Friendly URL</strong>
                                <ul>
                                    <li>The slug field will auto-generate based on your title</li>
                                    <li>Edit the slug if needed for better SEO (use hyphens between words)</li>
                                    <li>Avoid special characters and keep it concise</li>
                                </ul>
                            </li>

                            <li><strong>Review and Publish</strong>
                                <ul>
                                    <li>Preview your post by clicking the "Preview" button</li>
                                    <li>Make any necessary adjustments</li>
                                    <li>Click "Save" to publish the post immediately</li>
                                </ul>
                            </li>
                        </ol>
                    </div>
                </div>
            </div>

            <!-- User Management tab -->
            <div id="users" class="tab-content">
                <div class="content-header">
                    <div class="breadcrumbs">
                        <a href="#overview">Documentation</a>
                        <span class="separator">/</span>
                        <span>User Management</span>
                    </div>
                    <h1 class="content-title">User Management</h1>
                    <p class="content-subtitle">Manage user accounts, roles, and permissions for the admin panel.</p>
                </div>

                <div class="card">
                    <div class="card-header">
                        <h2 class="card-title">
                            <i class="fas fa-users"></i>
                            User Roles and Permissions
                        </h2>
                    </div>
                    <div class="card-body doc-content">
                        <p>The admin panel uses a role-based permission system to control what actions different users can perform. Understanding these roles is essential for maintaining proper security and access control.</p>

                        <div class="comparison-table">
                            <table>
                                <thead>
                                    <tr>
                                        <th>Role</th>
                                        <th>Description</th>
                                        <th>Permissions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>
                                            <strong>Administrator</strong>
                                            <span class="tag tag-primary">Admin</span>
                                        </td>
                                        <td>Full system access with all privileges</td>
                                        <td>
                                            <ul>
                                                <li>Manage all content</li>
                                                <li>Create and manage users</li>
                                                <li>Configure system settings</li>
                                                <li>Access all features</li>
                                            </ul>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>
                                            <strong>Editor</strong>
                                            <span class="tag tag-secondary">Editor</span>
                                        </td>
                                        <td>Content management access</td>
                                        <td>
                                            <ul>
                                                <li>Create and edit news posts</li>
                                                <li>Manage categories</li>
                                                <li>Respond to contact messages</li>
                                                <li>Use the frontend editor</li>
                                                <li>Cannot access system settings</li>
                                                <li>Cannot manage users</li>
                                            </ul>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>
                                            <strong>Viewer</strong>
                                            <span class="tag tag-secondary">Viewer</span>
                                        </td>
                                        <td>Read-only access to content</td>
                                        <td>
                                            <ul>
                                                <li>View dashboard statistics</li>
                                                <li>View news posts and categories</li>
                                                <li>View contact messages</li>
                                                <li>Cannot create or edit content</li>
                                                <li>Cannot access system settings</li>
                                                <li>Cannot manage users</li>
                                            </ul>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>

                        <div class="info-box">
                            <div class="info-box-title">
                                <i class="fas fa-shield-alt"></i>
                                Security Best Practices
                            </div>
                            <ul>
                                <li><strong>Principle of Least Privilege:</strong> Assign users the minimum level of access needed for their tasks</li>
                                <li><strong>Regular Audits:</strong> Periodically review user accounts and their permissions</li>
                                <li><strong>Limit Administrators:</strong> Restrict Administrator role to only those who truly need full access</li>
                                <li><strong>Remove Inactive Users:</strong> Deactivate or delete accounts for users who no longer need access</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <div class="card">
                    <div class="card-header">
                        <h2 class="card-title">
                            <i class="fas fa-user-plus"></i>
                            Creating and Managing Users
                        </h2>
                    </div>
                    <div class="card-body doc-content">
                        <h3>Adding a New User</h3>
                        <ol class="step-list">
                            <li>
                                <strong>Navigate to Users</strong>
                                <p>Access the user management page from the sidebar menu.</p>
                            </li>
                            <li>
                                <strong>Click "Add New User"</strong>
                                <p>This will open the user creation form.</p>
                            </li>
                            <li>
                                <strong>Enter User Details</strong>
                                <p>Fill in the required fields:</p>
                                <ul>
                                    <li><strong>Username:</strong> A unique login name (cannot be changed later)</li>
                                    <li><strong>Email:</strong> A valid email address for the user</li>
                                    <li><strong>Password:</strong> A secure password (minimum 8 characters)</li>
                                    <li><strong>Confirm Password:</strong> Re-enter the password to confirm</li>
                                </ul>
                            </li>
                            <li>
                                <strong>Assign a Role</strong>
                                <p>Select the appropriate role based on the user's responsibilities.</p>
                            </li>
                            <li>
                                <strong>Save the New User</strong>
                                <p>Click "Create User" to add the account to the system.</p>
                            </li>
                        </ol>

                        <h3>Managing Existing Users</h3>
                        <div class="image-preview">
                            <img src="../images/admin/user-management.jpg" alt="User Management Interface" onerror="this.src='images/admin/user-management.jpg'; if(this.src.indexOf('images/admin/user-management.jpg') !== -1) this.src='https://via.placeholder.com/800x450?text=User+Management+Interface';">
                            <figcaption>The User Management interface showing the list of users with action options</figcaption>
                        </div>

                        <div class="accordion">
                            <div class="accordion-item">
                                <div class="accordion-header">
                                    Editing User Information
                                    <i class="fas fa-chevron-down"></i>
                                </div>
                                <div class="accordion-content">
                                    <ol>
                                        <li>Navigate to the <strong>Users</strong> section</li>
                                        <li>Find the user you want to edit in the list</li>
                                        <li>Click the "Edit" button (pencil icon) next to their name</li>
                                        <li>Update their information as needed:
                                            <ul>
                                                <li>Email address</li>
                                                <li>Password (leave blank to keep current password)</li>
                                                <li>Role</li>
                                                <li>Profile information</li>
                                            </ul>
                                        </li>
                                        <li>Click "Save Changes" to apply your updates</li>
                                    </ol>
                                    <p><strong>Note:</strong> Usernames cannot be changed after an account is created.</p>
                                </div>
                            </div>

                            <div class="accordion-item">
                                <div class="accordion-header">
                                    Deactivating a User
                                    <i class="fas fa-chevron-down"></i>
                                </div>
                                <div class="accordion-content">
                                    <p>Deactivating a user prevents them from logging in without deleting their account.</p>
                                    <ol>
                                        <li>Navigate to the <strong>Users</strong> section</li>
                                        <li>Find the user you want to deactivate</li>
                                        <li>Click the "Status" toggle switch to change it from "Active" to "Inactive"</li>
                                        <li>Confirm the action in the popup dialog</li>
                                    </ol>
                                    <p>The user will remain in the system but will not be able to log in until reactivated.</p>
                                </div>
                            </div>

                            <div class="accordion-item">
                                <div class="accordion-header">
                                    Deleting a User
                                    <i class="fas fa-chevron-down"></i>
                                </div>
                                <div class="accordion-content">
                                    <p><strong>Warning:</strong> Deleting a user is permanent and cannot be undone. Consider deactivating instead.</p>
                                    <ol>
                                        <li>Navigate to the <strong>Users</strong> section</li>
                                        <li>Find the user you want to delete</li>
                                        <li>Click the "Delete" button (trash icon) next to their name</li>
                                        <li>Confirm the deletion in the popup dialog by typing the username</li>
                                    </ol>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="card">
                    <div class="card-header">
                        <h2 class="card-title">
                            <i class="fas fa-id-card"></i>
                            User Profiles
                        </h2>
                    </div>
                    <div class="card-body doc-content">
                        <p>Each user has a profile that contains personal information and preferences. Users can edit their own profiles, while administrators can edit any user's profile.</p>

                        <h3>Editing Your Profile</h3>
                        <ol>
                            <li>Click your username in the top-right corner of the admin panel</li>
                            <li>Select "Profile" from the dropdown menu</li>
                            <li>Update your information in the following tabs:
                                <ul>
                                    <li><strong>Personal Info:</strong> Name, email, bio, and profile picture</li>
                                    <li><strong>Password:</strong> Change your login password</li>
                                    <li><strong>Interface:</strong> Customize your admin panel experience</li>
                                    <li><strong>Notifications:</strong> Set your notification preferences</li>
                                </ul>
                            </li>
                            <li>Click "Save Changes" in each section to apply your updates</li>
                        </ol>

                        <h3>Profile Picture Guidelines</h3>
                        <div class="info-box">
                            <div class="info-box-title">
                                <i class="fas fa-image"></i>
                                Profile Image Requirements
                            </div>
                            <ul>
                                <li><strong>File Types:</strong> JPG, PNG, or GIF</li>
                                <li><strong>Maximum Size:</strong> 2MB</li>
                                <li><strong>Recommended Dimensions:</strong> 300x300 pixels (square)</li>
                                <li><strong>Best Practice:</strong> Use a professional headshot or appropriate avatar</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Settings tab -->
            <div id="settings" class="tab-content">
                <div class="content-header">
                    <div class="breadcrumbs">
                        <a href="#overview">Documentation</a>
                        <span class="separator">/</span>
                        <span>Settings</span>
                    </div>
                    <h1 class="content-title">Settings</h1>
                    <p class="content-subtitle">Configure system settings including email and news settings.</p>
                </div>

                <div class="card">
                    <div class="card-header">
                        <h2 class="card-title">
                            <i class="fas fa-cog"></i>
                            Settings Overview
                        </h2>
                    </div>
                    <div class="card-body doc-content">
                        <p>The Settings section allows you to configure various aspects of your website and admin panel. These settings control everything from email functionality to visual appearance and system behavior.</p>

                        <div class="info-box">
                            <div class="info-box-title">
                                <i class="fas fa-user-shield"></i>
                                Access Restrictions
                            </div>
                            <p>Only users with Administrator permissions can access and modify system settings. This restriction helps prevent unauthorized changes to critical system configurations.</p>
                        </div>

                        <h3>Settings Categories</h3>
                        <div class="comparison-table">
                            <table>
                                <thead>
                                    <tr>
                                        <th>Category</th>
                                        <th>Description</th>
                                        <th>Key Settings</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>
                                            <strong>General</strong>
                                            <span class="tag tag-primary">Core</span>
                                        </td>
                                        <td>Basic system configuration</td>
                                        <td>
                                            <ul>
                                                <li>Site name and description</li>
                                                <li>Admin email address</li>
                                                <li>Date and time format</li>
                                                <li>Default language</li>
                                                <li>Admin logo path</li>
                                            </ul>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>
                                            <strong>Email</strong>
                                            <span class="tag tag-primary">Communication</span>
                                        </td>
                                        <td>Email sending configuration</td>
                                        <td>
                                            <ul>
                                                <li>Mail method (PHP mail or SMTP)</li>
                                                <li>SMTP server settings</li>
                                                <li>From name and email</li>
                                                <li>Email templates with dedicated editor</li>
                                                <li>Test email functionality</li>
                                            </ul>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>
                                            <strong>News</strong>
                                            <span class="tag tag-secondary">Content</span>
                                        </td>
                                        <td>News system configuration</td>
                                        <td>
                                            <ul>
                                                <li>Posts per page</li>
                                                <li>Default category</li>
                                                <li>Image dimensions</li>
                                                <li>Excerpt length</li>
                                                <li>Dynamic content loading</li>
                                            </ul>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <div class="card">
                    <div class="card-header">
                        <h2 class="card-title">
                            <i class="fas fa-envelope"></i>
                            Email Configuration
                        </h2>
                    </div>
                    <div class="card-body doc-content">
                        <p>Email settings control how the system sends emails for contact form responses, notifications, and other automated communications.</p>

                        <h3>Mail Method Options</h3>
                        <div class="accordion">
                            <div class="accordion-item">
                                <div class="accordion-header">
                                    PHP mail() Function
                                    <i class="fas fa-chevron-down"></i>
                                </div>
                                <div class="accordion-content">
                                    <p>The PHP mail() function uses your server's default mail configuration to send emails.</p>
                                    <h4>Configuration Steps:</h4>
                                    <ol>
                                        <li>Select "PHP mail()" as the Mail Method</li>
                                        <li>Enter the From Name (e.g., "Manage Inc Support")</li>
                                        <li>Enter the From Email (e.g., "<EMAIL>")</li>
                                        <li>Enter the Reply-To Email if different from the From Email</li>
                                    </ol>
                                    <p><strong>Note:</strong> This method relies on your server's mail configuration. If emails are not being delivered, check with your hosting provider to ensure mail services are properly configured.</p>
                                </div>
                            </div>

                            <div class="accordion-item">
                                <div class="accordion-header">
                                    SMTP Server
                                    <i class="fas fa-chevron-down"></i>
                                </div>
                                <div class="accordion-content">
                                    <p>SMTP (Simple Mail Transfer Protocol) provides more reliable email delivery by connecting directly to a mail server.</p>
                                    <h4>Configuration Steps:</h4>
                                    <ol>
                                        <li>Select "SMTP" as the Mail Method</li>
                                        <li>Enter the SMTP Host (e.g., "smtp.gmail.com")</li>
                                        <li>Enter the SMTP Port (typically 587 for TLS or 465 for SSL)</li>
                                        <li>Select the Encryption type (TLS or SSL)</li>
                                        <li>Enter your SMTP Username (usually your email address)</li>
                                        <li>Enter your SMTP Password</li>
                                        <li>Enter the From Name and From Email</li>
                                        <li>Enter the Reply-To Email if different from the From Email</li>
                                    </ol>
                                    <p><strong>Recommended:</strong> SMTP is generally more reliable than PHP mail() and provides better delivery rates.</p>
                                </div>
                            </div>
                        </div>

                        <h3>Testing Email Configuration</h3>
                        <ol class="step-list">
                            <li>
                                <strong>Configure Email Settings</strong>
                                <p>Set up your preferred mail method as described above.</p>
                            </li>
                            <li>
                                <strong>Save Your Settings</strong>
                                <p>Click the "Save Settings" button to apply your configuration.</p>
                            </li>
                            <li>
                                <strong>Send a Test Email</strong>
                                <p>Click the "Test Email" button next to the "Use SMTP" option in the Email settings tab.</p>
                            </li>
                            <li>
                                <strong>Check Your Inbox</strong>
                                <p>Verify that the test email was received at the admin email address.</p>
                            </li>
                        </ol>

                        <h3>Email Templates</h3>
                        <p>Email templates can now be edited in a dedicated template editor page:</p>
                        <ol class="step-list">
                            <li>
                                <strong>Access Templates</strong>
                                <p>In the Email tab, scroll down to the Templates section.</p>
                            </li>
                            <li>
                                <strong>Edit a Template</strong>
                                <p>Click the edit icon next to the template you want to modify.</p>
                            </li>
                            <li>
                                <strong>Use the Template Editor</strong>
                                <p>The template editor opens in a new page with the following features:</p>
                                <ul>
                                    <li>HTML mode is selected by default</li>
                                    <li>Preview tab to see how the email will look</li>
                                    <li>Variable dropdown menus for easy insertion of dynamic content</li>
                                    <li>Save and Cancel buttons for managing your changes</li>
                                </ul>
                            </li>
                            <li>
                                <strong>Return to Templates</strong>
                                <p>Click "Back to Templates" to return to the settings page after saving.</p>
                            </li>
                        </ol>

                        <div class="info-box">
                            <div class="info-box-title">
                                <i class="fas fa-exclamation-triangle"></i>
                                Troubleshooting Email Issues
                            </div>
                            <ul>
                                <li><strong>Check Spam Folder:</strong> Test emails might be filtered as spam</li>
                                <li><strong>Verify Credentials:</strong> Ensure SMTP username and password are correct</li>
                                <li><strong>Port Blocking:</strong> Some networks block email ports (25, 465, 587)</li>
                                <li><strong>App Passwords:</strong> For Gmail, you may need to use an app password instead of your regular password</li>
                                <li><strong>Server Limits:</strong> Some hosting providers limit email sending</li>
                            </ul>
                        </div>

                        <h3>Admin Logo Configuration</h3>
                        <p>The admin logo can be configured in the General settings tab:</p>
                        <ol class="step-list">
                            <li>
                                <strong>Navigate to Settings</strong>
                                <p>Go to the Settings page and select the General tab.</p>
                            </li>
                            <li>
                                <strong>Update Logo Path</strong>
                                <p>Enter the path to your logo in the "Admin Logo Path" field.</p>
                            </li>
                            <li>
                                <strong>Real-time Preview</strong>
                                <p>The logo will update in real-time as you type, allowing you to see the changes immediately.</p>
                            </li>
                            <li>
                                <strong>Save Settings</strong>
                                <p>Click "Save Settings" to permanently apply your changes.</p>
                            </li>
                        </ol>

                        <div class="info-box">
                            <div class="info-box-title">
                                <i class="fas fa-info-circle"></i>
                                Admin Logo Tips
                            </div>
                            <ul>
                                <li><strong>Default Path:</strong> The default admin logo path is '../admin/images/logo.png'</li>
                                <li><strong>Logo Size:</strong> Recommended size is 200px × 50px</li>
                                <li><strong>Format:</strong> PNG format with transparency works best</li>
                                <li><strong>Theme Integration:</strong> The logo background will use the theme color automatically</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <div class="card">
                    <div class="card-header">
                        <h2 class="card-title">
                            <i class="fas fa-folder"></i>
                            Categories Management
                        </h2>
                    </div>
                    <div class="card-body doc-content">
                        <p>The Categories section allows you to organize your content by creating and managing categories. The interface has been redesigned to be more elegant and user-friendly.</p>

                        <h3>Categories Page Overview</h3>
                        <p>The Categories page features a clean, simplified design with clear visibility of text and components. Each category is displayed as a card with essential information and action buttons.</p>

                        <div class="info-box">
                            <div class="info-box-title">
                                <i class="fas fa-info-circle"></i>
                                Category Card Features
                            </div>
                            <ul>
                                <li><strong>Category Title:</strong> The name of the category</li>
                                <li><strong>Description:</strong> A brief description of the category's purpose</li>
                                <li><strong>Post Count:</strong> The number of posts assigned to the category</li>
                                <li><strong>Slug:</strong> The URL-friendly version of the category name</li>
                                <li><strong>Action Buttons:</strong> Edit and Delete options for managing the category</li>
                            </ul>
                        </div>

                        <h3>Managing Categories</h3>
                        <ol class="step-list">
                            <li>
                                <strong>Adding a New Category</strong>
                                <p>Click the "Add New Category" button at the top of the categories page.</p>
                                <ul>
                                    <li>Enter a name for the category</li>
                                    <li>Provide a description (optional but recommended)</li>
                                    <li>The slug will be generated automatically based on the name</li>
                                    <li>Click "Save" to create the category</li>
                                </ul>
                            </li>
                            <li>
                                <strong>Editing a Category</strong>
                                <p>Click the "Edit" button on the category card you want to modify.</p>
                                <ul>
                                    <li>Update the name, description, or slug as needed</li>
                                    <li>Click "Save Changes" to apply your modifications</li>
                                </ul>
                            </li>
                            <li>
                                <strong>Deleting a Category</strong>
                                <p>Click the "Delete" button on the category card you want to remove.</p>
                                <ul>
                                    <li>Confirm the deletion when prompted</li>
                                    <li>Note: You cannot delete categories that contain posts</li>
                                </ul>
                            </li>
                        </ol>

                        <div class="info-box">
                            <div class="info-box-title">
                                <i class="fas fa-lightbulb"></i>
                                Category Best Practices
                            </div>
                            <ul>
                                <li><strong>Descriptive Names:</strong> Use clear, concise names that accurately describe the content</li>
                                <li><strong>Consistent Structure:</strong> Maintain a logical hierarchy of categories</li>
                                <li><strong>Avoid Overlap:</strong> Each category should have a distinct purpose with minimal overlap</li>
                                <li><strong>Limit Depth:</strong> Try to keep your category structure relatively flat for better usability</li>
                                <li><strong>Regular Review:</strong> Periodically review and consolidate categories as your content evolves</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>

            <!-- HTML Editor tab -->
            <div id="editor" class="tab-content">
                <div class="content-header">
                    <div class="breadcrumbs">
                        <a href="#overview">Documentation</a>
                        <span class="separator">/</span>
                        <span>HTML Editor</span>
                    </div>
                    <h1 class="content-title">HTML Editor</h1>
                    <p class="content-subtitle">Edit website content directly through a visual HTML editor with code mode.</p>
                </div>

                <div class="card">
                    <div class="card-header">
                        <h2 class="card-title">
                            <i class="fas fa-code"></i>
                            Editor Overview
                        </h2>
                    </div>
                    <div class="card-body doc-content">
                        <p>The HTML Editor allows you to modify your website's HTML files directly from the admin panel. This tool provides both a visual editor for content changes and a code editor for more technical modifications.</p>

                        <div class="image-preview">
                            <img src="https://via.placeholder.com/800x450?text=HTML+Editor+Interface" alt="HTML Editor Interface">
                            <figcaption>The HTML Editor interface showing the file selector and editor pane</figcaption>
                        </div>

                        <h3>Editor Modes</h3>
                        <div class="comparison-table">
                            <table>
                                <thead>
                                    <tr>
                                        <th>Mode</th>
                                        <th>Best For</th>
                                        <th>Features</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>
                                            <strong>WYSIWYG Editor</strong>
                                            <span class="tag tag-primary">Content</span>
                                        </td>
                                        <td>Content editing, formatting text, adding images</td>
                                        <td>
                                            <ul>
                                                <li>Rich text formatting</li>
                                                <li>Image insertion and alignment</li>
                                                <li>Table creation and editing</li>
                                                <li>Link management</li>
                                                <li>No HTML knowledge required</li>
                                            </ul>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>
                                            <strong>Code Editor</strong>
                                            <span class="tag tag-secondary">HTML/CSS</span>
                                        </td>
                                        <td>HTML structure, CSS styling, advanced customizations</td>
                                        <td>
                                            <ul>
                                                <li>Syntax highlighting</li>
                                                <li>Line numbering</li>
                                                <li>Code folding</li>
                                                <li>Search and replace</li>
                                                <li>Full control over HTML/CSS</li>
                                            </ul>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>

                        <div class="info-box">
                            <div class="info-box-title">
                                <i class="fas fa-exclamation-circle"></i>
                                Important Considerations
                            </div>
                            <ul>
                                <li><strong>HTML Files Only:</strong> The HTML editor allows editing HTML files from your website</li>
                                <li><strong>Static Content:</strong> Best suited for static HTML content and page layouts</li>
                                <li><strong>Dynamic Content Templates:</strong> Files with dynamic content should be edited with caution</li>
                                <li><strong>Backup First:</strong> Always create a backup before making significant changes</li>
                                <li><strong>Version Comments:</strong> Add descriptive comments when saving changes to track modifications</li>
                                <li><strong>Preview Changes:</strong> Use the preview function to see how changes will look before publishing</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <div class="card">
                    <div class="card-header">
                        <h2 class="card-title">
                            <i class="fas fa-folder-open"></i>
                            File Selection
                        </h2>
                    </div>
                    <div class="card-body doc-content">
                        <p>The HTML Editor provides access to HTML files on your website through a dropdown file selector. You can select any HTML file to edit using the visual editor or code mode.</p>

                        <h3>File Structure</h3>
                        <div class="accordion">
                            <div class="accordion-item">
                                <div class="accordion-header">
                                    Pages Directory
                                    <i class="fas fa-chevron-down"></i>
                                </div>
                                <div class="accordion-content">
                                    <p>Contains individual static HTML page files for your website:</p>
                                    <ul>
                                        <li><strong>about.html</strong> - About page</li>
                                        <li><strong>services.html</strong> - Services page</li>
                                        <li><strong>contact-us.html</strong> - Contact page</li>
                                        <li><strong>news.html</strong> - News listing page (static template only)</li>
                                        <li><strong>news-detail.html</strong> - News article template (static template only)</li>
                                        <li>Other static content pages</li>
                                    </ul>
                                    <p><strong>Important:</strong> The news.html and news-detail.html files are templates that work with dynamic content loaded from the database. Edit these files with caution to avoid breaking the dynamic functionality.</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="card">
                    <div class="card-header">
                        <h2 class="card-title">
                            <i class="fas fa-edit"></i>
                            Editing Content
                        </h2>
                    </div>
                    <div class="card-body doc-content">
                        <h3>Using the WYSIWYG Editor</h3>
                        <ol class="step-list">
                            <li>
                                <strong>Select the File</strong>
                                <p>Use the file dropdown to select the HTML file you want to edit.</p>
                            </li>
                            <li>
                                <strong>Switch to WYSIWYG Mode</strong>
                                <p>If not already selected, click the "Visual Editor" tab at the top of the editor.</p>
                            </li>
                            <li>
                                <strong>Make Content Changes</strong>
                                <p>Use the formatting toolbar to modify text, add images, create links, etc.</p>
                            </li>
                            <li>
                                <strong>Preview Your Changes</strong>
                                <p>Click the "Preview" button to see how your changes will look on the live site.</p>
                            </li>
                            <li>
                                <strong>Add a Version Comment</strong>
                                <p>Enter a brief description of your changes in the "Version Comment" field.</p>
                            </li>
                            <li>
                                <strong>Save Your Changes</strong>
                                <p>Click "Save Changes" to publish your modifications to the live site.</p>
                            </li>
                        </ol>

                        <h3>Using the Code Editor</h3>
                        <div class="info-box">
                            <div class="info-box-title">
                                <i class="fas fa-code"></i>
                                HTML Editing Tips
                            </div>
                            <ul>
                                <li><strong>Maintain Structure:</strong> Be careful not to remove or modify essential HTML tags like <code>&lt;html&gt;</code>, <code>&lt;head&gt;</code>, or <code>&lt;body&gt;</code></li>
                                <li><strong>Preserve Includes:</strong> Don't remove code that includes the header or footer</li>
                                <li><strong>Check for Errors:</strong> Ensure all tags are properly closed and nested</li>
                                <li><strong>Use Comments:</strong> Add HTML comments to mark sections for easier future editing</li>
                                <li><strong>Indentation:</strong> Maintain proper indentation for readability</li>
                            </ul>
                        </div>

                        <h3>Working with Images</h3>
                        <div class="progress-steps">
                            <div class="progress-step active">
                                <div class="progress-step-icon">1</div>
                                <div class="progress-step-label">Upload</div>
                            </div>
                            <div class="progress-step">
                                <div class="progress-step-icon">2</div>
                                <div class="progress-step-label">Select</div>
                            </div>
                            <div class="progress-step">
                                <div class="progress-step-icon">3</div>
                                <div class="progress-step-label">Insert</div>
                            </div>
                            <div class="progress-step">
                                <div class="progress-step-icon">4</div>
                                <div class="progress-step-label">Align</div>
                            </div>
                        </div>

                        <p>To add images to your content:</p>
                        <ol>
                            <li>Position your cursor where you want to insert the image</li>
                            <li>Click the image icon in the editor toolbar</li>
                            <li>Choose "Upload" to add a new image or "Browse" to select an existing one</li>
                            <li>If uploading, select the file from your computer</li>
                            <li>Set image properties:
                                <ul>
                                    <li>Alternative text (for accessibility)</li>
                                    <li>Dimensions (width and height)</li>
                                    <li>Alignment (left, center, right)</li>
                                    <li>Border and spacing</li>
                                </ul>
                            </li>
                            <li>Click "Insert" to add the image to your content</li>
                        </ol>
                    </div>
                </div>
            </div>

            <!-- Inbox tab -->
            <div id="inbox" class="tab-content">
                <div class="content-header">
                    <div class="breadcrumbs">
                        <a href="#overview">Documentation</a>
                        <span class="separator">/</span>
                        <span>Inbox</span>
                    </div>
                    <h1 class="content-title">Inbox</h1>
                    <p class="content-subtitle">View and respond to contact form submissions from your website visitors.</p>
                </div>

                <div class="card">
                    <div class="card-header">
                        <h2 class="card-title">
                            <i class="fas fa-inbox"></i>
                            Inbox Overview
                        </h2>
                    </div>
                    <div class="card-body doc-content">
                        <p>The Inbox feature allows you to manage all contact form submissions from your website in one centralized location. You can view messages, respond to inquiries, and track communication history with your visitors.</p>

                        <div class="image-preview">
                            <img src="https://via.placeholder.com/800x450?text=Inbox+Interface" alt="Inbox Interface">
                            <figcaption>The Inbox interface showing the list of messages with filtering and action options</figcaption>
                        </div>

                        <h3>Message Status Indicators</h3>
                        <div class="comparison-table">
                            <table>
                                <thead>
                                    <tr>
                                        <th>Status</th>
                                        <th>Indicator</th>
                                        <th>Description</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>
                                            <span class="tag tag-primary">New</span>
                                        </td>
                                        <td><i class="fas fa-circle" style="color: #f1ca2f;"></i> Yellow dot</td>
                                        <td>Unread message that has not been viewed</td>
                                    </tr>
                                    <tr>
                                        <td>
                                            <span class="tag tag-secondary">Read</span>
                                        </td>
                                        <td><i class="fas fa-check" style="color: #666;"></i> Gray check</td>
                                        <td>Message has been viewed but not replied to</td>
                                    </tr>
                                    <tr>
                                        <td>
                                            <span class="tag tag-success">Replied</span>
                                        </td>
                                        <td><i class="fas fa-reply" style="color: #28a745;"></i> Green reply icon</td>
                                        <td>Message has been replied to</td>
                                    </tr>
                                    <tr>
                                        <td>
                                            <span class="tag tag-danger">Flagged</span>
                                        </td>
                                        <td><i class="fas fa-flag" style="color: #dc3545;"></i> Red flag</td>
                                        <td>Message has been marked for follow-up</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>

                        <div class="info-box">
                            <div class="info-box-title">
                                <i class="fas fa-bell"></i>
                                Inbox Notifications
                            </div>
                            <p>When new messages arrive in your inbox, you'll receive notifications in several ways:</p>
                            <ul>
                                <li><strong>Badge Counter:</strong> A number badge appears on the Inbox icon in the sidebar</li>
                                <li><strong>Header Notification:</strong> A notification appears in the admin header</li>
                                <li><strong>Email Alert:</strong> If enabled in settings, you'll receive an email notification</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <div class="card">
                    <div class="card-header">
                        <h2 class="card-title">
                            <i class="fas fa-envelope-open-text"></i>
                            Working with Messages
                        </h2>
                    </div>
                    <div class="card-body doc-content">
                        <h3>Viewing Messages</h3>
                        <ol class="step-list">
                            <li>
                                <strong>Navigate to Inbox</strong>
                                <p>Access the inbox from the sidebar menu.</p>
                            </li>
                            <li>
                                <strong>Browse Messages</strong>
                                <p>Messages are listed with the most recent at the top. Unread messages are highlighted.</p>
                            </li>
                            <li>
                                <strong>Open a Message</strong>
                                <p>Click on a message to view its full content.</p>
                            </li>
                            <li>
                                <strong>View Message Details</strong>
                                <p>The message view shows:
                                    <ul>
                                        <li>Sender's name and email</li>
                                        <li>Subject line</li>
                                        <li>Date and time received</li>
                                        <li>Message content</li>
                                        <li>Any attachments (if enabled)</li>
                                    </ul>
                                </p>
                            </li>
                        </ol>

                        <h3>Responding to Messages</h3>
                        <div class="accordion">
                            <div class="accordion-item">
                                <div class="accordion-header">
                                    Replying to a Message
                                    <i class="fas fa-chevron-down"></i>
                                </div>
                                <div class="accordion-content">
                                    <ol>
                                        <li>Open the message you want to reply to</li>
                                        <li>Click the "Reply" button at the bottom of the message</li>
                                        <li>A reply form will open with the recipient's email pre-filled</li>
                                        <li>Choose a template from the dropdown or write a custom message</li>
                                        <li>Use the editor to format your response</li>
                                        <li>Click "Send" to deliver your reply</li>
                                    </ol>
                                    <p>The message status will automatically update to "Replied" after sending.</p>
                                </div>
                            </div>

                            <div class="accordion-item">
                                <div class="accordion-header">
                                    Using Email Templates
                                    <i class="fas fa-chevron-down"></i>
                                </div>
                                <div class="accordion-content">
                                    <p>Email templates help you respond quickly with consistent messaging:</p>
                                    <ol>
                                        <li>When replying to a message, click the "Templates" dropdown</li>
                                        <li>Select a template from the list</li>
                                        <li>The template will load into the editor with variables automatically filled in:
                                            <ul>
                                                <li><code>{contact_name}</code> - The sender's name</li>
                                                <li><code>{contact_email}</code> - The sender's email</li>
                                                <li><code>{contact_subject}</code> - The original subject</li>
                                                <li><code>{contact_message}</code> - The original message</li>
                                                <li><code>{company_name}</code> - Your company name</li>
                                            </ul>
                                        </li>
                                        <li>Customize the template as needed before sending</li>
                                    </ol>
                                </div>
                            </div>

                            <div class="accordion-item">
                                <div class="accordion-header">
                                    Flagging Messages for Follow-up
                                    <i class="fas fa-chevron-down"></i>
                                </div>
                                <div class="accordion-content">
                                    <p>Flag important messages that need additional attention:</p>
                                    <ol>
                                        <li>Open the message you want to flag</li>
                                        <li>Click the "Flag" button at the top of the message</li>
                                        <li>Optionally, add a note explaining why the message is flagged</li>
                                        <li>Click "Save" to apply the flag</li>
                                    </ol>
                                    <p>Flagged messages appear with a red flag icon in the inbox list and can be filtered for easy access.</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="card">
                    <div class="card-header">
                        <h2 class="card-title">
                            <i class="fas fa-filter"></i>
                            Organizing Your Inbox
                        </h2>
                    </div>
                    <div class="card-body doc-content">
                        <h3>Filtering Messages</h3>
                        <p>Use the filter options at the top of the inbox to find specific messages:</p>
                        <ul>
                            <li><strong>Status Filter:</strong> Show only New, Read, Replied, or Flagged messages</li>
                            <li><strong>Date Range:</strong> Filter messages received within a specific time period</li>
                            <li><strong>Search:</strong> Find messages containing specific text in the sender, subject, or content</li>
                        </ul>

                        <h3>Bulk Actions</h3>
                        <p>Perform actions on multiple messages at once:</p>
                        <ol>
                            <li>Select messages by checking the boxes next to them</li>
                            <li>Use the "Bulk Actions" dropdown to choose an action:
                                <ul>
                                    <li><strong>Mark as Read:</strong> Change status to Read</li>
                                    <li><strong>Mark as Unread:</strong> Change status to New</li>
                                    <li><strong>Flag:</strong> Add flag for follow-up</li>
                                    <li><strong>Remove Flag:</strong> Clear flag status</li>
                                    <li><strong>Delete:</strong> Remove selected messages</li>
                                </ul>
                            </li>
                            <li>Click "Apply" to execute the selected action</li>
                        </ol>

                        <div class="info-box">
                            <div class="info-box-title">
                                <i class="fas fa-exclamation-triangle"></i>
                                Important Notes
                            </div>
                            <ul>
                                <li><strong>Message Retention:</strong> By default, messages are stored indefinitely. Check your organization's data retention policy.</li>
                                <li><strong>Deleted Messages:</strong> Once deleted, messages cannot be recovered. Consider archiving instead of deleting.</li>
                                <li><strong>Privacy Compliance:</strong> Ensure your handling of contact form data complies with applicable privacy regulations.</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>

            <!-- News Management tab -->
            <div id="news" class="tab-content">
                <div class="content-header">
                    <div class="breadcrumbs">
                        <a href="#overview">Documentation</a>
                        <span class="separator">/</span>
                        <span>News Management</span>
                    </div>
                    <h1 class="content-title">News Management</h1>
                    <p class="content-subtitle">Create, edit, and organize news posts and categories for your website.</p>
                </div>

                <div class="card">
                    <div class="card-header">
                        <h2 class="card-title">
                            <i class="fas fa-newspaper"></i>
                            Overview
                        </h2>
                    </div>
                    <div class="card-body doc-content">
                        <p>The News Management system allows you to create and manage all the news content on your website. This includes creating new posts, organizing them into categories, and controlling how they appear on your site.</p>

                        <div class="info-box">
                            <div class="info-box-title">
                                <i class="fas fa-lightbulb"></i>
                                Key Features
                            </div>
                            <ul>
                                <li>Create, edit, and delete news posts</li>
                                <li>Organize posts into categories</li>
                                <li>Upload and manage featured images</li>
                                <li>Set SEO-friendly URLs (slugs)</li>
                                <li>Format content with the WYSIWYG editor</li>
                                <li>Preview posts before publishing</li>
                            </ul>
                        </div>

                        <h3>News Management Interface</h3>
                        <div class="image-preview">
                            <img src="https://via.placeholder.com/800x450?text=News+Management+Interface" alt="News Management Interface">
                            <figcaption>The News Management interface showing the list of posts with filtering and action options</figcaption>
                        </div>

                        <h3>News Post Workflow</h3>
                        <div class="progress-steps">
                            <div class="progress-step active">
                                <div class="progress-step-icon">1</div>
                                <div class="progress-step-label">Create</div>
                            </div>
                            <div class="progress-step">
                                <div class="progress-step-icon">2</div>
                                <div class="progress-step-label">Edit</div>
                            </div>
                            <div class="progress-step">
                                <div class="progress-step-icon">3</div>
                                <div class="progress-step-label">Preview</div>
                            </div>
                            <div class="progress-step">
                                <div class="progress-step-icon">4</div>
                                <div class="progress-step-label">Publish</div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="card">
                    <div class="card-header">
                        <h2 class="card-title">
                            <i class="fas fa-edit"></i>
                            Creating and Editing News Posts
                        </h2>
                    </div>
                    <div class="card-body doc-content">
                        <h3>Creating a New Post</h3>
                        <ol class="step-list">
                            <li>
                                <strong>Navigate to News > Add News</strong>
                                <p>Access the news creation page from the sidebar menu.</p>
                            </li>
                            <li>
                                <strong>Enter Post Title</strong>
                                <p>Create a descriptive, engaging title for your news post.</p>
                            </li>
                            <li>
                                <strong>Select or Create a Category</strong>
                                <p>Choose an existing category or create a new one by clicking the "+" icon next to the dropdown.</p>
                            </li>
                            <li>
                                <strong>Add Content Using the Editor</strong>
                                <p>Use the WYSIWYG editor to format your content with headings, lists, links, and images.</p>
                            </li>
                            <li>
                                <strong>Upload a Featured Image</strong>
                                <p>Select an image that represents your post (recommended size: 1200x800px).</p>
                            </li>
                            <li>
                                <strong>Set SEO-Friendly URL</strong>
                                <p>The slug field will auto-generate based on your title, but you can customize it if needed.</p>
                            </li>
                            <li>
                                <strong>Preview and Publish</strong>
                                <p>Review your post using the Preview button, then click "Save" to publish it.</p>
                            </li>
                        </ol>

                        <h3>WYSIWYG Editor Features</h3>
                        <div class="comparison-table">
                            <table>
                                <thead>
                                    <tr>
                                        <th>Feature</th>
                                        <th>Description</th>
                                        <th>Usage</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>Text Formatting</td>
                                        <td>Bold, italic, underline, strikethrough</td>
                                        <td>Select text and click the formatting button</td>
                                    </tr>
                                    <tr>
                                        <td>Headings</td>
                                        <td>H1-H6 heading styles</td>
                                        <td>Select text and choose heading from dropdown</td>
                                    </tr>
                                    <tr>
                                        <td>Lists</td>
                                        <td>Bulleted and numbered lists</td>
                                        <td>Click list button and enter items</td>
                                    </tr>
                                    <tr>
                                        <td>Links</td>
                                        <td>Hyperlinks to internal or external pages</td>
                                        <td>Select text and click link button</td>
                                    </tr>
                                    <tr>
                                        <td>Images</td>
                                        <td>Insert and align images in content</td>
                                        <td>Click image button to upload or select</td>
                                    </tr>
                                    <tr>
                                        <td>Tables</td>
                                        <td>Create structured data tables</td>
                                        <td>Click table button and set dimensions</td>
                                    </tr>
                                    <tr>
                                        <td>HTML Mode</td>
                                        <td>Direct HTML editing</td>
                                        <td>Click the code view button</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <div class="card">
                    <div class="card-header">
                        <h2 class="card-title">
                            <i class="fas fa-search"></i>
                            SEO Features
                        </h2>
                    </div>
                    <div class="card-body doc-content">
                        <p>Our website is equipped with powerful SEO features that help improve your visibility in search engines and drive more organic traffic to your content.</p>

                        <h3>Clean, SEO-Friendly URLs</h3>
                        <div class="comparison-table">
                            <table>
                                <thead>
                                    <tr>
                                        <th>Old URL Format</th>
                                        <th>New SEO-Friendly Format</th>
                                        <th>Benefits</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>news-detail.html?slug=cloud-computing-trends</td>
                                        <td>cloud-computing-trends.html</td>
                                        <td>
                                            <ul>
                                                <li>More readable and memorable</li>
                                                <li>Keywords in URL improve rankings</li>
                                                <li>Higher click-through rates in search results</li>
                                            </ul>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>

                        <h3>Automatic XML Sitemap Generation</h3>
                        <p>The system automatically generates and updates an XML sitemap whenever you create, edit, or delete news content. This ensures search engines always have the latest information about your site structure.</p>

                        <div class="info-box">
                            <div class="info-box-title">
                                <i class="fas fa-lightbulb"></i>
                                Why XML Sitemaps Matter
                            </div>
                            <ul>
                                <li><strong>Faster Indexing:</strong> Search engines discover your new content more quickly</li>
                                <li><strong>Complete Coverage:</strong> Ensures all your important pages are found and indexed</li>
                                <li><strong>Update Signals:</strong> Tells search engines when content has been modified</li>
                                <li><strong>Hierarchy Information:</strong> Communicates the relative importance of different pages</li>
                            </ul>
                        </div>

                        <h3>Meta Tags and Structured Data</h3>
                        <p>Each news article automatically includes optimized meta tags and structured data markup:</p>

                        <div class="comparison-table">
                            <table>
                                <thead>
                                    <tr>
                                        <th>SEO Feature</th>
                                        <th>Description</th>
                                        <th>Impact</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>Meta Description</td>
                                        <td>Automatically generated from article content</td>
                                        <td>Improves click-through rates in search results</td>
                                    </tr>
                                    <tr>
                                        <td>Canonical Tags</td>
                                        <td>Prevents duplicate content issues</td>
                                        <td>Consolidates ranking signals to the preferred URL</td>
                                    </tr>
                                    <tr>
                                        <td>Open Graph Tags</td>
                                        <td>Optimizes content sharing on social media</td>
                                        <td>Better presentation when shared on Facebook, LinkedIn, etc.</td>
                                    </tr>
                                    <tr>
                                        <td>Twitter Card Tags</td>
                                        <td>Enhances Twitter sharing appearance</td>
                                        <td>More engaging and clickable tweets with your content</td>
                                    </tr>
                                    <tr>
                                        <td>JSON-LD Structured Data</td>
                                        <td>Provides context about content to search engines</td>
                                        <td>Enables rich results in search (enhanced listings)</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>

                        <h3>SEO Best Practices</h3>
                        <div class="info-box">
                            <div class="info-box-title">
                                <i class="fas fa-check-circle"></i>
                                Tips for Better Search Rankings
                            </div>
                            <ul>
                                <li><strong>Descriptive Titles:</strong> Create clear, keyword-rich titles for your news articles</li>
                                <li><strong>Quality Content:</strong> Write comprehensive, valuable content that answers user questions</li>
                                <li><strong>Proper Formatting:</strong> Use headings (H2, H3) to structure your content logically</li>
                                <li><strong>Image Optimization:</strong> Include descriptive filenames and alt text for images</li>
                                <li><strong>Internal Linking:</strong> Link between related articles to help users and search engines navigate</li>
                                <li><strong>Regular Updates:</strong> Frequently add new content and update existing articles</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <div class="card">
                    <div class="card-header">
                        <h2 class="card-title">
                            <i class="fas fa-folder"></i>
                            Managing Categories
                        </h2>
                    </div>
                    <div class="card-body doc-content">
                        <p>Categories help organize your news posts into logical groups, making it easier for visitors to find related content.</p>

                        <div class="accordion">
                            <div class="accordion-item">
                                <div class="accordion-header">
                                    Creating a New Category
                                    <i class="fas fa-chevron-down"></i>
                                </div>
                                <div class="accordion-content">
                                    <ol>
                                        <li>Navigate to <strong>News > Categories</strong> in the sidebar menu</li>
                                        <li>Click the "Add Category" button</li>
                                        <li>Enter a descriptive name for your category</li>
                                        <li>Optionally, add a description to explain what type of content belongs in this category</li>
                                        <li>Click "Save" to create the category</li>
                                    </ol>
                                </div>
                            </div>

                            <div class="accordion-item">
                                <div class="accordion-header">
                                    Editing Categories
                                    <i class="fas fa-chevron-down"></i>
                                </div>
                                <div class="accordion-content">
                                    <ol>
                                        <li>Navigate to <strong>News > Categories</strong> in the sidebar menu</li>
                                        <li>Find the category you want to edit in the list</li>
                                        <li>Click the "Edit" button (pencil icon) next to the category name</li>
                                        <li>Update the name or description as needed</li>
                                        <li>Click "Save" to apply your changes</li>
                                    </ol>
                                </div>
                            </div>

                            <div class="accordion-item">
                                <div class="accordion-header">
                                    Deleting Categories
                                    <i class="fas fa-chevron-down"></i>
                                </div>
                                <div class="accordion-content">
                                    <p><strong>Important:</strong> When you delete a category, any posts assigned to that category will be moved to the default "Uncategorized" category.</p>
                                    <ol>
                                        <li>Navigate to <strong>News > Categories</strong> in the sidebar menu</li>
                                        <li>Find the category you want to delete in the list</li>
                                        <li>Click the "Delete" button (trash icon) next to the category name</li>
                                        <li>Confirm the deletion in the popup dialog</li>
                                    </ol>
                                </div>
                            </div>
                        </div>

                        <h3>Category Best Practices</h3>
                        <div class="info-box">
                            <div class="info-box-title">
                                <i class="fas fa-check-circle"></i>
                                Tips for Effective Categories
                            </div>
                            <ul>
                                <li><strong>Use Clear Names:</strong> Choose descriptive, concise category names that visitors will understand</li>
                                <li><strong>Limit the Number:</strong> Aim for 5-10 main categories to avoid overwhelming users</li>
                                <li><strong>Be Consistent:</strong> Use a consistent naming convention for all categories</li>
                                <li><strong>Review Regularly:</strong> Periodically review your categories and consolidate or split them as needed</li>
                                <li><strong>Consider Navigation:</strong> Remember that categories may appear in your site navigation</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Glossary tab -->
            <div id="glossary" class="tab-content">
                <div class="content-header">
                    <div class="breadcrumbs">
                        <a href="#overview">Documentation</a>
                        <span class="separator">/</span>
                        <span>Glossary</span>
                    </div>
                    <h1 class="content-title">Glossary</h1>
                    <p class="content-subtitle">Definitions of technical terms used throughout the admin panel.</p>
                </div>

                <div class="card">
                    <div class="card-header">
                        <h2 class="card-title">
                            <i class="fas fa-book-open"></i>
                            Terminology Reference
                        </h2>
                    </div>
                    <div class="card-body doc-content">
                        <p>This glossary provides definitions for technical terms and concepts used throughout the Manage Inc Admin Panel.</p>

                        <div class="info-box">
                            <div class="info-box-title">
                                <i class="fas fa-search"></i>
                                Finding Terms
                            </div>
                            <p>Use your browser's search function (Ctrl+F or Cmd+F) to quickly find specific terms in this glossary.</p>
                        </div>

                        <h3 id="glossary-a">A</h3>
                        <dl>
                            <dt>Admin Panel</dt>
                            <dd>The web-based interface used to manage your website's content, users, and settings.</dd>

                            <dt>Administrator</dt>
                            <dd>A user role with full access to all features and settings in the admin panel.</dd>

                            <dt>API Key</dt>
                            <dd>A unique identifier used to authenticate and access external services, such as Google Fonts.</dd>
                        </dl>

                        <h3 id="glossary-c">C</h3>
                        <dl>
                            <dt>Category</dt>
                            <dd>A classification used to organize news posts into related groups.</dd>

                            <dt>CMS (Content Management System)</dt>
                            <dd>Software that allows users to create, edit, and publish digital content without specialized technical knowledge.</dd>

                            <dt>Collaborative Editing</dt>
                            <dd>A feature that prevents multiple users from editing the same content simultaneously to avoid conflicts.</dd>

                            <dt>CSS (Cascading Style Sheets)</dt>
                            <dd>A stylesheet language used to describe the presentation of a document written in HTML.</dd>
                        </dl>

                        <h3 id="glossary-d">D</h3>
                        <dl>
                            <dt>Dashboard</dt>
                            <dd>The main overview page of the admin panel showing statistics and recent activity.</dd>

                            <dt>Dark Mode</dt>
                            <dd>A display setting that uses a dark color scheme to reduce eye strain and save battery power.</dd>

                            <dt>Dynamic Content</dt>
                            <dd>Website content that changes based on user interactions, database queries, or other variables.</dd>
                        </dl>

                        <h3 id="glossary-e">E</h3>
                        <dl>
                            <dt>Editor</dt>
                            <dd>A user role that can manage content but cannot access system settings or user management.</dd>

                            <dt>Email Template</dt>
                            <dd>A pre-designed message format used for automated emails sent by the system.</dd>
                        </dl>

                        <h3 id="glossary-f">F</h3>
                        <dl>
                            <dt>Featured Image</dt>
                            <dd>The main image associated with a news post, displayed in listings and at the top of the post.</dd>

                            <dt>Frontend</dt>
                            <dd>The public-facing part of your website that visitors see.</dd>

                            <dt>Frontend Editor</dt>
                            <dd>A tool that allows you to edit website files directly through the admin panel.</dd>
                        </dl>

                        <h3 id="glossary-h">H</h3>
                        <dl>
                            <dt>HTML (HyperText Markup Language)</dt>
                            <dd>The standard markup language for documents designed to be displayed in a web browser.</dd>

                            <dt>HTML Editor</dt>
                            <dd>A tool that allows direct editing of HTML code, providing more control over page structure and content.</dd>
                        </dl>

                        <h3 id="glossary-p">P</h3>
                        <dl>
                            <dt>Permissions</dt>
                            <dd>Access rights granted to users that determine what actions they can perform in the admin panel.</dd>

                            <dt>PHP</dt>
                            <dd>A server-side scripting language designed for web development.</dd>
                        </dl>

                        <h3 id="glossary-r">R</h3>
                        <dl>
                            <dt>Responsive Design</dt>
                            <dd>A web design approach that makes web pages render well on a variety of devices and window or screen sizes.</dd>

                            <dt>Role</dt>
                            <dd>A set of permissions assigned to users that determines their access level in the admin panel.</dd>
                        </dl>

                        <h3 id="glossary-s">S</h3>
                        <dl>
                            <dt>Slug</dt>
                            <dd>A URL-friendly version of a string, typically a post title, containing only letters, numbers, and hyphens.</dd>

                            <dt>SMTP (Simple Mail Transfer Protocol)</dt>
                            <dd>An internet standard for email transmission used by the system to send emails.</dd>

                            <dt>Static Site</dt>
                            <dd>A website that consists of fixed HTML pages, as opposed to dynamic content generated on-the-fly.</dd>
                        </dl>

                        <h3 id="glossary-t">T</h3>
                        <dl>
                            <dt>Template</dt>
                            <dd>A pre-designed layout or pattern used as a starting point for creating content with consistent formatting.</dd>

                            <dt>Template Variables</dt>
                            <dd>Placeholders in email templates that are automatically replaced with actual values when the email is sent.</dd>
                        </dl>

                        <h3 id="glossary-u">U</h3>
                        <dl>
                            <dt>URL (Uniform Resource Locator)</dt>
                            <dd>The address of a web page or file on the internet.</dd>

                            <dt>User Interface (UI)</dt>
                            <dd>The visual elements and controls through which users interact with the admin panel.</dd>
                        </dl>

                        <h3 id="glossary-v">V</h3>
                        <dl>
                            <dt>Version Comment</dt>
                            <dd>A note added when saving changes to a file, describing what was modified.</dd>

                            <dt>Viewer</dt>
                            <dd>A user role with read-only access to content in the admin panel.</dd>
                        </dl>

                        <h3 id="glossary-w">W</h3>
                        <dl>
                            <dt>WYSIWYG (What You See Is What You Get)</dt>
                            <dd>An editor that allows you to see how the content will appear while you're editing it.</dd>
                        </dl>
                    </div>
                </div>
            </div>

            <!-- FAQ tab -->
            <div id="faq" class="tab-content">
                <div class="content-header">
                    <div class="breadcrumbs">
                        <a href="#overview">Documentation</a>
                        <span class="separator">/</span>
                        <span>FAQ</span>
                    </div>
                    <h1 class="content-title">Frequently Asked Questions</h1>
                    <p class="content-subtitle">Answers to common questions about the admin panel.</p>
                </div>

                <div class="card">
                    <div class="card-body doc-content">
                        <h2>General Questions</h2>

                        <h3>How do I reset my admin password?</h3>
                        <p>Use the "Forgot Password" link on the login page. You'll receive an email with instructions to reset your password. If you don't have access to your email, contact your system administrator.</p>

                        <h3>Can multiple users edit the same content simultaneously?</h3>
                        <p>The system has collaborative editing protection. If someone is editing a file, others will see it's locked. This prevents conflicting changes.</p>

                        <h3>How do I know if there are new contact submissions?</h3>
                        <p>New messages are indicated by a notification badge on the Inbox icon in the sidebar. You'll also see notifications in the admin header.</p>

                        <h2>Content Management</h2>

                        <h3>Why can't I edit certain files in the HTML Editor?</h3>
                        <p>Some files may be protected or contain dynamic content that requires careful editing. Always make backups before making changes.</p>

                        <h3>How do I create a new page for my website?</h3>
                        <p>Create a new HTML file on your server and then use the HTML Editor to edit its content. Make sure to include proper header and footer references.</p>

                        <h3>Can I schedule news posts to publish later?</h3>
                        <p>The current version doesn't support scheduled publishing. All posts are published immediately when saved.</p>
                    </div>
                </div>
            </div>
        </div>









                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Mobile menu toggle
        const menuToggle = document.getElementById('menuToggle');
        const sidebar = document.getElementById('sidebar');
        const overlay = document.getElementById('overlay');

        menuToggle.addEventListener('click', () => {
            sidebar.classList.toggle('active');
            overlay.classList.toggle('active');
            menuToggle.innerHTML = sidebar.classList.contains('active') ?
                '<i class="fas fa-times"></i>' : '<i class="fas fa-bars"></i>';
        });

        overlay.addEventListener('click', () => {
            sidebar.classList.remove('active');
            overlay.classList.remove('active');
            menuToggle.innerHTML = '<i class="fas fa-bars"></i>';
        });

        // Tab navigation
        const navLinks = document.querySelectorAll('.nav-link');
        const tabContents = document.querySelectorAll('.tab-content');
        const featureLinks = document.querySelectorAll('.feature-link');

        // Function to activate a tab
        function activateTab(tabId) {
            // Remove active class from all links and tabs
            navLinks.forEach(link => link.classList.remove('active'));
            tabContents.forEach(tab => tab.classList.remove('active'));

            // Add active class to corresponding link
            const link = document.querySelector(`.nav-link[data-tab="${tabId}"]`);
            if (link) {
                link.classList.add('active');
            }

            // Show corresponding tab content
            const tabContent = document.getElementById(tabId);
            if (tabContent) {
                tabContent.classList.add('active');
            }

            // Close mobile menu if open
            if (window.innerWidth < 992) {
                sidebar.classList.remove('active');
                overlay.classList.remove('active');
                menuToggle.innerHTML = '<i class="fas fa-bars"></i>';
            }

            // Update URL hash
            window.location.hash = tabId;
        }

        // Set up click handlers for navigation links
        navLinks.forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                const tabId = link.getAttribute('data-tab');
                activateTab(tabId);
            });
        });

        // Set up click handlers for feature card links
        featureLinks.forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                const tabId = link.getAttribute('data-tab');
                activateTab(tabId);
            });
        });

        // Handle URL hash on page load
        document.addEventListener('DOMContentLoaded', () => {
            if (window.location.hash) {
                const hash = window.location.hash.substring(1);
                activateTab(hash);
            }
        });

        // Search functionality
        const searchInput = document.getElementById('searchInput');

        searchInput.addEventListener('input', () => {
            const searchTerm = searchInput.value.toLowerCase();

            if (searchTerm.length < 2) {
                navLinks.forEach(link => {
                    link.parentElement.style.display = 'block';
                });
                return;
            }

            navLinks.forEach(link => {
                const text = link.textContent.toLowerCase();
                if (text.includes(searchTerm)) {
                    link.parentElement.style.display = 'block';
                } else {
                    link.parentElement.style.display = 'none';
                }
            });
        });

        // Accordion functionality
        document.addEventListener('click', function(e) {
            if (e.target.closest('.accordion-header')) {
                const accordionItem = e.target.closest('.accordion-item');
                const isActive = accordionItem.classList.contains('active');

                // Close all accordion items
                document.querySelectorAll('.accordion-item').forEach(item => {
                    item.classList.remove('active');
                });

                // Open clicked item if it wasn't already open
                if (!isActive) {
                    accordionItem.classList.add('active');
                }
            }
        });
    </script>

    <!-- Security Features Tab Content (Hidden by default, activated by navigation) -->
    <div id="security-features" class="tab-content">
        <div class="content-header">
            <div class="breadcrumbs">
                <a href="#overview">Documentation</a>
                <span class="separator">/</span>
                <span>Security Features</span>
            </div>
            <h1 class="content-title">Security Features</h1>
            <p class="content-subtitle">Comprehensive security measures implemented in the ManageInc Admin Panel.</p>
        </div>

        <div class="card">
            <div class="card-header">
                <h2 class="card-title">
                    <i class="fas fa-shield-alt"></i>
                    Security Overview
                </h2>
            </div>
            <div class="card-body doc-content">
                <p>The ManageInc Admin Panel includes comprehensive security features to protect your data and prevent unauthorized access. This document outlines the security measures implemented in the system.</p>

                <div class="info-box">
                    <div class="info-box-title">
                        <i class="fas fa-exclamation-circle"></i>
                        Important Note
                    </div>
                    <p>While we've implemented robust security measures, always follow security best practices and keep your system updated to maintain optimal protection.</p>
                </div>

                <h2>Authentication Security</h2>
                <ul>
                    <li><strong>Password Hashing:</strong> All passwords are securely hashed using PHP's password_hash() function with bcrypt algorithm.</li>
                    <li><strong>Login Attempt Limiting:</strong> The system limits login attempts (maximum 5) and implements account lockouts for 15 minutes after failed attempts.</li>
                    <li><strong>Password Requirements:</strong> Enforces strong password policies including minimum length (8 characters), mixed case, numbers, and special characters.</li>
                    <li><strong>Session Security:</strong> Sessions are protected with secure cookies, HTTP-only flags, and same-site restrictions.</li>
                    <li><strong>Session Regeneration:</strong> Session IDs are periodically regenerated to prevent session fixation attacks.</li>
                </ul>

                <h2>CSRF Protection</h2>
                <p>Cross-Site Request Forgery (CSRF) protection is implemented throughout the admin panel:</p>
                <ul>
                    <li>All forms include CSRF tokens that are validated on submission.</li>
                    <li>Tokens are generated using cryptographically secure random bytes.</li>
                    <li>Token validation is required for all state-changing operations (create, update, delete).</li>
                    <li>Implementation can be found in the security.php file with the generate_csrf_token() and verify_csrf_token() functions.</li>
                </ul>

                <h2>SQL Injection Prevention</h2>
                <p>The system is protected against SQL injection attacks through:</p>
                <ul>
                    <li>Consistent use of prepared statements with parameterized queries for all database operations.</li>
                    <li>Input validation and sanitization before processing or storing data.</li>
                    <li>Proper error handling that doesn't expose database information.</li>
                </ul>

                <h2>XSS Prevention</h2>
                <p>Cross-Site Scripting (XSS) attacks are prevented by:</p>
                <ul>
                    <li>Output escaping using htmlspecialchars() for all user-generated content.</li>
                    <li>Content Security Policy (CSP) headers that restrict script sources.</li>
                    <li>X-XSS-Protection headers for additional browser protection.</li>
                </ul>

                <h2>Security Headers</h2>
                <p>The admin panel implements the following security headers:</p>
                <ul>
                    <li><strong>Content-Security-Policy:</strong> Restricts resource loading to trusted sources.</li>
                    <li><strong>X-XSS-Protection:</strong> Enables browser's built-in XSS filters.</li>
                    <li><strong>X-Content-Type-Options:</strong> Prevents MIME type sniffing.</li>
                    <li><strong>X-Frame-Options:</strong> Prevents clickjacking by restricting framing to same origin.</li>
                    <li><strong>Referrer-Policy:</strong> Controls information sent in the Referer header.</li>
                </ul>
            </div>
        </div>

        <div class="card">
            <div class="card-header">
                <h2 class="card-title">
                    <i class="fas fa-user-shield"></i>
                    Role-Based Access Control
                </h2>
            </div>
            <div class="card-body doc-content">
                <p>The admin panel implements a comprehensive role-based access control system:</p>
                <ul>
                    <li>Three predefined roles: Admin, Editor, and Viewer with different permission levels.</li>
                    <li>Super Admin role with additional system-level permissions.</li>
                    <li>Menu items and functionality are dynamically shown/hidden based on user permissions.</li>
                    <li>Server-side permission verification for all actions.</li>
                </ul>

                <h3>Permission Enforcement</h3>
                <p>Permissions are enforced at multiple levels:</p>
                <ul>
                    <li><strong>UI Level:</strong> Menu items and buttons are only displayed if the user has permission to use them.</li>
                    <li><strong>Controller Level:</strong> All actions verify permissions before executing.</li>
                    <li><strong>API Level:</strong> All API endpoints validate permissions before processing requests.</li>
                </ul>

                <h3>Error Handling and Logging</h3>
                <ul>
                    <li>Detailed error logging with sensitive information kept private.</li>
                    <li>Generic error messages shown to users to prevent information disclosure.</li>
                    <li>All security events (login attempts, permission violations) are logged.</li>
                </ul>
            </div>
        </div>

        <div class="card">
            <div class="card-header">
                <h2 class="card-title">
                    <i class="fas fa-code"></i>
                    Implementation Details
                </h2>
            </div>
            <div class="card-body doc-content">
                <h3>Additional Security Measures</h3>
                <ul>
                    <li><strong>File Upload Validation:</strong> Strict validation of file uploads including type checking and size limitations.</li>
                    <li><strong>Email Verification:</strong> Optional email verification for new accounts.</li>
                    <li><strong>Security Wrapper:</strong> A comprehensive security wrapper (security.php) that's included in all admin pages.</li>
                    <li><strong>Configuration Protection:</strong> Sensitive configuration files are protected from direct access.</li>
                </ul>

                <h3>Security Implementation Files</h3>
                <p>The main security features are implemented in the following files:</p>
                <ul>
                    <li><strong>admin/includes/security.php:</strong> Core security wrapper with CSRF protection, security headers, and session security.</li>
                    <li><strong>admin/config.php:</strong> Security configuration settings and database connection security.</li>
                    <li><strong>admin/lib/Auth.php:</strong> Authentication and permission management.</li>
                </ul>

                <h3>Recent Security Enhancements</h3>
                <p>The following security improvements have been implemented to enhance the admin panel's stability and security:</p>
                <ul>
                    <li><strong>Headers Already Sent Protection:</strong> The security wrapper now checks if headers have already been sent before attempting to apply security headers, preventing warnings and errors.</li>
                    <li><strong>Improved Session Handling:</strong> Enhanced session cleanup during logout to prevent session fixation attacks.</li>
                    <li><strong>Email Function Improvements:</strong> The email system now properly handles both PHP mail() and SMTP methods with better error handling.</li>
                    <li><strong>HTML Editor Variable Insertion:</strong> Fixed template variable insertion in the HTML editor to work correctly in both WYSIWYG and code view modes.</li>
                </ul>

                <h3>Security Configuration</h3>
                <p>The following security settings are defined in config.php:</p>
                <pre><code>// Security settings
define('CSRF_PROTECTION', true);
define('SESSION_TIMEOUT', 3600); // 1 hour
define('PASSWORD_MIN_LENGTH', 8);
define('PASSWORD_REQUIRES_MIXED_CASE', true);
define('PASSWORD_REQUIRES_NUMBERS', true);
define('PASSWORD_REQUIRES_SYMBOLS', true);
define('MAX_LOGIN_ATTEMPTS', 5);
define('LOGIN_LOCKOUT_DURATION', 900); // 15 minutes
define('XSS_PROTECTION', true);
define('CONTENT_SECURITY_POLICY', true);</code></pre>
            </div>
        </div>
    </div>
</body>
</html>
