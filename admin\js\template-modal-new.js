/**
 * New Template Modal Handler
 * Completely overhauled template modal functionality
 */
document.addEventListener('DOMContentLoaded', function() {
    // Initialize the editor when the template modal is opened
    document.addEventListener('modal:opened', function(e) {
        if (e.detail.modalId === 'template-modal') {
            initTemplateModal();
        }
    });

    // Clean up when modal is closed
    document.addEventListener('modal:closed', function(e) {
        if (e.detail.modalId === 'template-modal') {
            cleanupTemplateModal();
        }
    });

    /**
     * Initialize the template modal
     */
    function initTemplateModal() {
        // Add resize handle and indicator
        addResizeElements();

        // Initialize tabs
        initTabs();

        // Initialize editor
        initEditor();

        // Initialize template variables
        initTemplateVariables();

        // Initialize preview
        initPreview();

        // Initialize save functionality
        initSaveTemplate();

        // Initialize format and clear buttons
        initControlButtons();
    }

    /**
     * Add resize handle and indicator to the modal
     */
    function addResizeElements() {
        const modalDialog = document.querySelector('#template-modal .admin-modal-dialog');
        if (!modalDialog) return;

        // Check if elements already exist
        if (modalDialog.querySelector('.resize-handle')) return;

        // Restore saved size if available
        try {
            const savedWidth = localStorage.getItem('templateModalWidth');
            const savedHeight = localStorage.getItem('templateModalHeight');

            if (savedWidth && savedHeight) {
                modalDialog.style.width = savedWidth + 'px';
                modalDialog.style.height = savedHeight + 'px';
            }
        } catch (e) {
            console.error('Error restoring modal size:', e);
        }

        // Create resize handle
        const resizeHandle = document.createElement('div');
        resizeHandle.className = 'resize-handle';
        resizeHandle.title = 'Drag to resize';
        modalDialog.appendChild(resizeHandle);

        // Create resize indicator
        const resizeIndicator = document.createElement('div');
        resizeIndicator.className = 'resize-indicator';
        resizeIndicator.textContent = 'Drag to resize';
        modalDialog.appendChild(resizeIndicator);

        // Add resize event listener
        modalDialog.addEventListener('mousedown', function(e) {
            if (e.target === resizeHandle) {
                // Show resize indicator during resize
                resizeIndicator.style.opacity = '1';

                // Update indicator with current size
                const updateSize = function() {
                    const width = Math.round(modalDialog.offsetWidth);
                    const height = Math.round(modalDialog.offsetHeight);
                    resizeIndicator.textContent = `${width} × ${height}`;
                };

                // Initial size update
                updateSize();

                // Add resize observer
                const resizeObserver = new ResizeObserver(function() {
                    updateSize();
                });
                resizeObserver.observe(modalDialog);

                // Clean up when resize ends
                const onMouseUp = function() {
                    // Hide indicator after a delay
                    setTimeout(function() {
                        resizeIndicator.style.opacity = '';
                        resizeIndicator.textContent = 'Drag to resize';
                    }, 1500);

                    // Disconnect observer
                    resizeObserver.disconnect();

                    // Remove event listener
                    document.removeEventListener('mouseup', onMouseUp);
                };

                // Add mouseup listener
                document.addEventListener('mouseup', onMouseUp);
            }
        });
    }

    /**
     * Initialize tabs functionality
     */
    function initTabs() {
        const tabs = document.querySelectorAll('#template-modal .admin-modal-tab');
        const tabContents = document.querySelectorAll('#template-modal .admin-modal-tab-content');

        tabs.forEach(tab => {
            tab.addEventListener('click', function() {
                // Remove active class from all tabs and contents
                tabs.forEach(t => t.classList.remove('active'));
                tabContents.forEach(c => c.classList.remove('active'));

                // Add active class to clicked tab
                this.classList.add('active');

                // Show corresponding content
                const tabId = this.getAttribute('data-tab');
                const content = document.querySelector(`#template-modal .admin-modal-tab-content[data-tab="${tabId}"]`);
                if (content) {
                    content.classList.add('active');
                }

                // If switching to preview tab, update preview
                if (tabId === 'preview') {
                    updatePreview();
                }
            });
        });
    }

    /**
     * Initialize the editor
     */
    function initEditor() {
        // Get the template content
        const templateContent = document.getElementById('template_content');
        if (!templateContent) return;

        const content = templateContent.value || '';

        try {
            // Destroy any existing editor instance
            if (window.templateEditor) {
                const editorContainer = document.querySelector('.simple-editor');
                if (editorContainer) {
                    editorContainer.parentNode.removeChild(editorContainer);
                }
            }

            // Create new editor instance
            window.templateEditor = new SimpleEditor('#template_content', {
                height: 300,
                placeholder: 'Enter email template content here...',
                buttons: [
                    'bold', 'italic', 'underline', 'strikethrough',
                    'heading1', 'heading2', 'paragraph',
                    'alignLeft', 'alignCenter', 'alignRight',
                    'orderedList', 'unorderedList',
                    'link', 'image', 'table', 'horizontalRule',
                    'foreColor', 'backColor', 'removeFormat',
                    'undo', 'redo',
                    'html'
                ],
                onChange: function(content) {
                    // Update preview when content changes
                    updatePreview();

                    // Track content changes for undo/redo
                    if (window.templateEditor && window.templateEditor.trackHistory) {
                        window.templateEditor.trackHistory();
                    }
                }
            });

            // Update editor content
            setTimeout(() => {
                const editorContent = document.querySelector('.simple-editor-content');
                if (editorContent && content) {
                    editorContent.innerHTML = content;

                    // Trigger change event
                    const event = new Event('input', {
                        bubbles: true,
                        cancelable: true
                    });
                    editorContent.dispatchEvent(event);

                    // Switch to HTML mode by default
                    setTimeout(() => {
                        const htmlButton = document.querySelector('.simple-editor-button[title="HTML Mode"]');
                        if (htmlButton) {
                            htmlButton.click();
                        }
                    }, 100);
                }
            }, 50);

            // Add editor container class for styling
            const editorContainer = document.querySelector('.simple-editor');
            if (editorContainer) {
                editorContainer.classList.add('editor-container');
            }
        } catch (error) {
            console.error('Error initializing editor:', error);

            // Fallback to plain textarea
            templateContent.style.display = 'block';
            templateContent.style.width = '100%';
            templateContent.style.minHeight = '300px';
            templateContent.style.padding = '10px';
            templateContent.style.border = '1px solid #ddd';
            templateContent.style.borderRadius = '4px';
            templateContent.addEventListener('input', updatePreview);
        }
    }

    /**
     * Initialize template variables
     */
    function initTemplateVariables() {
        // Get all template variable elements
        const templateVariables = document.querySelectorAll('.template-variable');

        // Add click event to each variable
        templateVariables.forEach(variable => {
            variable.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();

                // Get the variable text
                const variableText = this.getAttribute('data-variable');

                // Format the variable with double curly braces
                const cleanVariable = variableText.replace(/[{}]/g, '');
                const formattedVariable = '{{' + cleanVariable + '}}';

                // Insert the variable
                insertVariableIntoEditor(formattedVariable);

                // Add visual feedback
                this.classList.add('clicked');
                setTimeout(() => {
                    this.classList.remove('clicked');
                }, 300);
            });
        });

        // Initialize variable search
        const searchInput = document.getElementById('variable-search');
        const clearButton = document.getElementById('clear-search');
        const categorySelect = document.getElementById('variable-category');

        if (searchInput) {
            searchInput.addEventListener('input', function() {
                const category = categorySelect ? categorySelect.value : 'all';
                filterVariables(category, this.value);

                // Show/hide clear button
                if (clearButton) {
                    clearButton.style.display = this.value ? 'flex' : 'none';
                }
            });

            // Initial state - hide clear button
            if (clearButton) {
                clearButton.style.display = 'none';

                // Add click event to clear button
                clearButton.addEventListener('click', function() {
                    searchInput.value = '';
                    searchInput.focus();
                    this.style.display = 'none';

                    // Reapply category filter
                    const category = categorySelect ? categorySelect.value : 'all';
                    filterVariables(category, '');
                });
            }
        }

        // Initialize category filter
        if (categorySelect) {
            categorySelect.addEventListener('change', function() {
                const searchTerm = searchInput ? searchInput.value : '';
                filterVariables(this.value, searchTerm);
            });
        }
    }

    /**
     * Filter variables by category and search term
     */
    function filterVariables(category, searchTerm) {
        const variables = document.querySelectorAll('.template-variable');

        variables.forEach(variable => {
            const matchesCategory = category === 'all' || variable.getAttribute('data-category') === category;
            const matchesSearch = !searchTerm ||
                                 variable.textContent.toLowerCase().includes(searchTerm.toLowerCase()) ||
                                 variable.getAttribute('data-variable').toLowerCase().includes(searchTerm.toLowerCase());

            if (matchesCategory && matchesSearch) {
                variable.style.display = '';
            } else {
                variable.style.display = 'none';
            }
        });
    }

    /**
     * Insert a variable into the editor
     */
    function insertVariableIntoEditor(variableText) {
        // Determine which editor to use
        const editorContent = document.querySelector('.simple-editor-content');
        const htmlTextarea = document.querySelector('.simple-editor-html');
        const templateContent = document.getElementById('template_content');

        let targetEditor;

        // Check which editor is visible
        if (htmlTextarea && htmlTextarea.style.display !== 'none') {
            targetEditor = htmlTextarea;
            insertIntoTextarea(targetEditor, variableText);
        } else if (editorContent && editorContent.style.display !== 'none') {
            targetEditor = editorContent;
            insertIntoWysiwyg(targetEditor, variableText);
        } else if (templateContent && templateContent.style.display !== 'none') {
            targetEditor = templateContent;
            insertIntoTextarea(targetEditor, variableText);
        }

        // Show notification
        showNotification(`Variable "${variableText}" inserted`);
    }

    /**
     * Insert text into a textarea at cursor position
     */
    function insertIntoTextarea(textarea, text) {
        textarea.focus();

        const startPos = textarea.selectionStart || 0;
        const endPos = textarea.selectionEnd || 0;
        const currentValue = textarea.value;

        // Insert at cursor position
        textarea.value = currentValue.substring(0, startPos) +
                         text +
                         currentValue.substring(endPos);

        // Set cursor position after inserted text
        const newCursorPos = startPos + text.length;
        textarea.selectionStart = newCursorPos;
        textarea.selectionEnd = newCursorPos;

        // Trigger change event
        const event = new Event('input', {
            bubbles: true,
            cancelable: true
        });
        textarea.dispatchEvent(event);
    }

    /**
     * Insert text into WYSIWYG editor at cursor position
     */
    function insertIntoWysiwyg(editor, text) {
        editor.focus();

        // Use execCommand to insert at cursor position
        document.execCommand('insertText', false, text);

        // Trigger input event
        const event = new Event('input', {
            bubbles: true,
            cancelable: true
        });
        editor.dispatchEvent(event);
    }

    /**
     * Show a notification
     */
    function showNotification(message, duration = 2000) {
        // Create notification element if it doesn't exist
        let notification = document.querySelector('.template-notification');
        if (!notification) {
            notification = document.createElement('div');
            notification.className = 'template-notification';
            notification.style.position = 'fixed';
            notification.style.bottom = '20px';
            notification.style.right = '20px';
            notification.style.backgroundColor = '#4CAF50';
            notification.style.color = 'white';
            notification.style.padding = '10px 15px';
            notification.style.borderRadius = '4px';
            notification.style.boxShadow = '0 2px 10px rgba(0,0,0,0.2)';
            notification.style.zIndex = '9999';
            notification.style.opacity = '0';
            notification.style.transform = 'translateY(20px)';
            notification.style.transition = 'all 0.3s ease';
            document.body.appendChild(notification);
        }

        // Set message and show notification
        notification.textContent = message;
        notification.style.display = 'block';

        // Animate in
        setTimeout(() => {
            notification.style.opacity = '1';
            notification.style.transform = 'translateY(0)';
        }, 10);

        // Hide after duration
        setTimeout(() => {
            notification.style.opacity = '0';
            notification.style.transform = 'translateY(20px)';

            // Remove after animation
            setTimeout(() => {
                notification.style.display = 'none';
            }, 300);
        }, duration);
    }

    /**
     * Initialize preview functionality
     */
    function initPreview() {
        // Get preview elements
        const previewSubject = document.getElementById('preview-subject');
        const previewContent = document.getElementById('preview-content');
        const previewFrom = document.getElementById('preview-from');
        const previewTo = document.getElementById('preview-to');

        // Get company name and admin email from settings if available
        let companyName = 'Company Name';
        let adminEmail = '<EMAIL>';

        const companyNameElement = document.querySelector('input[name="company_name"]');
        if (companyNameElement) {
            companyName = companyNameElement.value || companyName;
        }

        const fromEmailElement = document.querySelector('input[name="from_email"]');
        if (fromEmailElement) {
            adminEmail = fromEmailElement.value || adminEmail;
        }

        // Set default preview values
        if (previewFrom) {
            previewFrom.textContent = `${companyName} <${adminEmail}>`;
        }

        if (previewTo) {
            previewTo.textContent = 'John Doe <<EMAIL>>';
        }

        // Initial preview update
        updatePreview();
    }

    /**
     * Update the email preview
     */
    function updatePreview() {
        const templateSubject = document.getElementById('template_subject');
        const previewSubject = document.getElementById('preview-subject');
        const previewContent = document.getElementById('preview-content');

        // Update subject
        if (templateSubject && previewSubject) {
            previewSubject.textContent = templateSubject.value || 'No subject';
        }

        // Get content from editor
        let content = '';
        const editorContent = document.querySelector('.simple-editor-content');
        const htmlTextarea = document.querySelector('.simple-editor-html');
        const templateContent = document.getElementById('template_content');

        if (htmlTextarea && htmlTextarea.style.display !== 'none') {
            content = htmlTextarea.value;
        } else if (editorContent && editorContent.style.display !== 'none') {
            content = editorContent.innerHTML;
        } else if (templateContent) {
            content = templateContent.value;
        }

        // Update preview content
        if (previewContent) {
            // Get logo path from settings
            let logoPath = '../admin/images/logo.png';
            const logoPathElement = document.querySelector('input[name="admin_logo_path"]');
            if (logoPathElement) {
                logoPath = logoPathElement.value || logoPath;
            }

            // Get theme color from settings
            let themeColor = '#f1ca2f';
            const themeColorElement = document.querySelector('input[name="admin_theme_color"]');
            if (themeColorElement) {
                themeColor = themeColorElement.value || themeColor;
            }

            // Create email template with logo and styling
            const emailTemplate = `
                <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
                    <div style="background-color: ${themeColor}; padding: 20px; text-align: center;">
                        <img src="${logoPath}" alt="Logo" style="max-height: 60px; max-width: 200px;">
                    </div>
                    <div style="padding: 20px; background-color: #ffffff; color: #333333; line-height: 1.6;">
                        ${content}
                    </div>
                    <div style="padding: 15px; background-color: #f8f8f8; font-size: 12px; color: #666666; text-align: center; border-top: 1px solid #dddddd;">
                        &copy; ${new Date().getFullYear()} All rights reserved.
                    </div>
                </div>
            `;

            previewContent.innerHTML = emailTemplate;
        }
    }

    /**
     * Initialize save template functionality
     */
    function initSaveTemplate() {
        const saveButton = document.getElementById('save-template-btn');
        if (!saveButton) return;

        saveButton.addEventListener('click', function() {
            const templateKey = document.getElementById('template_key').value;
            const templateSubject = document.getElementById('template_subject').value;

            // Get content from the editor
            let templateContent = '';
            const editorContent = document.querySelector('.simple-editor-content');
            const htmlTextarea = document.querySelector('.simple-editor-html');
            const originalTextarea = document.getElementById('template_content');

            // First, update the original textarea with the current editor content
            if (htmlTextarea && htmlTextarea.style.display !== 'none') {
                // HTML mode is active, get content from HTML textarea
                templateContent = htmlTextarea.value;
                if (originalTextarea) originalTextarea.value = templateContent;
            } else if (editorContent && editorContent.style.display !== 'none') {
                // WYSIWYG mode is active, get content from editor
                templateContent = editorContent.innerHTML;
                if (originalTextarea) originalTextarea.value = templateContent;
            } else {
                // Fallback to original textarea
                templateContent = originalTextarea ? originalTextarea.value : '';
            }

            // Log the content for debugging
            console.log('Template content to save:', {
                length: templateContent.length,
                preview: templateContent.substring(0, 100) + '...'
            });

            // Validate inputs
            if (!templateContent.trim()) {
                alert('Template content cannot be empty.');
                return;
            }

            if (!templateSubject.trim()) {
                alert('Subject line cannot be empty.');
                return;
            }

            // Show loading state
            const originalText = this.innerHTML;
            this.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Saving...';
            this.disabled = true;

            // Send AJAX request
            const formData = new FormData();
            formData.append('action', 'save_template');
            formData.append('template_key', templateKey);
            formData.append('subject', templateSubject);
            formData.append('content', templateContent);

            // Use the original textarea value to ensure consistency
            const originalTextarea = document.getElementById('template_content');
            if (originalTextarea) {
                formData.append('content', originalTextarea.value);
            }

            // Log the form data for debugging
            console.log('Sending data:', {
                action: 'save_template',
                template_key: templateKey,
                subject: templateSubject,
                content_length: originalTextarea ? originalTextarea.value.length : templateContent.length
            });

            fetch('save_template.php', {
                method: 'POST',
                body: formData,
                credentials: 'same-origin', // Include cookies for session authentication
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => {
                const contentType = response.headers.get('content-type');
                if (contentType && contentType.includes('application/json')) {
                    return response.json();
                } else {
                    return response.text().then(text => {
                        console.error('Received non-JSON response:', text);
                        throw new Error('Invalid response format');
                    });
                }
            })
            .then(data => {
                if (data.success) {
                    showNotification('Template saved successfully!');

                    // Close modal
                    AdminModal.close('template-modal');

                    // Reload page to show updated template
                    window.location.reload();
                } else {
                    alert('Error saving template: ' + (data.message || 'Unknown error'));

                    // Reset button state
                    this.innerHTML = originalText;
                    this.disabled = false;
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('An error occurred while saving the template. Please try again.');

                // Reset button state
                this.innerHTML = originalText;
                this.disabled = false;
            });
        });
    }

    /**
     * Initialize format and clear buttons
     */
    function initControlButtons() {
        // Format button
        const formatBtn = document.getElementById('format-btn');
        if (formatBtn) {
            formatBtn.addEventListener('click', function() {
                const htmlTextarea = document.querySelector('.simple-editor-html');
                if (htmlTextarea && htmlTextarea.style.display !== 'none') {
                    // Format HTML
                    try {
                        const formatted = formatHTML(htmlTextarea.value);
                        htmlTextarea.value = formatted;

                        // Trigger change event
                        const event = new Event('input', {
                            bubbles: true,
                            cancelable: true
                        });
                        htmlTextarea.dispatchEvent(event);

                        showNotification('HTML formatted successfully');
                    } catch (error) {
                        console.error('Error formatting HTML:', error);
                        alert('Error formatting HTML. Please check your HTML syntax.');
                    }
                }
            });
        }

        // Clear button
        const clearBtn = document.getElementById('clear-btn');
        if (clearBtn) {
            clearBtn.addEventListener('click', function() {
                if (confirm('Are you sure you want to clear all content?')) {
                    const editorContent = document.querySelector('.simple-editor-content');
                    const htmlTextarea = document.querySelector('.simple-editor-html');
                    const templateContent = document.getElementById('template_content');

                    // Clear content in all editors
                    if (editorContent) editorContent.innerHTML = '';
                    if (htmlTextarea) htmlTextarea.value = '';
                    if (templateContent) templateContent.value = '';

                    // Trigger change event
                    if (htmlTextarea && htmlTextarea.style.display !== 'none') {
                        const event = new Event('input', {
                            bubbles: true,
                            cancelable: true
                        });
                        htmlTextarea.dispatchEvent(event);
                    } else if (editorContent && editorContent.style.display !== 'none') {
                        const event = new Event('input', {
                            bubbles: true,
                            cancelable: true
                        });
                        editorContent.dispatchEvent(event);
                    }

                    showNotification('Content cleared');
                }
            });
        }
    }

    /**
     * Format HTML with proper indentation
     */
    function formatHTML(html) {
        let formatted = '';
        let indent = 0;

        // Replace all opening tags with newlines and proper indentation
        html = html.replace(/<([^\/].*?)>/g, (match) => {
            // Don't add newlines for inline elements
            const isInline = /^<(span|a|strong|b|em|i|u|s|sub|sup|code|br|img)(\s|>)/i.test(match);
            if (isInline) {
                return match;
            }

            const result = '\n' + '  '.repeat(indent) + match;
            indent++;
            return result;
        });

        // Replace all closing tags with newlines and proper indentation
        html = html.replace(/<\/(.+?)>/g, (match, tag) => {
            // Don't add newlines for inline elements
            const isInline = /^(span|a|strong|b|em|i|u|s|sub|sup|code|br|img)$/i.test(tag);
            if (isInline) {
                return match;
            }

            indent = Math.max(0, indent - 1);
            return '\n' + '  '.repeat(indent) + match;
        });

        // Replace escaped newlines with actual newlines
        formatted = html.replace(/\\n/g, '\n');

        return formatted;
    }

    /**
     * Clean up the template modal
     */
    function cleanupTemplateModal() {
        // Reset editor
        window.templateEditor = null;

        // Remove any notifications
        const notification = document.querySelector('.template-notification');
        if (notification) {
            notification.remove();
        }

        // Store modal size in localStorage for next time
        const modalDialog = document.querySelector('#template-modal .admin-modal-dialog');
        if (modalDialog) {
            try {
                const width = modalDialog.offsetWidth;
                const height = modalDialog.offsetHeight;

                if (width > 0 && height > 0) {
                    localStorage.setItem('templateModalWidth', width);
                    localStorage.setItem('templateModalHeight', height);
                }
            } catch (e) {
                console.error('Error saving modal size:', e);
            }
        }
    }
