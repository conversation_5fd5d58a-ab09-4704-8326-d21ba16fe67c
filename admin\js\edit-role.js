/**
 * Edit Role JavaScript
 *
 * This file contains JavaScript functionality for the edit role page.
 */

document.addEventListener('DOMContentLoaded', function() {
    // Initialize permission category toggles
    initPermissionCategories();
    
    // Initialize select all functionality
    initSelectAll();
    
    // Initialize form validation
    initFormValidation();
});

/**
 * Initialize permission category toggles
 */
function initPermissionCategories() {
    const categories = document.querySelectorAll('.permission-category');
    
    categories.forEach(category => {
        const header = category.querySelector('.permission-category-header');
        
        if (header) {
            header.addEventListener('click', function() {
                // Toggle expanded class
                category.classList.toggle('expanded');
                
                // Save state to localStorage
                const categoryId = category.getAttribute('data-category');
                if (categoryId) {
                    localStorage.setItem('role_category_' + categoryId, category.classList.contains('expanded') ? 'expanded' : 'collapsed');
                }
            });
        }
        
        // Check if this category should be expanded based on localStorage
        const categoryId = category.getAttribute('data-category');
        if (categoryId) {
            const savedState = localStorage.getItem('role_category_' + categoryId);
            if (savedState === 'expanded') {
                category.classList.add('expanded');
            } else if (savedState === null) {
                // Default to expanded for the first category
                if (category === categories[0]) {
                    category.classList.add('expanded');
                }
            }
        }
    });
}

/**
 * Initialize select all functionality for permission categories
 */
function initSelectAll() {
    const selectAllButtons = document.querySelectorAll('.select-all-permissions');
    
    selectAllButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();
            
            const categoryId = this.getAttribute('data-category');
            const checkboxes = document.querySelectorAll(`.permission-category[data-category="${categoryId}"] input[type="checkbox"]`);
            
            // Check if all are already checked
            const allChecked = Array.from(checkboxes).every(checkbox => checkbox.checked);
            
            // Toggle all checkboxes
            checkboxes.forEach(checkbox => {
                checkbox.checked = !allChecked;
            });
        });
    });
}

/**
 * Initialize form validation
 */
function initFormValidation() {
    const form = document.getElementById('edit-role-form');
    
    if (form) {
        form.addEventListener('submit', function(e) {
            const roleName = document.getElementById('role_name');
            
            if (!roleName.value.trim()) {
                e.preventDefault();
                
                // Show error message
                const errorMessage = document.createElement('div');
                errorMessage.className = 'admin-alert error';
                errorMessage.innerHTML = '<i class="fas fa-exclamation-circle"></i> Role name is required.';
                
                // Insert at the top of the form
                form.insertBefore(errorMessage, form.firstChild);
                
                // Scroll to error
                errorMessage.scrollIntoView({ behavior: 'smooth', block: 'start' });
                
                // Focus on the input
                roleName.focus();
                
                // Remove error after 5 seconds
                setTimeout(() => {
                    errorMessage.remove();
                }, 5000);
            }
        });
    }
}

/**
 * Toggle all permissions in a category
 * 
 * @param {string} categoryId - The category ID
 */
function toggleCategoryPermissions(categoryId) {
    const checkboxes = document.querySelectorAll(`.permission-category[data-category="${categoryId}"] input[type="checkbox"]`);
    const allChecked = Array.from(checkboxes).every(checkbox => checkbox.checked);
    
    checkboxes.forEach(checkbox => {
        checkbox.checked = !allChecked;
    });
}

/**
 * Confirm role deletion
 * 
 * @param {number} roleId - The role ID
 * @returns {boolean} - Whether the deletion was confirmed
 */
function confirmDeleteRole(roleId) {
    return confirm('Are you sure you want to delete this role? This action cannot be undone.');
}
