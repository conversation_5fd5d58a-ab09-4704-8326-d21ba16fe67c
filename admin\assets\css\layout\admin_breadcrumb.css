/**
 * Admin Breadcrumb CSS
 * 
 * This file contains styles for the admin breadcrumb navigation.
 */

/* Breadcrumb Container */
.admin-breadcrumb {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  padding: var(--spacing-3) 0;
  margin-bottom: var(--spacing-4);
  font-size: var(--font-size-sm);
}

/* Breadcrumb List */
.admin-breadcrumb-list {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  list-style: none;
  margin: 0;
  padding: 0;
}

/* Breadcrumb Item */
.admin-breadcrumb-item {
  display: flex;
  align-items: center;
  color: var(--text-light);
}

/* Breadcrumb Separator */
.admin-breadcrumb-item:not(:last-child)::after {
  content: "/";
  margin: 0 var(--spacing-2);
  color: var(--text-muted);
}

/* Breadcrumb Link */
.admin-breadcrumb-link {
  color: var(--text-light);
  text-decoration: none;
  transition: color var(--transition-fast) ease;
}

.admin-breadcrumb-link:hover {
  color: var(--primary-color);
}

/* Active Breadcrumb */
.admin-breadcrumb-item.active .admin-breadcrumb-link {
  color: var(--text-dark);
  font-weight: var(--font-weight-medium);
  pointer-events: none;
}

/* Breadcrumb with Icons */
.admin-breadcrumb-icon {
  margin-right: var(--spacing-1);
  font-size: var(--font-size-xs);
  color: var(--text-muted);
}

/* Breadcrumb with Background */
.admin-breadcrumb-bg {
  background-color: var(--white);
  border-radius: var(--radius-md);
  padding: var(--spacing-3) var(--spacing-4);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--border-color);
}

/* Breadcrumb with Arrows */
.admin-breadcrumb-arrows .admin-breadcrumb-item:not(:last-child)::after {
  content: "›";
  margin: 0 var(--spacing-2);
  color: var(--text-muted);
  font-size: var(--font-size-lg);
  line-height: 1;
}

/* Breadcrumb with Chevrons */
.admin-breadcrumb-chevrons .admin-breadcrumb-item {
  position: relative;
  padding-right: var(--spacing-4);
}

.admin-breadcrumb-chevrons .admin-breadcrumb-item:not(:last-child)::after {
  content: "";
  position: absolute;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 6px;
  height: 6px;
  border-top: 1px solid var(--text-muted);
  border-right: 1px solid var(--text-muted);
  transform: translateY(-50%) rotate(45deg);
}

/* Responsive Breadcrumb */
@media (max-width: 576px) {
  .admin-breadcrumb {
    font-size: var(--font-size-xs);
  }
  
  .admin-breadcrumb-responsive .admin-breadcrumb-item {
    display: none;
  }
  
  .admin-breadcrumb-responsive .admin-breadcrumb-item:first-child,
  .admin-breadcrumb-responsive .admin-breadcrumb-item:last-child {
    display: flex;
  }
  
  .admin-breadcrumb-responsive .admin-breadcrumb-item:first-child::after {
    content: "...";
    margin: 0 var(--spacing-2);
    color: var(--text-muted);
  }
}
