/**
 * Admin Forms JavaScript
 * Form handling for the admin panel
 * Consolidated from multiple JS files
 */

// Initialize form handling when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    // Initialize all form components
    AdminForms.init();
});

// Admin Forms Namespace
const AdminForms = {
    // Initialize all form components
    init: function() {
        this.initFormValidation();
        this.initAjaxForms();
        this.initDynamicForms();
        this.initFormDependencies();
        this.initFormFileUploads();
        this.initFormImagePreviews();
        this.initFormAutoSave();
        this.initFormResetConfirmation();
        this.initFormSubmitButtons();
        this.initFormCharacterCounters();
        this.initFormSlugGenerator();
        this.initFormColorPickers();
        this.initFormDatePickers();
        this.initFormTimePickers();
        this.initFormRichTextEditors();
        this.initFormCodeEditors();
        this.initFormTagInputs();
        this.initFormMultiSelect();
        this.initFormSortable();
        this.initFormRepeaters();
    },

    // Initialize form validation
    initFormValidation: function() {
        const forms = document.querySelectorAll('form[data-validate]');
        
        forms.forEach(function(form) {
            // Add novalidate attribute to disable browser's native validation
            form.setAttribute('novalidate', '');
            
            form.addEventListener('submit', function(e) {
                let isValid = true;
                
                // Check required fields
                const requiredFields = form.querySelectorAll('[required]');
                requiredFields.forEach(function(field) {
                    if (!field.value.trim()) {
                        isValid = false;
                        AdminForms.showFieldError(field, 'This field is required.');
                    } else {
                        AdminForms.clearFieldError(field);
                    }
                });
                
                // Check email fields
                const emailFields = form.querySelectorAll('[type="email"]');
                const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                
                emailFields.forEach(function(field) {
                    if (field.value.trim() && !emailRegex.test(field.value.trim())) {
                        isValid = false;
                        AdminForms.showFieldError(field, 'Please enter a valid email address.');
                    }
                });
                
                // Check URL fields
                const urlFields = form.querySelectorAll('[type="url"]');
                const urlRegex = /^(https?:\/\/)?([\da-z\.-]+)\.([a-z\.]{2,6})([\/\w \.-]*)*\/?$/;
                
                urlFields.forEach(function(field) {
                    if (field.value.trim() && !urlRegex.test(field.value.trim())) {
                        isValid = false;
                        AdminForms.showFieldError(field, 'Please enter a valid URL.');
                    }
                });
                
                // Check number fields
                const numberFields = form.querySelectorAll('[type="number"]');
                
                numberFields.forEach(function(field) {
                    if (field.value.trim()) {
                        const min = field.getAttribute('min');
                        const max = field.getAttribute('max');
                        const value = parseFloat(field.value);
                        
                        if (min !== null && value < parseFloat(min)) {
                            isValid = false;
                            AdminForms.showFieldError(field, `Value must be greater than or equal to ${min}.`);
                        } else if (max !== null && value > parseFloat(max)) {
                            isValid = false;
                            AdminForms.showFieldError(field, `Value must be less than or equal to ${max}.`);
                        }
                    }
                });
                
                // Check password fields
                const passwordFields = form.querySelectorAll('[type="password"][data-min-length]');
                
                passwordFields.forEach(function(field) {
                    if (field.value.trim()) {
                        const minLength = parseInt(field.getAttribute('data-min-length'));
                        
                        if (field.value.length < minLength) {
                            isValid = false;
                            AdminForms.showFieldError(field, `Password must be at least ${minLength} characters long.`);
                        }
                    }
                });
                
                // Check password confirmation
                const passwordConfirmFields = form.querySelectorAll('[data-confirm-password]');
                
                passwordConfirmFields.forEach(function(field) {
                    const passwordField = document.querySelector(field.getAttribute('data-confirm-password'));
                    
                    if (passwordField && field.value !== passwordField.value) {
                        isValid = false;
                        AdminForms.showFieldError(field, 'Passwords do not match.');
                    }
                });
                
                // Check custom validation
                const customValidationFields = form.querySelectorAll('[data-validate-pattern]');
                
                customValidationFields.forEach(function(field) {
                    if (field.value.trim()) {
                        const pattern = new RegExp(field.getAttribute('data-validate-pattern'));
                        const errorMessage = field.getAttribute('data-validate-message') || 'Invalid format.';
                        
                        if (!pattern.test(field.value)) {
                            isValid = false;
                            AdminForms.showFieldError(field, errorMessage);
                        }
                    }
                });
                
                if (!isValid) {
                    e.preventDefault();
                    
                    // Focus first invalid field
                    const firstInvalid = form.querySelector('.is-invalid');
                    if (firstInvalid) {
                        firstInvalid.focus();
                    }
                    
                    // Show validation error message
                    const errorMessage = form.getAttribute('data-validate-error') || 'Please fix the errors in the form.';
                    AdminCore.showNotification(errorMessage, 'error');
                }
            });
            
            // Live validation on input
            form.querySelectorAll('input, textarea, select').forEach(function(field) {
                field.addEventListener('input', function() {
                    // Clear error when user starts typing
                    AdminForms.clearFieldError(this);
                    
                    // Validate required fields on blur
                    if (field.hasAttribute('required')) {
                        field.addEventListener('blur', function() {
                            if (!this.value.trim()) {
                                AdminForms.showFieldError(this, 'This field is required.');
                            } else {
                                AdminForms.clearFieldError(this);
                            }
                        });
                    }
                    
                    // Validate email fields on blur
                    if (field.type === 'email') {
                        field.addEventListener('blur', function() {
                            if (this.value.trim()) {
                                const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                                
                                if (!emailRegex.test(this.value.trim())) {
                                    AdminForms.showFieldError(this, 'Please enter a valid email address.');
                                } else {
                                    AdminForms.clearFieldError(this);
                                }
                            }
                        });
                    }
                });
            });
        });
    },

    // Show field error
    showFieldError: function(field, message) {
        field.classList.add('is-invalid');
        
        // Create or update error message
        let errorMessage = field.nextElementSibling;
        if (!errorMessage || !errorMessage.classList.contains('invalid-feedback')) {
            errorMessage = document.createElement('div');
            errorMessage.className = 'invalid-feedback';
            field.parentNode.insertBefore(errorMessage, field.nextSibling);
        }
        
        errorMessage.textContent = message;
    },

    // Clear field error
    clearFieldError: function(field) {
        field.classList.remove('is-invalid');
        
        const errorMessage = field.nextElementSibling;
        if (errorMessage && errorMessage.classList.contains('invalid-feedback')) {
            errorMessage.textContent = '';
        }
    },

    // Initialize AJAX forms
    initAjaxForms: function() {
        const ajaxForms = document.querySelectorAll('form[data-ajax]');
        
        ajaxForms.forEach(function(form) {
            form.addEventListener('submit', function(e) {
                e.preventDefault();
                
                const formData = new FormData(this);
                const url = this.getAttribute('action') || window.location.href;
                const method = this.getAttribute('method') || 'POST';
                const submitButton = this.querySelector('[type="submit"]');
                
                // Disable submit button and show loading state
                if (submitButton) {
                    submitButton.disabled = true;
                    submitButton.classList.add('loading');
                    
                    const originalText = submitButton.innerHTML;
                    submitButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Loading...';
                }
                
                // Send AJAX request
                fetch(url, {
                    method: method,
                    body: formData,
                    credentials: 'same-origin'
                })
                .then(response => {
                    if (!response.ok) {
                        throw new Error('Network response was not ok');
                    }
                    return response.json();
                })
                .then(data => {
                    // Re-enable submit button
                    if (submitButton) {
                        submitButton.disabled = false;
                        submitButton.classList.remove('loading');
                        submitButton.innerHTML = originalText;
                    }
                    
                    // Handle response
                    if (data.success) {
                        // Show success message
                        AdminCore.showNotification(data.message || 'Action completed successfully.', 'success');
                        
                        // Redirect if specified
                        if (data.redirect) {
                            window.location.href = data.redirect;
                        }
                        
                        // Reload if specified
                        if (data.reload) {
                            window.location.reload();
                        }
                        
                        // Reset form if specified
                        if (data.reset) {
                            form.reset();
                        }
                        
                        // Call custom callback if specified
                        if (data.callback && typeof window[data.callback] === 'function') {
                            window[data.callback](data);
                        }
                    } else {
                        // Show error message
                        AdminCore.showNotification(data.message || 'An error occurred.', 'error');
                        
                        // Show field errors if any
                        if (data.errors) {
                            for (const field in data.errors) {
                                const input = form.querySelector('[name="' + field + '"]');
                                if (input) {
                                    AdminForms.showFieldError(input, data.errors[field]);
                                }
                            }
                        }
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    
                    // Re-enable submit button
                    if (submitButton) {
                        submitButton.disabled = false;
                        submitButton.classList.remove('loading');
                        submitButton.innerHTML = originalText;
                    }
                    
                    // Show error message
                    AdminCore.showNotification('An error occurred while processing your request.', 'error');
                });
            });
        });
    },

    // Initialize dynamic forms
    initDynamicForms: function() {
        // Add item button
        const addButtons = document.querySelectorAll('[data-add-item]');
        
        addButtons.forEach(function(button) {
            button.addEventListener('click', function(e) {
                e.preventDefault();
                
                const target = this.getAttribute('data-add-item');
                const container = document.querySelector(target);
                
                if (container) {
                    const template = container.querySelector('[data-template]');
                    
                    if (template) {
                        const newItem = template.cloneNode(true);
                        newItem.removeAttribute('data-template');
                        newItem.style.display = '';
                        
                        // Update IDs and names
                        const index = container.querySelectorAll(':scope > div:not([data-template])').length;
                        
                        newItem.querySelectorAll('[id], [name], [for]').forEach(function(el) {
                            ['id', 'name', 'for'].forEach(function(attr) {
                                if (el.hasAttribute(attr)) {
                                    el.setAttribute(attr, el.getAttribute(attr).replace(/\[\d+\]/, '[' + index + ']'));
                                }
                            });
                        });
                        
                        container.appendChild(newItem);
                        
                        // Initialize any components in the new item
                        AdminForms.initFormComponents(newItem);
                        
                        // Focus first input
                        const firstInput = newItem.querySelector('input, textarea, select');
                        if (firstInput) {
                            firstInput.focus();
                        }
                    }
                }
            });
        });
        
        // Remove item button (delegated event)
        document.addEventListener('click', function(e) {
            if (e.target.matches('[data-remove-item]') || e.target.closest('[data-remove-item]')) {
                e.preventDefault();
                
                const button = e.target.matches('[data-remove-item]') ? e.target : e.target.closest('[data-remove-item]');
                const item = button.closest('.dynamic-item');
                
                if (item) {
                    // Confirm removal if needed
                    const confirmMessage = button.getAttribute('data-confirm') || 'Are you sure you want to remove this item?';
                    
                    if (confirm(confirmMessage)) {
                        item.remove();
                    }
                }
            }
        });
    },

    // Initialize form components in a container
    initFormComponents: function(container) {
        // Initialize any components in the container
        // This is used for dynamically added form elements
        
        // Color pickers
        const colorPickers = container.querySelectorAll('.color-picker');
        if (colorPickers.length > 0 && typeof $.fn.spectrum !== 'undefined') {
            colorPickers.forEach(function(picker) {
                $(picker).spectrum({
                    preferredFormat: "hex",
                    showInput: true,
                    showInitial: true,
                    allowEmpty: true
                });
            });
        }
        
        // Date pickers
        const datePickers = container.querySelectorAll('.date-picker');
        if (datePickers.length > 0 && typeof $.fn.datepicker !== 'undefined') {
            datePickers.forEach(function(picker) {
                $(picker).datepicker({
                    format: 'yyyy-mm-dd',
                    autoclose: true
                });
            });
        }
        
        // Rich text editors
        const richTextEditors = container.querySelectorAll('.rich-text-editor');
        if (richTextEditors.length > 0 && typeof ClassicEditor !== 'undefined') {
            richTextEditors.forEach(function(editor) {
                ClassicEditor
                    .create(editor)
                    .catch(error => {
                        console.error(error);
                    });
            });
        }
    },

    // Initialize form dependencies
    initFormDependencies: function() {
        const dependentFields = document.querySelectorAll('[data-depends-on]');
        
        dependentFields.forEach(function(field) {
            const dependsOn = field.getAttribute('data-depends-on');
            const dependsValue = field.getAttribute('data-depends-value');
            const dependsNotValue = field.getAttribute('data-depends-not-value');
            const dependsContainer = field.closest('.form-group') || field;
            
            const sourceField = document.querySelector('[name="' + dependsOn + '"]');
            
            if (sourceField) {
                // Initial state
                updateVisibility();
                
                // Update on change
                sourceField.addEventListener('change', updateVisibility);
            }
            
            function updateVisibility() {
                let shouldShow = true;
                
                if (dependsValue !== null) {
                    if (sourceField.type === 'checkbox') {
                        shouldShow = sourceField.checked === (dependsValue === 'true');
                    } else if (sourceField.type === 'radio') {
                        const checkedRadio = document.querySelector('input[name="' + dependsOn + '"]:checked');
                        shouldShow = checkedRadio && checkedRadio.value === dependsValue;
                    } else {
                        shouldShow = sourceField.value === dependsValue;
                    }
                }
                
                if (dependsNotValue !== null) {
                    if (sourceField.type === 'checkbox') {
                        shouldShow = sourceField.checked !== (dependsNotValue === 'true');
                    } else if (sourceField.type === 'radio') {
                        const checkedRadio = document.querySelector('input[name="' + dependsOn + '"]:checked');
                        shouldShow = !checkedRadio || checkedRadio.value !== dependsNotValue;
                    } else {
                        shouldShow = sourceField.value !== dependsNotValue;
                    }
                }
                
                dependsContainer.style.display = shouldShow ? '' : 'none';
                
                // Disable fields when hidden to prevent submission
                const fields = dependsContainer.querySelectorAll('input, textarea, select');
                fields.forEach(function(f) {
                    f.disabled = !shouldShow;
                });
            }
        });
    },

    // Initialize form file uploads
    initFormFileUploads: function() {
        const fileInputs = document.querySelectorAll('.custom-file-input');
        
        fileInputs.forEach(function(input) {
            input.addEventListener('change', function() {
                const label = this.nextElementSibling;
                
                if (label && label.classList.contains('custom-file-label')) {
                    if (this.files.length > 1) {
                        label.textContent = this.files.length + ' files selected';
                    } else if (this.files.length === 1) {
                        label.textContent = this.files[0].name;
                    } else {
                        label.textContent = 'Choose file';
                    }
                }
            });
        });
    },

    // Initialize form image previews
    initFormImagePreviews: function() {
        const imageInputs = document.querySelectorAll('input[type="file"][data-preview]');
        
        imageInputs.forEach(function(input) {
            const previewTarget = input.getAttribute('data-preview');
            const preview = document.querySelector(previewTarget);
            
            if (preview) {
                input.addEventListener('change', function() {
                    if (this.files && this.files[0]) {
                        const reader = new FileReader();
                        
                        reader.onload = function(e) {
                            preview.src = e.target.result;
                            preview.style.display = 'block';
                        };
                        
                        reader.readAsDataURL(this.files[0]);
                    }
                });
            }
        });
    },

    // Initialize form auto-save
    initFormAutoSave: function() {
        const autoSaveForms = document.querySelectorAll('form[data-autosave]');
        
        autoSaveForms.forEach(function(form) {
            const autoSaveKey = form.getAttribute('data-autosave');
            const autoSaveInterval = parseInt(form.getAttribute('data-autosave-interval')) || 30000; // Default: 30 seconds
            
            // Load saved data
            const savedData = localStorage.getItem(autoSaveKey);
            if (savedData) {
                try {
                    const data = JSON.parse(savedData);
                    
                    // Fill form with saved data
                    for (const field in data) {
                        const input = form.querySelector('[name="' + field + '"]');
                        if (input) {
                            if (input.type === 'checkbox') {
                                input.checked = data[field];
                            } else if (input.type === 'radio') {
                                const radio = form.querySelector('[name="' + field + '"][value="' + data[field] + '"]');
                                if (radio) {
                                    radio.checked = true;
                                }
                            } else {
                                input.value = data[field];
                            }
                        }
                    }
                    
                    // Show restore message
                    const restoreTime = new Date(data._timestamp).toLocaleString();
                    const message = `Form data restored from ${restoreTime}. <button class="btn-link" data-autosave-discard>Discard</button>`;
                    
                    const alert = document.createElement('div');
                    alert.className = 'admin-alert info';
                    alert.innerHTML = message;
                    
                    form.prepend(alert);
                    
                    // Discard button
                    const discardButton = alert.querySelector('[data-autosave-discard]');
                    if (discardButton) {
                        discardButton.addEventListener('click', function() {
                            localStorage.removeItem(autoSaveKey);
                            alert.remove();
                            form.reset();
                        });
                    }
                } catch (e) {
                    console.error('Error loading autosaved data:', e);
                    localStorage.removeItem(autoSaveKey);
                }
            }
            
            // Set up auto-save
            let autoSaveTimer;
            
            function saveFormData() {
                const formData = {};
                
                // Collect form data
                form.querySelectorAll('input, textarea, select').forEach(function(input) {
                    if (input.name && !input.disabled) {
                        if (input.type === 'checkbox') {
                            formData[input.name] = input.checked;
                        } else if (input.type === 'radio') {
                            if (input.checked) {
                                formData[input.name] = input.value;
                            }
                        } else {
                            formData[input.name] = input.value;
                        }
                    }
                });
                
                // Add timestamp
                formData._timestamp = new Date().getTime();
                
                // Save to localStorage
                localStorage.setItem(autoSaveKey, JSON.stringify(formData));
            }
            
            // Save on input
            form.addEventListener('input', function() {
                clearTimeout(autoSaveTimer);
                autoSaveTimer = setTimeout(saveFormData, autoSaveInterval);
            });
            
            // Save on submit
            form.addEventListener('submit', function() {
                localStorage.removeItem(autoSaveKey);
            });
        });
    },

    // Initialize form reset confirmation
    initFormResetConfirmation: function() {
        const resetButtons = document.querySelectorAll('form button[type="reset"], form input[type="reset"]');
        
        resetButtons.forEach(function(button) {
            button.addEventListener('click', function(e) {
                if (!confirm('Are you sure you want to reset the form? All unsaved changes will be lost.')) {
                    e.preventDefault();
                }
            });
        });
    },

    // Initialize form submit buttons
    initFormSubmitButtons: function() {
        const submitButtons = document.querySelectorAll('form button[type="submit"], form input[type="submit"]');
        
        submitButtons.forEach(function(button) {
            button.addEventListener('click', function() {
                // Add loading state
                this.classList.add('loading');
                
                if (this.tagName === 'BUTTON') {
                    this.setAttribute('data-original-text', this.innerHTML);
                    this.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Loading...';
                } else {
                    this.setAttribute('data-original-value', this.value);
                    this.value = 'Loading...';
                }
            });
        });
    },

    // Initialize form character counters
    initFormCharacterCounters: function() {
        const countedFields = document.querySelectorAll('[data-max-length]');
        
        countedFields.forEach(function(field) {
            const maxLength = parseInt(field.getAttribute('data-max-length'));
            
            // Create counter element
            const counter = document.createElement('div');
            counter.className = 'character-counter';
            counter.textContent = `0/${maxLength}`;
            
            field.parentNode.insertBefore(counter, field.nextSibling);
            
            // Update counter
            function updateCounter() {
                const length = field.value.length;
                counter.textContent = `${length}/${maxLength}`;
                
                if (length > maxLength) {
                    counter.classList.add('exceeded');
                } else {
                    counter.classList.remove('exceeded');
                }
            }
            
            // Initial update
            updateCounter();
            
            // Update on input
            field.addEventListener('input', updateCounter);
        });
    },

    // Initialize form slug generator
    initFormSlugGenerator: function() {
        const slugFields = document.querySelectorAll('[data-slug-from]');
        
        slugFields.forEach(function(field) {
            const sourceSelector = field.getAttribute('data-slug-from');
            const sourceField = document.querySelector(sourceSelector);
            
            if (sourceField) {
                // Generate slug button
                const button = document.createElement('button');
                button.type = 'button';
                button.className = 'admin-btn small light generate-slug';
                button.innerHTML = '<i class="fas fa-sync-alt"></i> Generate';
                
                field.parentNode.insertBefore(button, field.nextSibling);
                
                // Generate slug function
                function generateSlug() {
                    const value = sourceField.value;
                    const slug = value
                        .toLowerCase()
                        .replace(/[^\w\s-]/g, '') // Remove special characters
                        .replace(/\s+/g, '-') // Replace spaces with hyphens
                        .replace(/-+/g, '-') // Replace multiple hyphens with single hyphen
                        .trim();
                    
                    field.value = slug;
                }
                
                // Generate on button click
                button.addEventListener('click', function(e) {
                    e.preventDefault();
                    generateSlug();
                });
                
                // Auto-generate if field is empty
                sourceField.addEventListener('blur', function() {
                    if (!field.value.trim()) {
                        generateSlug();
                    }
                });
            }
        });
    }
};
