<?php
/**
 * NewsSettings class
 *
 * Helper class to manage news settings in the admin panel
 */
class NewsSettings {
    private $conn;
    private $settings = [];
    private $user_id;

    /**
     * Constructor
     *
     * @param object $conn Database connection
     * @param int $user_id User ID (optional)
     */
    public function __construct($conn, $user_id = null) {
        $this->conn = $conn;
        $this->user_id = $user_id;
        $this->loadSettings();
    }

    /**
     * Load all news settings from database
     */
    private function loadSettings() {
        // Initialize settings array with categories
        $this->settings = [
            'news' => [],
            'admin_news' => [],
            'frontend_news' => []
        ];

        // Load settings from all news-related categories
        $categories = ['news', 'admin_news', 'frontend_news'];

        foreach ($categories as $category) {
            $sql = "SELECT * FROM system_settings WHERE category = '$category' ORDER BY order_index";
            $result = $this->conn->query($sql);

            if ($result && $result->num_rows > 0) {
                while ($row = $result->fetch_assoc()) {
                    $key = $row['setting_key'];
                    $this->settings[$category][$key] = $row;
                }
            }
        }

        // For backward compatibility, also load legacy settings into the main array
        if (!empty($this->settings['news'])) {
            foreach ($this->settings['news'] as $key => $value) {
                $this->settings[$key] = $value;
            }
        }
    }

    /**
     * Get all news settings
     *
     * @return array All news settings
     */
    public function getAllSettings() {
        return $this->settings;
    }

    /**
     * Get a specific setting value
     *
     * @param string $key Setting key
     * @param mixed $default Default value if setting not found
     * @param string $category Optional category to look in (news, admin_news, frontend_news)
     * @return mixed Setting value
     */
    public function getSetting($key, $default = null, $category = null) {
        // If category is specified, look only in that category
        if ($category !== null) {
            if (isset($this->settings[$category][$key])) {
                return $this->settings[$category][$key]['setting_value'];
            }
            return $default;
        }

        // Otherwise, check in all categories with priority: admin_news, frontend_news, news
        $categories = ['admin_news', 'frontend_news', 'news'];
        foreach ($categories as $cat) {
            if (isset($this->settings[$cat][$key])) {
                return $this->settings[$cat][$key]['setting_value'];
            }
        }

        // For backward compatibility, check in the main array
        if (isset($this->settings[$key])) {
            return $this->settings[$key]['setting_value'];
        }

        return $default;
    }

    /**
     * Update a setting
     *
     * @param string $key Setting key
     * @param string $value Setting value
     * @param string $category Category to update (news, admin_news, frontend_news)
     * @return bool True on success, false on failure
     */
    public function updateSetting($key, $value, $category = 'news') {
        $key = $this->conn->real_escape_string($key);
        $value = $this->conn->real_escape_string($value);
        $category = $this->conn->real_escape_string($category);

        // Check if setting exists
        $check_sql = "SELECT id FROM system_settings WHERE category = '$category' AND setting_key = '$key'";
        $check_result = $this->conn->query($check_sql);

        if ($check_result && $check_result->num_rows > 0) {
            // Setting exists, update it
            $sql = "UPDATE system_settings SET setting_value = '$value' WHERE category = '$category' AND setting_key = '$key'";
            $result = $this->conn->query($sql);

            if ($result) {
                // Update local cache
                if (isset($this->settings[$category][$key])) {
                    $this->settings[$category][$key]['setting_value'] = $value;
                }
                // Also update the main array for backward compatibility
                if (isset($this->settings[$key])) {
                    $this->settings[$key]['setting_value'] = $value;
                }
                return true;
            }
        }

        return false;
    }

    /**
     * Get settings grouped by section
     *
     * @return array Settings grouped by section
     */
    public function getSettingsBySection() {
        $sections = [
            'display' => [],
            'pagination' => [],
            'seo' => []
        ];

        foreach ($this->settings as $key => $setting) {
            $order_index = (int)$setting['order_index'];

            if ($order_index < 10) {
                $sections['display'][$key] = $setting;
            } elseif ($order_index < 20) {
                $sections['pagination'][$key] = $setting;
            } else {
                $sections['seo'][$key] = $setting;
            }
        }

        return $sections;
    }

    /**
     * Get frontend settings for news display
     *
     * @return array Frontend settings
     */
    public function getFrontendSettings() {
        // First check frontend_news category
        if (!empty($this->settings['frontend_news'])) {
            return [
                'news_per_page' => (int)$this->getSetting('frontend_news_per_page', 9, 'frontend_news'),
                'show_news_date' => (bool)$this->getSetting('frontend_show_news_date', true, 'frontend_news'),
                'show_news_category' => (bool)$this->getSetting('frontend_show_news_category', true, 'frontend_news'),
                'news_excerpt_length' => (int)$this->getSetting('frontend_news_excerpt_length', 150, 'frontend_news'),
                'news_image_width' => (int)$this->getSetting('frontend_news_image_width', 400, 'frontend_news'),
                'pagination_position' => $this->getSetting('frontend_pagination_position', 'bottom', 'frontend_news'),
                'default_sort_order' => $this->getSetting('frontend_default_sort_order', 'newest', 'frontend_news'),
                'items_per_page_options' => explode(',', $this->getSetting('frontend_items_per_page_options', '9,18,27,36', 'frontend_news'))
            ];
        }

        // Fallback to legacy news category
        return [
            'news_per_page' => (int)$this->getSetting('news_per_page', 9, 'news'),
            'show_news_date' => (bool)$this->getSetting('show_news_date', true, 'news'),
            'show_news_category' => (bool)$this->getSetting('show_news_category', true, 'news'),
            'news_excerpt_length' => (int)$this->getSetting('news_excerpt_length', 150, 'news'),
            'news_image_width' => (int)$this->getSetting('news_image_width', 400, 'news'),
            'pagination_position' => $this->getSetting('pagination_position', 'bottom', 'news'),
            'default_sort_order' => $this->getSetting('default_sort_order', 'newest', 'news'),
            'items_per_page_options' => explode(',', $this->getSetting('items_per_page_options', '9,18,27,36', 'news'))
        ];
    }

    /**
     * Get admin settings for news management
     *
     * @return array Admin settings
     */
    public function getAdminSettings() {
        // First check admin_news category
        if (!empty($this->settings['admin_news'])) {
            return [
                'news_per_page' => (int)$this->getSetting('admin_news_per_page', 10, 'admin_news'),
                'show_news_date' => (bool)$this->getSetting('admin_show_news_date', true, 'admin_news'),
                'show_news_category' => (bool)$this->getSetting('admin_show_news_category', true, 'admin_news'),
                'news_excerpt_length' => (int)$this->getSetting('admin_news_excerpt_length', 100, 'admin_news'),
                'pagination_position' => $this->getSetting('admin_pagination_position', 'bottom', 'admin_news'),
                'default_sort_order' => $this->getSetting('admin_default_sort_order', 'newest', 'admin_news'),
                'items_per_page_options' => explode(',', $this->getSetting('admin_items_per_page_options', '10,25,50,100', 'admin_news')),
                'generate_slugs' => (bool)$this->getSetting('generate_slugs', true, 'admin_news'),
                'slug_separator' => $this->getSetting('slug_separator', '-', 'admin_news')
            ];
        }

        // Fallback to legacy news category
        return [
            'news_per_page' => (int)$this->getSetting('news_per_page', 10, 'news'),
            'show_news_date' => (bool)$this->getSetting('show_news_date', true, 'news'),
            'show_news_category' => (bool)$this->getSetting('show_news_category', true, 'news'),
            'news_excerpt_length' => (int)$this->getSetting('news_excerpt_length', 100, 'news'),
            'pagination_position' => $this->getSetting('pagination_position', 'bottom', 'news'),
            'default_sort_order' => $this->getSetting('default_sort_order', 'newest', 'news'),
            'items_per_page_options' => explode(',', $this->getSetting('items_per_page_options', '10,25,50,100', 'news')),
            'generate_slugs' => (bool)$this->getSetting('generate_slugs', true, 'news'),
            'slug_separator' => $this->getSetting('slug_separator', '-', 'news')
        ];
    }

    /**
     * Get SEO settings for news pages
     *
     * @return array SEO settings
     */
    public function getSeoSettings() {
        // First check frontend_news category for SEO settings
        if (!empty($this->settings['frontend_news'])) {
            return [
                'generate_slugs' => (bool)$this->getSetting('generate_slugs', true, 'admin_news'), // This is an admin setting
                'slug_separator' => $this->getSetting('slug_separator', '-', 'admin_news'), // This is an admin setting
                'news_meta_title' => $this->getSetting('news_meta_title', 'Latest News | {site_name}', 'frontend_news'),
                'news_meta_description' => $this->getSetting('news_meta_description', 'Read the latest news and updates from {site_name}', 'frontend_news')
            ];
        }

        // Fallback to legacy news category
        return [
            'generate_slugs' => (bool)$this->getSetting('generate_slugs', true, 'news'),
            'slug_separator' => $this->getSetting('slug_separator', '-', 'news'),
            'news_meta_title' => $this->getSetting('news_meta_title', 'Latest News | {site_name}', 'news'),
            'news_meta_description' => $this->getSetting('news_meta_description', 'Read the latest news and updates from {site_name}', 'news')
        ];
    }
}
?>
