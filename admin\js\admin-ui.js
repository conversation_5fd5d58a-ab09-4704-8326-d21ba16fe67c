/**
 * Admin UI JavaScript
 * UI interactions for the admin panel
 * Consolidated from multiple JS files
 */

// Initialize UI components when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    // Initialize all UI components
    AdminUI.init();
});

// Admin UI Namespace
const AdminUI = {
    // Initialize all UI components
    init: function() {
        this.initResizableModals();
        this.initDraggableElements();
        this.initSortableTables();
        this.initDataTables();
        this.initSelectAll();
        this.initToggleSwitches();
        this.initColorPickers();
        this.initDatePickers();
        this.initRichTextEditors();
        this.initImagePreviews();
        this.initTableFilters();
        this.initCardView();
        this.initExpandableCards();
        this.initTooltips();
    },

    // Initialize resizable modals
    initResizableModals: function() {
        const modals = document.querySelectorAll('.admin-modal');
        
        modals.forEach(function(modal) {
            // Add resize handle
            const resizeHandle = document.createElement('div');
            resizeHandle.className = 'modal-resize-handle';
            modal.appendChild(resizeHandle);
            
            let startX, startY, startWidth, startHeight;
            
            resizeHandle.addEventListener('mousedown', function(e) {
                e.preventDefault();
                
                startX = e.clientX;
                startY = e.clientY;
                startWidth = modal.offsetWidth;
                startHeight = modal.offsetHeight;
                
                document.addEventListener('mousemove', resize);
                document.addEventListener('mouseup', stopResize);
            });
            
            function resize(e) {
                const width = startWidth + (e.clientX - startX);
                const height = startHeight + (e.clientY - startY);
                
                modal.style.width = width + 'px';
                modal.style.height = height + 'px';
            }
            
            function stopResize() {
                document.removeEventListener('mousemove', resize);
                document.removeEventListener('mouseup', stopResize);
            }
        });
    },

    // Initialize draggable elements
    initDraggableElements: function() {
        const draggableElements = document.querySelectorAll('[data-draggable]');
        
        draggableElements.forEach(function(element) {
            const handle = element.querySelector('[data-drag-handle]') || element;
            
            let offsetX, offsetY, isDragging = false;
            
            handle.addEventListener('mousedown', function(e) {
                if (e.target.closest('button, a, input, select, textarea')) return;
                
                e.preventDefault();
                
                const rect = element.getBoundingClientRect();
                offsetX = e.clientX - rect.left;
                offsetY = e.clientY - rect.top;
                
                element.style.position = 'absolute';
                element.style.zIndex = '1000';
                
                document.addEventListener('mousemove', drag);
                document.addEventListener('mouseup', stopDrag);
                
                isDragging = true;
            });
            
            function drag(e) {
                if (!isDragging) return;
                
                const x = e.clientX - offsetX;
                const y = e.clientY - offsetY;
                
                element.style.left = x + 'px';
                element.style.top = y + 'px';
            }
            
            function stopDrag() {
                document.removeEventListener('mousemove', drag);
                document.removeEventListener('mouseup', stopDrag);
                
                isDragging = false;
            }
        });
    },

    // Initialize sortable tables
    initSortableTables: function() {
        const sortableTables = document.querySelectorAll('table[data-sortable]');
        
        sortableTables.forEach(function(table) {
            const headers = table.querySelectorAll('th[data-sort]');
            
            headers.forEach(function(header) {
                header.classList.add('sortable');
                header.innerHTML += '<span class="sort-icon"></span>';
                
                header.addEventListener('click', function() {
                    const column = this.cellIndex;
                    const sortDirection = this.getAttribute('data-sort-direction') || 'asc';
                    const sortType = this.getAttribute('data-sort-type') || 'string';
                    
                    // Reset all headers
                    headers.forEach(function(h) {
                        h.removeAttribute('data-sort-direction');
                        h.classList.remove('sorted-asc', 'sorted-desc');
                    });
                    
                    // Set current header
                    const newDirection = sortDirection === 'asc' ? 'desc' : 'asc';
                    this.setAttribute('data-sort-direction', newDirection);
                    this.classList.add('sorted-' + newDirection);
                    
                    // Sort table
                    const tbody = table.querySelector('tbody');
                    const rows = Array.from(tbody.querySelectorAll('tr'));
                    
                    rows.sort(function(a, b) {
                        let aValue = a.cells[column].textContent.trim();
                        let bValue = b.cells[column].textContent.trim();
                        
                        if (sortType === 'number') {
                            aValue = parseFloat(aValue.replace(/[^\d.-]/g, '')) || 0;
                            bValue = parseFloat(bValue.replace(/[^\d.-]/g, '')) || 0;
                        } else if (sortType === 'date') {
                            aValue = new Date(aValue).getTime() || 0;
                            bValue = new Date(bValue).getTime() || 0;
                        }
                        
                        if (newDirection === 'asc') {
                            return aValue > bValue ? 1 : -1;
                        } else {
                            return aValue < bValue ? 1 : -1;
                        }
                    });
                    
                    // Reorder rows
                    rows.forEach(function(row) {
                        tbody.appendChild(row);
                    });
                });
            });
        });
    },

    // Initialize DataTables
    initDataTables: function() {
        const dataTables = document.querySelectorAll('table[data-datatable]');
        
        if (dataTables.length > 0 && typeof $.fn.DataTable !== 'undefined') {
            dataTables.forEach(function(table) {
                $(table).DataTable({
                    responsive: true,
                    pageLength: parseInt(table.getAttribute('data-page-length')) || 10,
                    lengthMenu: [[10, 25, 50, 100, -1], [10, 25, 50, 100, "All"]],
                    language: {
                        search: "_INPUT_",
                        searchPlaceholder: "Search...",
                        lengthMenu: "Show _MENU_ entries",
                        info: "Showing _START_ to _END_ of _TOTAL_ entries",
                        infoEmpty: "Showing 0 to 0 of 0 entries",
                        infoFiltered: "(filtered from _MAX_ total entries)",
                        paginate: {
                            first: '<i class="fas fa-angle-double-left"></i>',
                            previous: '<i class="fas fa-angle-left"></i>',
                            next: '<i class="fas fa-angle-right"></i>',
                            last: '<i class="fas fa-angle-double-right"></i>'
                        }
                    }
                });
            });
        }
    },

    // Initialize select all functionality
    initSelectAll: function() {
        const selectAllCheckboxes = document.querySelectorAll('[data-select-all]');
        
        selectAllCheckboxes.forEach(function(checkbox) {
            const target = checkbox.getAttribute('data-select-all');
            const checkboxes = document.querySelectorAll(target);
            
            checkbox.addEventListener('change', function() {
                checkboxes.forEach(function(cb) {
                    cb.checked = checkbox.checked;
                });
            });
            
            // Update select all when individual checkboxes change
            checkboxes.forEach(function(cb) {
                cb.addEventListener('change', function() {
                    const allChecked = Array.from(checkboxes).every(function(c) {
                        return c.checked;
                    });
                    
                    const someChecked = Array.from(checkboxes).some(function(c) {
                        return c.checked;
                    });
                    
                    checkbox.checked = allChecked;
                    checkbox.indeterminate = someChecked && !allChecked;
                });
            });
        });
    },

    // Initialize toggle switches
    initToggleSwitches: function() {
        const toggleSwitches = document.querySelectorAll('.custom-switch');
        
        toggleSwitches.forEach(function(toggle) {
            const input = toggle.querySelector('input[type="checkbox"]');
            const target = input.getAttribute('data-toggle-target');
            
            if (input && target) {
                const targetElement = document.querySelector(target);
                
                if (targetElement) {
                    // Initial state
                    targetElement.style.display = input.checked ? 'block' : 'none';
                    
                    // Toggle on change
                    input.addEventListener('change', function() {
                        targetElement.style.display = this.checked ? 'block' : 'none';
                    });
                }
            }
        });
    },

    // Initialize color pickers
    initColorPickers: function() {
        const colorPickers = document.querySelectorAll('.color-picker');
        
        if (colorPickers.length > 0 && typeof $.fn.spectrum !== 'undefined') {
            colorPickers.forEach(function(picker) {
                $(picker).spectrum({
                    preferredFormat: "hex",
                    showInput: true,
                    showInitial: true,
                    allowEmpty: true,
                    showPalette: true,
                    palette: [
                        ["#000", "#444", "#666", "#999", "#ccc", "#eee", "#f3f3f3", "#fff"],
                        ["#f00", "#f90", "#ff0", "#0f0", "#0ff", "#00f", "#90f", "#f0f"],
                        ["#f4cccc", "#fce5cd", "#fff2cc", "#d9ead3", "#d0e0e3", "#cfe2f3", "#d9d2e9", "#ead1dc"],
                        ["#ea9999", "#f9cb9c", "#ffe599", "#b6d7a8", "#a2c4c9", "#9fc5e8", "#b4a7d6", "#d5a6bd"],
                        ["#e06666", "#f6b26b", "#ffd966", "#93c47d", "#76a5af", "#6fa8dc", "#8e7cc3", "#c27ba0"],
                        ["#c00", "#e69138", "#f1c232", "#6aa84f", "#45818e", "#3d85c6", "#674ea7", "#a64d79"],
                        ["#900", "#b45f06", "#bf9000", "#38761d", "#134f5c", "#0b5394", "#351c75", "#741b47"],
                        ["#600", "#783f04", "#7f6000", "#274e13", "#0c343d", "#073763", "#20124d", "#4c1130"]
                    ]
                });
            });
        }
    },

    // Initialize date pickers
    initDatePickers: function() {
        const datePickers = document.querySelectorAll('.date-picker');
        
        if (datePickers.length > 0 && typeof $.fn.datepicker !== 'undefined') {
            datePickers.forEach(function(picker) {
                $(picker).datepicker({
                    format: 'yyyy-mm-dd',
                    autoclose: true,
                    todayHighlight: true,
                    todayBtn: 'linked'
                });
            });
        }
    },

    // Initialize rich text editors
    initRichTextEditors: function() {
        const richTextEditors = document.querySelectorAll('.rich-text-editor');
        
        if (richTextEditors.length > 0 && typeof ClassicEditor !== 'undefined') {
            richTextEditors.forEach(function(editor) {
                ClassicEditor
                    .create(editor, {
                        toolbar: [
                            'heading', '|',
                            'bold', 'italic', 'link', 'bulletedList', 'numberedList', '|',
                            'indent', 'outdent', '|',
                            'blockQuote', 'insertTable', 'mediaEmbed', '|',
                            'undo', 'redo'
                        ]
                    })
                    .catch(error => {
                        console.error(error);
                    });
            });
        }
    },

    // Initialize image previews
    initImagePreviews: function() {
        const imageInputs = document.querySelectorAll('input[type="file"][data-preview]');
        
        imageInputs.forEach(function(input) {
            const previewTarget = input.getAttribute('data-preview');
            const preview = document.querySelector(previewTarget);
            
            if (preview) {
                input.addEventListener('change', function() {
                    if (this.files && this.files[0]) {
                        const reader = new FileReader();
                        
                        reader.onload = function(e) {
                            preview.src = e.target.result;
                            preview.style.display = 'block';
                        };
                        
                        reader.readAsDataURL(this.files[0]);
                    }
                });
            }
        });
    },

    // Initialize table filters
    initTableFilters: function() {
        const tableFilters = document.querySelectorAll('.admin-table-filter');
        
        tableFilters.forEach(function(filter) {
            const input = filter.querySelector('input, select');
            const table = document.querySelector(filter.getAttribute('data-table'));
            
            if (input && table) {
                input.addEventListener('input', AdminUtils.debounce(function() {
                    const value = this.value.toLowerCase();
                    const column = filter.getAttribute('data-column');
                    const rows = table.querySelectorAll('tbody tr');
                    
                    rows.forEach(function(row) {
                        let text;
                        
                        if (column) {
                            const cell = row.querySelector(`td:nth-child(${parseInt(column) + 1})`);
                            text = cell ? cell.textContent.toLowerCase() : '';
                        } else {
                            text = row.textContent.toLowerCase();
                        }
                        
                        row.style.display = text.includes(value) ? '' : 'none';
                    });
                }, 300));
            }
        });
    },

    // Initialize card view for mobile
    initCardView: function() {
        const cardViewToggles = document.querySelectorAll('[data-toggle="card-view"]');
        
        cardViewToggles.forEach(function(toggle) {
            toggle.addEventListener('click', function(e) {
                e.preventDefault();
                
                const target = this.getAttribute('data-target');
                const tableView = document.querySelector(target + ' .table-view');
                const cardView = document.querySelector(target + ' .card-view');
                
                if (tableView && cardView) {
                    tableView.style.display = tableView.style.display === 'none' ? 'block' : 'none';
                    cardView.style.display = cardView.style.display === 'none' ? 'block' : 'none';
                    
                    // Update toggle text
                    this.innerHTML = tableView.style.display === 'none' ? 
                        '<i class="fas fa-table"></i> Table View' : 
                        '<i class="fas fa-th-large"></i> Card View';
                }
            });
        });
    },

    // Initialize expandable cards
    initExpandableCards: function() {
        const expandableCards = document.querySelectorAll('.admin-card[data-expandable]');
        
        expandableCards.forEach(function(card) {
            const header = card.querySelector('.admin-card-header');
            const body = card.querySelector('.admin-card-body');
            
            if (header && body) {
                // Add toggle button
                const toggleButton = document.createElement('button');
                toggleButton.className = 'card-expand-toggle';
                toggleButton.innerHTML = '<i class="fas fa-chevron-up"></i>';
                header.appendChild(toggleButton);
                
                // Check initial state
                if (card.getAttribute('data-expanded') === 'false') {
                    body.style.display = 'none';
                    toggleButton.innerHTML = '<i class="fas fa-chevron-down"></i>';
                }
                
                // Toggle on click
                toggleButton.addEventListener('click', function() {
                    const isExpanded = body.style.display !== 'none';
                    
                    body.style.display = isExpanded ? 'none' : 'block';
                    toggleButton.innerHTML = isExpanded ? 
                        '<i class="fas fa-chevron-down"></i>' : 
                        '<i class="fas fa-chevron-up"></i>';
                    
                    card.setAttribute('data-expanded', !isExpanded);
                });
            }
        });
    },

    // Initialize tooltips
    initTooltips: function() {
        const tooltipElements = document.querySelectorAll('[data-tooltip]');
        
        tooltipElements.forEach(function(element) {
            const tooltip = element.getAttribute('data-tooltip');
            const position = element.getAttribute('data-tooltip-position') || 'top';
            const multiline = element.hasAttribute('data-tooltip-multiline');
            
            // Create tooltip element
            const tooltipEl = document.createElement('div');
            tooltipEl.className = 'admin-tooltip ' + position;
            if (multiline) tooltipEl.classList.add('multiline');
            tooltipEl.textContent = tooltip;
            
            // Show tooltip on hover
            element.addEventListener('mouseenter', function() {
                document.body.appendChild(tooltipEl);
                
                const rect = element.getBoundingClientRect();
                const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
                const scrollLeft = window.pageXOffset || document.documentElement.scrollLeft;
                
                let top, left;
                
                switch (position) {
                    case 'top':
                        top = rect.top + scrollTop - tooltipEl.offsetHeight - 10;
                        left = rect.left + scrollLeft + (rect.width / 2) - (tooltipEl.offsetWidth / 2);
                        break;
                    case 'bottom':
                        top = rect.bottom + scrollTop + 10;
                        left = rect.left + scrollLeft + (rect.width / 2) - (tooltipEl.offsetWidth / 2);
                        break;
                    case 'left':
                        top = rect.top + scrollTop + (rect.height / 2) - (tooltipEl.offsetHeight / 2);
                        left = rect.left + scrollLeft - tooltipEl.offsetWidth - 10;
                        break;
                    case 'right':
                        top = rect.top + scrollTop + (rect.height / 2) - (tooltipEl.offsetHeight / 2);
                        left = rect.right + scrollLeft + 10;
                        break;
                }
                
                tooltipEl.style.top = top + 'px';
                tooltipEl.style.left = left + 'px';
                tooltipEl.classList.add('active');
            });
            
            // Hide tooltip on mouse leave
            element.addEventListener('mouseleave', function() {
                tooltipEl.classList.remove('active');
                if (tooltipEl.parentNode) {
                    tooltipEl.parentNode.removeChild(tooltipEl);
                }
            });
        });
    }
};
