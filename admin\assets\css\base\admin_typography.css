/**
 * Admin Typography CSS
 * 
 * This file contains typography styles for the admin panel.
 */

/* Base Typography */
body {
  font-family: var(--font-family-sans);
  font-size: var(--font-size-base);
  line-height: var(--line-height-normal);
  color: var(--text-color);
}

/* Headings */
h1, h2, h3, h4, h5, h6 {
  margin-bottom: var(--spacing-4);
  font-weight: var(--font-weight-semibold);
  line-height: var(--line-height-tight);
  color: var(--text-dark);
}

h1 {
  font-size: var(--font-size-3xl);
}

h2 {
  font-size: var(--font-size-2xl);
}

h3 {
  font-size: var(--font-size-xl);
}

h4 {
  font-size: var(--font-size-lg);
}

h5 {
  font-size: var(--font-size-base);
}

h6 {
  font-size: var(--font-size-sm);
}

/* Paragraphs */
p {
  margin-bottom: var(--spacing-4);
}

/* Links */
a {
  color: var(--primary-color);
  text-decoration: none;
  transition: color var(--transition-fast) ease;
}

a:hover {
  color: var(--primary-dark);
}

/* Lists */
ul, ol {
  margin-bottom: var(--spacing-4);
  padding-left: var(--spacing-6);
}

ul {
  list-style-type: disc;
}

ol {
  list-style-type: decimal;
}

li {
  margin-bottom: var(--spacing-2);
}

/* Blockquotes */
blockquote {
  margin: var(--spacing-4) 0;
  padding: var(--spacing-4);
  border-left: 4px solid var(--primary-color);
  background-color: var(--primary-very-light);
  color: var(--text-dark);
  font-style: italic;
}

blockquote p:last-child {
  margin-bottom: 0;
}

/* Code */
code {
  font-family: var(--font-family-mono);
  font-size: var(--font-size-sm);
  padding: var(--spacing-1) var(--spacing-2);
  background-color: var(--gray-100);
  border-radius: var(--radius-sm);
  color: var(--text-dark);
}

pre {
  margin-bottom: var(--spacing-4);
  padding: var(--spacing-4);
  background-color: var(--gray-100);
  border-radius: var(--radius-md);
  overflow-x: auto;
}

pre code {
  padding: 0;
  background-color: transparent;
  border-radius: 0;
}

/* Text Utilities */
.text-xs {
  font-size: var(--font-size-xs);
}

.text-sm {
  font-size: var(--font-size-sm);
}

.text-base {
  font-size: var(--font-size-base);
}

.text-lg {
  font-size: var(--font-size-lg);
}

.text-xl {
  font-size: var(--font-size-xl);
}

.text-2xl {
  font-size: var(--font-size-2xl);
}

.text-3xl {
  font-size: var(--font-size-3xl);
}

.text-4xl {
  font-size: var(--font-size-4xl);
}

.font-light {
  font-weight: var(--font-weight-light);
}

.font-normal {
  font-weight: var(--font-weight-normal);
}

.font-medium {
  font-weight: var(--font-weight-medium);
}

.font-semibold {
  font-weight: var(--font-weight-semibold);
}

.font-bold {
  font-weight: var(--font-weight-bold);
}

.text-left {
  text-align: left;
}

.text-center {
  text-align: center;
}

.text-right {
  text-align: right;
}

.text-justify {
  text-align: justify;
}

.uppercase {
  text-transform: uppercase;
}

.lowercase {
  text-transform: lowercase;
}

.capitalize {
  text-transform: capitalize;
}

.italic {
  font-style: italic;
}

.underline {
  text-decoration: underline;
}

.line-through {
  text-decoration: line-through;
}

.no-underline {
  text-decoration: none;
}

/* Text Colors */
.text-primary {
  color: var(--primary-color);
}

.text-secondary {
  color: var(--secondary-color);
}

.text-success {
  color: var(--success-color);
}

.text-danger {
  color: var(--danger-color);
}

.text-warning {
  color: var(--warning-color);
}

.text-info {
  color: var(--info-color);
}

.text-muted {
  color: var(--text-muted);
}

.text-light {
  color: var(--text-light);
}

.text-dark {
  color: var(--text-dark);
}

/* Responsive Typography */
@media (max-width: 768px) {
  h1 {
    font-size: var(--font-size-2xl);
  }
  
  h2 {
    font-size: var(--font-size-xl);
  }
  
  h3 {
    font-size: var(--font-size-lg);
  }
}
