<?php
// Clean Contact Form Handler - No output before JSON
ob_start();

// Error handling
error_reporting(E_ALL);
ini_set('display_errors', 0);
ini_set('log_errors', 1);

// Fatal error handler
register_shutdown_function(function() {
    $error = error_get_last();
    if ($error && in_array($error['type'], [E_ERROR, E_PARSE, E_CORE_ERROR, E_COMPILE_ERROR])) {
        if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
            while (ob_get_level()) ob_end_clean();
            header('Content-Type: application/json');
            echo json_encode(['success' => false, 'message' => 'Server error: ' . $error['message']]);
        }
    }
});

// Default response
$response = ['success' => false, 'message' => 'Invalid request'];

// Only process POST requests
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        // Start session
        if (session_status() === PHP_SESSION_NONE) {
            session_start();
        }
        
        // Include config
        require_once 'admin/config.php';
        require_once 'admin/includes/email-functions.php';
        
        // Sanitize function
        function clean_input($input) {
            return htmlspecialchars(trim($input), ENT_QUOTES, 'UTF-8');
        }
        
        // Get and validate form data
        $name = isset($_POST['name']) ? clean_input($_POST['name']) : '';
        $email = isset($_POST['email']) ? clean_input($_POST['email']) : '';
        $phone = isset($_POST['phone']) ? clean_input($_POST['phone']) : '';
        $message = isset($_POST['message']) ? clean_input($_POST['message']) : '';
        $source = isset($_POST['source']) ? clean_input($_POST['source']) : 'Clean Form';
        
        // Validation
        if (empty($name) || empty($email) || empty($message)) {
            $response = ['success' => false, 'message' => 'Please fill in all required fields.'];
        } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
            $response = ['success' => false, 'message' => 'Please enter a valid email address.'];
        } else {
            // Get email settings
            $settings = get_email_settings();
            
            // Determine recipient
            $recipient = get_setting('admin_email', '');
            if (empty($recipient)) {
                $recipient = $settings['from_email'];
            }
            if (empty($recipient)) {
                $recipient = '<EMAIL>';
            }
            
            // Create email content
            $subject = "Contact Form Submission from " . $name;
            $email_content = "New Contact Form Submission\n\n";
            $email_content .= "Name: " . $name . "\n";
            $email_content .= "Email: " . $email . "\n";
            if (!empty($phone)) {
                $email_content .= "Phone: " . $phone . "\n";
            }
            $email_content .= "Message: " . $message . "\n";
            $email_content .= "Source: " . $source . "\n";
            $email_content .= "Date: " . date('Y-m-d H:i:s') . "\n";
            
            // Send email
            $email_result = send_email($recipient, $subject, $email_content);
            
            if ($email_result) {
                $response = [
                    'success' => true,
                    'message' => 'Thank you for your message. We\'ll get back to you soon!'
                ];
                
                // Store in database if possible
                try {
                    // Check if table exists
                    $check_table = "SHOW TABLES LIKE 'contact_submissions'";
                    $table_result = $conn->query($check_table);
                    
                    if ($table_result->num_rows == 0) {
                        // Create table
                        $create_table = "CREATE TABLE `contact_submissions` (
                            `id` int(11) NOT NULL AUTO_INCREMENT,
                            `name` varchar(255) NOT NULL,
                            `email` varchar(255) NOT NULL,
                            `phone` varchar(50) DEFAULT NULL,
                            `message` text NOT NULL,
                            `source` varchar(50) NOT NULL DEFAULT 'Unknown',
                            `is_read` tinyint(1) NOT NULL DEFAULT 0,
                            `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
                            PRIMARY KEY (`id`)
                        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;";
                        $conn->query($create_table);
                    }
                    
                    // Insert submission
                    $sql = "INSERT INTO `contact_submissions` (`name`, `email`, `phone`, `message`, `source`) VALUES (?, ?, ?, ?, ?)";
                    $stmt = $conn->prepare($sql);
                    if ($stmt) {
                        $stmt->bind_param("sssss", $name, $email, $phone, $message, $source);
                        $stmt->execute();
                        $stmt->close();
                    }
                } catch (Exception $e) {
                    // Database error doesn't affect the response
                    error_log("Database error: " . $e->getMessage());
                }
                
            } else {
                $response = ['success' => false, 'message' => 'Failed to send email. Please try again.'];
            }
        }
        
    } catch (Exception $e) {
        $response = ['success' => false, 'message' => 'System error: ' . $e->getMessage()];
    }
}

// Return JSON for AJAX requests
if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
    // Clear all output
    while (ob_get_level()) {
        ob_end_clean();
    }
    
    // Set headers
    header('Content-Type: application/json');
    header('Cache-Control: no-cache, must-revalidate');
    
    // Output JSON and exit
    echo json_encode($response);
    exit;
}

// For non-AJAX requests, redirect back
$redirect = isset($_SERVER['HTTP_REFERER']) ? $_SERVER['HTTP_REFERER'] : 'index.html';
$separator = (parse_url($redirect, PHP_URL_QUERY) == NULL) ? '?' : '&';
$status = $response['success'] ? 'success' : 'error';
$redirect .= $separator . 'form_status=' . $status;
header("Location: $redirect");
exit;
?>
