/**
 * News Page Fixes
 * 
 * This CSS file addresses the following issues:
 * 1. Missing Visual Separation between news cards
 * 2. Inconsistent Card Styling
 * 3. Pagination Issues
 * 4. Responsive Layout Problems
 * 5. Date Formatting Issues
 * 6. Footer Text Overlap
 */

/* ===== 1. News Card Visual Separation ===== */
.news-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 30px;
    margin: 50px 0;
}

.news-item {
    background-color: #fff;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    border: 1px solid #eee;
    display: flex;
    flex-direction: column;
    height: 100%;
    position: relative;
    margin-bottom: 30px; /* Add bottom margin for separation */
}

/* Add a clear visual separator between news items */
.news-item::after {
    content: '';
    position: absolute;
    bottom: -15px;
    left: 0;
    width: 100%;
    height: 1px;
    background-color: #e0e0e0;
}

/* ===== 2. Consistent Card Styling ===== */
.news-image {
    height: 220px;
    overflow: hidden;
    position: relative;
    border-bottom: 1px solid #eee;
}

.news-content {
    padding: 25px;
    flex-grow: 1;
    display: flex;
    flex-direction: column;
    min-height: 200px;
    background-color: #fff;
    border-top: none;
}

.news-content h2 {
    font-size: 20px;
    font-weight: 600;
    margin-top: 0;
    margin-bottom: 12px;
    color: #3c3c45;
    line-height: 1.4;
    transition: color 0.3s ease;
}

.news-category {
    display: inline-block;
    color: #f1ca2f;
    font-size: 13px;
    font-weight: 600;
    margin-bottom: 15px;
    text-transform: uppercase;
    background-color: rgba(241, 202, 47, 0.1);
    padding: 4px 12px;
    border-radius: 20px;
}

.news-content p {
    color: #666;
    margin-bottom: 20px;
    line-height: 1.6;
    flex-grow: 1;
    font-size: 14px;
}

/* ===== 3. Pagination Fixes ===== */
.pagination-container {
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 40px 0;
    padding: 15px;
    background-color: #f8f9fa;
    border-radius: 8px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
}

.pagination {
    display: flex;
    list-style: none;
    padding: 0;
    margin: 0;
    gap: 5px;
}

.pagination li {
    margin: 0;
}

.pagination a,
.pagination span {
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 40px;
    height: 40px;
    padding: 0 10px;
    background-color: #fff;
    border: 1px solid #ddd;
    border-radius: 4px;
    color: #333;
    font-size: 14px;
    text-decoration: none;
    transition: all 0.3s;
}

.pagination a:hover {
    background-color: #f1ca2f;
    border-color: #f1ca2f;
    color: #333;
}

.pagination .active span {
    background-color: #f1ca2f;
    border-color: #f1ca2f;
    color: #333;
    font-weight: bold;
}

.pagination-info {
    margin-bottom: 15px;
    color: #666;
    font-size: 14px;
    text-align: center;
}

/* ===== 4. Responsive Layout Fixes ===== */
@media (max-width: 992px) {
    .news-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 25px;
    }
    
    .news-item {
        margin-bottom: 25px;
    }
}

@media (max-width: 768px) {
    .news-grid {
        grid-template-columns: 1fr;
        gap: 30px;
    }
    
    .news-item {
        margin-bottom: 30px;
    }
    
    .news-item::after {
        bottom: -20px;
        height: 2px;
    }
    
    .news-image {
        height: 200px;
    }
    
    .news-content {
        padding: 20px;
        min-height: 180px;
    }
}

/* ===== 5. Date Formatting Fixes ===== */
.news-date {
    display: flex;
    align-items: center;
    color: #666;
    font-size: 14px;
    margin-bottom: 15px;
}

.news-date i {
    margin-right: 5px;
    color: #f1ca2f;
}

.news-date-formatted {
    font-weight: 500;
}

/* ===== 6. Footer Text Overlap Fixes ===== */
.news-footer {
    padding: 15px 25px;
    border-top: 1px solid #eee;
    background-color: #f9f9f9;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.news-footer-text {
    font-size: 12px;
    color: #999;
}

.read-more-btn {
    display: inline-block;
    color: #f1ca2f;
    font-weight: 600;
    text-decoration: none;
    transition: all 0.3s;
    text-transform: lowercase;
    font-size: 14px;
    padding: 5px 10px;
    border-radius: 4px;
    background-color: rgba(241, 202, 47, 0.1);
}

.read-more-btn:hover {
    color: #333;
    background-color: rgba(241, 202, 47, 0.2);
}
