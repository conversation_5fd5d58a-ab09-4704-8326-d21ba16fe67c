<?php
// Buffer all output immediately
ob_start();

// Define installation version
define('INSTALL_VERSION', '1.1.0');

// Define security constants
define('XSS_PROTECTION', true);
define('CONTENT_SECURITY_POLICY', true);
define('SESSION_TIMEOUT', 3600); // 1 hour
define('PASSWORD_MIN_LENGTH', 8);
define('PASSWORD_REQUIRES_MIXED_CASE', true);
define('PASSWORD_REQUIRES_NUMBERS', true);
define('PASSWORD_REQUIRES_SYMBOLS', true);

// Enable error reporting for debugging
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Prevent caching
header("Cache-Control: no-store, no-cache, must-revalidate, max-age=0");
header("Cache-Control: post-check=0, pre-check=0", false);
header("Pragma: no-cache");
header("Expires: Mon, 26 Jul 1997 05:00:00 GMT");

// Set secure session parameters
ini_set('session.cookie_httponly', 1);
ini_set('session.use_only_cookies', 1);
ini_set('session.cookie_secure', isset($_SERVER['HTTPS']) ? 1 : 0);
ini_set('session.cookie_samesite', 'Strict');

// Start session
session_start();

// Check if installation is already completed
$installation_completed = false;
try {
    // Check if config.php exists
    if (file_exists('config.php')) {
        // Include the config file
        require_once 'config.php';

        // Check if DB_INSTALLED constant exists and is true
        if (defined('DB_INSTALLED') && DB_INSTALLED === true) {
            $installation_completed = true;

            // Set a message in session
            $_SESSION['installation_message'] = 'Installation has already been completed. Redirecting to login page...';

            // Redirect to index.php after a short delay
            header('Refresh: 3; URL=index.php');

            // Display a message
            echo '<!DOCTYPE html>
            <html lang="en">
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>Installation Already Completed</title>
                <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
                <style>
                    :root {
                        --primary-color: #f1ca2f;
                        --secondary-color: #2c3e50;
                        --success-color: #28a745;
                        --danger-color: #dc3545;
                        --warning-color: #ffc107;
                        --info-color: #17a2b8;
                        --light-color: #f8f9fa;
                        --dark-color: #343a40;
                        --border-color: #dee2e6;
                        --text-color: #333;
                        --text-light: #6c757d;
                    }
                    body {
                        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
                        line-height: 1.6;
                        color: var(--text-color);
                        background-color: #f8f9fa;
                        margin: 0;
                        padding: 0;
                        display: flex;
                        justify-content: center;
                        align-items: center;
                        min-height: 100vh;
                    }
                    .message-container {
                        max-width: 600px;
                        width: 100%;
                        padding: 30px;
                        background-color: #fff;
                        border-radius: 8px;
                        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
                        text-align: center;
                    }
                    .icon {
                        font-size: 48px;
                        color: var(--success-color);
                        margin-bottom: 20px;
                    }
                    h1 {
                        color: var(--secondary-color);
                        margin-bottom: 20px;
                    }
                    p {
                        margin-bottom: 20px;
                        font-size: 16px;
                    }
                    .redirect-link {
                        display: inline-block;
                        background-color: var(--primary-color);
                        color: var(--secondary-color);
                        padding: 10px 20px;
                        border-radius: 4px;
                        text-decoration: none;
                        font-weight: bold;
                        transition: background-color 0.3s;
                    }
                    .redirect-link:hover {
                        background-color: #e0b929;
                    }
                    .loader {
                        margin: 20px auto;
                        border: 4px solid #f3f3f3;
                        border-top: 4px solid var(--primary-color);
                        border-radius: 50%;
                        width: 30px;
                        height: 30px;
                        animation: spin 2s linear infinite;
                    }
                    @keyframes spin {
                        0% { transform: rotate(0deg); }
                        100% { transform: rotate(360deg); }
                    }
                </style>
            </head>
            <body>
                <div class="message-container">
                    <div class="icon"><i class="fas fa-check-circle"></i></div>
                    <h1>Installation Already Completed</h1>
                    <p>The system has already been installed and configured. You will be redirected to the login page in a few seconds.</p>
                    <div class="loader"></div>
                    <p>If you are not redirected automatically, please click the button below:</p>
                    <a href="index.php" class="redirect-link">Go to Login Page</a>
                </div>
            </body>
            </html>';

            // End the script
            exit;
        }
    }
} catch (Exception $e) {
    // If there's an error reading the config file, continue with installation
    debug_log("Error checking installation status: " . $e->getMessage());
}

// Generate CSRF token if not exists
if (empty($_SESSION['csrf_token'])) {
    $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
}

// Set step
$step = isset($_GET['step']) ? (int)$_GET['step'] : 1;
$error = '';
$success = '';

// Total steps: 1=Database, 2=Tables, 3=Site Config, 4=Admin User, 5=Generate Config, 6=Complete

// Debug function
function debug_log($message) {
    error_log(print_r($message, true));
}

// Validate CSRF token (fallback if not defined elsewhere)
if (!function_exists('validate_csrf_token')) {
    function validate_csrf_token($token) {
        return isset($_SESSION['csrf_token']) && hash_equals($_SESSION['csrf_token'], $token);
    }
}

// Sanitize input (fallback if not defined elsewhere)
if (!function_exists('sanitize_input')) {
    function sanitize_input($input) {
        if (is_array($input)) {
            foreach ($input as $key => $value) {
                $input[$key] = sanitize_input($value);
            }
            return $input;
        }

        if (is_string($input)) {
            return htmlspecialchars(trim($input), ENT_QUOTES, 'UTF-8');
        }

        return $input;
    }
}

// Simple redirect function (fallback if helpers.php is not available)
if (!function_exists('redirect')) {
    function redirect($url, $params = []) {
        // Validate URL to prevent header injection
        if (!filter_var($url, FILTER_VALIDATE_URL) && strpos($url, '/') !== 0) {
            $url = 'index.php'; // Default to index if invalid URL
        }

        // Add query parameters if provided
        if (!empty($params) && is_array($params)) {
            $separator = (strpos($url, '?') !== false) ? '&' : '?';
            $queryString = http_build_query($params);
            $url .= $separator . $queryString;
        }

        header('Location: ' . $url);
        exit;
    }
}

/**
 * Sanitize input data to prevent SQL injection
 *
 * @param mixed $input Input to sanitize
 * @return mixed Sanitized input
 */
function sanitize($input) {
    global $conn;

    // Handle arrays recursively
    if (is_array($input)) {
        foreach ($input as $key => $value) {
            $input[$key] = sanitize($value);
        }
        return $input;
    }

    // Handle strings
    if (is_string($input)) {
        if ($conn) {
            return $conn->real_escape_string(trim($input));
        }
        return trim($input);
    }

    // Return as is for other types
    return $input;
}

/**
 * Sanitize output to prevent XSS
 *
 * @param string $output String to sanitize
 * @return string Sanitized output
 */
function html_escape($output) {
    return htmlspecialchars($output, ENT_QUOTES, 'UTF-8');
}

/**
 * Include helpers.php if it exists and is not already included
 */
$helpers_file = __DIR__ . '/includes/helpers.php';
if (file_exists($helpers_file)) {
    // Check if any of the main helper functions exist to avoid redeclaration
    if (!function_exists('safe_redirect') && !function_exists('validate_csrf_token')) {
        require_once $helpers_file;
    }
}

/**
 * Log database errors
 *
 * @param string $message Error message
 * @return string Generic error message for display
 */
if (!function_exists('db_error')) {
    function db_error($message) {
        error_log('[DB Error] ' . $message);
        return 'Database error occurred. Please check the error log for details.';
    }
}

/**
 * Check if user is logged in
 *
 * @return bool True if logged in, false otherwise
 */
if (!function_exists('isLoggedIn')) {
    function isLoggedIn() {
        return isset($_SESSION['user_id']) && !empty($_SESSION['user_id']);
    }
}

/**
 * Validate password strength
 *
 * @param string $password Password to validate
 * @return array Result with status and message
 */
if (!function_exists('validate_password')) {
    function validate_password($password) {
    $result = [
        'valid' => true,
        'message' => ''
    ];

    if (strlen($password) < PASSWORD_MIN_LENGTH) {
        $result['valid'] = false;
        $result['message'] = 'Password must be at least ' . PASSWORD_MIN_LENGTH . ' characters long.';
        return $result;
    }

    if (PASSWORD_REQUIRES_MIXED_CASE && !preg_match('/[a-z]/', $password) || !preg_match('/[A-Z]/', $password)) {
        $result['valid'] = false;
        $result['message'] = 'Password must include both uppercase and lowercase letters.';
        return $result;
    }

    if (PASSWORD_REQUIRES_NUMBERS && !preg_match('/[0-9]/', $password)) {
        $result['valid'] = false;
        $result['message'] = 'Password must include at least one number.';
        return $result;
    }

    if (PASSWORD_REQUIRES_SYMBOLS && !preg_match('/[^A-Za-z0-9]/', $password)) {
        $result['valid'] = false;
        $result['message'] = 'Password must include at least one special character.';
        return $result;
    }

    return $result;
    }
}

/**
 * Initialize secure session settings if not already set
 */
if (!function_exists('init_secure_session')) {
    function init_secure_session() {
    if (session_status() === PHP_SESSION_NONE) {
        ini_set('session.cookie_httponly', 1);
        ini_set('session.use_only_cookies', 1);
        ini_set('session.cookie_secure', isset($_SERVER['HTTPS']) ? 1 : 0);
        ini_set('session.cookie_samesite', 'Strict');
        ini_set('session.gc_maxlifetime', SESSION_TIMEOUT);
        session_start();
    }

    // Regenerate session ID periodically for security
    if (!isset($_SESSION['last_regeneration'])) {
        $_SESSION['last_regeneration'] = time();
    } else if (time() - $_SESSION['last_regeneration'] > 300) { // Every 5 minutes
        session_regenerate_id(true);
        $_SESSION['last_regeneration'] = time();
    }
    }
}

/**
 * Prepare and execute a SQL query safely
 *
 * @param string $sql SQL query with placeholders
 * @param array $params Parameters to bind
 * @param string $types Types of parameters (s: string, i: integer, d: double, b: blob)
 * @return mixed Result object or false on failure
 */
if (!function_exists('db_query')) {
    function db_query($sql, $params = [], $types = '') {
    global $conn;

    if (!$conn) {
        error_log('Database connection not available');
        return false;
    }

    $stmt = $conn->prepare($sql);

    if (!$stmt) {
        error_log('Failed to prepare statement: ' . $conn->error);
        return false;
    }

    if (!empty($params)) {
        if (empty($types)) {
            // Auto-detect types if not provided
            $types = '';
            foreach ($params as $param) {
                if (is_int($param)) {
                    $types .= 'i';
                } elseif (is_float($param)) {
                    $types .= 'd';
                } elseif (is_string($param)) {
                    $types .= 's';
                } else {
                    $types .= 'b';
                }
            }
        }

        $stmt->bind_param($types, ...$params);
    }

    $stmt->execute();
    $result = $stmt->get_result();

    if (!$result && $stmt->errno) {
        error_log('Query execution failed: ' . $stmt->error);
    }

    return $result;
    }
}

/**
 * Set security headers to prevent common attacks
 */
if (!function_exists('set_security_headers')) {
    function set_security_headers() {
    // X-XSS-Protection
    if (XSS_PROTECTION) {
        header('X-XSS-Protection: 1; mode=block');
    }

    // Content-Security-Policy
    if (CONTENT_SECURITY_POLICY) {
        header("Content-Security-Policy: default-src 'self'; script-src 'self' 'unsafe-inline' https://cdnjs.cloudflare.com; style-src 'self' 'unsafe-inline' https://cdnjs.cloudflare.com https://fonts.googleapis.com; img-src 'self' data:; font-src 'self' https://cdnjs.cloudflare.com https://fonts.gstatic.com; connect-src 'self'");
    }

    // X-Content-Type-Options
    header('X-Content-Type-Options: nosniff');

    // X-Frame-Options
    header('X-Frame-Options: SAMEORIGIN');

    // Referrer-Policy
    header('Referrer-Policy: strict-origin-when-cross-origin');
    }
}

// Initialize secure session if not already started
if (session_status() === PHP_SESSION_NONE) {
    init_secure_session();
}

/**
 * Perform comprehensive rollback for step 3 (site configuration)
 *
 * @param array $rollback_data Data needed for rollback
 * @return bool Success status
 */
function perform_step3_rollback($rollback_data) {
    $success = true;

    try {
        // Rollback uploaded files
        if (!empty($rollback_data['uploaded_files'])) {
            foreach ($rollback_data['uploaded_files'] as $file_path) {
                if (file_exists($file_path)) {
                    if (is_file($file_path)) {
                        if (unlink($file_path)) {
                            debug_log("Rollback: Deleted uploaded file: $file_path");
                        } else {
                            debug_log("Rollback: Failed to delete file: $file_path");
                            $success = false;
                        }
                    } elseif (is_dir($file_path)) {
                        // Only remove directory if it's empty
                        if (count(scandir($file_path)) == 2) { // Only . and ..
                            if (rmdir($file_path)) {
                                debug_log("Rollback: Removed empty directory: $file_path");
                            } else {
                                debug_log("Rollback: Failed to remove directory: $file_path");
                                $success = false;
                            }
                        }
                    }
                }
            }
        }

        // Rollback database changes
        if (!empty($rollback_data['database_changes']) && !empty($_SESSION['db_host'])) {
            try {
                $mysqli = @new mysqli($_SESSION['db_host'], $_SESSION['db_user'], $_SESSION['db_pass'], $_SESSION['db_name']);

                if (!$mysqli->connect_error) {
                    $mysqli->set_charset("utf8mb4");

                    // Process rollback in reverse order
                    $changes = array_reverse($rollback_data['database_changes']);

                    foreach ($changes as $change) {
                        if ($change['action'] === 'insert') {
                            // Delete inserted records
                            if ($change['table'] === 'settings') {
                                $delete_stmt = $mysqli->prepare("DELETE FROM settings WHERE id = ?");
                                $delete_stmt->bind_param("i", $change['id']);
                                if ($delete_stmt->execute()) {
                                    debug_log("Rollback: Deleted inserted setting: {$change['key']}");
                                } else {
                                    debug_log("Rollback: Failed to delete setting: {$change['key']}");
                                    $success = false;
                                }
                            } elseif ($change['table'] === 'system_settings') {
                                $delete_stmt = $mysqli->prepare("DELETE FROM system_settings WHERE id = ?");
                                $delete_stmt->bind_param("i", $change['id']);
                                if ($delete_stmt->execute()) {
                                    debug_log("Rollback: Deleted inserted system setting: {$change['category']}.{$change['key']}");
                                } else {
                                    debug_log("Rollback: Failed to delete system setting: {$change['category']}.{$change['key']}");
                                    $success = false;
                                }
                            }
                        } elseif ($change['action'] === 'update') {
                            // Restore original values
                            if ($change['table'] === 'settings') {
                                $restore_stmt = $mysqli->prepare("UPDATE settings SET `value` = ? WHERE `key` = ?");
                                $restore_stmt->bind_param("ss", $change['old_value'], $change['key']);
                                if ($restore_stmt->execute()) {
                                    debug_log("Rollback: Restored setting: {$change['key']}");
                                } else {
                                    debug_log("Rollback: Failed to restore setting: {$change['key']}");
                                    $success = false;
                                }
                            } elseif ($change['table'] === 'system_settings') {
                                $restore_stmt = $mysqli->prepare("UPDATE system_settings SET setting_value = ? WHERE category = ? AND setting_key = ?");
                                $restore_stmt->bind_param("sss", $change['old_value'], $change['category'], $change['key']);
                                if ($restore_stmt->execute()) {
                                    debug_log("Rollback: Restored system setting: {$change['category']}.{$change['key']}");
                                } else {
                                    debug_log("Rollback: Failed to restore system setting: {$change['category']}.{$change['key']}");
                                    $success = false;
                                }
                            }
                        }
                    }

                    $mysqli->close();
                } else {
                    debug_log("Rollback: Could not connect to database for rollback");
                    $success = false;
                }
            } catch (Exception $e) {
                debug_log("Rollback: Database rollback error: " . $e->getMessage());
                $success = false;
            }
        }

    } catch (Exception $e) {
        debug_log("Rollback: General rollback error: " . $e->getMessage());
        $success = false;
    }

    return $success;
}

// Debug: Check if we're processing any form
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    debug_log("POST request received");
    debug_log("POST keys: " . implode(', ', array_keys($_POST)));
    debug_log("setup_db isset: " . (isset($_POST['setup_db']) ? 'YES' : 'NO'));
}

// Process database setup
if ($_SERVER['REQUEST_METHOD'] === 'POST' && (isset($_POST['setup_db']) || (isset($_POST['step_action']) && $_POST['step_action'] === 'setup_db'))) {
    debug_log("Database setup form submitted");
    debug_log("POST data: " . print_r($_POST, true));

    // Get form data
    $db_host = trim($_POST['db_host']);
    $db_user = trim($_POST['db_user']);
    $db_pass = $_POST['db_pass'];
    $db_name = trim($_POST['db_name']);

    debug_log("Database credentials - Host: $db_host, User: $db_user, Database: $db_name");

    try {
        // Simple connection test first
        debug_log("Attempting simple database connection");
        $mysqli = @new mysqli($db_host, $db_user, $db_pass, $db_name);

        if ($mysqli->connect_error) {
            debug_log("Initial connection failed: " . $mysqli->connect_error);

            // Try alternative host if localhost/127.0.0.1
            if ($db_host === 'localhost') {
                debug_log("Trying 127.0.0.1 instead of localhost");
                $mysqli = @new mysqli('127.0.0.1', $db_user, $db_pass, $db_name);
                if (!$mysqli->connect_error) {
                    $db_host = '127.0.0.1';
                    debug_log("Connection successful with 127.0.0.1");
                }
            } elseif ($db_host === '127.0.0.1') {
                debug_log("Trying localhost instead of 127.0.0.1");
                $mysqli = @new mysqli('localhost', $db_user, $db_pass, $db_name);
                if (!$mysqli->connect_error) {
                    $db_host = 'localhost';
                    debug_log("Connection successful with localhost");
                }
            }
        } else {
            debug_log("Initial connection successful");
        }

        if ($mysqli->connect_error) {
            debug_log("Final connection error: " . $mysqli->connect_error);

            // Provide user-friendly error messages
            $error_message = $mysqli->connect_error;

            if (strpos($error_message, "Access denied") !== false) {
                throw new Exception("Access denied. Please check your database username and password.");
            } elseif (strpos($error_message, "Unknown database") !== false) {
                throw new Exception("Database '$db_name' does not exist. Please create it first.");
            } elseif (strpos($error_message, "Connection refused") !== false) {
                throw new Exception("Connection refused. Please check your database host and port.");
            } else {
                throw new Exception("Database connection failed: " . $error_message);
            }
        }

        debug_log("Database connection successful!");

        // Test the connection with a simple query
        $test_result = $mysqli->query("SELECT 1");
        if (!$test_result) {
            throw new Exception("Database connection test failed: " . $mysqli->error);
        }

        debug_log("Database connection test passed");

        // Store database info in session
        $_SESSION['db_host'] = $db_host;
        $_SESSION['db_user'] = $db_user;
        $_SESSION['db_pass'] = $db_pass;
        $_SESSION['db_name'] = $db_name;
        $_SESSION['db_setup'] = true;

        // Close the connection
        $mysqli->close();

        $success = "Database connection successful!";
        $step = 2;

        debug_log("Database setup completed successfully, moving to step 2");

    } catch (Exception $e) {
        $error = "Database setup failed: " . $e->getMessage();
        debug_log("Database setup exception: " . $e->getMessage());
        $step = 1;
    }
}

// Process tables setup
if ($_SERVER['REQUEST_METHOD'] === 'POST' && (isset($_POST['setup_tables']) || (isset($_POST['step_action']) && $_POST['step_action'] === 'setup_tables'))) {
    if (!isset($_SESSION['db_setup'])) {
        $error = "Please complete the database setup first.";
        $step = 1;
    } else {
        try {
            // Get database credentials from session
            $db_host = $_SESSION['db_host'];
            $db_user = $_SESSION['db_user'];
            $db_pass = $_SESSION['db_pass'];
            $db_name = $_SESSION['db_name'];

            // Connect to the database
            $mysqli = @new mysqli($db_host, $db_user, $db_pass, $db_name);

            if ($mysqli->connect_error) {
                throw new Exception("Connection failed: " . $mysqli->connect_error);
            }

            debug_log("Connected to database for table creation");

            // Set charset
            $mysqli->set_charset("utf8mb4");

            // Read and execute SQL file
            $sql_file = 'database/database.sql';
            if (!file_exists($sql_file)) {
                throw new Exception("Database SQL file not found: $sql_file");
            }

            $sql_content = file_get_contents($sql_file);
            if ($sql_content === false) {
                throw new Exception("Could not read SQL file: $sql_file");
            }

            debug_log("Read SQL file successfully");
            debug_log("SQL file size: " . strlen($sql_content) . " bytes");

            // Remove comments and clean up the SQL
            $lines = explode("\n", $sql_content);
            $cleaned_lines = [];

            foreach ($lines as $line) {
                $line = trim($line);
                // Skip empty lines and comment lines
                if (empty($line) || substr($line, 0, 2) === '--') {
                    continue;
                }
                $cleaned_lines[] = $line;
            }

            $cleaned_sql = implode("\n", $cleaned_lines);
            debug_log("Cleaned SQL size: " . strlen($cleaned_sql) . " bytes");

            // Split SQL into individual statements by semicolon
            $statements = explode(';', $cleaned_sql);

            $executed_count = 0;
            $failed_count = 0;

            foreach ($statements as $statement) {
                $statement = trim($statement);

                // Skip empty statements
                if (empty($statement)) {
                    continue;
                }

                debug_log("Executing SQL: " . substr($statement, 0, 100) . "...");

                if ($mysqli->query($statement)) {
                    $executed_count++;
                    debug_log("SQL executed successfully");
                } else {
                    $failed_count++;
                    debug_log("SQL execution failed: " . $mysqli->error);

                    // Check if it's a "table already exists" or "duplicate entry" error
                    if (strpos($mysqli->error, "already exists") === false &&
                        strpos($mysqli->error, "Duplicate entry") === false) {
                        throw new Exception("SQL execution failed: " . $mysqli->error . "<br>Statement: " . htmlspecialchars(substr($statement, 0, 200)));
                    } else {
                        if (strpos($mysqli->error, "already exists") !== false) {
                            debug_log("Table already exists, continuing...");
                        } else {
                            debug_log("Duplicate entry found, continuing...");
                        }
                        $executed_count++; // Count as successful since table exists or data already exists
                        $failed_count--; // Don't count as failed
                    }
                }
            }

            debug_log("SQL execution completed. Executed: $executed_count, Failed: $failed_count");

            // Run database repair to ensure all tables and columns exist
            debug_log("Running database repair to ensure all required tables exist");

            // Create a temporary connection for the repair script
            $repair_conn = @new mysqli($db_host, $db_user, $db_pass, $db_name);
            if (!$repair_conn->connect_error) {
                $repair_conn->set_charset("utf8mb4");

                // Database repair operations are now handled directly in database.sql
                debug_log("Database repair operations completed via database.sql");

                $repair_conn->close();
                debug_log("Database repair completed");
            }

            // Close the connection
            $mysqli->close();

            $success = "Database tables created successfully! ($executed_count statements executed)";
            $step = 3;
            $_SESSION['tables_setup'] = true;

        } catch (Exception $e) {
            $error = "Tables setup failed: " . $e->getMessage();
            debug_log("Tables setup exception: " . $e->getMessage());
            $step = 2;
        }
    }
}

// Process site configuration
if ($_SERVER['REQUEST_METHOD'] === 'POST' && (isset($_POST['configure_site']) || (isset($_POST['step_action']) && $_POST['step_action'] === 'configure_site'))) {
    if (!isset($_SESSION['tables_setup'])) {
        $error = "Please complete the database and tables setup first.";
        $step = 2;
    } else {
        // Initialize rollback tracking
        $rollback_data = [
            'uploaded_files' => [],
            'database_changes' => [],
            'original_settings' => []
        ];

        try {
            // Validate CSRF token
            if (!validate_csrf_token($_POST['csrf_token'])) {
                throw new Exception("Invalid CSRF token");
            }

            // Get form data
            $site_name = sanitize_input($_POST['site_name']);
            $site_description = sanitize_input($_POST['site_description']);
            $from_email = sanitize_input($_POST['from_email']);
            $from_name = sanitize_input($_POST['from_name']);

            // Validate input
            if (empty($site_name) || empty($site_description) || empty($from_email) || empty($from_name)) {
                throw new Exception("All fields are required");
            }

            // Validate email
            if (!filter_var($from_email, FILTER_VALIDATE_EMAIL)) {
                throw new Exception("Invalid email address");
            }

            // Handle admin logo upload with enhanced validation and rollback tracking
            $admin_logo_path = '';
            if (isset($_FILES['admin_logo']) && $_FILES['admin_logo']['error'] === UPLOAD_ERR_OK) {
                // Validate file upload
                $file_info = pathinfo($_FILES['admin_logo']['name']);
                $allowed_extensions = ['jpg', 'jpeg', 'png', 'gif', 'webp'];
                $max_file_size = 5242880; // 5MB

                // Check file extension
                if (!isset($file_info['extension']) || !in_array(strtolower($file_info['extension']), $allowed_extensions)) {
                    throw new Exception("Invalid file type. Only JPG, JPEG, PNG, GIF, and WebP files are allowed.");
                }

                // Check file size
                if ($_FILES['admin_logo']['size'] > $max_file_size) {
                    throw new Exception("File size too large. Maximum size is 5MB.");
                }

                // Check if it's actually an image
                $image_info = getimagesize($_FILES['admin_logo']['tmp_name']);
                if ($image_info === false) {
                    throw new Exception("Invalid image file. Please upload a valid image.");
                }

                // Check image dimensions (reasonable limits)
                if ($image_info[0] > 2000 || $image_info[1] > 2000) {
                    throw new Exception("Image dimensions too large. Maximum size is 2000x2000 pixels.");
                }

                // Create uploads directory structure
                $upload_base_dir = '../uploads/';
                $upload_dir = $upload_base_dir . 'admin/';

                if (!is_dir($upload_base_dir)) {
                    if (!mkdir($upload_base_dir, 0755, true)) {
                        throw new Exception("Failed to create uploads directory.");
                    }
                    $rollback_data['uploaded_files'][] = $upload_base_dir;
                }

                if (!is_dir($upload_dir)) {
                    if (!mkdir($upload_dir, 0755, true)) {
                        throw new Exception("Failed to create admin uploads directory.");
                    }
                    $rollback_data['uploaded_files'][] = $upload_dir;
                }

                // Generate unique filename with timestamp
                $timestamp = date('Y-m-d_H-i-s');
                $new_filename = 'admin-logo-' . $timestamp . '.' . strtolower($file_info['extension']);
                $upload_path = $upload_dir . $new_filename;
                $relative_path = 'uploads/admin/' . $new_filename; // Path relative to website root

                // Check if file already exists and backup if needed
                if (file_exists($upload_path)) {
                    $backup_name = 'admin-logo-backup-' . $timestamp . '.' . strtolower($file_info['extension']);
                    rename($upload_path, $upload_dir . $backup_name);
                    $rollback_data['uploaded_files'][] = $upload_dir . $backup_name;
                }

                // Move uploaded file
                if (move_uploaded_file($_FILES['admin_logo']['tmp_name'], $upload_path)) {
                    $admin_logo_path = $relative_path;
                    $rollback_data['uploaded_files'][] = $upload_path;
                    debug_log("Admin logo uploaded successfully: " . $admin_logo_path);
                } else {
                    throw new Exception("Failed to upload admin logo. Please check file permissions.");
                }
            } elseif (isset($_FILES['admin_logo']) && $_FILES['admin_logo']['error'] !== UPLOAD_ERR_NO_FILE) {
                // Handle upload errors
                $upload_errors = [
                    UPLOAD_ERR_INI_SIZE => 'File size exceeds server limit.',
                    UPLOAD_ERR_FORM_SIZE => 'File size exceeds form limit.',
                    UPLOAD_ERR_PARTIAL => 'File was only partially uploaded.',
                    UPLOAD_ERR_NO_TMP_DIR => 'Missing temporary folder.',
                    UPLOAD_ERR_CANT_WRITE => 'Failed to write file to disk.',
                    UPLOAD_ERR_EXTENSION => 'File upload stopped by extension.'
                ];

                $error_message = isset($upload_errors[$_FILES['admin_logo']['error']])
                    ? $upload_errors[$_FILES['admin_logo']['error']]
                    : 'Unknown upload error.';

                throw new Exception("Logo upload failed: " . $error_message);
            }

            // Get database credentials from session
            $db_host = $_SESSION['db_host'];
            $db_user = $_SESSION['db_user'];
            $db_pass = $_SESSION['db_pass'];
            $db_name = $_SESSION['db_name'];

            // Connect to the database
            $mysqli = @new mysqli($db_host, $db_user, $db_pass, $db_name);

            if ($mysqli->connect_error) {
                throw new Exception("Database connection failed: " . $mysqli->connect_error);
            }

            debug_log("Connected to database for site configuration");

            // Set charset and enable transactions
            $mysqli->set_charset("utf8mb4");
            $mysqli->autocommit(false); // Start transaction mode

            debug_log("Starting database transaction for site configuration");

            // Backup existing settings for rollback
            $backup_settings_stmt = $mysqli->prepare("SELECT `key`, `value` FROM settings WHERE `key` IN ('site_name', 'site_description', 'from_email', 'from_name')");
            $backup_settings_stmt->execute();
            $backup_result = $backup_settings_stmt->get_result();
            while ($row = $backup_result->fetch_assoc()) {
                $rollback_data['original_settings'][$row['key']] = $row['value'];
            }

            // Insert/update site settings to main settings table
            $main_settings = [
                'site_name' => $site_name,
                'site_description' => $site_description,
                'from_email' => $from_email,
                'from_name' => $from_name
            ];

            foreach ($main_settings as $key => $value) {
                // Check if setting already exists
                $check_stmt = $mysqli->prepare("SELECT id, `value` FROM settings WHERE `key` = ?");
                $check_stmt->bind_param("s", $key);
                $check_stmt->execute();
                $result = $check_stmt->get_result();

                if ($result->num_rows > 0) {
                    // Store original value for rollback if not already stored
                    $existing_row = $result->fetch_assoc();
                    if (!isset($rollback_data['original_settings'][$key])) {
                        $rollback_data['original_settings'][$key] = $existing_row['value'];
                    }

                    // Update existing setting
                    $update_stmt = $mysqli->prepare("UPDATE settings SET `value` = ?, `updated_at` = NOW() WHERE `key` = ?");
                    $update_stmt->bind_param("ss", $value, $key);

                    if (!$update_stmt->execute()) {
                        throw new Exception("Failed to update setting '$key': " . $update_stmt->error);
                    }
                    $rollback_data['database_changes'][] = ['action' => 'update', 'table' => 'settings', 'key' => $key, 'old_value' => $existing_row['value']];
                    debug_log("Updated existing setting: $key");
                } else {
                    // Insert new setting
                    $description = ucfirst(str_replace('_', ' ', $key));
                    $insert_stmt = $mysqli->prepare("INSERT INTO settings (`key`, `value`, `type`, `description`, `group`, `created_at`, `updated_at`) VALUES (?, ?, 'string', ?, 'general', NOW(), NOW())");
                    $insert_stmt->bind_param("sss", $key, $value, $description);

                    if (!$insert_stmt->execute()) {
                        throw new Exception("Failed to save setting '$key': " . $insert_stmt->error);
                    }
                    $rollback_data['database_changes'][] = ['action' => 'insert', 'table' => 'settings', 'key' => $key, 'id' => $mysqli->insert_id];
                    debug_log("Inserted new setting: $key");
                }
            }

            // Insert/update system_settings for better organization
            $system_settings = [
                ['category' => 'general', 'key' => 'site_name', 'value' => $site_name, 'type' => 'text', 'display_name' => 'Site Name', 'description' => 'The name of your website'],
                ['category' => 'general', 'key' => 'site_description', 'value' => $site_description, 'type' => 'textarea', 'display_name' => 'Site Description', 'description' => 'A brief description of your website'],
                ['category' => 'email', 'key' => 'from_email', 'value' => $from_email, 'type' => 'email', 'display_name' => 'From Email', 'description' => 'Default email address for outgoing emails'],
                ['category' => 'email', 'key' => 'from_name', 'value' => $from_name, 'type' => 'text', 'display_name' => 'From Name', 'description' => 'Default name for outgoing emails']
            ];

            foreach ($system_settings as $setting) {
                // Check if system setting already exists
                $check_sys_stmt = $mysqli->prepare("SELECT id, setting_value FROM system_settings WHERE category = ? AND setting_key = ?");
                $check_sys_stmt->bind_param("ss", $setting['category'], $setting['key']);
                $check_sys_stmt->execute();
                $sys_result = $check_sys_stmt->get_result();

                if ($sys_result->num_rows > 0) {
                    // Update existing system setting
                    $existing_sys_row = $sys_result->fetch_assoc();
                    $update_sys_stmt = $mysqli->prepare("UPDATE system_settings SET setting_value = ?, updated_at = NOW() WHERE category = ? AND setting_key = ?");
                    $update_sys_stmt->bind_param("sss", $setting['value'], $setting['category'], $setting['key']);

                    if (!$update_sys_stmt->execute()) {
                        throw new Exception("Failed to update system setting '{$setting['key']}': " . $update_sys_stmt->error);
                    }
                    $rollback_data['database_changes'][] = ['action' => 'update', 'table' => 'system_settings', 'category' => $setting['category'], 'key' => $setting['key'], 'old_value' => $existing_sys_row['setting_value']];
                    debug_log("Updated existing system setting: {$setting['category']}.{$setting['key']}");
                } else {
                    // Insert new system setting
                    $insert_sys_stmt = $mysqli->prepare("INSERT INTO system_settings (category, setting_key, setting_value, setting_type, display_name, description, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, NOW(), NOW())");
                    $insert_sys_stmt->bind_param("ssssss", $setting['category'], $setting['key'], $setting['value'], $setting['type'], $setting['display_name'], $setting['description']);

                    if (!$insert_sys_stmt->execute()) {
                        throw new Exception("Failed to save system setting '{$setting['key']}': " . $insert_sys_stmt->error);
                    }
                    $rollback_data['database_changes'][] = ['action' => 'insert', 'table' => 'system_settings', 'category' => $setting['category'], 'key' => $setting['key'], 'id' => $mysqli->insert_id];
                    debug_log("Inserted new system setting: {$setting['category']}.{$setting['key']}");
                }
            }

            // Insert admin logo to system_settings table if uploaded
            if (!empty($admin_logo_path)) {
                // Check if admin_logo setting already exists
                $check_logo_stmt = $mysqli->prepare("SELECT id, setting_value FROM system_settings WHERE category = 'appearance' AND setting_key = 'admin_logo'");
                $check_logo_stmt->execute();
                $logo_result = $check_logo_stmt->get_result();

                if ($logo_result->num_rows > 0) {
                    // Update existing admin logo setting
                    $existing_logo_row = $logo_result->fetch_assoc();
                    $update_logo_stmt = $mysqli->prepare("UPDATE system_settings SET setting_value = ?, updated_at = NOW() WHERE category = 'appearance' AND setting_key = 'admin_logo'");
                    $update_logo_stmt->bind_param("s", $admin_logo_path);

                    if (!$update_logo_stmt->execute()) {
                        throw new Exception("Failed to update admin logo setting: " . $update_logo_stmt->error);
                    }
                    $rollback_data['database_changes'][] = ['action' => 'update', 'table' => 'system_settings', 'category' => 'appearance', 'key' => 'admin_logo', 'old_value' => $existing_logo_row['setting_value']];
                    debug_log("Updated existing admin logo setting");
                } else {
                    // Insert new admin logo setting
                    $insert_logo_stmt = $mysqli->prepare("INSERT INTO system_settings (`category`, `setting_key`, `setting_value`, `setting_type`, `display_name`, `description`, `created_at`, `updated_at`) VALUES ('appearance', 'admin_logo', ?, 'file', 'Admin Logo', 'Logo displayed in the admin panel header', NOW(), NOW())");
                    $insert_logo_stmt->bind_param("s", $admin_logo_path);

                    if (!$insert_logo_stmt->execute()) {
                        throw new Exception("Failed to save admin logo setting: " . $insert_logo_stmt->error);
                    }
                    $rollback_data['database_changes'][] = ['action' => 'insert', 'table' => 'system_settings', 'category' => 'appearance', 'key' => 'admin_logo', 'id' => $mysqli->insert_id];
                    debug_log("Inserted new admin logo setting");
                }
            }

            // Commit all database changes
            $mysqli->commit();
            debug_log("Database transaction committed successfully");

            // Store site info in session
            $_SESSION['site_name'] = $site_name;
            $_SESSION['site_description'] = $site_description;
            $_SESSION['from_email'] = $from_email;
            $_SESSION['from_name'] = $from_name;
            if (!empty($admin_logo_path)) {
                $_SESSION['admin_logo'] = $admin_logo_path;
            }

            // Close the connection
            $mysqli->close();

            debug_log("Site configuration saved successfully");
            $success = "Site configuration saved successfully!";
            $step = 4;
            $_SESSION['site_setup'] = true;

        } catch (Exception $e) {
            // Rollback database changes
            if (isset($mysqli) && $mysqli->ping()) {
                $mysqli->rollback();
                debug_log("Database transaction rolled back");
            }

            // Perform comprehensive rollback
            $rollback_success = perform_step3_rollback($rollback_data);

            $error = "Site configuration failed: " . $e->getMessage();
            if (!$rollback_success) {
                $error .= " Warning: Some changes could not be rolled back automatically.";
            }

            debug_log("Site configuration exception: " . $e->getMessage());
            debug_log("Rollback performed: " . ($rollback_success ? 'Success' : 'Partial'));

            // Close database connection if still open
            if (isset($mysqli) && $mysqli->ping()) {
                $mysqli->close();
            }

            $step = 3;
        }
    }
}

// Process admin user creation
if ($_SERVER['REQUEST_METHOD'] === 'POST' && (isset($_POST['create_admin']) || (isset($_POST['step_action']) && $_POST['step_action'] === 'create_admin'))) {
    if (!isset($_SESSION['site_setup'])) {
        $error = "Please complete the site configuration first.";
        $step = 3;
    } else {
        try {
            // Validate CSRF token
            if (!validate_csrf_token($_POST['csrf_token'])) {
                throw new Exception("Invalid CSRF token");
            }

            // Get form data
            $admin_username = sanitize_input($_POST['admin_username']);
            $admin_email = sanitize_input($_POST['admin_email']);
            $admin_password = $_POST['admin_password'];
            $admin_password_confirm = $_POST['admin_password_confirm'];

            // Validate input
            if (empty($admin_username) || empty($admin_email) || empty($admin_password)) {
                throw new Exception("All fields are required");
            }

            if ($admin_password !== $admin_password_confirm) {
                throw new Exception("Passwords do not match");
            }

            // Validate email
            if (!filter_var($admin_email, FILTER_VALIDATE_EMAIL)) {
                throw new Exception("Invalid email address");
            }

            // Validate password strength
            $password_validation = validate_password($admin_password);
            if (!$password_validation['valid']) {
                throw new Exception($password_validation['message']);
            }

            // Get database credentials from session
            $db_host = $_SESSION['db_host'];
            $db_user = $_SESSION['db_user'];
            $db_pass = $_SESSION['db_pass'];
            $db_name = $_SESSION['db_name'];

            // Connect to the database
            $mysqli = @new mysqli($db_host, $db_user, $db_pass, $db_name);

            if ($mysqli->connect_error) {
                throw new Exception("Connection failed: " . $mysqli->connect_error);
            }

            debug_log("Connected to database for admin user creation");

            // Set charset
            $mysqli->set_charset("utf8mb4");

            // Check if admin user already exists
            $check_stmt = $mysqli->prepare("SELECT id FROM users WHERE username = ? OR email = ?");
            $check_stmt->bind_param("ss", $admin_username, $admin_email);
            $check_stmt->execute();
            $result = $check_stmt->get_result();

            if ($result->num_rows > 0) {
                throw new Exception("Admin user with this username or email already exists");
            }

            // Hash the password
            $hashed_password = password_hash($admin_password, PASSWORD_DEFAULT);

            // Insert admin user with is_admin = 1
            $insert_stmt = $mysqli->prepare("INSERT INTO users (username, email, password, role, status, is_admin, created_at) VALUES (?, ?, ?, 'admin', 'active', 1, NOW())");
            $insert_stmt->bind_param("sss", $admin_username, $admin_email, $hashed_password);

            if (!$insert_stmt->execute()) {
                throw new Exception("Failed to create admin user: " . $insert_stmt->error);
            }

            $admin_id = $mysqli->insert_id;
            debug_log("Admin user created with ID: $admin_id");

            // Assign Administrator role to the admin user
            $role_stmt = $mysqli->prepare("INSERT IGNORE INTO user_roles (user_id, role_id, assigned_by) SELECT ?, r.id, ? FROM roles r WHERE r.name = 'Administrator'");
            $role_stmt->bind_param("ii", $admin_id, $admin_id);

            if (!$role_stmt->execute()) {
                debug_log("Warning: Failed to assign Administrator role: " . $role_stmt->error);
                // Don't throw exception as user creation was successful
            } else {
                debug_log("Administrator role assigned to admin user");
            }

            // Send welcome email to admin
            try {
                $subject = "Welcome to " . $_SESSION['site_name'] . " - Admin Account Created";
                $message = "Dear " . $admin_username . ",\n\n";
                $message .= "Your administrator account has been successfully created for " . $_SESSION['site_name'] . ".\n\n";
                $message .= "Login Details:\n";
                $message .= "Username: " . $admin_username . "\n";
                $message .= "Email: " . $admin_email . "\n\n";
                $message .= "You can now log in to the admin panel and start managing your website.\n\n";
                $message .= "Best regards,\n";
                $message .= $_SESSION['site_name'] . " Installation System";

                $headers = "From: " . $_SESSION['from_name'] . " <" . $_SESSION['from_email'] . ">\r\n";
                $headers .= "Reply-To: " . $_SESSION['from_email'] . "\r\n";
                $headers .= "X-Mailer: PHP/" . phpversion();

                if (mail($admin_email, $subject, $message, $headers)) {
                    debug_log("Welcome email sent to admin: $admin_email");
                } else {
                    debug_log("Failed to send welcome email to admin: $admin_email");
                }
            } catch (Exception $email_error) {
                debug_log("Email sending error: " . $email_error->getMessage());
                // Don't fail installation if email fails
            }

            // Store admin info in session
            $_SESSION['admin_username'] = $admin_username;
            $_SESSION['admin_email'] = $admin_email;

            // Close the connection
            $mysqli->close();

            $success = "Admin user created successfully!";
            $step = 5;
            $_SESSION['admin_setup'] = true;

        } catch (Exception $e) {
            $error = "Admin user creation failed: " . $e->getMessage();
            debug_log("Admin user creation exception: " . $e->getMessage());
            $step = 4;
        }
    }
}

// Process configuration file generation
if ($_SERVER['REQUEST_METHOD'] === 'POST' && (isset($_POST['generate_config']) || (isset($_POST['step_action']) && $_POST['step_action'] === 'generate_config'))) {
    if (!isset($_SESSION['admin_setup'])) {
        $error = "Please complete all previous steps first.";
        $step = 4;
    } else {
        try {
            // Get database credentials from session
            $db_host = $_SESSION['db_host'];
            $db_user = $_SESSION['db_user'];
            $db_pass = $_SESSION['db_pass'];
            $db_name = $_SESSION['db_name'];

            // Generate a secure secret key
            $secret_key = bin2hex(random_bytes(32));

            // Create config file content
            $config_content = '<?php
// Database Configuration
define(\'DB_HOST\', \'' . addslashes($db_host) . '\');
define(\'DB_USER\', \'' . addslashes($db_user) . '\');
define(\'DB_PASS\', \'' . addslashes($db_pass) . '\');
define(\'DB_NAME\', \'' . addslashes($db_name) . '\');

// Installation Status
define(\'DB_INSTALLED\', true);

// Security Configuration
define(\'SECRET_KEY\', \'' . $secret_key . '\');
define(\'CSRF_PROTECTION\', true);
define(\'CSRF_TOKEN_EXPIRY\', 3600); // 1 hour
define(\'SESSION_TIMEOUT\', 3600); // 1 hour
define(\'MAX_LOGIN_ATTEMPTS\', 5);
define(\'LOGIN_LOCKOUT_TIME\', 900); // 15 minutes
define(\'LOGIN_LOCKOUT_DURATION\', 900); // 15 minutes

// Password Requirements
define(\'PASSWORD_MIN_LENGTH\', 8);
define(\'PASSWORD_REQUIRES_MIXED_CASE\', true);
define(\'PASSWORD_REQUIRES_NUMBERS\', true);
define(\'PASSWORD_REQUIRES_SYMBOLS\', true);

// Security Headers
define(\'XSS_PROTECTION\', true);
define(\'CONTENT_SECURITY_POLICY\', true);

// File Upload Configuration
define(\'MAX_FILE_SIZE\', 10485760); // 10MB
define(\'ALLOWED_FILE_TYPES\', array(\'jpg\', \'jpeg\', \'png\', \'gif\', \'pdf\', \'doc\', \'docx\', \'txt\'));

// Email Configuration (to be configured later)
define(\'SMTP_HOST\', \'\');
define(\'SMTP_PORT\', 587);
define(\'SMTP_USERNAME\', \'\');
define(\'SMTP_PASSWORD\', \'\');
define(\'SMTP_ENCRYPTION\', \'tls\');
define(\'FROM_EMAIL\', \'' . addslashes($_SESSION['from_email']) . '\');
define(\'FROM_NAME\', \'' . addslashes($_SESSION['from_name']) . '\');

// Site Configuration
define(\'SITE_NAME\', \'' . addslashes($_SESSION['site_name']) . '\');
define(\'SITE_DESCRIPTION\', \'' . addslashes($_SESSION['site_description']) . '\');
define(\'SITE_URL\', \'http\' . (isset($_SERVER[\'HTTPS\']) ? \'s\' : \'\') . \'://\' . $_SERVER[\'HTTP_HOST\'] . dirname($_SERVER[\'SCRIPT_NAME\']));
define(\'ADMIN_EMAIL\', \'' . addslashes($_SESSION['admin_email']) . '\');

// Timezone
date_default_timezone_set(\'UTC\');

// Error Reporting (disable in production)
ini_set(\'display_errors\', 0);
ini_set(\'log_errors\', 1);
error_reporting(E_ALL & ~E_NOTICE & ~E_DEPRECATED);

// Database Connection with retry mechanism
$conn = null;
$max_retries = 3;
$retry_count = 0;
$connection_error = \'\';

// Skip database connection if we\'re in the installation process
if (!defined(\'INSTALLING\') || !INSTALLING) {
    while ($retry_count < $max_retries && $conn === null) {
        try {
            // Create connection - with error suppression to handle it gracefully
            $conn = @new mysqli(DB_HOST, DB_USER, DB_PASS, DB_NAME);

            // Check connection
            if ($conn->connect_error) {
                throw new Exception(\'Connection failed: \' . $conn->connect_error);
            }

            // Set charset
            $conn->set_charset(\'utf8mb4\');

        } catch (Exception $e) {
            $connection_error = $e->getMessage();
            $retry_count++;

            if ($retry_count >= $max_retries) {
                error_log(\'Database connection failed after \' . $max_retries . \' attempts: \' . $connection_error);

                // Don\'t exit immediately, allow the page to load with an error message
                // This prevents blank pages when database connection fails
                $conn = null;
                $db_error = true;
                $db_error_message = $connection_error;

                // Only show error page if not on index.php or login.php
                $current_script = basename($_SERVER[\'SCRIPT_NAME\']);
                if ($current_script !== \'index.php\' && $current_script !== \'login.php\') {
                    // Show error page
                    $error_file = __DIR__ . \'/includes/db-error.php\';
                    if (file_exists($error_file)) {
                        include_once $error_file;
                    } else {
                        echo "Database connection failed: " . $connection_error;
                    }
                    exit;
                }
            }

            // Wait before retrying
            sleep(1);
        }
    }
} else {
    // We\'re in the installation process, log this
    error_log(\'Skipping database connection during installation\');
}

/**
 * Include helpers.php if it exists and is not already included
 */
$helpers_file = __DIR__ . \'/includes/helpers.php\';
if (file_exists($helpers_file)) {
    require_once $helpers_file;
}

/**
 * Sanitize input data to prevent SQL injection
 *
 * @param mixed $input Input to sanitize
 * @return mixed Sanitized input
 */
function sanitize($input) {
    global $conn;

    // Handle arrays recursively
    if (is_array($input)) {
        foreach ($input as $key => $value) {
            $input[$key] = sanitize($value);
        }
        return $input;
    }

    // Handle strings
    if (is_string($input)) {
        if ($conn) {
            return $conn->real_escape_string(trim($input));
        }
        return trim($input);
    }

    // Return as is for other types
    return $input;
}

/**
 * Sanitize output to prevent XSS
 *
 * @param string $output String to sanitize
 * @return string Sanitized output
 */
function html_escape($output) {
    return htmlspecialchars($output, ENT_QUOTES, \'UTF-8\');
}

// CSRF functions are now defined in security.php
// We\'ll include that file if the functions don\'t exist

if (!function_exists(\'generate_csrf_token\') || !function_exists(\'validate_csrf_token\')) {
    // Check if security.php exists
    $security_file = __DIR__ . \'/includes/security.php\';
    if (file_exists($security_file)) {
        // Include security.php which defines these functions
        require_once $security_file;
    } else {
        // Define the functions here as a fallback

        /**
         * Generate CSRF token
         *
         * @return string CSRF token
         */
        if (!function_exists(\'generate_csrf_token\')) {
            function generate_csrf_token() {
                // Make sure session is started
                if (session_status() === PHP_SESSION_NONE) {
                    session_start();
                }

                if (empty($_SESSION[\'csrf_token\'])) {
                    $_SESSION[\'csrf_token\'] = bin2hex(random_bytes(32));
                }
                return $_SESSION[\'csrf_token\'];
            }
        }

        /**
         * Validate CSRF token
         *
         * @param string $token Token to validate
         * @return bool True if valid, false otherwise
         */
        if (!function_exists(\'validate_csrf_token\')) {
            function validate_csrf_token($token) {
                // Make sure session is started
                if (session_status() === PHP_SESSION_NONE) {
                    session_start();
                }

                // If CSRF protection is disabled, always return true
                if (!defined(\'CSRF_PROTECTION\') || !CSRF_PROTECTION) {
                    return true;
                }

                // If token is empty, return false
                if (empty($token)) {
                    error_log("CSRF validation failed: Empty token provided");
                    return false;
                }

                // If session token is not set, return false
                if (!isset($_SESSION[\'csrf_token\']) || empty($_SESSION[\'csrf_token\'])) {
                    error_log("CSRF validation failed: No token in session");
                    return false;
                }

                // Compare tokens
                return hash_equals($_SESSION[\'csrf_token\'], $token);
            }
        }
    }
}

/**
 * Log database errors
 *
 * @param string $message Error message
 * @return string Generic error message for display
 */
function db_error($message) {
    error_log(\'[DB Error] \' . $message);
    return \'Database error occurred. Please check the error log for details.\';
}

/**
 * Check if user is logged in
 *
 * @return bool True if logged in, false otherwise
 */
function isLoggedIn() {
    return isset($_SESSION[\'user_id\']) && !empty($_SESSION[\'user_id\']);
}

/**
 * Validate password strength
 *
 * @param string $password Password to validate
 * @return array Result with status and message
 */
function validate_password($password) {
    $result = [
        \'valid\' => true,
        \'message\' => \'\'
    ];

    if (strlen($password) < PASSWORD_MIN_LENGTH) {
        $result[\'valid\'] = false;
        $result[\'message\'] = \'Password must be at least \' . PASSWORD_MIN_LENGTH . \' characters long.\';
        return $result;
    }

    if (PASSWORD_REQUIRES_MIXED_CASE && (!preg_match(\'/[a-z]/\', $password) || !preg_match(\'/[A-Z]/\', $password))) {
        $result[\'valid\'] = false;
        $result[\'message\'] = \'Password must include both uppercase and lowercase letters.\';
        return $result;
    }

    if (PASSWORD_REQUIRES_NUMBERS && !preg_match(\'/[0-9]/\', $password)) {
        $result[\'valid\'] = false;
        $result[\'message\'] = \'Password must include at least one number.\';
        return $result;
    }

    if (PASSWORD_REQUIRES_SYMBOLS && !preg_match(\'/[^A-Za-z0-9]/\', $password)) {
        $result[\'valid\'] = false;
        $result[\'message\'] = \'Password must include at least one special character.\';
        return $result;
    }

    return $result;
}

/**
 * Prepare and execute a SQL query safely
 *
 * @param string $sql SQL query with placeholders
 * @param array $params Parameters to bind
 * @param string $types Types of parameters (s: string, i: integer, d: double, b: blob)
 * @return mixed Result object or false on failure
 */
function db_query($sql, $params = [], $types = \'\') {
    global $conn;

    if (!$conn) {
        error_log(\'Database connection not available\');
        return false;
    }

    $stmt = $conn->prepare($sql);

    if (!$stmt) {
        error_log(\'Failed to prepare statement: \' . $conn->error);
        return false;
    }

    if (!empty($params)) {
        if (empty($types)) {
            // Auto-detect types if not provided
            $types = \'\';
            foreach ($params as $param) {
                if (is_int($param)) {
                    $types .= \'i\';
                } elseif (is_float($param)) {
                    $types .= \'d\';
                } elseif (is_string($param)) {
                    $types .= \'s\';
                } else {
                    $types .= \'b\';
                }
            }
        }

        $stmt->bind_param($types, ...$params);
    }

    $stmt->execute();
    $result = $stmt->get_result();

    if (!$result && $stmt->errno) {
        error_log(\'Query execution failed: \' . $stmt->error);
    }

    return $result;
}

/**
 * Set security headers to prevent common attacks
 */
function set_security_headers() {
    // X-XSS-Protection
    if (defined(\'XSS_PROTECTION\') && XSS_PROTECTION) {
        header(\'X-XSS-Protection: 1; mode=block\');
    }

    // Content-Security-Policy
    if (defined(\'CONTENT_SECURITY_POLICY\') && CONTENT_SECURITY_POLICY) {
        header("Content-Security-Policy: default-src \'self\'; script-src \'self\' \'unsafe-inline\' https://cdnjs.cloudflare.com; style-src \'self\' \'unsafe-inline\' https://cdnjs.cloudflare.com https://fonts.googleapis.com; img-src \'self\' data:; font-src \'self\' https://cdnjs.cloudflare.com https://fonts.gstatic.com; connect-src \'self\'");
    }

    // X-Content-Type-Options
    header(\'X-Content-Type-Options: nosniff\');

    // X-Frame-Options
    header(\'X-Frame-Options: SAMEORIGIN\');

    // Referrer-Policy
    header(\'Referrer-Policy: strict-origin-when-cross-origin\');
}

/**
 * Initialize secure session settings if not already set
 */
function init_secure_session() {
    if (session_status() === PHP_SESSION_NONE) {
        ini_set(\'session.cookie_httponly\', 1);
        ini_set(\'session.use_only_cookies\', 1);
        ini_set(\'session.cookie_secure\', isset($_SERVER[\'HTTPS\']) ? 1 : 0);
        ini_set(\'session.cookie_samesite\', \'Strict\');
        ini_set(\'session.gc_maxlifetime\', SESSION_TIMEOUT);
        session_start();
    }

    // Regenerate session ID periodically for security
    if (!isset($_SESSION[\'last_regeneration\'])) {
        $_SESSION[\'last_regeneration\'] = time();
    } else if (time() - $_SESSION[\'last_regeneration\'] > 300) { // Every 5 minutes
        session_regenerate_id(true);
        $_SESSION[\'last_regeneration\'] = time();
    }
}

// Initialize security headers
set_security_headers();

// Initialize secure session if not already started
if (session_status() === PHP_SESSION_NONE) {
    init_secure_session();
}

// Create db-error.php file if it doesn\'t exist
$error_file_path = __DIR__ . \'/includes/db-error.php\';
if (!file_exists($error_file_path)) {
    $error_page = <<<HTML
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Database Error</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
            text-align: center;
        }
        .error-container {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            border-radius: 4px;
            padding: 20px;
            margin-bottom: 20px;
        }
        h1 {
            color: #721c24;
        }
        p {
            margin-bottom: 15px;
        }
        .btn {
            display: inline-block;
            background-color: #f1ca2f;
            color: #333;
            text-decoration: none;
            padding: 10px 20px;
            border-radius: 4px;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="error-container">
        <h1>Database Connection Error</h1>
        <p>We\'re having trouble connecting to the database. This could be due to:</p>
        <ul style="text-align: left; display: inline-block;">
            <li>Database server is down</li>
            <li>Database credentials are incorrect</li>
            <li>Database name doesn\'t exist</li>
        </ul>
        <p>Please try again later or contact the administrator.</p>
    </div>
    <a href="../index.php" class="btn">Go to Homepage</a>
    <a href="install.php?run_installation=1" class="btn">Run Installation</a>
</body>
</html>
HTML;

    $includes_dir = __DIR__ . \'/includes\';
    if (!is_dir($includes_dir)) {
        mkdir($includes_dir, 0755, true);
    }

    file_put_contents($error_file_path, $error_page);
}
?>';

            // Write config file
            $config_path = 'config.php';
            debug_log("Attempting to write config file to: $config_path");

            // Check if the file exists and is writable
            if (file_exists($config_path)) {
                if (is_writable($config_path)) {
                    debug_log("Config file exists and is writable");
                } else {
                    debug_log("Config file exists but is not writable");
                }
            } else {
                debug_log("Config file does not exist, will attempt to create it");
            }

            // Try to write the file
            if (file_put_contents($config_path, $config_content)) {
                debug_log("Config file created successfully at: $config_path");
            } else {
                debug_log("Failed to create config file at: $config_path");

                // Try writing to a temporary file as a fallback
                $fallback_path = 'config.tmp.php';
                debug_log("Attempting to write to fallback location: $fallback_path");

                if (file_put_contents($fallback_path, $config_content)) {
                    debug_log("Config file created successfully at fallback location: $fallback_path");

                    // Copy the file to the correct location
                    if (copy($fallback_path, $config_path)) {
                        debug_log("Config file copied from fallback to correct location");
                        unlink($fallback_path); // Remove the temporary file
                    } else {
                        debug_log("Failed to copy config file from fallback to correct location");
                    }
                } else {
                    debug_log("Failed to create config file at fallback location");
                }
            }

            $success = "Configuration file generated successfully! Installation completed.";
            $step = 6;
            $_SESSION['config_setup'] = true;

        } catch (Exception $e) {
            $error = "Configuration generation failed: " . $e->getMessage();
            debug_log("Configuration generation exception: " . $e->getMessage());
            $step = 4;
        }
    }
}

// Function to get step title
function get_step_title($step) {
    switch ($step) {
        case 1: return "Database Setup";
        case 2: return "Create Tables";
        case 3: return "Site Configuration";
        case 4: return "Create Admin User";
        case 5: return "Generate Configuration";
        case 6: return "Installation Complete";
        default: return "Installation";
    }
}

// Function to get step description
function get_step_description($step) {
    switch ($step) {
        case 1: return "Configure your database connection settings";
        case 2: return "Create the required database tables";
        case 3: return "Configure your website settings";
        case 4: return "Create your administrator account";
        case 5: return "Generate the configuration file";
        case 6: return "Installation completed successfully";
        default: return "Website installation wizard";
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo get_step_title($step); ?> - Installation Wizard</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <style>
        :root {
            --primary-color: #f1ca2f;
            --secondary-color: #2c3e50;
            --success-color: #28a745;
            --danger-color: #dc3545;
            --warning-color: #ffc107;
            --info-color: #17a2b8;
            --light-color: #f8f9fa;
            --dark-color: #343a40;
            --border-color: #dee2e6;
            --text-color: #333;
            --text-light: #6c757d;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
            line-height: 1.4;
            color: var(--text-color);
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 10px;
            font-size: 14px;
        }

        .container {
            max-width: 700px;
            margin: 0 auto;
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, var(--secondary-color) 0%, #34495e 100%);
            color: white;
            padding: 20px;
            text-align: center;
        }

        .header h1 {
            font-size: 1.8rem;
            margin-bottom: 8px;
            font-weight: 300;
        }

        .header p {
            font-size: 0.95rem;
            opacity: 0.9;
        }

        .progress-bar {
            background-color: rgba(255, 255, 255, 0.2);
            height: 4px;
            border-radius: 2px;
            margin-top: 15px;
            overflow: hidden;
        }

        .progress-fill {
            background: linear-gradient(90deg, var(--primary-color) 0%, #f39c12 100%);
            height: 100%;
            border-radius: 3px;
            transition: width 0.3s ease;
        }

        .step-indicator {
            display: flex;
            justify-content: space-between;
            margin: 20px;
            position: relative;
        }

        .step-indicator::before {
            content: '';
            position: absolute;
            top: 20px;
            left: 0;
            right: 0;
            height: 2px;
            background-color: var(--border-color);
            z-index: 1;
        }

        .step {
            display: flex;
            flex-direction: column;
            align-items: center;
            position: relative;
            z-index: 2;
            background-color: white;
            padding: 0 10px;
        }

        .step-number {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background-color: var(--border-color);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-bottom: 6px;
            transition: all 0.3s ease;
            font-size: 0.9rem;
        }

        .step.active .step-number {
            background-color: var(--primary-color);
            transform: scale(1.1);
        }

        .step.completed .step-number {
            background-color: var(--success-color);
        }

        .step-label {
            font-size: 0.8rem;
            text-align: center;
            color: var(--text-light);
            font-weight: 500;
        }

        .step.active .step-label {
            color: var(--text-color);
            font-weight: 600;
        }

        .content {
            padding: 25px;
        }

        .alert {
            padding: 12px 16px;
            border-radius: 6px;
            margin-bottom: 20px;
            border: 1px solid transparent;
            display: flex;
            align-items: center;
            font-size: 0.9rem;
        }

        .alert i {
            margin-right: 8px;
            font-size: 1rem;
        }

        .alert-success {
            background-color: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }

        .alert-danger {
            background-color: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }

        .form-group {
            margin-bottom: 18px;
        }

        .form-group label {
            display: block;
            margin-bottom: 6px;
            font-weight: 600;
            color: var(--text-color);
            font-size: 0.9rem;
        }

        .form-control {
            width: 100%;
            padding: 10px 12px;
            border: 1px solid var(--border-color);
            border-radius: 6px;
            font-size: 0.9rem;
            transition: all 0.3s ease;
            background-color: #fff;
        }

        .form-control:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(241, 202, 47, 0.1);
        }

        .form-control[type="file"] {
            padding: 8px 12px;
            background-color: #fff;
            border: 2px dashed var(--border-color);
            border-radius: 6px;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .form-control[type="file"]:hover {
            border-color: var(--primary-color);
            background-color: rgba(241, 202, 47, 0.05);
        }

        .form-control[type="file"]:focus {
            border-color: var(--primary-color);
            border-style: solid;
            background-color: rgba(241, 202, 47, 0.05);
        }

        .btn {
            display: inline-block;
            padding: 10px 24px;
            border: none;
            border-radius: 6px;
            font-size: 0.9rem;
            font-weight: 600;
            text-decoration: none;
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: center;
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--primary-color) 0%, #f39c12 100%);
            color: var(--secondary-color);
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(241, 202, 47, 0.4);
        }

        .btn-secondary {
            background-color: var(--border-color);
            color: var(--text-color);
        }

        .btn-secondary:hover {
            background-color: #adb5bd;
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none !important;
            box-shadow: none !important;
        }

        .form-actions {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 20px;
            padding-top: 15px;
            border-top: 1px solid var(--border-color);
        }

        .help-text {
            font-size: 0.8rem;
            color: var(--text-light);
            margin-top: 4px;
        }

        .card {
            background-color: #fff;
            border: 1px solid var(--border-color);
            border-radius: 6px;
            padding: 16px;
            margin-bottom: 16px;
        }

        .card-header {
            font-weight: 600;
            margin-bottom: 12px;
            color: var(--secondary-color);
            font-size: 1rem;
        }

        @media (max-width: 768px) {
            body {
                padding: 5px;
            }

            .container {
                margin: 5px;
                border-radius: 6px;
            }

            .header {
                padding: 15px;
            }

            .header h1 {
                font-size: 1.5rem;
            }

            .content {
                padding: 15px;
            }

            .step-indicator {
                margin: 15px 10px;
            }

            .step-number {
                width: 28px;
                height: 28px;
                font-size: 0.8rem;
            }

            .step-label {
                font-size: 0.7rem;
            }

            .form-actions {
                flex-direction: column;
                gap: 12px;
            }

            .btn {
                width: 100%;
                padding: 12px 20px;
            }

            .card {
                padding: 12px;
                margin-bottom: 12px;
            }

            .form-group {
                margin-bottom: 15px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-cogs"></i> Installation Wizard</h1>
            <p><?php echo get_step_description($step); ?></p>
            <div class="progress-bar">
                <div class="progress-fill" style="width: <?php echo ($step / 6) * 100; ?>%"></div>
            </div>
        </div>

        <div class="step-indicator">
            <div class="step <?php echo $step >= 1 ? 'active' : ''; ?> <?php echo $step > 1 ? 'completed' : ''; ?>">
                <div class="step-number">
                    <?php echo $step > 1 ? '<i class="fas fa-check"></i>' : '1'; ?>
                </div>
                <div class="step-label">Database</div>
            </div>
            <div class="step <?php echo $step >= 2 ? 'active' : ''; ?> <?php echo $step > 2 ? 'completed' : ''; ?>">
                <div class="step-number">
                    <?php echo $step > 2 ? '<i class="fas fa-check"></i>' : '2'; ?>
                </div>
                <div class="step-label">Tables</div>
            </div>
            <div class="step <?php echo $step >= 3 ? 'active' : ''; ?> <?php echo $step > 3 ? 'completed' : ''; ?>">
                <div class="step-number">
                    <?php echo $step > 3 ? '<i class="fas fa-check"></i>' : '3'; ?>
                </div>
                <div class="step-label">Site Config</div>
            </div>
            <div class="step <?php echo $step >= 4 ? 'active' : ''; ?> <?php echo $step > 4 ? 'completed' : ''; ?>">
                <div class="step-number">
                    <?php echo $step > 4 ? '<i class="fas fa-check"></i>' : '4'; ?>
                </div>
                <div class="step-label">Admin User</div>
            </div>
            <div class="step <?php echo $step >= 5 ? 'active' : ''; ?> <?php echo $step > 5 ? 'completed' : ''; ?>">
                <div class="step-number">
                    <?php echo $step > 5 ? '<i class="fas fa-check"></i>' : '5'; ?>
                </div>
                <div class="step-label">Config</div>
            </div>
            <div class="step <?php echo $step >= 6 ? 'active' : ''; ?>">
                <div class="step-number">
                    <?php echo $step >= 6 ? '<i class="fas fa-check"></i>' : '6'; ?>
                </div>
                <div class="step-label">Complete</div>
            </div>
        </div>

        <div class="content">
            <?php if (!empty($error)): ?>
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle"></i>
                    <?php echo $error; ?>
                </div>
            <?php endif; ?>

            <?php if (!empty($success)): ?>
                <div class="alert alert-success">
                    <i class="fas fa-check-circle"></i>
                    <?php echo $success; ?>
                </div>
            <?php endif; ?>



            <?php if ($step == 1): ?>
                <div class="card">
                    <div class="card-header">
                        <i class="fas fa-database"></i> Database Configuration
                    </div>
                    <form method="POST" action="">
                        <input type="hidden" name="csrf_token" value="<?php echo $_SESSION['csrf_token']; ?>">
                        <input type="hidden" name="step_action" value="setup_db">

                        <div class="form-group">
                            <label for="db_host">Database Host</label>
                            <input type="text" id="db_host" name="db_host" class="form-control"
                                   value="<?php echo isset($_POST['db_host']) ? htmlspecialchars($_POST['db_host']) : 'localhost'; ?>" required>
                        </div>

                        <div class="form-group">
                            <label for="db_user">Database Username</label>
                            <input type="text" id="db_user" name="db_user" class="form-control"
                                   value="<?php echo isset($_POST['db_user']) ? htmlspecialchars($_POST['db_user']) : ''; ?>" required>
                        </div>

                        <div class="form-group">
                            <label for="db_pass">Database Password</label>
                            <input type="password" id="db_pass" name="db_pass" class="form-control" required>
                        </div>

                        <div class="form-group">
                            <label for="db_name">Database Name</label>
                            <input type="text" id="db_name" name="db_name" class="form-control"
                                   value="<?php echo isset($_POST['db_name']) ? htmlspecialchars($_POST['db_name']) : ''; ?>" required>
                        </div>

                        <div class="form-actions">
                            <button type="submit" name="setup_db" class="btn btn-primary">
                                <i class="fas fa-arrow-right"></i> Connect & Continue
                            </button>
                        </div>
                    </form>
                </div>
            <?php endif; ?>

            <?php if ($step == 2): ?>
                <div class="card">
                    <div class="card-header">
                        <i class="fas fa-table"></i> Create Database Tables
                    </div>

                    <form method="POST" action="">
                        <input type="hidden" name="csrf_token" value="<?php echo $_SESSION['csrf_token']; ?>">
                        <input type="hidden" name="step_action" value="setup_tables">

                        <div class="form-actions">
                            <a href="?step=1" class="btn btn-secondary">
                                <i class="fas fa-arrow-left"></i> Back
                            </a>
                            <button type="submit" name="setup_tables" class="btn btn-primary">
                                <i class="fas fa-play"></i> Create Tables
                            </button>
                        </div>
                    </form>
                </div>
            <?php endif; ?>

            <?php if ($step == 3): ?>
                <div class="card">
                    <div class="card-header">
                        <i class="fas fa-cog"></i> Site Configuration
                    </div>

                    <form method="POST" action="" enctype="multipart/form-data">
                        <input type="hidden" name="csrf_token" value="<?php echo $_SESSION['csrf_token']; ?>">
                        <input type="hidden" name="step_action" value="configure_site">

                        <div class="form-group">
                            <label for="site_name">Site Name <span style="color: #dc3545;">*</span></label>
                            <input type="text" id="site_name" name="site_name" class="form-control"
                                   value="<?php echo isset($_POST['site_name']) ? htmlspecialchars($_POST['site_name']) : 'Manage Inc'; ?>" required>
                            <div class="help-text">The name of your website that will appear in the admin panel and emails.</div>
                        </div>

                        <div class="form-group">
                            <label for="site_description">Site Description <span style="color: #dc3545;">*</span></label>
                            <textarea id="site_description" name="site_description" class="form-control" rows="3" required><?php echo isset($_POST['site_description']) ? htmlspecialchars($_POST['site_description']) : 'A professional website management system'; ?></textarea>
                            <div class="help-text">A brief description of your website for SEO and admin purposes.</div>
                        </div>

                        <div class="form-group">
                            <label for="from_email">From Email <span style="color: #dc3545;">*</span></label>
                            <input type="email" id="from_email" name="from_email" class="form-control"
                                   value="<?php echo isset($_POST['from_email']) ? htmlspecialchars($_POST['from_email']) : 'noreply@' . $_SERVER['HTTP_HOST']; ?>" required>
                            <div class="help-text">Default email address for outgoing system emails.</div>
                        </div>

                        <div class="form-group">
                            <label for="from_name">From Name <span style="color: #dc3545;">*</span></label>
                            <input type="text" id="from_name" name="from_name" class="form-control"
                                   value="<?php echo isset($_POST['from_name']) ? htmlspecialchars($_POST['from_name']) : 'Website Admin'; ?>" required>
                            <div class="help-text">Default sender name for outgoing system emails.</div>
                        </div>

                        <div class="form-group">
                            <label for="admin_logo">Admin Panel Logo <span style="color: #6c757d;">(Optional)</span></label>
                            <input type="file" id="admin_logo" name="admin_logo" class="form-control" accept="image/*">
                            <div class="help-text">
                                Upload a logo for the admin panel header. Supported formats: JPG, JPEG, PNG, GIF, WebP.
                                Maximum size: 5MB. Recommended dimensions: 200x60 pixels or similar aspect ratio.
                            </div>
                            <div id="logo-preview" style="margin-top: 10px; display: none;">
                                <img id="logo-preview-img" src="" alt="Logo Preview" style="max-width: 200px; max-height: 60px; border: 1px solid #dee2e6; border-radius: 4px; padding: 5px;">
                            </div>
                        </div>

                        <div class="form-actions">
                            <a href="?step=2" class="btn btn-secondary">
                                <i class="fas fa-arrow-left"></i> Back
                            </a>
                            <button type="submit" name="configure_site" class="btn btn-primary">
                                <i class="fas fa-save"></i> Save Configuration
                            </button>
                        </div>
                    </form>
                </div>

                <script>
                document.getElementById('admin_logo').addEventListener('change', function(e) {
                    const file = e.target.files[0];
                    const preview = document.getElementById('logo-preview');
                    const previewImg = document.getElementById('logo-preview-img');

                    if (file) {
                        // Validate file type
                        const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
                        if (!allowedTypes.includes(file.type)) {
                            alert('Invalid file type. Please select a JPG, PNG, GIF, or WebP image.');
                            e.target.value = '';
                            preview.style.display = 'none';
                            return;
                        }

                        // Validate file size (5MB)
                        if (file.size > 5242880) {
                            alert('File size too large. Please select an image smaller than 5MB.');
                            e.target.value = '';
                            preview.style.display = 'none';
                            return;
                        }

                        // Show preview
                        const reader = new FileReader();
                        reader.onload = function(e) {
                            previewImg.src = e.target.result;
                            preview.style.display = 'block';
                        };
                        reader.readAsDataURL(file);
                    } else {
                        preview.style.display = 'none';
                    }
                });
                </script>
            <?php endif; ?>

            <?php if ($step == 4): ?>
                <div class="card">
                    <div class="card-header">
                        <i class="fas fa-user-shield"></i> Create Administrator Account
                    </div>

                    <form method="POST" action="">
                        <input type="hidden" name="csrf_token" value="<?php echo $_SESSION['csrf_token']; ?>">

                        <div class="form-group">
                            <label for="admin_username">Username</label>
                            <input type="text" id="admin_username" name="admin_username" class="form-control"
                                   value="<?php echo isset($_POST['admin_username']) ? htmlspecialchars($_POST['admin_username']) : 'admin'; ?>"
                                   required minlength="3" maxlength="50">
                        </div>

                        <div class="form-group">
                            <label for="admin_email">Email Address</label>
                            <input type="email" id="admin_email" name="admin_email" class="form-control"
                                   value="<?php echo isset($_POST['admin_email']) ? htmlspecialchars($_POST['admin_email']) : ''; ?>" required>
                        </div>

                        <div class="form-group">
                            <label for="admin_password">Password</label>
                            <input type="password" id="admin_password" name="admin_password" class="form-control"
                                   required minlength="8">
                        </div>

                        <div class="form-group">
                            <label for="admin_password_confirm">Confirm Password</label>
                            <input type="password" id="admin_password_confirm" name="admin_password_confirm" class="form-control"
                                   required minlength="8">
                        </div>

                        <div class="form-actions">
                            <a href="?step=3" class="btn btn-secondary">
                                <i class="fas fa-arrow-left"></i> Back
                            </a>
                            <button type="submit" name="create_admin" class="btn btn-primary">
                                <i class="fas fa-user-plus"></i> Create Account
                            </button>
                        </div>
                    </form>
                </div>
            <?php endif; ?>

            <?php if ($step == 5): ?>
                <div class="card">
                    <div class="card-header">
                        <i class="fas fa-cog"></i> Generate Configuration File
                    </div>
                    <p>Almost done! Now we'll generate the configuration file that contains all your settings and security configurations.</p>

                    <form method="POST" action="">
                        <input type="hidden" name="csrf_token" value="<?php echo $_SESSION['csrf_token']; ?>">

                        <div class="card" style="background-color: #f8f9fa; border-color: #e9ecef;">
                            <div class="card-header" style="background-color: transparent; border-bottom: 1px solid #e9ecef;">
                                <i class="fas fa-info-circle"></i> Configuration Details:
                            </div>
                            <div style="padding: 15px;">
                                <p><strong>Database:</strong> <?php echo htmlspecialchars($_SESSION['db_name'] ?? 'Unknown'); ?> on <?php echo htmlspecialchars($_SESSION['db_host'] ?? 'Unknown'); ?></p>
                                <p><strong>Site Name:</strong> <?php echo htmlspecialchars($_SESSION['site_name'] ?? 'Unknown'); ?></p>
                                <p><strong>From Email:</strong> <?php echo htmlspecialchars($_SESSION['from_email'] ?? 'Unknown'); ?></p>
                                <p><strong>Admin User:</strong> <?php echo htmlspecialchars($_SESSION['admin_username'] ?? 'Unknown'); ?></p>
                                <p><strong>Admin Email:</strong> <?php echo htmlspecialchars($_SESSION['admin_email'] ?? 'Unknown'); ?></p>
                                <p><strong>Security:</strong> CSRF protection, password hashing, session security</p>
                                <p><strong>File:</strong> config.php (will be created in the admin directory)</p>
                            </div>
                        </div>

                        <div class="card" style="background-color: #d4edda; border-color: #c3e6cb;">
                            <div class="card-header" style="background-color: transparent; border-bottom: 1px solid #c3e6cb; color: #155724;">
                                <i class="fas fa-shield-alt"></i> Security Features Included:
                            </div>
                            <ul style="margin: 0; padding-left: 20px; color: #155724;">
                                <li>Secure password hashing (PHP password_hash)</li>
                                <li>CSRF token protection</li>
                                <li>Session security settings</li>
                                <li>XSS protection headers</li>
                                <li>Content Security Policy</li>
                                <li>Secure file upload restrictions</li>
                                <li>Login attempt limiting</li>
                            </ul>
                        </div>

                        <div class="form-actions">
                            <a href="?step=4" class="btn btn-secondary">
                                <i class="fas fa-arrow-left"></i> Back to Admin Setup
                            </a>
                            <button type="submit" name="generate_config" class="btn btn-primary">
                                <i class="fas fa-file-code"></i> Generate Configuration
                            </button>
                        </div>
                    </form>
                </div>

                <div class="card">
                    <div class="card-header">
                        <i class="fas fa-check-circle"></i> Installation Progress
                    </div>
                    <p><i class="fas fa-check text-success"></i> Database connection established</p>
                    <p><i class="fas fa-check text-success"></i> Database tables created successfully</p>
                    <p><i class="fas fa-check text-success"></i> Site configuration completed</p>
                    <p><i class="fas fa-check text-success"></i> Administrator account created</p>
                    <p><i class="fas fa-clock text-warning"></i> Configuration file - Ready to generate</p>
                </div>
            <?php endif; ?>

            <?php if ($step == 6): ?>
                <div class="card" style="background-color: #d4edda; border-color: #c3e6cb; text-align: center;">
                    <div style="padding: 40px;">
                        <i class="fas fa-check-circle" style="font-size: 4rem; color: #28a745; margin-bottom: 20px;"></i>
                        <h2 style="color: #155724; margin-bottom: 20px;">Installation Completed Successfully!</h2>
                        <p style="font-size: 1.1rem; color: #155724; margin-bottom: 30px;">
                            Your website has been installed and configured. You can now start using the admin panel.
                        </p>

                        <div style="background-color: #fff; border: 1px solid #c3e6cb; border-radius: 8px; padding: 20px; margin: 20px 0; text-align: left;">
                            <h4 style="color: #155724; margin-bottom: 15px;"><i class="fas fa-cog"></i> Site Configuration:</h4>
                            <p><strong>Site Name:</strong> <?php echo htmlspecialchars($_SESSION['site_name'] ?? 'Unknown'); ?></p>
                            <p><strong>Site Description:</strong> <?php echo htmlspecialchars($_SESSION['site_description'] ?? 'Unknown'); ?></p>
                            <p><strong>From Email:</strong> <?php echo htmlspecialchars($_SESSION['from_email'] ?? 'Unknown'); ?></p>
                            <p><strong>From Name:</strong> <?php echo htmlspecialchars($_SESSION['from_name'] ?? 'Unknown'); ?></p>
                            <?php if (isset($_SESSION['admin_logo'])): ?>
                            <p><strong>Admin Logo:</strong> <?php echo htmlspecialchars($_SESSION['admin_logo']); ?></p>
                            <?php endif; ?>
                        </div>

                        <div style="background-color: #fff; border: 1px solid #c3e6cb; border-radius: 8px; padding: 20px; margin: 20px 0; text-align: left;">
                            <h4 style="color: #155724; margin-bottom: 15px;"><i class="fas fa-user-shield"></i> Your Admin Credentials:</h4>
                            <p><strong>Username:</strong> <?php echo htmlspecialchars($_SESSION['admin_username'] ?? 'Unknown'); ?></p>
                            <p><strong>Email:</strong> <?php echo htmlspecialchars($_SESSION['admin_email'] ?? 'Unknown'); ?></p>
                            <p><strong>Password:</strong> <em>The password you created</em></p>
                        </div>

                        <div style="background-color: #fff3cd; border: 1px solid #ffeaa7; border-radius: 8px; padding: 20px; margin: 20px 0; text-align: left;">
                            <h4 style="color: #856404; margin-bottom: 15px;"><i class="fas fa-exclamation-triangle"></i> Important Security Steps:</h4>
                            <ul style="color: #856404; margin: 0; padding-left: 20px;">
                                <li><strong>Delete this installer:</strong> Remove or rename <code>install.php</code> for security</li>
                                <li><strong>Secure your config:</strong> Make sure <code>config.php</code> is not publicly accessible</li>
                                <li><strong>Update permissions:</strong> Set appropriate file permissions on your server</li>
                                <li><strong>Change default passwords:</strong> Update any default passwords in your system</li>
                                <li><strong>Enable HTTPS:</strong> Use SSL/TLS encryption for your website</li>
                            </ul>
                        </div>

                        <div style="background-color: #d1ecf1; border: 1px solid #bee5eb; border-radius: 8px; padding: 20px; margin: 20px 0; text-align: left;">
                            <h4 style="color: #0c5460; margin-bottom: 15px;"><i class="fas fa-info-circle"></i> What's Next:</h4>
                            <ul style="color: #0c5460; margin: 0; padding-left: 20px;">
                                <li>Configure email settings in the admin panel</li>
                                <li>Customize your website settings</li>
                                <li>Add content and manage users</li>
                                <li>Set up regular backups</li>
                                <li>Monitor your website's security</li>
                            </ul>
                        </div>

                        <div style="margin-top: 30px;">
                            <a href="index.php" class="btn btn-primary" style="font-size: 1.1rem; padding: 15px 40px;">
                                <i class="fas fa-sign-in-alt"></i> Go to Admin Login
                            </a>
                        </div>
                    </div>
                </div>

                <div class="card">
                    <div class="card-header">
                        <i class="fas fa-check-circle"></i> Installation Summary
                    </div>
                    <p><i class="fas fa-check text-success"></i> Database connection established</p>
                    <p><i class="fas fa-check text-success"></i> Database tables created successfully</p>
                    <p><i class="fas fa-check text-success"></i> Site configuration completed</p>
                    <p><i class="fas fa-check text-success"></i> Administrator account created</p>
                    <p><i class="fas fa-check text-success"></i> Configuration file generated</p>
                    <p><i class="fas fa-check text-success"></i> Installation completed successfully</p>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <script>
        // Add some basic form validation
        document.addEventListener('DOMContentLoaded', function() {
            // Password confirmation validation
            const passwordField = document.getElementById('admin_password');
            const confirmField = document.getElementById('admin_password_confirm');

            if (passwordField && confirmField) {
                function validatePasswords() {
                    if (passwordField.value !== confirmField.value) {
                        confirmField.setCustomValidity('Passwords do not match');
                    } else {
                        confirmField.setCustomValidity('');
                    }
                }

                passwordField.addEventListener('input', validatePasswords);
                confirmField.addEventListener('input', validatePasswords);
            }

            // Form submission loading state
            const forms = document.querySelectorAll('form');
            forms.forEach(form => {
                form.addEventListener('submit', function(e) {
                    const submitBtn = form.querySelector('button[type="submit"]');
                    if (submitBtn) {
                        // Add a hidden input with the button name/value before disabling
                        const hiddenInput = document.createElement('input');
                        hiddenInput.type = 'hidden';
                        hiddenInput.name = submitBtn.name;
                        hiddenInput.value = submitBtn.value || '1';
                        form.appendChild(hiddenInput);

                        // Now disable the button
                        submitBtn.disabled = true;
                        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Processing...';
                    }
                });
            });
        });
    </script>
</body>
</html>
<?php
// Flush the output buffer
ob_end_flush();
?>