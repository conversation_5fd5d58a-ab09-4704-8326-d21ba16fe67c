<?php
/**
 * Test Contact Notifications
 * 
 * This page demonstrates and tests the contact form notification integration
 */

// Start session and check authentication
session_start();

// Check if user is logged in and is admin
if (!isset($_SESSION['user_id']) || !isset($_SESSION['is_admin']) || !$_SESSION['is_admin']) {
    header('Location: login.php');
    exit;
}

// Include database connection
require_once 'config/database.php';
require_once 'lib/ContactNotifications.php';

$page_title = 'Test Contact Notifications';
include 'includes/header.php';

$success_message = '';
$error_message = '';

// Handle test notification creation
if ($_POST && isset($_POST['create_test_notification'])) {
    try {
        $contact_notifications = new ContactNotifications($conn);
        
        // Create a test contact submission
        $test_submission = [
            'id' => 999, // Test ID
            'name' => '<PERSON> Doe',
            'email' => '<EMAIL>',
            'phone' => '+****************',
            'message' => 'This is a test contact form submission to demonstrate the notification system integration.',
            'source' => 'Test Form'
        ];
        
        // Create the notification
        $success = $contact_notifications->createContactSubmissionNotification($test_submission);
        
        if ($success) {
            $success_message = "Test contact notification created successfully! Check the notification dropdown in the header.";
        } else {
            $error_message = "Failed to create test notification. Check error logs for details.";
        }
        
    } catch (Exception $e) {
        $error_message = "Error creating test notification: " . $e->getMessage();
        error_log("Test contact notification error: " . $e->getMessage());
    }
}

// Handle sync existing submissions
if ($_POST && isset($_POST['sync_existing'])) {
    try {
        $contact_notifications = new ContactNotifications($conn);
        $success = $contact_notifications->syncExistingSubmissions();
        
        if ($success) {
            $success_message = "Successfully synced existing contact submissions with notifications!";
        } else {
            $error_message = "Failed to sync existing submissions. Check error logs for details.";
        }
        
    } catch (Exception $e) {
        $error_message = "Error syncing submissions: " . $e->getMessage();
        error_log("Sync submissions error: " . $e->getMessage());
    }
}

// Get current statistics
$stats = [];
try {
    // Total contact submissions
    $result = $conn->query("SELECT COUNT(*) as count FROM contact_submissions");
    if ($result && $result->num_rows > 0) {
        $row = $result->fetch_assoc();
        $stats['total_submissions'] = (int)$row['count'];
    }
    
    // Unread contact submissions
    $result = $conn->query("SELECT COUNT(*) as count FROM contact_submissions WHERE is_read = 0");
    if ($result && $result->num_rows > 0) {
        $row = $result->fetch_assoc();
        $stats['unread_submissions'] = (int)$row['count'];
    }
    
    // Contact notifications
    $result = $conn->query("SELECT COUNT(*) as count FROM notifications WHERE title LIKE '%Contact Form%'");
    if ($result && $result->num_rows > 0) {
        $row = $result->fetch_assoc();
        $stats['contact_notifications'] = (int)$row['count'];
    }
    
    // Total notifications
    $result = $conn->query("SELECT COUNT(*) as count FROM notifications");
    if ($result && $result->num_rows > 0) {
        $row = $result->fetch_assoc();
        $stats['total_notifications'] = (int)$row['count'];
    }
    
} catch (Exception $e) {
    error_log("Error getting stats: " . $e->getMessage());
}
?>

<div class="admin-container">
    <!-- Content Header -->
    <div class="content-header">
        <div class="content-header-left">
            <div class="content-header-icon">
                <i class="fas fa-bell"></i>
            </div>
            <div class="content-header-text">
                <h1>Test Contact Notifications</h1>
                <p>Test and demonstrate the contact form notification integration</p>
            </div>
        </div>
        <div class="content-header-actions">
            <a href="inbox.php" class="btn btn-secondary">
                <i class="fas fa-inbox"></i> Inbox
            </a>
            <a href="all_notifications.php" class="btn btn-primary">
                <i class="fas fa-bell"></i> All Notifications
            </a>
        </div>
    </div>

    <!-- Success/Error Messages -->
    <?php if ($success_message): ?>
        <div class="alert alert-success alert-dismissible">
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            <i class="fas fa-check-circle"></i>
            <?php echo htmlspecialchars($success_message); ?>
        </div>
    <?php endif; ?>

    <?php if ($error_message): ?>
        <div class="alert alert-danger alert-dismissible">
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            <i class="fas fa-exclamation-triangle"></i>
            <?php echo htmlspecialchars($error_message); ?>
        </div>
    <?php endif; ?>

    <div class="row">
        <!-- Statistics -->
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h3><i class="fas fa-chart-bar"></i> Current Statistics</h3>
                </div>
                <div class="card-body">
                    <div class="stats-grid">
                        <div class="stat-item">
                            <div class="stat-value"><?php echo number_format($stats['total_submissions'] ?? 0); ?></div>
                            <div class="stat-label">Total Contact Submissions</div>
                        </div>
                        
                        <div class="stat-item">
                            <div class="stat-value text-warning"><?php echo number_format($stats['unread_submissions'] ?? 0); ?></div>
                            <div class="stat-label">Unread Submissions</div>
                        </div>
                        
                        <div class="stat-item">
                            <div class="stat-value text-primary"><?php echo number_format($stats['contact_notifications'] ?? 0); ?></div>
                            <div class="stat-label">Contact Notifications</div>
                        </div>
                        
                        <div class="stat-item">
                            <div class="stat-value"><?php echo number_format($stats['total_notifications'] ?? 0); ?></div>
                            <div class="stat-label">Total Notifications</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Test Actions -->
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h3><i class="fas fa-flask"></i> Test Actions</h3>
                </div>
                <div class="card-body">
                    <form method="post" action="">
                        <div class="test-action">
                            <h5>Create Test Notification</h5>
                            <p>Create a test contact form notification to see how it appears in the notification dropdown.</p>
                            <button type="submit" name="create_test_notification" class="btn btn-primary">
                                <i class="fas fa-plus"></i> Create Test Notification
                            </button>
                        </div>
                        
                        <hr>
                        
                        <div class="test-action">
                            <h5>Sync Existing Submissions</h5>
                            <p>Create notifications for existing contact submissions that don't have notifications yet.</p>
                            <button type="submit" name="sync_existing" class="btn btn-warning">
                                <i class="fas fa-sync-alt"></i> Sync Existing Submissions
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- How It Works -->
    <div class="card mt-4">
        <div class="card-header">
            <h3><i class="fas fa-info-circle"></i> How Contact Notifications Work</h3>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <h5>Automatic Integration</h5>
                    <ul>
                        <li><strong>New Submissions:</strong> Automatically create notifications when contact forms are submitted</li>
                        <li><strong>Real-time Updates:</strong> Notifications appear instantly in the dropdown menu</li>
                        <li><strong>Visual Distinction:</strong> Contact form notifications have special styling with envelope icons</li>
                        <li><strong>Direct Links:</strong> Click notifications to go directly to the inbox message</li>
                    </ul>
                </div>
                
                <div class="col-md-6">
                    <h5>Notification Features</h5>
                    <ul>
                        <li><strong>Auto-read:</strong> Notifications marked as read when you view the message</li>
                        <li><strong>Auto-cleanup:</strong> Notifications deleted when messages are deleted</li>
                        <li><strong>Bulk actions:</strong> Mark multiple notifications as read at once</li>
                        <li><strong>Badge count:</strong> Shows unread notification count in header</li>
                    </ul>
                </div>
            </div>
            
            <div class="mt-4">
                <h5>Testing Steps</h5>
                <ol>
                    <li>Click "Create Test Notification" above</li>
                    <li>Look at the notification bell icon in the header - it should show a badge</li>
                    <li>Click the notification bell to open the dropdown</li>
                    <li>You should see a contact form notification with an envelope icon and special styling</li>
                    <li>Click the notification to test the link functionality</li>
                </ol>
            </div>
        </div>
    </div>

    <!-- Recent Contact Notifications -->
    <div class="card mt-4">
        <div class="card-header">
            <h3><i class="fas fa-envelope"></i> Recent Contact Notifications</h3>
        </div>
        <div class="card-body">
            <?php
            try {
                $sql = "SELECT n.*, cs.name, cs.email 
                        FROM notifications n 
                        LEFT JOIN contact_submissions cs ON n.link = CONCAT('inbox.php?id=', cs.id)
                        WHERE n.title LIKE '%Contact Form%' 
                        ORDER BY n.created_at DESC 
                        LIMIT 10";
                
                $result = $conn->query($sql);
                
                if ($result && $result->num_rows > 0):
            ?>
                <div class="table-responsive">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>Title</th>
                                <th>Contact</th>
                                <th>Status</th>
                                <th>Created</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php while ($notification = $result->fetch_assoc()): ?>
                                <tr>
                                    <td>
                                        <strong><?php echo htmlspecialchars($notification['title']); ?></strong>
                                        <br>
                                        <small class="text-muted"><?php echo htmlspecialchars(substr($notification['message'], 0, 100)); ?>...</small>
                                    </td>
                                    <td>
                                        <?php if ($notification['name']): ?>
                                            <strong><?php echo htmlspecialchars($notification['name']); ?></strong>
                                            <br>
                                            <small><?php echo htmlspecialchars($notification['email']); ?></small>
                                        <?php else: ?>
                                            <span class="text-muted">Contact not found</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php if ($notification['is_read']): ?>
                                            <span class="badge bg-success">Read</span>
                                        <?php else: ?>
                                            <span class="badge bg-warning">Unread</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php echo date('M j, Y g:i A', strtotime($notification['created_at'])); ?>
                                    </td>
                                    <td>
                                        <?php if ($notification['link']): ?>
                                            <a href="<?php echo htmlspecialchars($notification['link']); ?>" class="btn btn-sm btn-primary">
                                                <i class="fas fa-eye"></i> View
                                            </a>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                            <?php endwhile; ?>
                        </tbody>
                    </table>
                </div>
            <?php else: ?>
                <div class="text-center py-4">
                    <i class="fas fa-envelope-open fa-3x text-muted mb-3"></i>
                    <h5>No Contact Notifications Yet</h5>
                    <p class="text-muted">Contact form notifications will appear here when submissions are received.</p>
                </div>
            <?php endif; ?>
            <?php } catch (Exception $e): ?>
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle"></i>
                    Error loading notifications: <?php echo htmlspecialchars($e->getMessage()); ?>
                </div>
            <?php endtry; ?>
        </div>
    </div>
</div>

<style>
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 1rem;
}

.stat-item {
    text-align: center;
    padding: 1rem;
    background: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #e9ecef;
}

.stat-value {
    font-size: 2rem;
    font-weight: 600;
    color: #212529;
    margin-bottom: 0.5rem;
}

.stat-value.text-warning {
    color: #f1ca2f !important;
}

.stat-value.text-primary {
    color: #007bff !important;
}

.stat-label {
    font-size: 0.875rem;
    color: #6c757d;
    font-weight: 500;
}

.test-action {
    margin-bottom: 1.5rem;
}

.test-action:last-child {
    margin-bottom: 0;
}

.test-action h5 {
    margin-bottom: 0.5rem;
    color: #212529;
}

.test-action p {
    margin-bottom: 1rem;
    color: #6c757d;
    font-size: 0.875rem;
}
</style>

<script>
// Auto-refresh notification badge after test actions
document.addEventListener('DOMContentLoaded', function() {
    // Check if we just created a notification
    const urlParams = new URLSearchParams(window.location.search);
    const successMessage = document.querySelector('.alert-success');
    
    if (successMessage && successMessage.textContent.includes('notification created')) {
        // Refresh the notification dropdown after a short delay
        setTimeout(function() {
            // Trigger a refresh of the notifications
            if (typeof loadNotifications === 'function') {
                loadNotifications();
            }
            
            // Show a helpful message
            const helpText = document.createElement('div');
            helpText.className = 'alert alert-info mt-3';
            helpText.innerHTML = '<i class="fas fa-info-circle"></i> <strong>Tip:</strong> Check the notification bell icon in the header to see your test notification!';
            successMessage.parentNode.insertBefore(helpText, successMessage.nextSibling);
        }, 1000);
    }
});
</script>

<?php include 'includes/footer.php'; ?>
