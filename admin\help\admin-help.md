# Manage Inc Admin Panel User Guide

## Overview

The Manage Inc Admin Panel is a comprehensive content management system designed for website administrators and content managers. This powerful tool allows you to manage your website content, user accounts, system settings, and communication with site visitors through a user-friendly interface.

### Key Features

- **Dashboard**: Get a quick overview of your website statistics and recent activity
- **News Management**: Create, edit, and organize news posts and categories
- **User Management**: Manage user accounts and permissions
- **Inbox**: View and respond to contact form submissions
- **Frontend Editor**: Edit website content directly through a WYSIWYG editor
- **Settings**: Configure system settings including email, appearance, and fonts
- **Email Templates**: Customize automated email responses

## Getting Started

### Accessing the Admin Panel

1. Navigate to your website's admin URL (typically yourdomain.com/admin)
2. Enter your username and password in the login form
3. Click "Sign In" to access the dashboard

### Dashboard Overview

The dashboard provides a snapshot of your website's activity and quick access to common tasks:

- **Statistics Cards**: View total news posts, categories, and active users
- **Quick Actions**: Access frequently used features with a single click
- **Recent Activity**: See the latest actions taken in the admin panel
- **Recent News**: View and manage your most recent news posts

## News Management

### Creating a News Post

1. Navigate to **News > Add News** from the sidebar menu
2. Fill in the required fields:
   - **Title**: The headline of your news post
   - **Content**: The main body text (using the WYSIWYG editor)
   - **Category**: Select from existing categories or create a new one
   - **Image**: Upload a featured image for your post
   - **Slug**: Custom URL segment for SEO (auto-generated if left blank)
3. Click "Save" to publish your news post

### Managing News Posts

1. Go to **News > All News** to see a list of all posts
2. Use the filter and search options to find specific posts
3. Available actions for each post:
   - **Edit**: Modify post content, image, or category
   - **Duplicate**: Create a copy of an existing post
   - **Delete**: Remove a post permanently
   - **View**: See how the post appears on the website

### Categories

1. Navigate to **News > Categories** to manage post categories
2. Create new categories with descriptive names
3. Edit or delete existing categories as needed
4. Organize posts by assigning them to relevant categories

## User Management

### Creating User Accounts

1. Go to **Users** in the sidebar menu
2. Click "Add New User" button
3. Fill in the required information:
   - **Username**: Unique login name
   - **Email**: Valid email address
   - **Password**: Secure password
   - **Role**: Administrator, Editor, or Viewer
4. Click "Create User" to add the new account

### Managing User Permissions

Different user roles have different access levels:

- **Administrator**: Full access to all features
- **Editor**: Can manage content but not system settings or users
- **Viewer**: Can view dashboard and content but cannot make changes

### Editing Your Profile

1. Click your username in the top-right corner
2. Select "Profile" from the dropdown menu
3. Update your personal information, password, or profile picture
4. Adjust interface preferences in the "Interface" tab
5. Click "Save Changes" to update your profile

## Inbox

### Viewing Contact Submissions

1. Click **Inbox** in the sidebar menu
2. View a list of all contact form submissions
3. Unread messages are highlighted for easy identification
4. Click on a message to view its full content

### Responding to Messages

1. Open a message from the inbox
2. Click "Reply" to compose a response
3. Use the template options or write a custom message
4. Click "Send" to deliver your response via email

## Frontend Editor

### Editing Website Pages

1. Navigate to **Frontend Editor** in the sidebar
2. Select the directory containing the file you want to edit
3. Choose a file from the list to open it in the editor
4. Make changes using the WYSIWYG editor or code editor
5. Add a version comment (optional) to track changes
6. Click "Save Changes" to publish your edits

### File Management

The Frontend Editor allows you to navigate through different directories:

- **Root**: Main website files
- **Pages**: Individual page files
- **Images**: Image files and directories
- **CSS**: Stylesheet files

## Settings

### General Settings

Configure basic system settings:

- **Admin Email**: Primary contact email for the system
- **Timezone**: Set the default timezone for date/time display
- **Date Format**: Choose how dates appear throughout the site

### Email Settings

Configure email functionality:

- **Mail Method**: Choose between PHP mail() or SMTP
- **SMTP Settings**: Configure server details if using SMTP
- **From Email/Name**: Set default sender information
- **Reply-To Email**: Set default reply address

### Font Settings

Customize typography:

- **Google Fonts API Key**: Enable custom font integration
- **Heading Font**: Choose font for headings
- **Body Font**: Choose font for body text

### Appearance Settings

Customize the visual style:

- **Primary Color**: Main accent color
- **Secondary Color**: Supporting accent color
- **Dark Mode**: Enable/disable dark mode option

## Email Templates

### Managing Email Templates

1. Go to **Settings > Email** tab
2. Scroll to the Email Templates section
3. Click "Edit" on any template to modify it

### Available Templates

- **Contact Auto-Reply**: Sent automatically to users who submit the contact form
- **Contact Reply**: Template for manually responding to contact submissions
- **Password Reset**: Sent when users request a password reset
- **Account Verification**: Sent to verify new user accounts

### Template Variables

Use these variables in your templates to include dynamic content:

- `{contact_name}`: The name of the person who submitted the form
- `{contact_email}`: The email address of the contact
- `{contact_subject}`: The subject of the contact message
- `{contact_message}`: The content of the contact message
- `{company_name}`: Your company name
- `{admin_name}`: The name of the admin sending the response
- `{site_url}`: Your website URL

## Troubleshooting

### Common Issues

1. **Login Problems**
   - Ensure caps lock is off
   - Use the "Forgot Password" link if needed
   - Check that your account is active

2. **Email Sending Failures**
   - Verify SMTP settings are correct
   - Check that the from email address is valid
   - Ensure server allows outgoing mail

3. **Image Upload Issues**
   - Check that the image is in a supported format (JPG, PNG, GIF)
   - Ensure the image size is under the maximum limit
   - Verify that the upload directory has proper permissions

### Getting Help

If you encounter issues not covered in this guide:

1. Check the error message for specific information
2. Look for related settings that might need adjustment
3. Contact your system administrator for assistance

## Best Practices

- **Regular Backups**: Always back up your content before making major changes
- **Strong Passwords**: Use complex passwords and change them regularly
- **Version Comments**: Add descriptive comments when editing files to track changes
- **Image Optimization**: Compress images before uploading to improve site performance
- **Regular Updates**: Keep your content fresh and relevant with regular updates
