/**
 * Settings Page CSS
 *
 * This file contains styles specific to the settings page.
 */

/* Settings Container */
.settings-container {
  display: flex;
  gap: var(--spacing-6);
  margin-bottom: var(--spacing-6);
}

/* Settings Sidebar */
.settings-sidebar {
  width: 240px;
  flex-shrink: 0;
}

/* Settings Content */
.settings-content {
  flex: 1;
  min-width: 0;
}

/* Settings Nav */
.settings-nav {
  background-color: var(--white);
  border-radius: var(--card-border-radius);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--border-color);
  overflow: hidden;
}

.settings-nav-title {
  padding: var(--spacing-3) var(--spacing-4);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-semibold);
  color: var(--text-dark);
  border-bottom: 1px solid var(--border-color);
  background-color: var(--gray-50);
}

.settings-nav-list {
  list-style: none;
  margin: 0;
  padding: var(--spacing-2) 0;
}

.settings-nav-item {
  margin: 0;
  padding: 0;
}

.settings-nav-link {
  display: flex;
  align-items: center;
  padding: var(--spacing-3) var(--spacing-4);
  color: var(--text-color);
  text-decoration: none;
  font-size: var(--font-size-sm);
  transition: all var(--transition-fast) ease;
}

.settings-nav-link:hover {
  background-color: var(--gray-50);
  color: var(--text-dark);
}

.settings-nav-link.active {
  background-color: var(--primary-very-light);
  color: var(--primary-color);
  font-weight: var(--font-weight-medium);
  border-left: 3px solid var(--primary-color);
}

.settings-nav-icon {
  margin-right: var(--spacing-3);
  font-size: var(--font-size-base);
  width: 20px;
  text-align: center;
  color: var(--text-light);
}

.settings-nav-link:hover .settings-nav-icon,
.settings-nav-link.active .settings-nav-icon {
  color: var(--primary-color);
}

.settings-nav-badge {
  margin-left: auto;
  font-size: var(--font-size-xs);
  background-color: var(--gray-200);
  color: var(--text-dark);
  padding: 2px 6px;
  border-radius: var(--radius-full);
}

.settings-nav-divider {
  height: 1px;
  background-color: var(--border-color);
  margin: var(--spacing-2) 0;
}

/* Settings Card */
.settings-card {
  background-color: var(--white);
  border-radius: var(--card-border-radius);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--border-color);
  overflow: hidden;
  margin-bottom: var(--spacing-6);
}

.settings-card:last-child {
  margin-bottom: 0;
}

.settings-card-header {
  padding: var(--spacing-4);
  border-bottom: 1px solid var(--border-color);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.settings-card-title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--text-dark);
  margin: 0;
}

.settings-card-subtitle {
  font-size: var(--font-size-sm);
  color: var(--text-light);
  margin: var(--spacing-1) 0 0;
}

.settings-card-body {
  padding: var(--spacing-4);
}

.settings-card-footer {
  padding: var(--spacing-4);
  border-top: 1px solid var(--border-color);
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: var(--spacing-2);
}

/* Settings Form */
.settings-form .form-group {
  margin-bottom: var(--spacing-4);
}

.settings-form .form-group:last-child {
  margin-bottom: 0;
}

.settings-form-row {
  display: flex;
  gap: var(--spacing-4);
  margin-bottom: var(--spacing-4);
}

.settings-form-col {
  flex: 1;
}

/* Settings Tabs */
.settings-tabs {
  margin-bottom: var(--spacing-4);
}

.settings-tabs .tab-list {
  background-color: var(--white);
  border-radius: var(--card-border-radius);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--border-color);
  padding: var(--spacing-1) var(--spacing-1);
}

.settings-tabs .tab-link {
  padding: var(--spacing-2) var(--spacing-4);
  border-radius: var(--radius-md);
  border-bottom: none;
}

.settings-tabs .tab-link.active {
  background-color: var(--primary-very-light);
  color: var(--primary-color);
}

/* Settings Switch */
.settings-switch {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-3) 0;
  border-bottom: 1px solid var(--border-light);
}

.settings-switch:last-child {
  border-bottom: none;
  padding-bottom: 0;
}

.settings-switch-label {
  display: flex;
  flex-direction: column;
}

.settings-switch-title {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--text-dark);
  margin-bottom: var(--spacing-1);
}

.settings-switch-description {
  font-size: var(--font-size-xs);
  color: var(--text-light);
}

/* Settings List */
.settings-list {
  margin: 0;
  padding: 0;
  list-style: none;
}

.settings-list-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-3) 0;
  border-bottom: 1px solid var(--border-light);
}

.settings-list-item:last-child {
  border-bottom: none;
}

.settings-list-content {
  display: flex;
  flex-direction: column;
}

.settings-list-title {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--text-dark);
  margin-bottom: var(--spacing-1);
}

.settings-list-description {
  font-size: var(--font-size-xs);
  color: var(--text-light);
}

.settings-list-actions {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
}

/* Settings Color Picker */
.settings-color-picker {
  display: flex;
  gap: var(--spacing-2);
  flex-wrap: wrap;
  margin-top: var(--spacing-2);
}

.settings-color-option {
  width: 24px;
  height: 24px;
  border-radius: var(--radius-full);
  cursor: pointer;
  border: 2px solid transparent;
  transition: all var(--transition-fast) ease;
}

.settings-color-option:hover {
  transform: scale(1.1);
}

.settings-color-option.active {
  border-color: var(--white);
  box-shadow: 0 0 0 2px var(--primary-color);
}

/* Settings Avatar */
.settings-avatar {
  display: flex;
  align-items: center;
  gap: var(--spacing-4);
  margin-bottom: var(--spacing-4);
}

.settings-avatar-img {
  width: 80px;
  height: 80px;
  border-radius: var(--radius-full);
  object-fit: cover;
  border: 1px solid var(--border-color);
}

.settings-avatar-actions {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-2);
}

/* Utility Classes */
.hidden-element {
  display: none !important;
}

/* Animation Classes */
.fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

/* Tooltip Icon */
.tooltip-icon {
  margin-left: 5px;
  font-size: 14px;
  cursor: help;
  color: #999;
}

.tooltip-icon:hover {
  color: #666;
}

/* Logo Update Message */
.logo-update-message {
  margin-left: 10px;
  color: green;
  font-size: 0.8em;
  font-style: italic;
}

/* Tab Hover Effect */
.tab-hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

/* Loading Overlay */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  z-index: 9999;
}

.loading-spinner {
  border: 5px solid #f3f3f3;
  border-top: 5px solid #f1ca2f;
  border-radius: 50%;
  width: 50px;
  height: 50px;
  animation: spin 2s linear infinite;
  margin-bottom: 20px;
}

.loading-message {
  color: white;
  font-size: 18px;
  font-weight: bold;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Responsive Styles */
@media (max-width: 992px) {
  .settings-container {
    flex-direction: column;
  }

  .settings-sidebar {
    width: 100%;
  }

  .settings-nav {
    margin-bottom: var(--spacing-4);
  }

  .settings-form-row {
    flex-direction: column;
    gap: var(--spacing-3);
  }
}
