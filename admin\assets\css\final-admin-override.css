/**
 * FINAL ADMIN OVERRIDE CSS
 * This file loads ABSOLUTELY LAST and overrides ALL conflicting styles
 * DO NOT MODIFY OTHER CSS FILES - MAKE ALL CHANGES HERE
 */

/* ========================================
   MODERN SIDEBAR DESIGN - FINAL OVERRIDE
   ======================================== */

.admin-sidebar {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  width: 260px !important;
  height: 100vh !important;
  background: #212529 !important;
  color: #ffffff !important;
  z-index: 1000 !important;
  display: flex !important;
  flex-direction: column !important;
  transition: all 0.3s ease !important;
  overflow-x: hidden !important;
  overflow-y: auto !important;
  box-shadow: 2px 0 8px rgba(0, 0, 0, 0.15) !important;
  border-right: 1px solid #343a40 !important;
}

/* Collapsed sidebar */
body.sidebar-collapsed .admin-sidebar,
.admin-sidebar.collapsed {
  width: 70px !important;
}

/* Sidebar Header */
.admin-sidebar-header {
  padding: 1.5rem 1rem !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1) !important;
  min-height: 80px !important;
  background: linear-gradient(135deg, #343a40, #212529) !important;
}

.sidebar-logo {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  width: 100% !important;
  transition: all 0.3s ease !important;
}

.sidebar-logo img {
  max-height: 40px !important;
  max-width: 100% !important;
  transition: all 0.3s ease !important;
}

body.sidebar-collapsed .sidebar-logo img {
  max-width: 40px !important;
}

/* Sidebar Menu */
.admin-sidebar-menu {
  flex: 1 !important;
  padding: 1rem 0 !important;
  overflow-y: auto !important;
}

.sidebar-nav {
  list-style: none !important;
  margin: 0 !important;
  padding: 0 !important;
}

.sidebar-item {
  margin: 0 !important;
  padding: 0 !important;
}

.sidebar-link {
  display: flex !important;
  align-items: center !important;
  padding: 0.75rem 1.5rem !important;
  color: rgba(255, 255, 255, 0.8) !important;
  text-decoration: none !important;
  transition: all 0.2s ease !important;
  position: relative !important;
  border: none !important;
  background: none !important;
  width: 100% !important;
  text-align: left !important;
}

.sidebar-link:hover {
  background: rgba(255, 255, 255, 0.1) !important;
  color: #ffffff !important;
}

.sidebar-link.active {
  background: linear-gradient(135deg, #f1ca2f, #e6b82a) !important;
  color: #212529 !important;
  font-weight: 600 !important;
}

.sidebar-link.active::before {
  content: '' !important;
  position: absolute !important;
  left: 0 !important;
  top: 0 !important;
  bottom: 0 !important;
  width: 4px !important;
  background: #212529 !important;
}

.sidebar-icon {
  width: 20px !important;
  height: 20px !important;
  margin-right: 0.75rem !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  font-size: 1rem !important;
  flex-shrink: 0 !important;
}

.sidebar-text {
  flex: 1 !important;
  font-size: 0.875rem !important;
  font-weight: 500 !important;
  transition: all 0.3s ease !important;
}

/* Collapsed sidebar text hiding */
body.sidebar-collapsed .sidebar-text {
  opacity: 0 !important;
  visibility: hidden !important;
  width: 0 !important;
  margin: 0 !important;
}

/* Submenu styles */
.sidebar-item.has-submenu .submenu-icon {
  margin-left: auto !important;
  transition: transform 0.2s ease !important;
  font-size: 0.75rem !important;
}

.sidebar-item.has-submenu.open .submenu-icon {
  transform: rotate(180deg) !important;
}

.sidebar-submenu {
  list-style: none !important;
  margin: 0 !important;
  padding: 0 !important;
  background: rgba(0, 0, 0, 0.2) !important;
  max-height: 0 !important;
  overflow: hidden !important;
  transition: max-height 0.3s ease !important;
}

.sidebar-item.has-submenu.open .sidebar-submenu {
  max-height: 500px !important;
}

.sidebar-sublink {
  display: block !important;
  padding: 0.5rem 1.5rem 0.5rem 3.5rem !important;
  color: rgba(255, 255, 255, 0.7) !important;
  text-decoration: none !important;
  font-size: 0.8125rem !important;
  transition: all 0.2s ease !important;
}

.sidebar-sublink:hover {
  background: rgba(255, 255, 255, 0.1) !important;
  color: #ffffff !important;
}

.sidebar-sublink.active {
  background: rgba(241, 202, 47, 0.2) !important;
  color: #f1ca2f !important;
  font-weight: 500 !important;
}

/* Sidebar Footer */
.admin-sidebar-footer {
  padding: 1rem !important;
  border-top: 1px solid rgba(255, 255, 255, 0.1) !important;
  background: rgba(0, 0, 0, 0.1) !important;
}

.sidebar-footer-link {
  display: flex !important;
  align-items: center !important;
  padding: 0.75rem !important;
  color: rgba(255, 255, 255, 0.8) !important;
  text-decoration: none !important;
  border-radius: 6px !important;
  transition: all 0.2s ease !important;
}

.sidebar-footer-link:hover {
  background: rgba(220, 53, 69, 0.1) !important;
  color: #dc3545 !important;
}

.sidebar-footer-icon {
  margin-right: 0.75rem !important;
  font-size: 1rem !important;
}

.sidebar-footer-text {
  font-size: 0.875rem !important;
  font-weight: 500 !important;
}

/* ========================================
   MAIN CONTENT LAYOUT - FINAL OVERRIDE
   ======================================== */

.admin-main {
  margin-left: 260px !important;
  min-height: 100vh !important;
  background: #f8f9fa !important;
  transition: margin-left 0.3s ease !important;
}

body.sidebar-collapsed .admin-main {
  margin-left: 70px !important;
}

/* ========================================
   TOPBAR - FINAL OVERRIDE
   ======================================== */

.admin-topbar {
  background: #ffffff !important;
  border-bottom: 1px solid #e9ecef !important;
  padding: 0.75rem 1.5rem !important;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05) !important;
  position: sticky !important;
  top: 0 !important;
  z-index: 999 !important;
}

/* ========================================
   PROFILE PAGE - FINAL OVERRIDE
   ======================================== */

body.page-profile .admin-container {
  max-width: none !important;
  width: 100% !important;
  padding: 1.5rem !important;
}

/* Profile page specific styling */
body.page-profile .profile-container {
  max-width: 100% !important;
  width: 100% !important;
  margin: 0 !important;
}

body.page-profile .profile-tabs {
  background: #ffffff !important;
  border-radius: 12px !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05) !important;
  border: 1px solid #e9ecef !important;
  overflow: hidden !important;
}

body.page-profile .profile-tab-nav {
  display: flex !important;
  background: #f8f9fa !important;
  border-bottom: 1px solid #e9ecef !important;
  margin: 0 !important;
  padding: 0 !important;
  list-style: none !important;
}

body.page-profile .profile-tab-nav li {
  margin: 0 !important;
  padding: 0 !important;
}

body.page-profile .profile-tab-nav a {
  display: block !important;
  padding: 1rem 1.5rem !important;
  color: #6c757d !important;
  text-decoration: none !important;
  border-bottom: 3px solid transparent !important;
  transition: all 0.2s ease !important;
  font-weight: 500 !important;
}

body.page-profile .profile-tab-nav a:hover {
  color: #f1ca2f !important;
  background: rgba(241, 202, 47, 0.05) !important;
}

body.page-profile .profile-tab-nav a.active {
  color: #f1ca2f !important;
  background: #ffffff !important;
  border-bottom-color: #f1ca2f !important;
  font-weight: 600 !important;
}

body.page-profile .profile-tab-content {
  padding: 2rem !important;
}

/* Profile form styling */
body.page-profile .form-group {
  margin-bottom: 1.5rem !important;
}

body.page-profile .form-group label {
  display: block !important;
  margin-bottom: 0.5rem !important;
  font-weight: 600 !important;
  color: #212529 !important;
}

body.page-profile .form-control {
  width: 100% !important;
  padding: 0.75rem !important;
  border: 1px solid #ced4da !important;
  border-radius: 6px !important;
  font-size: 0.875rem !important;
  transition: border-color 0.15s ease !important;
}

body.page-profile .form-control:focus {
  border-color: #f1ca2f !important;
  box-shadow: 0 0 0 3px rgba(241, 202, 47, 0.1) !important;
  outline: none !important;
}

/* Profile buttons */
body.page-profile .btn {
  padding: 0.75rem 1.5rem !important;
  border-radius: 6px !important;
  font-weight: 500 !important;
  text-decoration: none !important;
  display: inline-flex !important;
  align-items: center !important;
  gap: 0.5rem !important;
  transition: all 0.15s ease !important;
  border: 1px solid transparent !important;
}

body.page-profile .btn-primary {
  background: #f1ca2f !important;
  color: #212529 !important;
  border-color: #f1ca2f !important;
}

body.page-profile .btn-primary:hover {
  background: #e6b82a !important;
  border-color: #e6b82a !important;
  transform: translateY(-1px) !important;
  box-shadow: 0 4px 8px rgba(241, 202, 47, 0.3) !important;
}

/* ========================================
   RESPONSIVE DESIGN - FINAL OVERRIDE
   ======================================== */

@media (max-width: 768px) {
  .admin-sidebar {
    transform: translateX(-100%) !important;
    width: 280px !important;
    z-index: 9999 !important;
  }

  .admin-sidebar.show {
    transform: translateX(0) !important;
  }

  .admin-main {
    margin-left: 0 !important;
  }

  body.sidebar-collapsed .admin-main {
    margin-left: 0 !important;
  }
}

/* ========================================
   NOTIFICATION BADGES - FINAL OVERRIDE
   ======================================== */

.sidebar-badge {
  position: absolute !important;
  top: 50% !important;
  right: 12px !important;
  transform: translateY(-50%) !important;
  background-color: #dc3545 !important;
  color: #ffffff !important;
  border-radius: 12px !important;
  min-width: 24px !important;
  height: 24px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  font-size: 11px !important;
  font-weight: 600 !important;
  line-height: 1 !important;
  box-shadow: 0 2px 4px rgba(220, 53, 69, 0.3) !important;
  transition: all 0.2s ease !important;
  z-index: 10 !important;
}

.sidebar-badge.urgent {
  background-color: #f1ca2f !important;
  color: #212529 !important;
  box-shadow: 0 2px 8px rgba(241, 202, 47, 0.4) !important;
  animation: urgentPulse 2s infinite !important;
}

@keyframes urgentPulse {
  0%, 100% {
    transform: translateY(-50%) scale(1);
    box-shadow: 0 2px 8px rgba(241, 202, 47, 0.4);
  }
  50% {
    transform: translateY(-50%) scale(1.1);
    box-shadow: 0 4px 12px rgba(241, 202, 47, 0.6);
  }
}

/* ========================================
   GENERAL ADMIN PAGES - FINAL OVERRIDE
   ======================================== */

/* Ensure all admin containers use full width */
.admin-container {
  max-width: none !important;
  width: 100% !important;
  padding: 1.5rem !important;
  margin: 0 !important;
}

/* Modern card styling for all pages */
.admin-card,
.card {
  background: #ffffff !important;
  border-radius: 12px !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05) !important;
  border: 1px solid #e9ecef !important;
  overflow: hidden !important;
  margin-bottom: 1.5rem !important;
}

.admin-card-header,
.card-header {
  background: #f8f9fa !important;
  padding: 1.5rem !important;
  border-bottom: 1px solid #e9ecef !important;
  margin: 0 !important;
}

.admin-card-body,
.card-body {
  padding: 1.5rem !important;
}

/* Modern form styling */
.form-group {
  margin-bottom: 1.5rem !important;
}

.form-group label {
  display: block !important;
  margin-bottom: 0.5rem !important;
  font-weight: 600 !important;
  color: #212529 !important;
  font-size: 0.875rem !important;
}

.form-control,
input[type="text"],
input[type="email"],
input[type="password"],
input[type="number"],
textarea,
select {
  width: 100% !important;
  padding: 0.75rem !important;
  border: 1px solid #ced4da !important;
  border-radius: 6px !important;
  font-size: 0.875rem !important;
  transition: border-color 0.15s ease !important;
  background: #ffffff !important;
  color: #212529 !important;
}

.form-control:focus,
input:focus,
textarea:focus,
select:focus {
  border-color: #f1ca2f !important;
  box-shadow: 0 0 0 3px rgba(241, 202, 47, 0.1) !important;
  outline: none !important;
}

/* Modern button styling */
.btn,
.admin-btn,
button[type="submit"],
input[type="submit"] {
  padding: 0.75rem 1.5rem !important;
  border-radius: 6px !important;
  font-weight: 500 !important;
  text-decoration: none !important;
  display: inline-flex !important;
  align-items: center !important;
  gap: 0.5rem !important;
  transition: all 0.15s ease !important;
  border: 1px solid transparent !important;
  cursor: pointer !important;
  font-size: 0.875rem !important;
}

.btn-primary,
.admin-btn.primary {
  background: #f1ca2f !important;
  color: #212529 !important;
  border-color: #f1ca2f !important;
}

.btn-primary:hover,
.admin-btn.primary:hover {
  background: #e6b82a !important;
  border-color: #e6b82a !important;
  transform: translateY(-1px) !important;
  box-shadow: 0 4px 8px rgba(241, 202, 47, 0.3) !important;
}

.btn-secondary,
.admin-btn.secondary {
  background: #6c757d !important;
  color: #ffffff !important;
  border-color: #6c757d !important;
}

.btn-secondary:hover,
.admin-btn.secondary:hover {
  background: #5a6268 !important;
  border-color: #545b62 !important;
}

/* ========================================
   FORCE MODERN DESIGN - FINAL OVERRIDE
   ======================================== */

/* Remove any old styling that might interfere */
.admin-sidebar *,
.admin-main *,
.admin-container * {
  box-sizing: border-box !important;
}

/* Ensure proper font family everywhere */
body,
.admin-sidebar,
.admin-sidebar *,
.admin-main,
.admin-main *,
.admin-container,
.admin-container * {
  font-family: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
}

/* Force modern layout */
body {
  margin: 0 !important;
  padding: 0 !important;
  background: #f8f9fa !important;
  color: #212529 !important;
  line-height: 1.6 !important;
}
