!function(e){"object"==typeof exports&&"object"==typeof module?e(require("../../lib/codemirror")):"function"==typeof define&&define.amd?define(["../../lib/codemirror"],e):e(CodeMirror)}(function(y){"use strict";y.registerGlobalHelper("fold","comment",function(e){return e.blockCommentStart&&e.blockCommentEnd},function(e,t){var n=e.getModeAt(t),o=n.blockCommentStart,r=n.blockCommentEnd;if(o&&r){for(var i,f=t.line,l=e.getLine(f),c=t.ch,m=0;;){var a=c<=0?-1:l.lastIndexOf(o,c-1);if(-1==a){if(1==m)return;m=1,c=l.length}else{if(1==m&&a<t.ch)return;if(/comment/.test(e.getTokenTypeAt(y.Pos(f,a+1)))&&(0==a||l.slice(a-r.length,a)==r||!/comment/.test(e.getTokenTypeAt(y.Pos(f,a))))){i=a+o.length;break}c=a-1}}var d,s,u=1,b=e.lastLine();e:for(var g=f;g<=b;++g)for(var h=e.getLine(g),k=g==f?i:0;;){var p=h.indexOf(o,k),v=h.indexOf(r,k);if(p<0&&(p=h.length),v<0&&(v=h.length),(k=Math.min(p,v))==h.length)break;if(k==p)++u;else if(!--u){d=g,s=k;break e}++k}if(null!=d&&(f!=d||s!=i))return{from:y.Pos(f,i),to:y.Pos(d,s)}}})});