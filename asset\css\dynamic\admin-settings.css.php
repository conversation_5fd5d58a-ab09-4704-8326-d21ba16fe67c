<?php
/**
 * Dynamic CSS for Admin Settings
 *
 * This file generates CSS based on the admin_appearance settings in the database
 */

// Disable error reporting to prevent output before headers
error_reporting(0);
ini_set('display_errors', 0);

// Set content type to CSS
header('Content-Type: text/css');

// Include database configuration
try {
    require_once '../config.php';

// Default admin appearance settings
$admin_primary_color = '#f1ca2f';
$admin_secondary_color = '#3c3c45';
$admin_success_color = '#28a745';
$admin_warning_color = '#ffc107';
$admin_danger_color = '#dc3545';
$admin_enable_dark_mode = false;
$admin_logo_path = 'images/admin-logo.png';

// Get admin appearance settings from database
// First try the admin_appearance category (new structure)
$appearance_query = "SELECT setting_key, setting_value FROM system_settings WHERE category = 'admin_appearance'";
$appearance_result = $conn->query($appearance_query);

if ($appearance_result && $appearance_result->num_rows > 0) {
    while ($row = $appearance_result->fetch_assoc()) {
        switch ($row['setting_key']) {
            case 'admin_primary_color':
                $admin_primary_color = $row['setting_value'];
                break;
            case 'admin_secondary_color':
                $admin_secondary_color = $row['setting_value'];
                break;
            case 'admin_success_color':
                $admin_success_color = $row['setting_value'];
                break;
            case 'admin_warning_color':
                $admin_warning_color = $row['setting_value'];
                break;
            case 'admin_danger_color':
                $admin_danger_color = $row['setting_value'];
                break;
            case 'admin_enable_dark_mode':
                $admin_enable_dark_mode = ($row['setting_value'] == '1');
                break;
            case 'admin_logo_path':
                $admin_logo_path = $row['setting_value'];
                break;
        }
    }
}

// Then check the appearance category (old structure) for any missing values
$appearance_query = "SELECT setting_key, setting_value FROM system_settings WHERE category = 'appearance'";
$appearance_result = $conn->query($appearance_query);

if ($appearance_result && $appearance_result->num_rows > 0) {
    while ($row = $appearance_result->fetch_assoc()) {
        switch ($row['setting_key']) {
            case 'theme_color':
            case 'primary_color':
                if (empty($admin_primary_color) || $admin_primary_color == '#f1ca2f') {
                    $admin_primary_color = $row['setting_value'];
                }
                break;
            case 'secondary_color':
                if (empty($admin_secondary_color) || $admin_secondary_color == '#3c3c45') {
                    $admin_secondary_color = $row['setting_value'];
                }
                break;
            case 'success_color':
                if (empty($admin_success_color) || $admin_success_color == '#28a745') {
                    $admin_success_color = $row['setting_value'];
                }
                break;
            case 'warning_color':
                if (empty($admin_warning_color) || $admin_warning_color == '#ffc107') {
                    $admin_warning_color = $row['setting_value'];
                }
                break;
            case 'danger_color':
                if (empty($admin_danger_color) || $admin_danger_color == '#dc3545') {
                    $admin_danger_color = $row['setting_value'];
                }
                break;
            case 'enable_dark_mode':
                if (!$admin_enable_dark_mode) {
                    $admin_enable_dark_mode = ($row['setting_value'] == '1');
                }
                break;
            case 'admin_logo':
                if (empty($admin_logo_path) || $admin_logo_path == 'images/admin-logo.png') {
                    $admin_logo_path = $row['setting_value'];
                }
                break;
        }
    }
}

// Get admin fonts settings
$admin_heading_font = 'Arial, sans-serif';
$admin_body_font = 'Arial, sans-serif';
$admin_heading_font_weight = '700';
$admin_body_font_weight = '400';

// First try admin_fonts category (new structure)
$fonts_query = "SELECT setting_key, setting_value FROM system_settings WHERE category = 'admin_fonts'";
$fonts_result = $conn->query($fonts_query);

if ($fonts_result && $fonts_result->num_rows > 0) {
    while ($row = $fonts_result->fetch_assoc()) {
        switch ($row['setting_key']) {
            case 'admin_heading_font':
                $admin_heading_font = $row['setting_value'];
                break;
            case 'admin_body_font':
                $admin_body_font = $row['setting_value'];
                break;
            case 'admin_heading_font_weight':
                $admin_heading_font_weight = $row['setting_value'];
                break;
            case 'admin_body_font_weight':
                $admin_body_font_weight = $row['setting_value'];
                break;
        }
    }
}

// Then check fonts category (old structure)
$fonts_query = "SELECT setting_key, setting_value FROM system_settings WHERE category = 'fonts'";
$fonts_result = $conn->query($fonts_query);

if ($fonts_result && $fonts_result->num_rows > 0) {
    while ($row = $fonts_result->fetch_assoc()) {
        switch ($row['setting_key']) {
            case 'heading_font_family':
                if (empty($admin_heading_font) || $admin_heading_font == 'Arial, sans-serif') {
                    $admin_heading_font = $row['setting_value'];
                }
                break;
            case 'body_font_family':
                if (empty($admin_body_font) || $admin_body_font == 'Arial, sans-serif') {
                    $admin_body_font = $row['setting_value'];
                }
                break;
            case 'heading_font_weight':
                if (empty($admin_heading_font_weight) || $admin_heading_font_weight == '700') {
                    $admin_heading_font_weight = $row['setting_value'];
                }
                break;
            case 'body_font_weight':
                if (empty($admin_body_font_weight) || $admin_body_font_weight == '400') {
                    $admin_body_font_weight = $row['setting_value'];
                }
                break;
        }
    }
}

// Generate CSS
?>
/* Admin Settings CSS - Generated <?php echo date('Y-m-d H:i:s'); ?> */

/* Define CSS variables for light mode (default) */
:root {
    /* Brand colors from appearance settings */
    --primary-color: <?php echo $admin_primary_color; ?>;
    --secondary-color: <?php echo $admin_secondary_color; ?>;
    --success-color: <?php echo $admin_success_color; ?>;
    --warning-color: <?php echo $admin_warning_color; ?>;
    --danger-color: <?php echo $admin_danger_color; ?>;

    /* Derived colors */
    --primary-dark: <?php echo adjustBrightness($admin_primary_color, -20); ?>;
    --primary-light: <?php echo adjustBrightness($admin_primary_color, 20); ?>;
    --primary-very-light: <?php echo adjustBrightness($admin_primary_color, 40); ?>;
    --secondary-dark: <?php echo adjustBrightness($admin_secondary_color, -20); ?>;
    --secondary-light: <?php echo adjustBrightness($admin_secondary_color, 20); ?>;

    /* Base colors */
    --bg-color: #f5f5f5;
    --text-color: #333;
    --text-muted: #6c757d;
    --link-color: var(--primary-color);
    --link-hover-color: var(--primary-dark);

    /* UI elements */
    --card-bg: #fff;
    --card-border: #e0e0e0;
    --card-shadow: rgba(0, 0, 0, 0.1);
    --header-bg: #fff;
    --header-border: #e0e0e0;
    --footer-bg: #f8f9fa;
    --footer-border: #e0e0e0;

    /* Sidebar */
    --sidebar-bg: var(--secondary-color);
    --sidebar-text: #fff;
    --sidebar-hover: var(--primary-color);
    --sidebar-active: var(--primary-color);
    --sidebar-submenu: var(--secondary-dark);
    --sidebar-icon: #fff;
    --logo-bg: var(--secondary-color);

    /* Fonts */
    --heading-font: <?php echo $admin_heading_font; ?>;
    --body-font: <?php echo $admin_body_font; ?>;
    --heading-font-weight: <?php echo $admin_heading_font_weight; ?>;
    --body-font-weight: <?php echo $admin_body_font_weight; ?>;
}

/* Additional theme-specific styles */
.admin-sidebar {
    background-color: var(--sidebar-bg);
}

.admin-sidebar-menu ul li a.active {
    background-color: var(--sidebar-active);
    color: #fff;
}

.admin-sidebar-menu ul li a:hover {
    background-color: var(--sidebar-hover);
    color: #fff;
}

.admin-sidebar-header {
    background-color: var(--logo-bg);
}

/* Apply theme color to buttons */
.admin-btn.primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.admin-btn.primary:hover {
    background-color: var(--primary-dark);
    border-color: var(--primary-dark);
}

.admin-btn.secondary {
    background-color: var(--secondary-color);
    border-color: var(--secondary-color);
}

.admin-btn.secondary:hover {
    background-color: var(--secondary-dark);
    border-color: var(--secondary-dark);
}

/* Apply font settings */
body {
    font-family: var(--body-font);
    font-weight: var(--body-font-weight);
}

h1, h2, h3, h4, h5, h6,
.admin-header h2,
.admin-card-header h3,
.admin-sidebar-header,
.admin-modal-header h2 {
    font-family: var(--heading-font);
    font-weight: var(--heading-font-weight);
}

/* Admin logo path */
.sidebar-logo img {
    content: url('../<?php echo $admin_logo_path; ?>');
}

<?php
// Helper function to adjust color brightness
function adjustBrightness($hex, $steps) {
    // Steps should be between -255 and 255. Negative = darker, positive = lighter
    $steps = max(-255, min(255, $steps));

    // Format the hex color string
    $hex = str_replace('#', '', $hex);
    if (strlen($hex) == 3) {
        $hex = str_repeat(substr($hex, 0, 1), 2) . str_repeat(substr($hex, 1, 1), 2) . str_repeat(substr($hex, 2, 1), 2);
    }

    // Get decimal values
    $r = hexdec(substr($hex, 0, 2));
    $g = hexdec(substr($hex, 2, 2));
    $b = hexdec(substr($hex, 4, 2));

    // Adjust
    $r = max(0, min(255, $r + $steps));
    $g = max(0, min(255, $g + $steps));
    $b = max(0, min(255, $b + $steps));

    // Convert back to hex
    $r_hex = str_pad(dechex($r), 2, '0', STR_PAD_LEFT);
    $g_hex = str_pad(dechex($g), 2, '0', STR_PAD_LEFT);
    $b_hex = str_pad(dechex($b), 2, '0', STR_PAD_LEFT);

    return '#' . $r_hex . $g_hex . $b_hex;
}

} catch (Exception $e) {
    // Log error but don't display it
    error_log('Error in admin-settings.css.php: ' . $e->getMessage());

    // Output default CSS if there's an error
    echo "/* Error loading custom admin settings - using defaults */\n";
    echo ":root {\n";
    echo "  --primary-color: #f1ca2f;\n";
    echo "  --secondary-color: #3c3c45;\n";
    echo "  --success-color: #28a745;\n";
    echo "  --warning-color: #ffc107;\n";
    echo "  --danger-color: #dc3545;\n";
    echo "  --heading-font: 'Arial', sans-serif;\n";
    echo "  --body-font: 'Arial', sans-serif;\n";
    echo "}\n";
}
?>
