<?php
/**
 * Get Admin Appearance Settings
 *
 * This file returns the admin appearance settings from the database.
 */

// Start output buffering to prevent any output before JSON
ob_start();

// Enable error logging but disable display
error_reporting(E_ALL);
ini_set('display_errors', 0);
ini_set('log_errors', 1);

// Set proper JSON headers
header('Content-Type: application/json');
header('Cache-Control: no-store, no-cache, must-revalidate, max-age=0');
header('Cache-Control: post-check=0, pre-check=0', false);
header('Pragma: no-cache');

// Start session and check if user is logged in
session_start();
if (!isset($_SESSION['user_id'])) {
    echo json_encode([
        'success' => false,
        'message' => 'Unauthorized'
    ]);
    exit;
}

// Wrap everything in a try-catch to prevent any errors from breaking JSON output
try {
    // Include database configuration
    require_once '../config.php';

    /**
     * Get admin appearance settings from the database
     *
     * @param mysqli $conn Database connection
     * @return array Admin appearance settings
     */
    function getAdminAppearanceSettings($conn) {
    // Initialize settings array
    $settings = [];

    // Get admin_appearance settings
    $appearance_query = "SELECT setting_key, setting_value FROM system_settings WHERE category = 'admin_appearance'";
    $appearance_result = $conn->query($appearance_query);

    if ($appearance_result && $appearance_result->num_rows > 0) {
        while ($row = $appearance_result->fetch_assoc()) {
            $settings[$row['setting_key']] = $row['setting_value'];
        }
    }

    // Get admin_fonts settings
    $fonts_query = "SELECT setting_key, setting_value FROM system_settings WHERE category = 'admin_fonts'";
    $fonts_result = $conn->query($fonts_query);

    if ($fonts_result && $fonts_result->num_rows > 0) {
        while ($row = $fonts_result->fetch_assoc()) {
            $settings[$row['setting_key']] = $row['setting_value'];
        }
    }

    // Set default values for missing settings
    $default_settings = [
        'admin_primary_color' => '#f1ca2f',
        'admin_secondary_color' => '#3c3c45',
        'admin_success_color' => '#28a745',
        'admin_warning_color' => '#ffc107',
        'admin_danger_color' => '#dc3545',
        'admin_enable_dark_mode' => '0',
        'admin_logo_path' => 'images/admin-logo.png',
        'admin_heading_font' => 'Arial, sans-serif',
        'admin_body_font' => 'Arial, sans-serif',
        'admin_heading_font_weight' => '700',
        'admin_body_font_weight' => '400'
    ];

    // Merge default settings with database settings
    $settings = array_merge($default_settings, $settings);

    // If no admin settings found, try to get legacy appearance settings
    if (count($settings) <= count($default_settings)) {
        $legacy_query = "SELECT setting_key, setting_value FROM system_settings WHERE category = 'appearance'";
        $legacy_result = $conn->query($legacy_query);

        if ($legacy_result && $legacy_result->num_rows > 0) {
            while ($row = $legacy_result->fetch_assoc()) {
                // Map legacy settings to new settings
                switch ($row['setting_key']) {
                    case 'primary_color':
                        $settings['admin_primary_color'] = $row['setting_value'];
                        break;
                    case 'secondary_color':
                        $settings['admin_secondary_color'] = $row['setting_value'];
                        break;
                    case 'success_color':
                        $settings['admin_success_color'] = $row['setting_value'];
                        break;
                    case 'warning_color':
                        $settings['admin_warning_color'] = $row['setting_value'];
                        break;
                    case 'danger_color':
                        $settings['admin_danger_color'] = $row['setting_value'];
                        break;
                    case 'enable_dark_mode':
                        $settings['admin_enable_dark_mode'] = $row['setting_value'];
                        break;
                    case 'logo_path':
                        $settings['admin_logo_path'] = $row['setting_value'];
                        break;
                    default:
                        // Include legacy setting as-is
                        $settings[$row['setting_key']] = $row['setting_value'];
                        break;
                }
            }
        }

        // Get legacy font settings
        $legacy_fonts_query = "SELECT setting_key, setting_value FROM system_settings WHERE category = 'fonts'";
        $legacy_fonts_result = $conn->query($legacy_fonts_query);

        if ($legacy_fonts_result && $legacy_fonts_result->num_rows > 0) {
            while ($row = $legacy_fonts_result->fetch_assoc()) {
                // Map legacy font settings to new settings
                switch ($row['setting_key']) {
                    case 'heading_font':
                        $settings['admin_heading_font'] = $row['setting_value'];
                        break;
                    case 'body_font':
                        $settings['admin_body_font'] = $row['setting_value'];
                        break;
                    case 'heading_font_weight':
                        $settings['admin_heading_font_weight'] = $row['setting_value'];
                        break;
                    case 'body_font_weight':
                        $settings['admin_body_font_weight'] = $row['setting_value'];
                        break;
                    default:
                        // Include legacy setting as-is
                        $settings[$row['setting_key']] = $row['setting_value'];
                        break;
                }
            }
        }
    }

    return $settings;
}

    // Get admin appearance settings
    $settings = getAdminAppearanceSettings($conn);

    // Return settings as JSON
    echo json_encode([
        'success' => true,
        'settings' => $settings
    ], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);

} catch (Exception $e) {
    // Log the error
    error_log('Error in get_admin_appearance_settings.php: ' . $e->getMessage());

    // Return a generic error message with default settings
    echo json_encode([
        'success' => false,
        'message' => 'An unexpected error occurred',
        'settings' => [
            'admin_primary_color' => '#f1ca2f',
            'admin_secondary_color' => '#3c3c45',
            'admin_success_color' => '#28a745',
            'admin_warning_color' => '#ffc107',
            'admin_danger_color' => '#dc3545',
            'admin_enable_dark_mode' => '0',
            'admin_logo_path' => 'images/admin-logo.png',
            'admin_heading_font' => 'Arial, sans-serif',
            'admin_body_font' => 'Arial, sans-serif'
        ]
    ], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
}

// Check for JSON encoding errors
if (json_last_error() !== JSON_ERROR_NONE) {
    // Clear any previous output
    ob_clean();

    // Return a simplified error response
    echo json_encode([
        'success' => false,
        'message' => 'JSON encoding error: ' . json_last_error_msg(),
        'settings' => [
            'admin_primary_color' => '#f1ca2f',
            'admin_secondary_color' => '#3c3c45',
            'admin_success_color' => '#28a745',
            'admin_warning_color' => '#ffc107',
            'admin_danger_color' => '#dc3545',
            'admin_enable_dark_mode' => '0',
            'admin_logo_path' => 'images/admin-logo.png',
            'admin_heading_font' => 'Arial, sans-serif',
            'admin_body_font' => 'Arial, sans-serif'
        ]
    ], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
}

// End output buffering and flush
ob_end_flush();
exit;
?>
