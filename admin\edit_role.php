<?php
session_start();
require_once 'config.php';
require_once 'lib/Permissions.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    redirect('index.php');
}

// Initialize permission system
$permissions = new Permissions($conn, $_SESSION['user_id']);

// Check if user has permission to manage roles
if (!$permissions->hasPermission('manage_roles')) {
    $_SESSION['error_message'] = "You do not have permission to manage roles.";
    redirect('dashboard.php');
}

// Check if role ID is provided
if (!isset($_GET['id']) || empty($_GET['id'])) {
    $_SESSION['error_message'] = "No role specified.";
    redirect('roles.php');
}

$role_id = (int)$_GET['id'];

// Get role information
$role_sql = "SELECT * FROM roles WHERE id = ?";
$role_stmt = $conn->prepare($role_sql);
$role_stmt->bind_param("i", $role_id);
$role_stmt->execute();
$role_result = $role_stmt->get_result();

if (!$role_result || $role_result->num_rows === 0) {
    $_SESSION['error_message'] = "Role not found.";
    redirect('roles.php');
}

$role = $role_result->fetch_assoc();

// Get role permissions
$role_permissions_sql = "SELECT permission_id FROM role_permissions WHERE role_id = ?";
$role_permissions_stmt = $conn->prepare($role_permissions_sql);
$role_permissions_stmt->bind_param("i", $role_id);
$role_permissions_stmt->execute();
$role_permissions_result = $role_permissions_stmt->get_result();

$role_permissions = [];
while ($row = $role_permissions_result->fetch_assoc()) {
    $role_permissions[] = $row['permission_id'];
}

// Process form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Update Role
    if (isset($_POST['update_role'])) {
        $role_name = trim($_POST['role_name']);
        $role_description = trim($_POST['role_description']);
        $selected_permissions = isset($_POST['permissions']) ? $_POST['permissions'] : [];

        // Validate input
        if (empty($role_name)) {
            $error_message = "Role name is required.";
        } else {
            // Check if role name already exists (excluding current role)
            $stmt = $conn->prepare("SELECT id FROM roles WHERE name = ? AND id != ?");
            $stmt->bind_param("si", $role_name, $role_id);
            $stmt->execute();
            $result = $stmt->get_result();

            if ($result->num_rows > 0) {
                $error_message = "A role with this name already exists.";
            }

            if (!isset($error_message)) {
                // Begin transaction
                $conn->begin_transaction();

                try {
                    // Update role
                    $stmt = $conn->prepare("UPDATE roles SET name = ?, description = ? WHERE id = ?");
                    $stmt->bind_param("ssi", $role_name, $role_description, $role_id);
                    $stmt->execute();

                    // Delete existing permissions
                    $delete_stmt = $conn->prepare("DELETE FROM role_permissions WHERE role_id = ?");
                    $delete_stmt->bind_param("i", $role_id);
                    $delete_stmt->execute();

                    // Add selected permissions
                    if (!empty($selected_permissions)) {
                        $stmt = $conn->prepare("INSERT INTO role_permissions (role_id, permission_id) VALUES (?, ?)");

                        foreach ($selected_permissions as $permission_id) {
                            $stmt->bind_param("ii", $role_id, $permission_id);
                            $stmt->execute();
                        }
                    }

                    // Commit transaction
                    $conn->commit();

                    $_SESSION['success_message'] = "Role updated successfully.";
                    redirect('roles.php');
                } catch (Exception $e) {
                    // Rollback transaction on error
                    $conn->rollback();
                    $error_message = "Error updating role: " . $e->getMessage();
                }
            }
        }
    }
}

// Get all permissions
$permissions_result = $conn->query("SELECT * FROM permissions ORDER BY category, name");
$all_permissions = [];
$permission_categories = [];

while ($row = $permissions_result->fetch_assoc()) {
    $all_permissions[] = $row;

    if (!isset($permission_categories[$row['category']])) {
        $permission_categories[$row['category']] = [];
    }

    $permission_categories[$row['category']][] = $row;
}

// Set page title
$page_title = "Edit Role";

// Add page-specific CSS
$extra_css = '<link rel="stylesheet" href="assets/css/pages/roles.css?v=' . time() . '">';

include 'includes/header.php';
?>

<div class="admin-container">
    <?php
    // Set up variables for enhanced header
    $page_icon = 'fas fa-edit';
    $page_subtitle = 'Edit role and manage permissions';
    $back_link = ['url' => 'roles.php', 'text' => 'Back to Roles'];

    // Include the content header
    include 'includes/content-header.php';
    ?>

    <?php if (isset($error_message)): ?>
        <div class="admin-alert error">
            <i class="fas fa-exclamation-circle"></i> <?php echo $error_message; ?>
        </div>
    <?php endif; ?>

    <form method="post" action="edit_role.php?id=<?php echo $role_id; ?>">
        <div class="admin-content-layout">
            <div class="admin-content-main">
                <div class="admin-card">
                    <div class="admin-card-header">
                        <h3>
                            <i class="fas fa-user-tag"></i>
                            Edit Role
                        </h3>
                    </div>
                    <div class="admin-card-body">
                        <div class="form-group">
                            <label for="role_name">Role Name</label>
                            <input type="text" id="role_name" name="role_name" class="form-control" 
                                value="<?php echo isset($_POST['role_name']) ? htmlspecialchars($_POST['role_name']) : htmlspecialchars($role['name']); ?>" required>
                        </div>

                        <div class="form-group">
                            <label for="role_description">Description</label>
                            <textarea id="role_description" name="role_description" class="form-control" rows="3"><?php echo isset($_POST['role_description']) ? htmlspecialchars($_POST['role_description']) : htmlspecialchars($role['description']); ?></textarea>
                        </div>

                        <div class="form-group">
                            <label>Permissions</label>

                            <?php foreach ($permission_categories as $category => $category_permissions): ?>
                            <div class="permission-category">
                                <h4><?php echo htmlspecialchars($category); ?></h4>
                                <div class="permission-category-content">
                                    <?php foreach ($category_permissions as $permission): ?>
                                    <div class="permission-item">
                                        <label class="custom-checkbox">
                                            <input type="checkbox" name="permissions[]" value="<?php echo $permission['id']; ?>" 
                                                <?php echo (isset($_POST['permissions']) && in_array($permission['id'], $_POST['permissions'])) || 
                                                    (!isset($_POST['permissions']) && in_array($permission['id'], $role_permissions)) ? 'checked' : ''; ?>>
                                            <span class="checkmark"></span>
                                            <?php echo htmlspecialchars($permission['name']); ?>
                                        </label>
                                        <span class="permission-description"><?php echo htmlspecialchars($permission['description']); ?></span>
                                    </div>
                                    <?php endforeach; ?>
                                </div>
                            </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                </div>
            </div>
            <div class="admin-content-sidebar">
                <div class="admin-card">
                    <div class="admin-card-header">
                        <h3><i class="fas fa-save"></i> Save</h3>
                    </div>
                    <div class="admin-card-body">
                        <div class="form-actions">
                            <button type="submit" name="update_role" value="1" class="admin-btn success">
                                <i class="fas fa-save"></i> Update Role
                            </button>
                            <a href="roles.php" class="admin-btn outline">
                                <i class="fas fa-times"></i> Cancel
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>

<?php include 'includes/footer.php'; ?>
