/**
 * Admin Top Actions CSS
 * 
 * This file contains styles for the top actions bar in the admin panel.
 */

/* Top Actions Container */
.admin-top-actions {
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-wrap: wrap;
  gap: var(--spacing-3);
  margin-bottom: var(--spacing-4);
  padding: var(--spacing-3) var(--spacing-4);
  background-color: var(--white);
  border-radius: var(--radius-md);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--border-color);
}

/* Top Actions Left */
.admin-top-actions-left {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
  flex-wrap: wrap;
}

/* Top Actions Right */
.admin-top-actions-right {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
  flex-wrap: wrap;
}

/* Top Actions Title */
.admin-top-actions-title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--text-dark);
  margin: 0;
}

/* Top Actions Subtitle */
.admin-top-actions-subtitle {
  font-size: var(--font-size-sm);
  color: var(--text-light);
  margin: 0;
}

/* Top Actions Button Group */
.admin-top-actions-buttons {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
}

/* Top Actions Search */
.admin-top-actions-search {
  position: relative;
  width: 250px;
}

.admin-top-actions-search-input {
  width: 100%;
  height: 36px;
  padding: var(--spacing-2) var(--spacing-2) var(--spacing-2) var(--spacing-8);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-full);
  background-color: var(--white);
  color: var(--text-color);
  font-size: var(--font-size-sm);
  transition: all var(--transition-fast) ease;
}

.admin-top-actions-search-input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(241, 202, 47, 0.15);
}

.admin-top-actions-search-button {
  position: absolute;
  left: 0;
  top: 0;
  height: 36px;
  width: 36px;
  background: transparent;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-muted);
  cursor: pointer;
  transition: color var(--transition-fast) ease;
}

.admin-top-actions-search-button:hover {
  color: var(--text-color);
}

/* Top Actions Filter */
.admin-top-actions-filter {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
}

.admin-top-actions-filter-label {
  font-size: var(--font-size-sm);
  color: var(--text-color);
}

.admin-top-actions-filter-select {
  height: 36px;
  padding: var(--spacing-1) var(--spacing-3);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  background-color: var(--white);
  color: var(--text-color);
  font-size: var(--font-size-sm);
  min-width: 150px;
}

/* Top Actions Pagination */
.admin-top-actions-pagination {
  display: flex;
  align-items: center;
  gap: var(--spacing-1);
}

.admin-top-actions-pagination-info {
  font-size: var(--font-size-sm);
  color: var(--text-light);
  margin-right: var(--spacing-2);
}

.admin-top-actions-pagination-button {
  width: 32px;
  height: 32px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border-radius: var(--radius-md);
  background-color: var(--white);
  color: var(--text-color);
  border: 1px solid var(--border-color);
  cursor: pointer;
  transition: all var(--transition-fast) ease;
}

.admin-top-actions-pagination-button:hover {
  background-color: var(--gray-100);
  color: var(--text-dark);
}

.admin-top-actions-pagination-button.active {
  background-color: var(--primary-color);
  color: var(--secondary-dark);
  border-color: var(--primary-color);
}

/* Top Actions with Tabs */
.admin-top-actions-tabs {
  display: flex;
  align-items: center;
  gap: var(--spacing-1);
  margin-top: var(--spacing-3);
  border-bottom: 1px solid var(--border-color);
  width: 100%;
}

.admin-top-actions-tab {
  padding: var(--spacing-2) var(--spacing-4);
  font-size: var(--font-size-sm);
  color: var(--text-light);
  border-bottom: 2px solid transparent;
  cursor: pointer;
  transition: all var(--transition-fast) ease;
}

.admin-top-actions-tab:hover {
  color: var(--text-dark);
}

.admin-top-actions-tab.active {
  color: var(--primary-color);
  border-bottom-color: var(--primary-color);
  font-weight: var(--font-weight-medium);
}

/* Top Actions with Breadcrumb */
.admin-top-actions-breadcrumb {
  margin-bottom: var(--spacing-3);
}

/* Responsive Styles */
@media (max-width: 992px) {
  .admin-top-actions-search {
    width: 200px;
  }
}

@media (max-width: 768px) {
  .admin-top-actions {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .admin-top-actions-left,
  .admin-top-actions-right {
    width: 100%;
    justify-content: space-between;
  }
  
  .admin-top-actions-search {
    width: 100%;
    max-width: 100%;
  }
  
  .admin-top-actions-tabs {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
    padding-bottom: var(--spacing-1);
  }
  
  .admin-top-actions-tab {
    white-space: nowrap;
  }
}
