/**
 * Simple Editor CSS
 * Styles for the SimpleEditor component
 */

/* Editor Container */
.simple-editor-container {
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  overflow: hidden;
  margin-bottom: 1rem;
  background-color: #fff;
}

/* Editor Toolbar Container */
.simple-editor-toolbar-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem;
  background-color: #f8f9fa;
  border-bottom: 1px solid var(--border-color);
  flex-wrap: wrap;
  gap: 0.5rem;
}

/* Editor Toolbar */
.simple-editor-toolbar {
  display: flex;
  flex-wrap: wrap;
  gap: 0.25rem;
}

.simple-editor-toolbar-group {
  display: flex;
  margin-right: 0.5rem;
  border-right: 1px solid var(--border-color);
  padding-right: 0.5rem;
}

.simple-editor-toolbar-group:last-child {
  border-right: none;
}

.simple-editor-toolbar-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 2rem;
  height: 2rem;
  background-color: transparent;
  border: 1px solid transparent;
  border-radius: var(--radius-sm);
  color: var(--text-dark);
  cursor: pointer;
  transition: all 0.2s ease;
}

.simple-editor-toolbar-button:hover {
  background-color: rgba(0, 0, 0, 0.05);
  border-color: var(--border-color);
}

.simple-editor-toolbar-button.active {
  background-color: rgba(0, 0, 0, 0.1);
  border-color: var(--border-color);
}

/* Editor Content */
.simple-editor-content {
  padding: 1rem;
  min-height: 200px;
  max-height: 600px;
  overflow-y: auto;
  outline: none;
  line-height: 1.5;
}

/* HTML View */
.simple-editor-html-view {
  display: none;
  padding: 0;
  border-top: 1px solid var(--border-color);
}

.simple-editor-html-view textarea {
  width: 100%;
  min-height: 300px;
  padding: 1rem;
  border: none;
  resize: vertical;
  font-family: monospace;
  line-height: 1.5;
  outline: none;
}

/* Mode Toggle */
.editor-mode-toggle {
  display: flex;
  border: 1px solid var(--border-color);
  border-radius: var(--radius-sm);
  overflow: hidden;
}

.editor-mode-btn {
  padding: 0.5rem 1rem;
  background-color: #fff;
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  transition: all 0.2s ease;
}

.editor-mode-btn:first-child {
  border-right: 1px solid var(--border-color);
}

.editor-mode-btn.active {
  background-color: var(--primary-color);
  color: #fff;
}

.editor-mode-btn:not(.active):hover {
  background-color: rgba(0, 0, 0, 0.05);
}

/* Variables Dropdown */
.variables-dropdown {
  position: relative;
}

.variables-dropdown-btn {
  padding: 0.5rem 1rem;
  background-color: #fff;
  border: 1px solid var(--border-color);
  border-radius: var(--radius-sm);
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  transition: all 0.2s ease;
}

.variables-dropdown-btn:hover {
  background-color: rgba(0, 0, 0, 0.05);
}

.variables-dropdown-content {
  position: absolute;
  top: 100%;
  right: 0;
  width: 250px;
  background-color: #fff;
  border: 1px solid var(--border-color);
  border-radius: var(--radius-sm);
  box-shadow: var(--shadow-md);
  z-index: 10;
  display: none;
  padding: 0.5rem;
  max-height: 300px;
  overflow-y: auto;
}

.variables-dropdown-content.show {
  display: block;
}

.variables-list {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.variables-group {
  margin-bottom: 1rem;
}

.variables-group-title {
  font-weight: bold;
  margin-bottom: 0.5rem;
  padding-bottom: 0.25rem;
  border-bottom: 1px solid var(--border-color);
}

.template-variable {
  padding: 0.5rem;
  background-color: #f8f9fa;
  border-radius: var(--radius-sm);
  cursor: pointer;
  transition: all 0.2s ease;
}

.template-variable:hover {
  background-color: var(--primary-color);
  color: #fff;
}

.template-variable.clicked {
  background-color: var(--success-color);
  color: #fff;
}

/* Responsive styles */
@media (max-width: 768px) {
  .simple-editor-toolbar-container {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .editor-mode-toggle {
    width: 100%;
    margin-bottom: 0.5rem;
  }
  
  .editor-mode-btn {
    flex: 1;
    justify-content: center;
  }
  
  .variables-dropdown {
    width: 100%;
  }
  
  .variables-dropdown-btn {
    width: 100%;
    justify-content: center;
  }
  
  .variables-dropdown-content {
    width: 100%;
    left: 0;
    right: auto;
  }
}
