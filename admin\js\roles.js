/**
 * Roles JavaScript
 *
 * Handles functionality for the role management page
 */

document.addEventListener('DOMContentLoaded', function() {
    // Initialize collapsible permission categories
    initPermissionCategories();

    // Initialize category toggle buttons
    initCategoryToggles();

    // Initialize search functionality
    initPermissionSearch();
});

/**
 * Initialize collapsible permission categories
 */
function initPermissionCategories() {
    const categories = document.querySelectorAll('.permission-category');

    categories.forEach(category => {
        const header = category.querySelector('h4');
        const content = category.querySelector('.permission-category-content');

        if (header && content) {
            // Add toggle icon
            const toggleIcon = document.createElement('i');
            toggleIcon.className = 'fas fa-chevron-down toggle-icon';
            header.appendChild(toggleIcon);

            // Add collapsed class by default
            category.classList.add('collapsed');

            // Add click event
            header.addEventListener('click', () => {
                category.classList.toggle('collapsed');
            });
        }
    });
}

/**
 * Initialize category toggle buttons
 */
function initCategoryToggles() {
    // Create toggle container if it doesn't exist
    let toggleContainer = document.querySelector('.category-toggle');

    if (!toggleContainer) {
        // Find the permissions label using a more reliable method
        const permissionsLabels = document.querySelectorAll('.form-group label');
        let permissionsLabel = null;

        permissionsLabels.forEach(label => {
            if (label.textContent.trim() === 'Permissions') {
                permissionsLabel = label;
            }
        });

        if (permissionsLabel) {
            toggleContainer = document.createElement('div');
            toggleContainer.className = 'category-toggle';

            // Insert after the label
            permissionsLabel.parentNode.insertBefore(toggleContainer, permissionsLabel.nextSibling);

            // Create toggle buttons
            const expandAllBtn = document.createElement('button');
            expandAllBtn.type = 'button';
            expandAllBtn.innerHTML = '<i class="fas fa-expand-alt"></i> Expand All';
            expandAllBtn.addEventListener('click', expandAllCategories);

            const collapseAllBtn = document.createElement('button');
            collapseAllBtn.type = 'button';
            collapseAllBtn.innerHTML = '<i class="fas fa-compress-alt"></i> Collapse All';
            collapseAllBtn.addEventListener('click', collapseAllCategories);

            const selectAllBtn = document.createElement('button');
            selectAllBtn.type = 'button';
            selectAllBtn.innerHTML = '<i class="fas fa-check-square"></i> Select All';
            selectAllBtn.addEventListener('click', selectAllPermissions);

            const deselectAllBtn = document.createElement('button');
            deselectAllBtn.type = 'button';
            deselectAllBtn.innerHTML = '<i class="fas fa-square"></i> Deselect All';
            deselectAllBtn.addEventListener('click', deselectAllPermissions);

            toggleContainer.appendChild(expandAllBtn);
            toggleContainer.appendChild(collapseAllBtn);
            toggleContainer.appendChild(selectAllBtn);
            toggleContainer.appendChild(deselectAllBtn);
        }
    }
}

/**
 * Initialize permission search functionality
 */
function initPermissionSearch() {
    // Find the permissions label using a more reliable method
    const permissionsLabels = document.querySelectorAll('.form-group label');
    let permissionsLabel = null;

    permissionsLabels.forEach(label => {
        if (label.textContent.trim() === 'Permissions') {
            permissionsLabel = label;
        }
    });

    if (permissionsLabel) {
        // Create search input
        const searchContainer = document.createElement('div');
        searchContainer.className = 'permission-search';
        searchContainer.style.marginBottom = '15px';

        const searchInput = document.createElement('input');
        searchInput.type = 'text';
        searchInput.className = 'form-control';
        searchInput.placeholder = 'Search permissions...';
        searchInput.style.padding = '8px 12px';
        searchInput.style.fontSize = '14px';

        searchContainer.appendChild(searchInput);

        // Insert after the label
        permissionsLabel.parentNode.insertBefore(searchContainer, permissionsLabel.nextSibling);

        // Add search functionality
        searchInput.addEventListener('input', function() {
            const searchTerm = this.value.toLowerCase();
            const permissionItems = document.querySelectorAll('.permission-item');

            permissionItems.forEach(item => {
                const permissionName = item.querySelector('label').textContent.toLowerCase();
                const permissionDesc = item.querySelector('.permission-description').textContent.toLowerCase();

                if (permissionName.includes(searchTerm) || permissionDesc.includes(searchTerm)) {
                    item.style.display = '';

                    // Expand parent category
                    const category = item.closest('.permission-category');
                    if (category) {
                        category.classList.remove('collapsed');
                    }
                } else {
                    item.style.display = 'none';
                }
            });

            // Hide empty categories
            const categories = document.querySelectorAll('.permission-category');
            categories.forEach(category => {
                const visibleItems = category.querySelectorAll('.permission-item[style="display: none;"]');
                const totalItems = category.querySelectorAll('.permission-item').length;

                if (visibleItems.length === totalItems) {
                    category.style.display = 'none';
                } else {
                    category.style.display = '';
                }
            });
        });
    }
}

/**
 * Expand all permission categories
 */
function expandAllCategories() {
    document.querySelectorAll('.permission-category').forEach(category => {
        category.classList.remove('collapsed');
    });
}

/**
 * Collapse all permission categories
 */
function collapseAllCategories() {
    document.querySelectorAll('.permission-category').forEach(category => {
        category.classList.add('collapsed');
    });
}

/**
 * Select all permissions
 */
function selectAllPermissions() {
    document.querySelectorAll('input[name="permissions[]"]').forEach(checkbox => {
        checkbox.checked = true;
    });
}

/**
 * Deselect all permissions
 */
function deselectAllPermissions() {
    document.querySelectorAll('input[name="permissions[]"]').forEach(checkbox => {
        checkbox.checked = false;
    });
}
