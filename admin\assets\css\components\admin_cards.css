/**
 * Admin Cards CSS
 *
 * This file contains card styles for the admin panel.
 */

/* Base Card */
.card {
  position: relative;
  display: flex;
  flex-direction: column;
  min-width: 0;
  word-wrap: break-word;
  background-color: var(--white);
  background-clip: border-box;
  border: 1px solid var(--border-color);
  border-radius: var(--card-border-radius);
  box-shadow: var(--shadow-sm);
  transition: box-shadow var(--transition-fast) ease, transform var(--transition-fast) ease;
  overflow: hidden;
}

.card:hover {
  box-shadow: var(--shadow-md);
}

/* Card Header */
.card-header {
  padding: var(--spacing-4);
  margin-bottom: 0;
  background-color: var(--white);
  border-bottom: 1px solid var(--border-color);
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-wrap: wrap;
  gap: var(--spacing-2);
}

.card-header:first-child {
  border-radius: calc(var(--card-border-radius) - 1px) calc(var(--card-border-radius) - 1px) 0 0;
}

.card-header-title {
  margin: 0;
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--text-dark);
}

.card-header-subtitle {
  margin: var(--spacing-1) 0 0;
  font-size: var(--font-size-sm);
  color: var(--text-light);
}

.card-header-actions {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
}

/* Card Body */
.card-body {
  flex: 1 1 auto;
  padding: var(--spacing-4);
}

/* Card Footer */
.card-footer {
  padding: var(--spacing-4);
  background-color: var(--white);
  border-top: 1px solid var(--border-color);
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-wrap: wrap;
  gap: var(--spacing-2);
}

.card-footer:last-child {
  border-radius: 0 0 calc(var(--card-border-radius) - 1px) calc(var(--card-border-radius) - 1px);
}

/* Card Image */
.card-img,
.card-img-top,
.card-img-bottom {
  width: 100%;
}

.card-img,
.card-img-top {
  border-top-left-radius: calc(var(--card-border-radius) - 1px);
  border-top-right-radius: calc(var(--card-border-radius) - 1px);
}

.card-img,
.card-img-bottom {
  border-bottom-right-radius: calc(var(--card-border-radius) - 1px);
  border-bottom-left-radius: calc(var(--card-border-radius) - 1px);
}

/* Card Title and Text */
.card-title {
  margin-bottom: var(--spacing-2);
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--text-dark);
}

.card-subtitle {
  margin-top: calc(-1 * var(--spacing-1));
  margin-bottom: var(--spacing-2);
  font-size: var(--font-size-sm);
  color: var(--text-light);
}

.card-text {
  margin-bottom: var(--spacing-3);
  font-size: var(--font-size-sm);
  color: var(--text-color);
}

.card-text:last-child {
  margin-bottom: 0;
}

/* Card Link */
.card-link {
  color: var(--primary-color);
  text-decoration: none;
  transition: color var(--transition-fast) ease;
}

.card-link:hover {
  color: var(--primary-dark);
  text-decoration: underline;
}

.card-link + .card-link {
  margin-left: var(--spacing-3);
}

/* Card Variants */
.card-primary {
  border-color: var(--primary-color);
}

.card-primary .card-header {
  background-color: var(--primary-color);
  color: var(--secondary-dark);
  border-color: var(--primary-color);
}

.card-primary .card-header-title,
.card-primary .card-header-subtitle {
  color: var(--secondary-dark);
}

.card-secondary {
  border-color: var(--secondary-color);
}

.card-secondary .card-header {
  background-color: var(--secondary-color);
  color: var(--white);
  border-color: var(--secondary-color);
}

.card-secondary .card-header-title,
.card-secondary .card-header-subtitle {
  color: var(--white);
}

.card-success {
  border-color: var(--success-color);
}

.card-success .card-header {
  background-color: var(--success-color);
  color: var(--white);
  border-color: var(--success-color);
}

.card-success .card-header-title,
.card-success .card-header-subtitle {
  color: var(--white);
}

.card-danger {
  border-color: var(--danger-color);
}

.card-danger .card-header {
  background-color: var(--danger-color);
  color: var(--white);
  border-color: var(--danger-color);
}

.card-danger .card-header-title,
.card-danger .card-header-subtitle {
  color: var(--white);
}

.card-warning {
  border-color: var(--warning-color);
}

.card-warning .card-header {
  background-color: var(--warning-color);
  color: var(--secondary-dark);
  border-color: var(--warning-color);
}

.card-warning .card-header-title,
.card-warning .card-header-subtitle {
  color: var(--secondary-dark);
}

.card-info {
  border-color: var(--info-color);
}

.card-info .card-header {
  background-color: var(--info-color);
  color: var(--white);
  border-color: var(--info-color);
}

.card-info .card-header-title,
.card-info .card-header-subtitle {
  color: var(--white);
}

/* Card with Border Left */
.card-border-left {
  border-left-width: 4px;
}

.card-border-left-primary {
  border-left-color: var(--primary-color);
}

.card-border-left-secondary {
  border-left-color: var(--secondary-color);
}

.card-border-left-success {
  border-left-color: var(--success-color);
}

.card-border-left-danger {
  border-left-color: var(--danger-color);
}

.card-border-left-warning {
  border-left-color: var(--warning-color);
}

.card-border-left-info {
  border-left-color: var(--info-color);
}

/* Card with Icon */
.card-icon {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
}

.card-icon-wrapper {
  width: 48px;
  height: 48px;
  border-radius: var(--radius-md);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--font-size-xl);
  flex-shrink: 0;
}

.card-icon-wrapper.primary {
  background-color: var(--primary-very-light);
  color: var(--primary-color);
}

.card-icon-wrapper.secondary {
  background-color: rgba(44, 62, 80, 0.1);
  color: var(--secondary-color);
}

.card-icon-wrapper.success {
  background-color: rgba(16, 185, 129, 0.1);
  color: var(--success-color);
}

.card-icon-wrapper.danger {
  background-color: rgba(239, 68, 68, 0.1);
  color: var(--danger-color);
}

.card-icon-wrapper.warning {
  background-color: rgba(245, 158, 11, 0.1);
  color: var(--warning-color);
}

.card-icon-wrapper.info {
  background-color: rgba(59, 130, 246, 0.1);
  color: var(--info-color);
}

.card-icon-content {
  flex: 1;
}

/* Card Grid */
.card-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: var(--spacing-4);
}

/* Card Deck */
.card-deck {
  display: flex;
  flex-flow: row wrap;
  margin-right: calc(-1 * var(--spacing-3));
  margin-left: calc(-1 * var(--spacing-3));
}

.card-deck .card {
  flex: 1 0 0%;
  margin-right: var(--spacing-3);
  margin-bottom: var(--spacing-4);
  margin-left: var(--spacing-3);
}

/* Card Group */
.card-group {
  display: flex;
  flex-flow: row wrap;
}

.card-group > .card {
  flex: 1 0 0%;
  margin-bottom: 0;
}

.card-group > .card + .card {
  margin-left: 0;
  border-left: 0;
}

.card-group > .card:not(:last-child) {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}

.card-group > .card:not(:first-child) {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}

/* Card Columns */
.card-columns {
  column-count: 3;
  column-gap: var(--spacing-4);
  orphans: 1;
  widows: 1;
}

.card-columns .card {
  display: inline-block;
  width: 100%;
  margin-bottom: var(--spacing-4);
}

/* Responsive */
@media (max-width: 992px) {
  .card-columns {
    column-count: 2;
  }

  .card-grid {
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  }

  .card-deck {
    flex-direction: column;
  }

  .card-deck .card {
    margin-right: 0;
    margin-left: 0;
    margin-bottom: var(--spacing-4);
  }

  .card-group {
    flex-direction: column;
  }

  .card-group > .card {
    margin-bottom: var(--spacing-4);
  }

  .card-group > .card:not(:last-child) {
    border-bottom-left-radius: var(--card-border-radius);
    border-bottom-right-radius: var(--card-border-radius);
    border-top-right-radius: var(--card-border-radius);
  }

  .card-group > .card:not(:first-child) {
    border-top-left-radius: var(--card-border-radius);
    border-top-right-radius: var(--card-border-radius);
    border-left: 1px solid var(--border-color);
  }
}

@media (max-width: 768px) {
  /* Card structure */
  .card {
    width: 100% !important;
  }

  .card-header,
  .card-footer {
    flex-direction: column;
    align-items: flex-start;
    padding: var(--spacing-3) !important;
  }

  .card-body {
    padding: var(--spacing-3) !important;
  }

  .card-header-actions,
  .card-footer-actions {
    margin-top: var(--spacing-2);
    width: 100%;
    justify-content: flex-start !important;
  }

  .card-header-title {
    width: 100%;
    font-size: var(--font-size-base) !important;
  }

  /* Card layouts */
  .card-columns {
    column-count: 1;
  }

  .card-grid {
    grid-template-columns: 1fr;
    gap: var(--spacing-3);
  }

  /* Card with icon */
  .card-icon {
    flex-direction: column;
    align-items: flex-start;
  }

  .card-icon-wrapper {
    margin-bottom: var(--spacing-2);
  }

  /* Quick action cards */
  .quick-action-card {
    width: 100% !important;
    margin-bottom: var(--spacing-3) !important;
  }
}

@media (max-width: 480px) {
  .card-title {
    font-size: var(--font-size-base) !important;
  }

  .card-text {
    font-size: var(--font-size-xs) !important;
  }

  .card-header-actions .admin-btn,
  .card-footer-actions .admin-btn {
    padding: 6px 12px !important;
    font-size: var(--font-size-xs) !important;
  }
}
