<?php
/**
 * Debug Contact Form Processing
 * This script helps debug contact form issues
 */

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

echo "<h1>🔍 Contact Form Debug Tool</h1>";

// Include necessary files
try {
    require_once 'admin/config.php';
    echo "<p>✅ Config loaded successfully</p>";
} catch (Exception $e) {
    echo "<p>❌ Config failed: " . $e->getMessage() . "</p>";
    exit;
}

try {
    require_once 'admin/includes/email-functions.php';
    echo "<p>✅ Email functions loaded successfully</p>";
} catch (Exception $e) {
    echo "<p>❌ Email functions failed: " . $e->getMessage() . "</p>";
    exit;
}

try {
    require_once 'admin/lib/Notifications.php';
    echo "<p>✅ Notifications class loaded successfully</p>";
} catch (Exception $e) {
    echo "<p>❌ Notifications class failed: " . $e->getMessage() . "</p>";
}

// Test database connection
if ($conn) {
    echo "<p>✅ Database connection successful</p>";
} else {
    echo "<p>❌ Database connection failed</p>";
    exit;
}

// Test email settings
echo "<h2>📧 Email Settings</h2>";
$settings = get_email_settings();
echo "<pre>";
print_r($settings);
echo "</pre>";

// Test basic email functionality
echo "<h2>🧪 Test Email Functionality</h2>";

if (isset($_POST['test_contact_form'])) {
    echo "<h3>Processing Test Contact Form...</h3>";
    
    $name = $_POST['name'] ?? 'Test User';
    $email = $_POST['email'] ?? '<EMAIL>';
    $phone = $_POST['phone'] ?? '';
    $message = $_POST['message'] ?? 'This is a test message';
    $source = 'Debug Test';
    
    echo "<p><strong>Form Data:</strong></p>";
    echo "<ul>";
    echo "<li>Name: " . htmlspecialchars($name) . "</li>";
    echo "<li>Email: " . htmlspecialchars($email) . "</li>";
    echo "<li>Phone: " . htmlspecialchars($phone) . "</li>";
    echo "<li>Message: " . htmlspecialchars($message) . "</li>";
    echo "<li>Source: " . htmlspecialchars($source) . "</li>";
    echo "</ul>";
    
    // Test email recipient determination
    $recipient = get_setting('admin_email', '');
    if (empty($recipient)) {
        $recipient = $settings['from_email'];
        echo "<p>⚠️ Admin email not set, using from_email as recipient: " . $recipient . "</p>";
    }
    
    if (empty($recipient)) {
        $recipient = '<EMAIL>';
        echo "<p>⚠️ No admin_email or from_email found, using default recipient: " . $recipient . "</p>";
    }
    
    echo "<p><strong>Email will be sent to:</strong> " . htmlspecialchars($recipient) . "</p>";
    
    // Test email content creation
    $subject = "Contact Form Submission from " . $name;
    $htmlMessage = "
    <html>
    <head><title>Contact Form Submission</title></head>
    <body>
        <h2>New Contact Form Submission</h2>
        <p>You have received a new message from your website contact form.</p>
        <table>
            <tr><th>Name:</th><td>" . htmlspecialchars($name) . "</td></tr>
            <tr><th>Email:</th><td>" . htmlspecialchars($email) . "</td></tr>
            <tr><th>Phone:</th><td>" . htmlspecialchars($phone) . "</td></tr>
            <tr><th>Message:</th><td>" . nl2br(htmlspecialchars($message)) . "</td></tr>
            <tr><th>Source:</th><td>" . htmlspecialchars($source) . "</td></tr>
            <tr><th>Date:</th><td>" . date('Y-m-d H:i:s') . "</td></tr>
        </table>
    </body>
    </html>";
    
    echo "<p><strong>Email Subject:</strong> " . htmlspecialchars($subject) . "</p>";
    
    // Test email sending
    echo "<h3>📤 Testing Email Sending...</h3>";
    try {
        $email_result = send_email($recipient, $subject, $htmlMessage);
        if ($email_result) {
            echo "<p>✅ <strong>Email sent successfully!</strong></p>";
        } else {
            echo "<p>❌ <strong>Email sending failed!</strong></p>";
        }
    } catch (Exception $e) {
        echo "<p>❌ <strong>Exception during email sending:</strong> " . $e->getMessage() . "</p>";
    }
    
    // Test database storage
    echo "<h3>💾 Testing Database Storage...</h3>";
    try {
        // Check if contact_submissions table exists
        $check_table = "SHOW TABLES LIKE 'contact_submissions'";
        $table_result = $conn->query($check_table);
        
        if ($table_result->num_rows == 0) {
            echo "<p>⚠️ contact_submissions table doesn't exist, creating it...</p>";
            $create_table = "CREATE TABLE `contact_submissions` (
                `id` int(11) NOT NULL AUTO_INCREMENT,
                `name` varchar(255) NOT NULL,
                `email` varchar(255) NOT NULL,
                `phone` varchar(50) DEFAULT NULL,
                `message` text NOT NULL,
                `source` varchar(50) NOT NULL DEFAULT 'Unknown',
                `is_read` tinyint(1) NOT NULL DEFAULT 0,
                `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
                PRIMARY KEY (`id`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;";
            
            if ($conn->query($create_table)) {
                echo "<p>✅ Table created successfully</p>";
            } else {
                echo "<p>❌ Failed to create table: " . $conn->error . "</p>";
            }
        } else {
            echo "<p>✅ contact_submissions table exists</p>";
        }
        
        // Insert test submission
        $sql = "INSERT INTO `contact_submissions` (`name`, `email`, `phone`, `message`, `source`) VALUES (?, ?, ?, ?, ?)";
        $stmt = $conn->prepare($sql);
        
        if ($stmt) {
            $stmt->bind_param("sssss", $name, $email, $phone, $message, $source);
            
            if ($stmt->execute()) {
                $submission_id = $stmt->insert_id;
                echo "<p>✅ <strong>Contact submission stored successfully with ID: $submission_id</strong></p>";
                $stmt->close();
            } else {
                echo "<p>❌ Failed to insert contact submission: " . $stmt->error . "</p>";
                $stmt->close();
            }
        } else {
            echo "<p>❌ Failed to prepare statement: " . $conn->error . "</p>";
        }
        
    } catch (Exception $e) {
        echo "<p>❌ <strong>Exception during database storage:</strong> " . $e->getMessage() . "</p>";
    }
    
    // Test notifications
    echo "<h3>🔔 Testing Notifications...</h3>";
    try {
        $notifications = new Notifications($conn);
        
        $notification_title = "Debug Test Contact Form Submission";
        $notification_message = "Debug test message from $name ($email)";
        
        $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
        $script_dir = dirname($_SERVER['SCRIPT_NAME']);
        $admin_url = $protocol . '://' . $_SERVER['HTTP_HOST'] . $script_dir . '/admin/';
        
        $notification_result = $notifications->addNotification(
            0,
            $notification_title,
            $notification_message,
            'info',
            $admin_url . 'inbox.php'
        );
        
        if ($notification_result) {
            echo "<p>✅ <strong>Notification created successfully!</strong></p>";
        } else {
            echo "<p>❌ <strong>Notification creation failed!</strong></p>";
        }
        
    } catch (Exception $e) {
        echo "<p>❌ <strong>Exception during notification creation:</strong> " . $e->getMessage() . "</p>";
    }
    
    echo "<hr>";
}

?>

<h2>🧪 Test Contact Form Processing</h2>
<form method="post" style="max-width: 500px;">
    <p>
        <label>Name:</label><br>
        <input type="text" name="name" value="Test User" required style="width: 100%; padding: 8px;">
    </p>
    <p>
        <label>Email:</label><br>
        <input type="email" name="email" value="<EMAIL>" required style="width: 100%; padding: 8px;">
    </p>
    <p>
        <label>Phone:</label><br>
        <input type="tel" name="phone" value="************" style="width: 100%; padding: 8px;">
    </p>
    <p>
        <label>Message:</label><br>
        <textarea name="message" required style="width: 100%; padding: 8px; height: 100px;">This is a test message from the debug tool.</textarea>
    </p>
    <p>
        <button type="submit" name="test_contact_form" style="background: #f1ca2f; color: #333; padding: 10px 20px; border: none; cursor: pointer;">
            🚀 Test Contact Form Processing
        </button>
    </p>
</form>

<h2>📋 Instructions</h2>
<ol>
    <li>Fill out the form above with test data</li>
    <li>Click "Test Contact Form Processing"</li>
    <li>Review the results to identify any issues</li>
    <li>Check your email inbox for the test email</li>
    <li>Check the admin panel for notifications and inbox entries</li>
</ol>

<p><strong>Note:</strong> This tool helps identify exactly where the contact form processing might be failing.</p>

<style>
body { font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; line-height: 1.6; }
h1, h2, h3 { color: #333; }
pre { background: #f5f5f5; padding: 10px; border-radius: 3px; overflow-x: auto; }
p { margin: 10px 0; }
ul, ol { margin: 10px 0; padding-left: 30px; }
hr { margin: 30px 0; border: none; border-top: 1px solid #ddd; }
</style>
