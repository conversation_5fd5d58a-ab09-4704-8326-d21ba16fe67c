/**
 * Admin Override Protection CSS
 * This file contains !important declarations to protect ALL critical admin styles
 * from being overridden by external CSS or loading order issues
 * Load this LAST to guarantee admin panel functionality
 */

/* ========================================
   ADMIN LAYOUT PROTECTION
   ======================================== */

/* Admin Header Protection */
.admin-header {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  height: 60px !important;
  background-color: #ffffff !important;
  border-bottom: 1px solid #dee2e6 !important;
  z-index: 1000 !important;
  display: flex !important;
  align-items: center !important;
  padding: 0 1.5rem !important;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
}

.admin-header-left {
  display: flex !important;
  align-items: center !important;
  gap: 1rem !important;
}

.admin-header-right {
  margin-left: auto !important;
  display: flex !important;
  align-items: center !important;
  gap: 0.5rem !important;
}

/* Admin Sidebar Protection */
.admin-sidebar {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  width: 260px !important;
  height: 100vh !important;
  background: #212529 !important;
  color: #ffffff !important;
  border-right: 1px solid #343a40 !important;
  z-index: 1000 !important;
  overflow-y: auto !important;
  overflow-x: hidden !important;
  display: flex !important;
  flex-direction: column !important;
  transition: all 0.3s ease !important;
}

/* Main Content Protection */
.admin-main,
.admin-main-content {
  margin-left: 260px !important;
  margin-top: 0 !important;
  padding: 0 !important;
  min-height: 100vh !important;
  background-color: #f8f9fa !important;
  transition: margin-left 0.3s ease !important;
}

/* Content area padding */
.admin-container {
  padding: 1.5rem !important;
  max-width: none !important;
  width: 100% !important;
  margin: 0 !important;
}

/* Collapsed sidebar adjustments */
body.sidebar-collapsed .admin-main,
body.sidebar-collapsed .admin-main-content {
  margin-left: 70px !important;
}

/* Collapsed sidebar styling */
body.sidebar-collapsed .admin-sidebar,
.admin-sidebar.collapsed {
  width: 70px !important;
}

body.sidebar-collapsed .sidebar-logo img {
  max-width: 40px !important;
  max-height: 30px !important;
}

/* Sidebar Header Protection */
.admin-sidebar-header {
  padding: 1.5rem 1rem !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1) !important;
  min-height: 80px !important;
  background: linear-gradient(135deg, #343a40, #212529) !important;
}

.sidebar-logo {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  width: 100% !important;
}

.sidebar-logo img {
  max-height: 40px !important;
  max-width: 100% !important;
  transition: all 0.3s ease !important;
}

/* Sidebar Menu Protection */
.admin-sidebar-menu {
  flex: 1 !important;
  padding: 1rem 0 !important;
  overflow-y: auto !important;
}

.sidebar-nav {
  list-style: none !important;
  margin: 0 !important;
  padding: 0 !important;
}

.sidebar-item {
  margin: 0 !important;
  padding: 0 !important;
}

.sidebar-link {
  display: flex !important;
  align-items: center !important;
  padding: 0.75rem 1.5rem !important;
  color: rgba(255, 255, 255, 0.8) !important;
  text-decoration: none !important;
  transition: all 0.2s ease !important;
  position: relative !important;
  border: none !important;
  background: none !important;
  width: 100% !important;
  text-align: left !important;
}

.sidebar-link:hover {
  background: rgba(255, 255, 255, 0.1) !important;
  color: #ffffff !important;
}

.sidebar-link.active {
  background: linear-gradient(135deg, #f1ca2f, #e6b82a) !important;
  color: #212529 !important;
  font-weight: 600 !important;
}

.sidebar-icon {
  width: 20px !important;
  height: 20px !important;
  margin-right: 0.75rem !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  font-size: 1rem !important;
  flex-shrink: 0 !important;
}

.sidebar-text {
  flex: 1 !important;
  font-size: 0.875rem !important;
  font-weight: 500 !important;
  transition: all 0.3s ease !important;
}

/* Collapsed sidebar text hiding */
body.sidebar-collapsed .sidebar-text,
body.sidebar-collapsed .sidebar-footer-text {
  opacity: 0 !important;
  visibility: hidden !important;
  width: 0 !important;
  margin: 0 !important;
  overflow: hidden !important;
}

/* Hide submenu icons and arrows in collapsed mode */
body.sidebar-collapsed .submenu-icon {
  display: none !important;
}

/* Submenu styling */
.sidebar-item.has-submenu .submenu-icon {
  margin-left: auto !important;
  transition: transform 0.2s ease !important;
  font-size: 0.75rem !important;
}

.sidebar-item.has-submenu.open .submenu-icon {
  transform: rotate(180deg) !important;
}

.sidebar-submenu {
  list-style: none !important;
  margin: 0 !important;
  padding: 0 !important;
  background: rgba(0, 0, 0, 0.2) !important;
  max-height: 0 !important;
  overflow: hidden !important;
  transition: max-height 0.3s ease !important;
}

.sidebar-item.has-submenu.open .sidebar-submenu {
  max-height: 500px !important;
}

.sidebar-sublink {
  display: block !important;
  padding: 0.5rem 1.5rem 0.5rem 3.5rem !important;
  color: rgba(255, 255, 255, 0.7) !important;
  text-decoration: none !important;
  font-size: 0.8125rem !important;
  transition: all 0.2s ease !important;
}

.sidebar-sublink:hover {
  background: rgba(255, 255, 255, 0.1) !important;
  color: #ffffff !important;
}

.sidebar-sublink.active {
  background: rgba(241, 202, 47, 0.2) !important;
  color: #f1ca2f !important;
  font-weight: 500 !important;
}

/* Hide submenus completely in collapsed mode */
body.sidebar-collapsed .sidebar-submenu {
  display: none !important;
}

/* Sidebar Footer Protection */
.admin-sidebar-footer {
  padding: 1rem !important;
  border-top: 1px solid rgba(255, 255, 255, 0.1) !important;
  background: rgba(0, 0, 0, 0.1) !important;
}

.sidebar-footer-link {
  display: flex !important;
  align-items: center !important;
  padding: 0.75rem !important;
  color: rgba(255, 255, 255, 0.8) !important;
  text-decoration: none !important;
  border-radius: 6px !important;
  transition: all 0.2s ease !important;
}

.sidebar-footer-link:hover {
  background: rgba(220, 53, 69, 0.1) !important;
  color: #dc3545 !important;
}

.sidebar-footer-icon {
  margin-right: 0.75rem !important;
  font-size: 1rem !important;
}

.sidebar-footer-text {
  font-size: 0.875rem !important;
  font-weight: 500 !important;
}

/* Topbar Protection */
.admin-topbar {
  background: #ffffff !important;
  border-bottom: 1px solid #e9ecef !important;
  padding: 0.75rem 1.5rem !important;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05) !important;
  position: sticky !important;
  top: 0 !important;
  z-index: 999 !important;
  display: flex !important;
  flex-direction: column !important;
  gap: 0.5rem !important;
}

.topbar-actions {
  display: flex !important;
  align-items: center !important;
  gap: 1rem !important;
  justify-content: space-between !important;
}

/* Sidebar Toggle Button - Always Visible */
.sidebar-toggle-btn {
  background: #f8f9fa !important;
  border: 1px solid #dee2e6 !important;
  border-radius: 6px !important;
  padding: 0.5rem !important;
  color: #495057 !important;
  cursor: pointer !important;
  transition: all 0.2s ease !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  width: 40px !important;
  height: 40px !important;
  flex-shrink: 0 !important;
  order: -1 !important; /* Ensure it appears first */
}

.sidebar-toggle-btn:hover {
  background: #e9ecef !important;
  color: #212529 !important;
  transform: scale(1.05) !important;
}

.sidebar-toggle-btn:active {
  background: #f1ca2f !important;
  color: #212529 !important;
  border-color: #f1ca2f !important;
  transform: scale(0.95) !important;
}

/* Ensure toggle button is visible in all states */
.sidebar-toggle-btn {
  visibility: visible !important;
  opacity: 1 !important;
  position: relative !important;
  z-index: 1001 !important;
}

/* Mobile Responsive Design */
@media (max-width: 768px) {
  .admin-sidebar {
    transform: translateX(-100%) !important;
    width: 280px !important;
    z-index: 9999 !important;
  }

  .admin-sidebar.show {
    transform: translateX(0) !important;
  }

  .admin-main,
  .admin-main-content {
    margin-left: 0 !important;
  }

  body.sidebar-collapsed .admin-main,
  body.sidebar-collapsed .admin-main-content {
    margin-left: 0 !important;
  }

  .sidebar-toggle-btn {
    display: none !important;
  }
}

/* ========================================
   BUTTON PROTECTION
   ======================================== */

.btn {
  display: inline-flex !important;
  align-items: center !important;
  justify-content: flex-start !important;
  padding: 0.25rem 1rem !important;
  font-size: 0.875rem !important;
  font-weight: 500 !important;
  line-height: 1.5 !important;
  text-align: center !important;
  text-decoration: none !important;
  vertical-align: middle !important;
  cursor: pointer !important;
  user-select: none !important;
  border: 1px solid transparent !important;
  border-radius: 0.375rem !important;
  transition: all 0.15s ease !important;
  position: relative !important;
  overflow: hidden !important;
  gap: 0.5rem !important;
  height: auto !important;
  min-height: 36px !important;
  white-space: nowrap !important;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05) !important;
}

.btn-primary {
  color: #3c3c45 !important;
  background-color: #f1ca2f !important;
  border-color: #f1ca2f !important;
  font-weight: 600 !important;
}

.btn-primary:hover {
  color: #3c3c45 !important;
  background-color: #e6b82a !important;
  border-color: #e6b82a !important;
  transform: translateY(-1px) !important;
}

.btn-secondary {
  color: #ffffff !important;
  background-color: #6c757d !important;
  border-color: #6c757d !important;
}

.btn-success {
  color: #ffffff !important;
  background-color: #28a745 !important;
  border-color: #28a745 !important;
}

.btn-danger {
  color: #ffffff !important;
  background-color: #dc3545 !important;
  border-color: #dc3545 !important;
}

/* ========================================
   FORM PROTECTION
   ======================================== */

.form-control {
  display: block !important;
  width: 100% !important;
  padding: 0.5rem 0.75rem !important;
  font-size: 0.875rem !important;
  font-weight: 400 !important;
  line-height: 1.5 !important;
  color: #333333 !important;
  background-color: #ffffff !important;
  background-image: none !important;
  border: 1px solid #dee2e6 !important;
  border-radius: 0.375rem !important;
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out !important;
}

.form-control:focus {
  color: #333333 !important;
  background-color: #ffffff !important;
  border-color: #f1ca2f !important;
  outline: 0 !important;
  box-shadow: 0 0 0 0.2rem rgba(241, 202, 47, 0.25) !important;
}

.form-label {
  display: inline-block !important;
  margin-bottom: 0.5rem !important;
  font-size: 0.875rem !important;
  font-weight: 500 !important;
  color: #212529 !important;
}

.form-group {
  margin-bottom: 1.5rem !important;
}

/* ========================================
   TABLE PROTECTION
   ======================================== */

.table {
  width: 100% !important;
  margin-bottom: 24px !important;
  color: #333 !important;
  vertical-align: top !important;
  border-color: #e0e0e0 !important;
  border-collapse: separate !important;
  border-spacing: 0 !important;
}

.table > :not(caption) > * > * {
  padding: 16px 20px !important;
  border-bottom: 1px solid #f0f0f0 !important;
}

.table thead th {
  font-weight: 600 !important;
  color: #333 !important;
  background-color: #f5f7fa !important;
  font-size: 14px !important;
  border-bottom: 1px solid #e0e0e0 !important;
  position: relative !important;
}

.table tbody tr:hover {
  background-color: #f8f9fa !important;
}

.table-responsive {
  overflow-x: auto !important;
  -webkit-overflow-scrolling: touch !important;
  border-radius: 12px !important;
  background-color: #fff !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05) !important;
}

/* ========================================
   CARD PROTECTION
   ======================================== */

.card {
  position: relative !important;
  display: flex !important;
  flex-direction: column !important;
  min-width: 0 !important;
  word-wrap: break-word !important;
  background-color: #ffffff !important;
  background-clip: border-box !important;
  border: 1px solid #dee2e6 !important;
  border-radius: 0.5rem !important;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
}

.card-header {
  padding: 1rem 1.5rem !important;
  margin-bottom: 0 !important;
  background-color: #f8f9fa !important;
  border-bottom: 1px solid #dee2e6 !important;
  border-top-left-radius: 0.5rem !important;
  border-top-right-radius: 0.5rem !important;
}

.card-body {
  flex: 1 1 auto !important;
  padding: 1.5rem !important;
}

.card-footer {
  padding: 1rem 1.5rem !important;
  background-color: #f8f9fa !important;
  border-top: 1px solid #dee2e6 !important;
  border-bottom-left-radius: 0.5rem !important;
  border-bottom-right-radius: 0.5rem !important;
}

/* ========================================
   MODAL PROTECTION
   ======================================== */

.modal {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  z-index: 1055 !important;
  display: none !important;
  width: 100% !important;
  height: 100% !important;
  overflow-x: hidden !important;
  overflow-y: auto !important;
  outline: 0 !important;
}

.modal-dialog {
  position: relative !important;
  width: auto !important;
  margin: 0.5rem !important;
  pointer-events: none !important;
}

.modal-content {
  position: relative !important;
  display: flex !important;
  flex-direction: column !important;
  width: 100% !important;
  pointer-events: auto !important;
  background-color: #ffffff !important;
  background-clip: padding-box !important;
  border: 1px solid #dee2e6 !important;
  border-radius: 0.5rem !important;
  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important;
  outline: 0 !important;
}

.modal-backdrop {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  z-index: 1050 !important;
  width: 100vw !important;
  height: 100vh !important;
  background-color: #000000 !important;
  opacity: 0.5 !important;
}

/* ========================================
   ALERT PROTECTION
   ======================================== */

.alert {
  position: relative !important;
  padding: 1rem 1.5rem !important;
  margin-bottom: 1rem !important;
  border: 1px solid transparent !important;
  border-radius: 0.375rem !important;
}

.alert-success {
  color: #155724 !important;
  background-color: #d4edda !important;
  border-color: #c3e6cb !important;
}

.alert-danger {
  color: #721c24 !important;
  background-color: #f8d7da !important;
  border-color: #f5c6cb !important;
}

.alert-warning {
  color: #856404 !important;
  background-color: #fff3cd !important;
  border-color: #ffeaa7 !important;
}

.alert-info {
  color: #0c5460 !important;
  background-color: #d1ecf1 !important;
  border-color: #bee5eb !important;
}

/* ========================================
   NAVIGATION PROTECTION
   ======================================== */

.nav {
  display: flex !important;
  flex-wrap: wrap !important;
  padding-left: 0 !important;
  margin-bottom: 0 !important;
  list-style: none !important;
}

.nav-link {
  display: block !important;
  padding: 0.5rem 1rem !important;
  color: #333333 !important;
  text-decoration: none !important;
  transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out !important;
}

.nav-link:hover {
  color: #f1ca2f !important;
  background-color: #f8f9fa !important;
}

.nav-link.active {
  color: #f1ca2f !important;
  background-color: #f8f9fa !important;
  font-weight: 600 !important;
}

/* ========================================
   PAGINATION PROTECTION
   ======================================== */

.pagination {
  display: flex !important;
  padding-left: 0 !important;
  list-style: none !important;
  border-radius: 0.375rem !important;
}

.page-link {
  position: relative !important;
  display: block !important;
  padding: 0.5rem 0.75rem !important;
  margin-left: -1px !important;
  line-height: 1.25 !important;
  color: #333333 !important;
  text-decoration: none !important;
  background-color: #ffffff !important;
  border: 1px solid #dee2e6 !important;
}

.page-link:hover {
  z-index: 2 !important;
  color: #f1ca2f !important;
  text-decoration: none !important;
  background-color: #f8f9fa !important;
  border-color: #dee2e6 !important;
}

/* ========================================
   UTILITY CLASSES PROTECTION
   ======================================== */

.d-none { display: none !important; }
.d-inline { display: inline !important; }
.d-inline-block { display: inline-block !important; }
.d-block { display: block !important; }
.d-flex { display: flex !important; }
.d-inline-flex { display: inline-flex !important; }

.justify-content-start { justify-content: flex-start !important; }
.justify-content-end { justify-content: flex-end !important; }
.justify-content-center { justify-content: center !important; }
.justify-content-between { justify-content: space-between !important; }
.justify-content-around { justify-content: space-around !important; }

.align-items-start { align-items: flex-start !important; }
.align-items-end { align-items: flex-end !important; }
.align-items-center { align-items: center !important; }
.align-items-baseline { align-items: baseline !important; }
.align-items-stretch { align-items: stretch !important; }

.text-left { text-align: left !important; }
.text-right { text-align: right !important; }
.text-center { text-align: center !important; }

.text-primary { color: #f1ca2f !important; }
.text-secondary { color: #6c757d !important; }
.text-success { color: #28a745 !important; }
.text-danger { color: #dc3545 !important; }
.text-warning { color: #ffc107 !important; }
.text-info { color: #17a2b8 !important; }
.text-dark { color: #212529 !important; }
.text-muted { color: #6c757d !important; }

.bg-primary { background-color: #f1ca2f !important; }
.bg-secondary { background-color: #6c757d !important; }
.bg-success { background-color: #28a745 !important; }
.bg-danger { background-color: #dc3545 !important; }
.bg-warning { background-color: #ffc107 !important; }
.bg-info { background-color: #17a2b8 !important; }
.bg-light { background-color: #f8f9fa !important; }
.bg-dark { background-color: #343a40 !important; }
.bg-white { background-color: #ffffff !important; }

.w-25 { width: 25% !important; }
.w-50 { width: 50% !important; }
.w-75 { width: 75% !important; }
.w-100 { width: 100% !important; }

.h-25 { height: 25% !important; }
.h-50 { height: 50% !important; }
.h-75 { height: 75% !important; }
.h-100 { height: 100% !important; }

.position-static { position: static !important; }
.position-relative { position: relative !important; }
.position-absolute { position: absolute !important; }
.position-fixed { position: fixed !important; }
.position-sticky { position: sticky !important; }
