<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg width="1920" height="300" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="yellowGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#f5e7b5;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#f1ca2f;stop-opacity:0.8" />
    </linearGradient>
    <pattern id="grid" width="100" height="100" patternUnits="userSpaceOnUse">
      <path d="M 100 0 L 0 0 0 100" fill="none" stroke="#f1ca2f" stroke-width="0.5" stroke-opacity="0.3"/>
    </pattern>
    <pattern id="smallGrid" width="20" height="20" patternUnits="userSpaceOnUse">
      <rect width="20" height="20" fill="none" stroke="#f1ca2f" stroke-width="0.25" stroke-opacity="0.2"/>
    </pattern>
  </defs>
  <rect width="100%" height="100%" fill="url(#yellowGradient)" />
  <rect width="100%" height="100%" fill="url(#smallGrid)" />
  <rect width="100%" height="100%" fill="url(#grid)" />
  <path d="M0,300 C400,250 800,280 1200,230 C1600,180 1920,220 1920,220 L1920,300 L0,300 Z" fill="#ffffff" fill-opacity="0.1"/>
  <path d="M0,300 C600,200 1200,250 1920,150 L1920,300 L0,300 Z" fill="#ffffff" fill-opacity="0.05"/>
</svg>
