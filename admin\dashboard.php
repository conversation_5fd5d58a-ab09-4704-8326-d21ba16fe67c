<?php
// Include the security wrapper
require_once 'includes/security.php';
require_once 'config.php';
require_once 'lib/Permissions.php';
require_once 'includes/helpers.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    redirect('index.php');
}

// Initialize permission system
$permissions = new Permissions($conn, $_SESSION['user_id']);

// Check if user has permission to view dashboard
if (!$permissions->hasPermission('view_dashboard')) {
    $_SESSION['error_message'] = "You do not have permission to access the Dashboard.";
    redirect('logout.php');
}

// Check if required columns exist in users table
$required_columns = ['is_verified', 'is_admin', 'profile_image'];
foreach ($required_columns as $column) {
    $check_column_sql = "SHOW COLUMNS FROM users LIKE '$column'";
    $check_column_result = $conn->query($check_column_sql);

    if ($check_column_result->num_rows == 0) {
        // Redirect to fix_users_table.php to add missing columns
        redirect('fix_users_table.php');
    }
}

// Check which image column exists (image or featured_image)
$image_column = 'image'; // Default
$column_check = $conn->query("SHOW COLUMNS FROM news LIKE 'featured_image'");
if ($column_check && $column_check->num_rows > 0) {
    $image_column = 'featured_image';
}

// Get recent news posts with categories (limit to 5)
$recent_news_sql = "SELECT n.*, n.$image_column as image, c.name as category_name
        FROM news n
        LEFT JOIN categories c ON n.category_id = c.id
        ORDER BY n.created_at DESC
        LIMIT 5";
$recent_news = $conn->query($recent_news_sql);

// Get total count of news posts
$count_news_sql = "SELECT COUNT(*) as total FROM news";
$count_news = $conn->query($count_news_sql)->fetch_assoc()['total'];

// Get total count of categories
$count_categories_sql = "SELECT COUNT(*) as total FROM categories";
$count_categories = $conn->query($count_categories_sql)->fetch_assoc()['total'];

// Get user info
$user_id = $_SESSION['user_id'];
$user_sql = "SELECT * FROM users WHERE id = $user_id";
$user = $conn->query($user_sql)->fetch_assoc();

// Set page title
$page_title = "Dashboard";
?>
<?php
// Add page-specific class to body
$body_class = 'page-dashboard';
include 'includes/header.php';
?>

    <div class="container dashboard-container">
        <?php
        // Database checks are now handled automatically during installation
        // No need to show database update notices

        // Set up variables for content header
        $page_icon = 'fas fa-tachometer-alt';
        $page_subtitle = "Welcome back, " . $user['username'] . ". Today is " . date('l, F j, Y') . ".";

        // Include the content header
        include 'includes/content-header.php';
        ?>

        <!-- Dashboard Overview -->
        <div class="dashboard-stats">
            <div class="dashboard-stat-card">
                <div class="dashboard-stat-header">
                    <h3 class="dashboard-stat-title">Total News Posts</h3>
                    <div class="dashboard-stat-icon primary">
                        <i class="fas fa-newspaper"></i>
                    </div>
                </div>
                <div class="dashboard-stat-value"><?php echo $count_news; ?></div>
                <div class="dashboard-stat-change positive">
                    <i class="fas fa-arrow-up"></i> New posts this week
                </div>
            </div>
            <div class="dashboard-stat-card">
                <div class="dashboard-stat-header">
                    <h3 class="dashboard-stat-title">Categories</h3>
                    <div class="dashboard-stat-icon success">
                        <i class="fas fa-folder"></i>
                    </div>
                </div>
                <div class="dashboard-stat-value"><?php echo $count_categories; ?></div>
                <div class="dashboard-stat-change">
                    <i class="fas fa-check"></i> All categories active
                </div>
            </div>
            <div class="dashboard-stat-card">
                <div class="dashboard-stat-header">
                    <h3 class="dashboard-stat-title">Active Users</h3>
                    <div class="dashboard-stat-icon info">
                        <i class="fas fa-users"></i>
                    </div>
                </div>
                <div class="dashboard-stat-value">1</div>
                <div class="dashboard-stat-change">
                    <i class="fas fa-user-check"></i> Currently online
                </div>
            </div>
            <div class="dashboard-stat-card">
                <div class="dashboard-stat-header">
                    <h3 class="dashboard-stat-title">Page Views Today</h3>
                    <div class="dashboard-stat-icon warning">
                        <i class="fas fa-chart-line"></i>
                    </div>
                </div>
                <div class="dashboard-stat-value">0</div>
                <div class="dashboard-stat-change">
                    <i class="fas fa-info-circle"></i> Analytics tracking
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="dashboard-section">
            <div class="dashboard-section-header">
                <h3 class="dashboard-section-title"><i class="fas fa-bolt"></i> Quick Actions</h3>
            </div>
            <div class="dashboard-quick-actions">
                <?php if ($permissions->hasPermission('create_news')): ?>
                <a href="create_news.php" class="dashboard-quick-action">
                    <div class="dashboard-quick-action-icon">
                        <i class="fas fa-plus"></i>
                    </div>
                    <h4 class="dashboard-quick-action-title">Add News</h4>
                    <p class="dashboard-quick-action-description">Create a new news post</p>
                </a>
                <?php endif; ?>

                <?php if ($permissions->hasPermission('manage_categories')): ?>
                <a href="categories.php" class="dashboard-quick-action">
                    <div class="dashboard-quick-action-icon">
                        <i class="fas fa-tags"></i>
                    </div>
                    <h4 class="dashboard-quick-action-title">Manage Categories</h4>
                    <p class="dashboard-quick-action-description">Add or edit categories</p>
                </a>
                <?php endif; ?>

                <a href="profile.php" class="dashboard-quick-action">
                    <div class="dashboard-quick-action-icon">
                        <i class="fas fa-user-edit"></i>
                    </div>
                    <h4 class="dashboard-quick-action-title">Edit Profile</h4>
                    <p class="dashboard-quick-action-description">Update your profile</p>
                </a>

                <a href="../index.html" target="_blank" class="dashboard-quick-action">
                    <div class="dashboard-quick-action-icon">
                        <i class="fas fa-external-link-alt"></i>
                    </div>
                    <h4 class="dashboard-quick-action-title">View Website</h4>
                    <p class="dashboard-quick-action-description">See your site live</p>
                </a>

                <?php if ($permissions->hasPermission('manage_inbox')): ?>
                <a href="inbox.php" class="dashboard-quick-action">
                    <div class="dashboard-quick-action-icon">
                        <i class="fas fa-inbox"></i>
                    </div>
                    <h4 class="dashboard-quick-action-title">Inbox</h4>
                    <p class="dashboard-quick-action-description">View contact submissions</p>
                </a>
                <?php endif; ?>

                <?php if ($permissions->hasPermission('view_files')): ?>
                <a href="html_editor.php" class="dashboard-quick-action">
                    <div class="dashboard-quick-action-icon">
                        <i class="fas fa-code"></i>
                    </div>
                    <h4 class="dashboard-quick-action-title">HTML Editor</h4>
                    <p class="dashboard-quick-action-description">Edit website files</p>
                </a>
                <?php endif; ?>
            </div>
        </div>

        <!-- Recent News -->
        <div class="dashboard-section">
            <div class="dashboard-section-header">
                <h3 class="dashboard-section-title"><i class="fas fa-newspaper"></i> Recent News Posts</h3>
            </div>
            <div class="dashboard-card">
                <?php if ($recent_news->num_rows > 0): ?>
                    <div class="table-responsive">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th class="title-column">Title</th>
                                    <th class="category-column">Category</th>
                                    <th class="date-column">Date</th>
                                    <th class="actions-column">Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php while ($row = $recent_news->fetch_assoc()): ?>
                                    <tr>
                                        <td class="title-column">
                                            <div class="news-title-area">
                                                <div class="news-thumbnail">
                                                    <img src="../images/news/<?php echo $row['image']; ?>" alt="<?php echo $row['title']; ?>">
                                                </div>
                                                <div class="news-info">
                                                    <strong class="news-title"><?php echo $row['title']; ?></strong>
                                                    <div class="news-actions-inline">
                                                        <?php if ($permissions->hasPermission('manage_news')): ?>
                                                        <a href="edit_news.php?id=<?php echo $row['id']; ?>" class="action-link">Edit</a> |
                                                        <a href="delete_news.php?id=<?php echo $row['id']; ?>" class="action-link text-danger" onclick="return confirm('Are you sure you want to delete this post?')">Delete</a> |
                                                        <?php endif; ?>
                                                        <a href="../<?php echo !empty($row['slug']) ? $row['slug'] . '.html' : 'news-detail.php?id=' . $row['id']; ?>" class="action-link" target="_blank">View</a>
                                                    </div>
                                                </div>
                                            </div>
                                        </td>
                                        <td class="category-column">
                                            <span class="news-category"><?php echo !empty($row['category_name']) ? $row['category_name'] : 'Uncategorized'; ?></span>
                                        </td>
                                        <td class="date-column">
                                            <div class="news-date">
                                                <span class="date-display"><?php echo date('Y/m/d', strtotime($row['created_at'])); ?></span>
                                                <span class="time-display"><?php echo date('g:i a', strtotime($row['created_at'])); ?></span>
                                            </div>
                                        </td>
                                        <td class="actions-column">
                                            <div class="news-actions">
                                                <?php if ($permissions->hasPermission('manage_news')): ?>
                                                <a href="edit_news.php?id=<?php echo $row['id']; ?>" class="btn btn-icon btn-sm" title="Edit"><i class="fas fa-edit"></i></a>
                                                <?php endif; ?>
                                                <a href="../<?php echo !empty($row['slug']) ? $row['slug'] . '.html' : 'news-detail.php?id=' . $row['id']; ?>" class="btn btn-icon btn-sm btn-secondary" title="View" target="_blank"><i class="fas fa-eye"></i></a>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endwhile; ?>
                            </tbody>
                        </table>
                    </div>

                <?php else: ?>
                    <div class="alert alert-info">
                        <i class="alert-icon fas fa-info-circle"></i>
                        <div class="alert-content">
                            <div class="alert-title">No news posts found.</div>
                            <div class="alert-text">Click "Add New Post" to create your first news post.</div>
                        </div>
                    </div>

                <?php endif; ?>
            </div>
        </div>

        <!-- Recent Activity and System Status -->
        <div class="dashboard-grid-2">
            <div class="dashboard-card">
                <div class="dashboard-card-header">
                    <h3 class="dashboard-card-title"><i class="fas fa-history"></i> Recent Activity</h3>
                </div>
                <div class="dashboard-card-body">
                    <div class="activity-list">
                        <div class="activity-item">
                            <div class="activity-icon">
                                <i class="fas fa-sign-in-alt"></i>
                            </div>
                            <div class="activity-content">
                                <div class="activity-message">User Login</div>
                                <div class="activity-time">
                                    <?php echo $user['username']; ?> - <?php echo date('g:i A'); ?>
                                </div>
                            </div>
                        </div>
                        <?php if ($count_news > 0): ?>
                        <div class="activity-item">
                            <div class="activity-icon success">
                                <i class="fas fa-newspaper"></i>
                            </div>
                            <div class="activity-content">
                                <div class="activity-message">Content Published</div>
                                <div class="activity-time">
                                    Admin - Yesterday
                                </div>
                            </div>
                        </div>
                        <?php endif; ?>
                        <div class="activity-item">
                            <div class="activity-icon info">
                                <i class="fas fa-cog"></i>
                            </div>
                            <div class="activity-content">
                                <div class="activity-message">System Maintenance</div>
                                <div class="activity-time">
                                    System - 2 days ago
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="dashboard-card">
                <div class="dashboard-card-header">
                    <h3 class="dashboard-card-title"><i class="fas fa-tasks"></i> System Status</h3>
                </div>
                <div class="dashboard-card-body">
                    <div class="status-list">
                        <div class="status-item">
                            <div class="status-label">System Health</div>
                            <div class="status-value">
                                <span class="badge badge-success">Operational</span>
                            </div>
                        </div>
                        <div class="status-item">
                            <div class="status-label">Server Software</div>
                            <div class="status-value"><?php echo $_SERVER['SERVER_SOFTWARE']; ?></div>
                        </div>
                        <div class="status-item">
                            <div class="status-label">PHP Runtime</div>
                            <div class="status-value">v<?php echo PHP_VERSION; ?></div>
                        </div>
                        <div class="status-item">
                            <div class="status-label">Database</div>
                            <div class="status-value">MySQL <?php echo $conn->server_info; ?></div>
                        </div>
                        <div class="status-item">
                            <div class="status-label">Storage Usage</div>
                            <div class="status-value">
                                <?php
                                $free_space = disk_free_space("/");
                                $total_space = disk_total_space("/");
                                if ($free_space !== false && $total_space !== false) {
                                    $used_percent = round(($total_space - $free_space) / $total_space * 100);
                                    echo $used_percent . "% used";
                                } else {
                                    echo "N/A";
                                }
                                ?>
                                <span class="status-indicator <?php echo (isset($used_percent) && $used_percent > 80) ? 'warning' : 'success'; ?>"></span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- SEO Improviser section hidden - will be implemented in future development
        <div class="dashboard-section">
            <div class="dashboard-section-header">
                <h3 class="dashboard-section-title"><i class="fas fa-chart-line"></i> SEO Improviser</h3>
            </div>
            <div class="dashboard-card">
                <div class="dashboard-card-body">
                    <!-- SEO content will be added in future development -->
                </div>
            </div>
        </div>
        -->

        <!-- Footer will be added automatically -->
    </div>

<?php include 'includes/footer.php'; ?>
<?php
// Flush the output buffer and send the content to the browser
ob_end_flush();
?>
