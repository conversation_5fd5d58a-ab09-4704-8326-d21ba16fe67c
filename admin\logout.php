<?php
/**
 * Logout Script
 *
 * This script handles user logout by properly cleaning up the session
 * and redirecting to the login page with a success message.
 */

// Include configuration file for redirect function
require_once 'config.php';

// Include user functions if available
if (file_exists('includes/user-functions.php')) {
    require_once 'includes/user-functions.php';
}

// Start the session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Store username for logout message if available
$username = isset($_SESSION['username']) ? $_SESSION['username'] : '';

// Log the logout activity if function exists
if (function_exists('log_activity')) {
    log_activity('logout', 'User logged out');
}

// Clear all session variables
$_SESSION = array();

// If a session cookie is used, destroy it
if (ini_get("session.use_cookies")) {
    $params = session_get_cookie_params();
    setcookie(session_name(), '', time() - 42000,
        $params["path"], $params["domain"],
        $params["secure"], $params["httponly"]
    );
}

// Destroy the session
session_destroy();

// Start a new session for the logout message
session_start();

// Set a flag to indicate successful logout
$_SESSION['logged_out'] = true;
$_SESSION['logout_message'] = $username ? "You have been successfully logged out, $username." : "You have been successfully logged out.";

// Set cache control headers to prevent caching
header("Cache-Control: no-store, no-cache, must-revalidate, max-age=0");
header("Cache-Control: post-check=0, pre-check=0", false);
header("Pragma: no-cache");
header("Expires: Mon, 26 Jul 1997 05:00:00 GMT");

// Redirect to login page with a cache-busting parameter
redirect('index.php', ['logout' => time()]);
exit;
?>
