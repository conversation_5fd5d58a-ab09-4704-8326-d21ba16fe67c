/**
 * External Image Handler
 * 
 * This script handles external images in the frontend editor by:
 * 1. Detecting external images
 * 2. Replacing them with proxied versions
 * 3. Restoring original URLs when saving
 */

class ExternalImageHandler {
    constructor(options = {}) {
        this.options = {
            proxyUrl: 'image-proxy.php',
            dataAttribute: 'data-original-src',
            hostname: window.location.hostname,
            debug: false,
            ...options
        };

        // Initialize
        this.init();
    }

    /**
     * Initialize the handler
     */
    init() {
        this.log('Initializing External Image Handler');
        
        // Set up MutationObserver to watch for dynamically added images
        this.setupMutationObserver();
        
        // Process images on page load
        document.addEventListener('DOMContentLoaded', () => {
            this.processAllImages();
        });
        
        // Process images when window is fully loaded
        window.addEventListener('load', () => {
            this.processAllImages();
        });
        
        // Process images again after a delay to catch any late-loading images
        setTimeout(() => {
            this.processAllImages();
        }, 1000);
    }

    /**
     * Process all images on the page
     */
    processAllImages() {
        this.log('Processing all images');
        const images = document.querySelectorAll('img');
        images.forEach(img => this.processImage(img));
    }

    /**
     * Process a single image
     * @param {HTMLImageElement} img - The image element to process
     */
    processImage(img) {
        try {
            // Skip if already processed
            if (img.hasAttribute('data-processed')) {
                return;
            }
            
            // Mark as processed to avoid duplicate processing
            img.setAttribute('data-processed', 'true');
            
            // Store original source if not already stored
            if (!img.hasAttribute(this.options.dataAttribute) && img.src) {
                img.setAttribute(this.options.dataAttribute, img.src);
                this.log('Stored original source:', img.src);
            }
            
            // Check if this is an external image
            if (img.src && !this.isInternalUrl(img.src)) {
                this.log('Processing external image:', img.src);
                
                // Add error handler
                img.addEventListener('error', () => {
                    this.handleImageError(img);
                });
                
                // Check if image is already broken
                if (img.complete && (img.naturalWidth === 0 || img.naturalHeight === 0)) {
                    this.handleImageError(img);
                }
            }
        } catch (e) {
            this.log('Error processing image:', e);
        }
    }

    /**
     * Handle image loading error
     * @param {HTMLImageElement} img - The image element that failed to load
     */
    handleImageError(img) {
        try {
            this.log('Handling error for image:', img.src);
            
            // Get original source
            const originalSrc = img.getAttribute(this.options.dataAttribute);
            if (!originalSrc) {
                this.log('No original source found for image');
                return;
            }
            
            // Skip if already using proxy
            if (img.src.indexOf(this.options.proxyUrl) !== -1) {
                this.log('Image already using proxy, skipping');
                return;
            }
            
            // Replace with blank image first to prevent flickering
            img.src = 'data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7';
            
            // Use proxy with cache-busting parameter
            setTimeout(() => {
                const proxiedUrl = `${this.options.proxyUrl}?url=${encodeURIComponent(originalSrc)}&t=${new Date().getTime()}`;
                this.log('Replacing with proxied URL:', proxiedUrl);
                img.src = proxiedUrl;
            }, 50);
        } catch (e) {
            this.log('Error handling image error:', e);
        }
    }

    /**
     * Check if a URL is internal (from the same domain)
     * @param {string} url - The URL to check
     * @return {boolean} - True if the URL is internal, false otherwise
     */
    isInternalUrl(url) {
        try {
            // Handle relative URLs
            if (url.startsWith('/') || url.startsWith('./') || url.startsWith('../')) {
                return true;
            }
            
            // Handle data URLs
            if (url.startsWith('data:')) {
                return true;
            }
            
            // Check hostname
            return url.indexOf(this.options.hostname) !== -1;
        } catch (e) {
            this.log('Error checking URL:', e);
            return true; // Assume internal on error
        }
    }

    /**
     * Set up MutationObserver to watch for dynamically added images
     */
    setupMutationObserver() {
        try {
            const observer = new MutationObserver(mutations => {
                mutations.forEach(mutation => {
                    // Process added nodes
                    if (mutation.addedNodes.length) {
                        mutation.addedNodes.forEach(node => {
                            // Check if the node is an image
                            if (node.nodeName === 'IMG') {
                                this.processImage(node);
                            }
                            
                            // Check for images inside the node
                            if (node.querySelectorAll) {
                                const images = node.querySelectorAll('img');
                                images.forEach(img => this.processImage(img));
                            }
                        });
                    }
                    
                    // Check for attribute changes on images
                    if (mutation.type === 'attributes' && 
                        mutation.attributeName === 'src' && 
                        mutation.target.nodeName === 'IMG') {
                        this.processImage(mutation.target);
                    }
                });
            });
            
            // Start observing the document
            observer.observe(document.body, {
                childList: true,
                subtree: true,
                attributes: true,
                attributeFilter: ['src']
            });
            
            this.log('MutationObserver set up');
        } catch (e) {
            this.log('Error setting up MutationObserver:', e);
        }
    }

    /**
     * Restore original image URLs in content
     * @param {string} html - The HTML content
     * @return {string} - The HTML content with original image URLs restored
     */
    restoreOriginalUrls(html) {
        try {
            this.log('Restoring original URLs in content');
            
            // Create a temporary div to parse the HTML
            const tempDiv = document.createElement('div');
            tempDiv.innerHTML = html;
            
            // Find all images
            const images = tempDiv.querySelectorAll('img');
            images.forEach(img => {
                // Check if this is a proxied image
                if (img.src && img.src.indexOf(this.options.proxyUrl) !== -1) {
                    // Get original source
                    let originalSrc = img.getAttribute(this.options.dataAttribute);
                    
                    if (!originalSrc) {
                        // Try to extract from proxy URL
                        try {
                            const url = new URL(img.src, window.location.href);
                            originalSrc = decodeURIComponent(url.searchParams.get('url'));
                        } catch (e) {
                            this.log('Error extracting original URL:', e);
                        }
                    }
                    
                    if (originalSrc) {
                        // Restore original URL
                        this.log('Restoring original URL:', originalSrc);
                        img.src = originalSrc;
                        
                        // Remove data attribute
                        img.removeAttribute(this.options.dataAttribute);
                    }
                }
            });
            
            return tempDiv.innerHTML;
        } catch (e) {
            this.log('Error restoring original URLs:', e);
            return html;
        }
    }

    /**
     * Log a message if debugging is enabled
     * @param {...any} args - The arguments to log
     */
    log(...args) {
        if (this.options.debug) {
            console.log('[ExternalImageHandler]', ...args);
        }
    }
}

// Create global instance
window.externalImageHandler = new ExternalImageHandler({
    debug: true
});
