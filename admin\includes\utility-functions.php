<?php
/**
 * Utility Functions
 * General utility functions
 * Consolidated from multiple PHP files
 */

/**
 * Sanitize input
 * 
 * @param string $input Input to sanitize
 * @return string Sanitized input
 */
function sanitize_input($input) {
    return htmlspecialchars(trim($input), ENT_QUOTES, 'UTF-8');
}

/**
 * Validate email
 * 
 * @param string $email Email to validate
 * @return bool True if valid, false otherwise
 */
function validate_email($email) {
    return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
}

/**
 * Validate URL
 * 
 * @param string $url URL to validate
 * @return bool True if valid, false otherwise
 */
function validate_url($url) {
    return filter_var($url, FILTER_VALIDATE_URL) !== false;
}

/**
 * Generate random string
 * 
 * @param int $length Length of string
 * @return string Random string
 */
function generate_random_string($length = 10) {
    $characters = '0123456789abcdef<PERSON>ijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
    $charactersLength = strlen($characters);
    $randomString = '';
    
    for ($i = 0; $i < $length; $i++) {
        $randomString .= $characters[rand(0, $charactersLength - 1)];
    }
    
    return $randomString;
}

/**
 * Generate slug from string
 * 
 * @param string $string String to convert to slug
 * @return string Slug
 */
function generate_slug($string) {
    // Replace non letter or digits by -
    $string = preg_replace('~[^\pL\d]+~u', '-', $string);
    
    // Transliterate
    $string = iconv('utf-8', 'us-ascii//TRANSLIT', $string);
    
    // Remove unwanted characters
    $string = preg_replace('~[^-\w]+~', '', $string);
    
    // Trim
    $string = trim($string, '-');
    
    // Remove duplicate -
    $string = preg_replace('~-+~', '-', $string);
    
    // Lowercase
    $string = strtolower($string);
    
    if (empty($string)) {
        return 'n-a';
    }
    
    return $string;
}

/**
 * Format date
 * 
 * @param string $date Date to format
 * @param string $format Format string
 * @return string Formatted date
 */
function format_date($date, $format = 'Y-m-d H:i:s') {
    return date($format, strtotime($date));
}

/**
 * Format time ago
 * 
 * @param string $datetime Date and time
 * @return string Time ago
 */
function time_ago($datetime) {
    $time = strtotime($datetime);
    $now = time();
    $diff = $now - $time;
    
    if ($diff < 60) {
        return 'just now';
    } elseif ($diff < 3600) {
        $minutes = floor($diff / 60);
        return $minutes . ' minute' . ($minutes > 1 ? 's' : '') . ' ago';
    } elseif ($diff < 86400) {
        $hours = floor($diff / 3600);
        return $hours . ' hour' . ($hours > 1 ? 's' : '') . ' ago';
    } elseif ($diff < 604800) {
        $days = floor($diff / 86400);
        return $days . ' day' . ($days > 1 ? 's' : '') . ' ago';
    } elseif ($diff < 2592000) {
        $weeks = floor($diff / 604800);
        return $weeks . ' week' . ($weeks > 1 ? 's' : '') . ' ago';
    } elseif ($diff < 31536000) {
        $months = floor($diff / 2592000);
        return $months . ' month' . ($months > 1 ? 's' : '') . ' ago';
    } else {
        $years = floor($diff / 31536000);
        return $years . ' year' . ($years > 1 ? 's' : '') . ' ago';
    }
}

/**
 * Format file size
 * 
 * @param int $bytes File size in bytes
 * @param int $precision Precision
 * @return string Formatted file size
 */
function format_file_size($bytes, $precision = 2) {
    $units = ['B', 'KB', 'MB', 'GB', 'TB'];
    
    $bytes = max($bytes, 0);
    $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
    $pow = min($pow, count($units) - 1);
    
    $bytes /= pow(1024, $pow);
    
    return round($bytes, $precision) . ' ' . $units[$pow];
}

/**
 * Truncate text
 * 
 * @param string $text Text to truncate
 * @param int $length Maximum length
 * @param string $suffix Suffix to add if truncated
 * @return string Truncated text
 */
function truncate_text($text, $length = 100, $suffix = '...') {
    if (strlen($text) <= $length) {
        return $text;
    }
    
    return substr($text, 0, $length) . $suffix;
}

/**
 * Get file extension
 * 
 * @param string $filename Filename
 * @return string File extension
 */
function get_file_extension($filename) {
    return strtolower(pathinfo($filename, PATHINFO_EXTENSION));
}

/**
 * Check if file is image
 * 
 * @param string $filename Filename
 * @return bool True if image, false otherwise
 */
function is_image($filename) {
    $ext = get_file_extension($filename);
    $image_extensions = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'svg'];
    
    return in_array($ext, $image_extensions);
}

/**
 * Get image dimensions
 * 
 * @param string $path Image path
 * @return array|bool Array with width and height, or false on failure
 */
function get_image_dimensions($path) {
    if (!file_exists($path)) {
        return false;
    }
    
    $size = getimagesize($path);
    
    if (!$size) {
        return false;
    }
    
    return [
        'width' => $size[0],
        'height' => $size[1]
    ];
}

/**
 * Resize image
 * 
 * @param string $source Source image path
 * @param string $destination Destination image path
 * @param int $width New width
 * @param int $height New height
 * @param bool $crop Whether to crop image
 * @return bool True on success, false on failure
 */
function resize_image($source, $destination, $width, $height, $crop = false) {
    if (!file_exists($source)) {
        return false;
    }
    
    $info = getimagesize($source);
    
    if (!$info) {
        return false;
    }
    
    $src_width = $info[0];
    $src_height = $info[1];
    $src_type = $info[2];
    
    switch ($src_type) {
        case IMAGETYPE_JPEG:
            $src_img = imagecreatefromjpeg($source);
            break;
        case IMAGETYPE_PNG:
            $src_img = imagecreatefrompng($source);
            break;
        case IMAGETYPE_GIF:
            $src_img = imagecreatefromgif($source);
            break;
        default:
            return false;
    }
    
    if (!$src_img) {
        return false;
    }
    
    if ($crop) {
        // Calculate crop dimensions
        $ratio_src = $src_width / $src_height;
        $ratio_dst = $width / $height;
        
        if ($ratio_dst > $ratio_src) {
            $src_y = 0;
            $src_x = ($src_width - $src_height * $ratio_dst) / 2;
            $src_w = $src_height * $ratio_dst;
            $src_h = $src_height;
        } else {
            $src_x = 0;
            $src_y = ($src_height - $src_width / $ratio_dst) / 2;
            $src_w = $src_width;
            $src_h = $src_width / $ratio_dst;
        }
        
        $dst_img = imagecreatetruecolor($width, $height);
        
        // Preserve transparency
        if ($src_type === IMAGETYPE_PNG || $src_type === IMAGETYPE_GIF) {
            imagecolortransparent($dst_img, imagecolorallocate($dst_img, 0, 0, 0));
            imagealphablending($dst_img, false);
            imagesavealpha($dst_img, true);
        }
        
        imagecopyresampled($dst_img, $src_img, 0, 0, $src_x, $src_y, $width, $height, $src_w, $src_h);
    } else {
        // Calculate resize dimensions
        if ($width === 0) {
            $width = $src_width * $height / $src_height;
        } elseif ($height === 0) {
            $height = $src_height * $width / $src_width;
        }
        
        $dst_img = imagecreatetruecolor($width, $height);
        
        // Preserve transparency
        if ($src_type === IMAGETYPE_PNG || $src_type === IMAGETYPE_GIF) {
            imagecolortransparent($dst_img, imagecolorallocate($dst_img, 0, 0, 0));
            imagealphablending($dst_img, false);
            imagesavealpha($dst_img, true);
        }
        
        imagecopyresampled($dst_img, $src_img, 0, 0, 0, 0, $width, $height, $src_width, $src_height);
    }
    
    // Save image
    $result = false;
    
    switch ($src_type) {
        case IMAGETYPE_JPEG:
            $result = imagejpeg($dst_img, $destination, 90);
            break;
        case IMAGETYPE_PNG:
            $result = imagepng($dst_img, $destination, 9);
            break;
        case IMAGETYPE_GIF:
            $result = imagegif($dst_img, $destination);
            break;
    }
    
    imagedestroy($src_img);
    imagedestroy($dst_img);
    
    return $result;
}

/**
 * Get admin URL
 * 
 * @param string $path Path to append to admin URL
 * @return string Admin URL
 */
function get_admin_url($path = '') {
    $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
    $host = $_SERVER['HTTP_HOST'];
    $script_name = $_SERVER['SCRIPT_NAME'];
    $admin_dir = dirname($script_name);
    
    return $protocol . '://' . $host . $admin_dir . '/' . ltrim($path, '/');
}

/**
 * Get site URL
 * 
 * @param string $path Path to append to site URL
 * @return string Site URL
 */
function get_site_url($path = '') {
    $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
    $host = $_SERVER['HTTP_HOST'];
    $script_name = $_SERVER['SCRIPT_NAME'];
    $site_dir = dirname(dirname($script_name));
    
    if ($site_dir === '/' || $site_dir === '\\') {
        $site_dir = '';
    }
    
    return $protocol . '://' . $host . $site_dir . '/' . ltrim($path, '/');
}

/**
 * Get current URL
 * 
 * @return string Current URL
 */
function get_current_url() {
    $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
    $host = $_SERVER['HTTP_HOST'];
    $uri = $_SERVER['REQUEST_URI'];
    
    return $protocol . '://' . $host . $uri;
}

/**
 * Redirect to URL
 * 
 * @param string $url URL to redirect to
 * @return void
 */
function redirect($url) {
    header('Location: ' . $url);
    exit;
}

/**
 * Get client IP address
 * 
 * @return string IP address
 */
function get_client_ip() {
    if (!empty($_SERVER['HTTP_CLIENT_IP'])) {
        return $_SERVER['HTTP_CLIENT_IP'];
    } elseif (!empty($_SERVER['HTTP_X_FORWARDED_FOR'])) {
        return $_SERVER['HTTP_X_FORWARDED_FOR'];
    } else {
        return $_SERVER['REMOTE_ADDR'];
    }
}

/**
 * Get user agent
 * 
 * @return string User agent
 */
function get_user_agent() {
    return $_SERVER['HTTP_USER_AGENT'];
}

/**
 * Check if request is AJAX
 * 
 * @return bool True if AJAX request, false otherwise
 */
function is_ajax() {
    return !empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) === 'xmlhttprequest';
}

/**
 * Get request method
 * 
 * @return string Request method
 */
function get_request_method() {
    return $_SERVER['REQUEST_METHOD'];
}

/**
 * Check if request is POST
 * 
 * @return bool True if POST request, false otherwise
 */
function is_post() {
    return get_request_method() === 'POST';
}

/**
 * Check if request is GET
 * 
 * @return bool True if GET request, false otherwise
 */
function is_get() {
    return get_request_method() === 'GET';
}

/**
 * Get JSON response
 * 
 * @param array $data Response data
 * @param int $status HTTP status code
 * @return void
 */
function json_response($data, $status = 200) {
    http_response_code($status);
    header('Content-Type: application/json');
    echo json_encode($data);
    exit;
}

/**
 * Get pagination
 * 
 * @param int $total Total items
 * @param int $per_page Items per page
 * @param int $current_page Current page
 * @param string $url_pattern URL pattern with %d placeholder for page number
 * @return string Pagination HTML
 */
function get_pagination($total, $per_page, $current_page, $url_pattern) {
    $total_pages = ceil($total / $per_page);
    
    if ($total_pages <= 1) {
        return '';
    }
    
    $html = '<div class="admin-pagination"><ul class="admin-pagination-list">';
    
    // Previous page
    if ($current_page > 1) {
        $html .= '<li class="admin-pagination-item"><a href="' . sprintf($url_pattern, $current_page - 1) . '"><i class="fas fa-chevron-left"></i></a></li>';
    } else {
        $html .= '<li class="admin-pagination-item disabled"><span><i class="fas fa-chevron-left"></i></span></li>';
    }
    
    // Page numbers
    $start_page = max(1, $current_page - 2);
    $end_page = min($total_pages, $current_page + 2);
    
    if ($start_page > 1) {
        $html .= '<li class="admin-pagination-item"><a href="' . sprintf($url_pattern, 1) . '">1</a></li>';
        if ($start_page > 2) {
            $html .= '<li class="admin-pagination-item disabled"><span>...</span></li>';
        }
    }
    
    for ($i = $start_page; $i <= $end_page; $i++) {
        if ($i === $current_page) {
            $html .= '<li class="admin-pagination-item active"><span>' . $i . '</span></li>';
        } else {
            $html .= '<li class="admin-pagination-item"><a href="' . sprintf($url_pattern, $i) . '">' . $i . '</a></li>';
        }
    }
    
    if ($end_page < $total_pages) {
        if ($end_page < $total_pages - 1) {
            $html .= '<li class="admin-pagination-item disabled"><span>...</span></li>';
        }
        $html .= '<li class="admin-pagination-item"><a href="' . sprintf($url_pattern, $total_pages) . '">' . $total_pages . '</a></li>';
    }
    
    // Next page
    if ($current_page < $total_pages) {
        $html .= '<li class="admin-pagination-item"><a href="' . sprintf($url_pattern, $current_page + 1) . '"><i class="fas fa-chevron-right"></i></a></li>';
    } else {
        $html .= '<li class="admin-pagination-item disabled"><span><i class="fas fa-chevron-right"></i></span></li>';
    }
    
    $html .= '</ul></div>';
    
    return $html;
}
