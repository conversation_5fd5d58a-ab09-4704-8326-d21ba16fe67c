<?php
/**
 * Pagination Component
 *
 * This file provides a reusable pagination component for admin tables.
 * It can be included in any page that needs pagination.
 *
 * Usage:
 * 1. Set the following variables before including this file:
 *    - $page: Current page number
 *    - $items_per_page: Number of items per page
 *    - $total_items: Total number of items
 *    - $base_url: Base URL for pagination links (e.g., 'all_news.php')
 *    - $query_params: Array of additional query parameters (optional)
 *
 * 2. Include this file where you want to display pagination:
 *    include 'includes/pagination.php';
 */

// Ensure required variables are set
if (!isset($page) || !isset($items_per_page) || !isset($total_items) || !isset($base_url)) {
    echo "<!-- Pagination error: Required variables not set -->";
    return;
}

// Calculate pagination values
$total_pages = ceil($total_items / $items_per_page);
$offset = ($page - 1) * $items_per_page;

// Only show pagination if we have more than one page
$show_pagination = ($total_items > $items_per_page);

// Don't force minimum pages if we don't have enough items
$forced_total_pages = $total_pages;

// Build query string for pagination links
$query_params = isset($query_params) ? $query_params : [];
$query_string = '';
foreach ($query_params as $key => $value) {
    $query_string .= "&{$key}=" . urlencode($value);
}

// CSS for pagination
if (!isset($pagination_css_added)) {
    $pagination_css_added = true;
    ?>
    <style>
        /* Pagination styles */
        .admin-table-pagination {
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 15px;
            margin-top: 20px;
            padding: 15px;
            background-color: #f8f9fa;
            border-top: 1px solid #ddd;
            border-radius: 0 0 4px 4px;
        }

        .admin-table-pagination-info {
            color: #666;
            font-size: 14px;
        }

        .admin-table-pagination-per-page {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .admin-table-pagination-per-page label {
            color: #666;
            font-size: 14px;
        }

        .admin-table-pagination-per-page select {
            padding: 5px 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background-color: #fff;
            font-size: 14px;
            cursor: pointer;
        }

        /* Pagination controls */
        .admin-table-pagination-controls {
            display: flex;
            gap: 5px;
            flex-wrap: wrap;
        }

        .admin-table-pagination-button {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            min-width: 36px;
            height: 36px;
            padding: 0 10px;
            background-color: #fff;
            border: 1px solid #ddd;
            border-radius: 4px;
            color: #333;
            font-size: 14px;
            text-decoration: none;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .admin-table-pagination-button:hover {
            background-color: #f1ca2f;
            border-color: #f1ca2f;
            color: #333;
        }

        .admin-table-pagination-button.active {
            background-color: #f1ca2f;
            border-color: #f1ca2f;
            color: #333;
            font-weight: bold;
        }

        .admin-table-pagination-button.disabled {
            opacity: 0.5;
            cursor: not-allowed;
            pointer-events: none;
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
            .admin-table-pagination {
                flex-direction: column;
                align-items: flex-start;
            }

            .admin-table-pagination-controls {
                margin-top: 10px;
                width: 100%;
                justify-content: center;
            }
        }
    </style>
    <?php
}
?>

<!-- Pagination -->
<div class="admin-table-pagination">
    <div class="admin-table-pagination-info">
        Showing <?php echo $total_items > 0 ? $offset + 1 : 0; ?> to <?php echo min($offset + $items_per_page, $total_items); ?> of <?php echo $total_items; ?> entries
    </div>
    <div class="admin-table-pagination-per-page">
        <label for="items-per-page">Items per page:</label>
        <select id="items-per-page" onchange="changeItemsPerPage(this.value)">
            <option value="5" <?php echo $items_per_page == 5 ? 'selected' : ''; ?>>5</option>
            <option value="10" <?php echo $items_per_page == 10 ? 'selected' : ''; ?>>10</option>
            <option value="25" <?php echo $items_per_page == 25 ? 'selected' : ''; ?>>25</option>
            <option value="50" <?php echo $items_per_page == 50 ? 'selected' : ''; ?>>50</option>
            <option value="100" <?php echo $items_per_page == 100 ? 'selected' : ''; ?>>100</option>
        </select>
    </div>

    <?php if ($show_pagination && $total_pages > 1): ?>
    <div class="admin-table-pagination-controls">
        <?php
        // First and Previous buttons
        if ($page > 1): ?>
            <a href="<?php echo $base_url; ?>?page=1<?php echo $query_string; ?>&per_page=<?php echo $items_per_page; ?>" class="admin-table-pagination-button" title="First Page">
                <i class="fas fa-angle-double-left"></i>
            </a>
            <a href="<?php echo $base_url; ?>?page=<?php echo $page - 1; ?><?php echo $query_string; ?>&per_page=<?php echo $items_per_page; ?>" class="admin-table-pagination-button" title="Previous Page">
                <i class="fas fa-angle-left"></i>
            </a>
        <?php else: ?>
            <span class="admin-table-pagination-button disabled" title="First Page">
                <i class="fas fa-angle-double-left"></i>
            </span>
            <span class="admin-table-pagination-button disabled" title="Previous Page">
                <i class="fas fa-angle-left"></i>
            </span>
        <?php endif; ?>

        <?php
        // Calculate range of page numbers to show
        $range = 2; // Show 2 pages before and after current page
        $start_page = max(1, $page - $range);
        $end_page = min($total_pages, $page + $range);

        // Always show first page
        if ($start_page > 1) {
            echo '<a href="' . $base_url . '?page=1' . $query_string . '&per_page=' . $items_per_page . '" class="admin-table-pagination-button">1</a>';
            if ($start_page > 2) {
                echo '<span class="admin-table-pagination-button disabled">...</span>';
            }
        }

        // Show page numbers
        for ($i = $start_page; $i <= $end_page; $i++) {
            if ($i == $page) {
                echo '<span class="admin-table-pagination-button active">' . $i . '</span>';
            } else {
                echo '<a href="' . $base_url . '?page=' . $i . $query_string . '&per_page=' . $items_per_page . '" class="admin-table-pagination-button">' . $i . '</a>';
            }
        }

        // Always show last page if we have more than one page
        if ($end_page < $total_pages) {
            if ($end_page < $total_pages - 1) {
                echo '<span class="admin-table-pagination-button disabled">...</span>';
            }
            echo '<a href="' . $base_url . '?page=' . $total_pages . $query_string . '&per_page=' . $items_per_page . '" class="admin-table-pagination-button">' . $total_pages . '</a>';
        }
        ?>

        <?php
        // Next and Last buttons
        if ($page < $total_pages): ?>
            <a href="<?php echo $base_url; ?>?page=<?php echo $page + 1; ?><?php echo $query_string; ?>&per_page=<?php echo $items_per_page; ?>" class="admin-table-pagination-button" title="Next Page">
                <i class="fas fa-angle-right"></i>
            </a>
            <a href="<?php echo $base_url; ?>?page=<?php echo $total_pages; ?><?php echo $query_string; ?>&per_page=<?php echo $items_per_page; ?>" class="admin-table-pagination-button" title="Last Page">
                <i class="fas fa-angle-double-right"></i>
            </a>
        <?php else: ?>
            <span class="admin-table-pagination-button disabled" title="Next Page">
                <i class="fas fa-angle-right"></i>
            </span>
            <span class="admin-table-pagination-button disabled" title="Last Page">
                <i class="fas fa-angle-double-right"></i>
            </span>
        <?php endif; ?>
    </div>
    <?php endif; ?>
</div>

<!-- JavaScript for changing items per page -->
<script>
if (typeof changeItemsPerPage !== 'function') {
    function changeItemsPerPage(value) {
        // Update user preference via AJAX
        const xhr = new XMLHttpRequest();
        xhr.open('POST', 'update_items_per_page.php', true);
        xhr.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded');
        xhr.onload = function() {
            if (xhr.status === 200) {
                // Reload the page with the new items per page
                const currentUrl = new URL(window.location.href);
                currentUrl.searchParams.set('page', '1');
                currentUrl.searchParams.set('per_page', value);
                window.location.href = currentUrl.toString();
            }
        };
        xhr.send(`items_per_page=${value}`);
    }
}
</script>
