/**
 * Message View Interface Enhancements
 * 
 * This script improves the message view interface by:
 * 1. Enhancing the reply editor toolbar functionality
 * 2. Improving character counting
 * 3. Adding smooth transitions and animations
 * 4. Fixing mobile layout issues
 */

document.addEventListener('DOMContentLoaded', function() {
    // Elements
    const replyForm = document.getElementById('reply-form');
    const replySubject = document.getElementById('reply_subject');
    const replyMessage = document.getElementById('reply_message');
    const charCount = document.getElementById('char-count');
    const toolbarButtons = document.querySelectorAll('.reply-toolbar-btn');
    const variablesBtn = document.getElementById('variables-btn');
    const variablesDropdown = document.getElementById('variables-dropdown');
    const variableLinks = document.querySelectorAll('[data-variable]');
    const previewBtn = document.querySelector('.preview-reply-btn');
    const sendBtn = document.getElementById('send-reply-btn');
    
    // Initialize character counter
    if (replyMessage && charCount) {
        updateCharCount();
        
        // Update character count on input
        replyMessage.addEventListener('input', updateCharCount);
    }
    
    // Toolbar button functionality
    if (toolbarButtons.length > 0) {
        toolbarButtons.forEach(button => {
            button.addEventListener('click', function(e) {
                e.preventDefault();
                
                if (!replyMessage) return;
                
                const action = this.getAttribute('data-action');
                const selStart = replyMessage.selectionStart;
                const selEnd = replyMessage.selectionEnd;
                const selectedText = replyMessage.value.substring(selStart, selEnd);
                const beforeText = replyMessage.value.substring(0, selStart);
                const afterText = replyMessage.value.substring(selEnd);
                
                switch (action) {
                    case 'bold':
                        insertFormatting('**', '**', selectedText);
                        break;
                    case 'italic':
                        insertFormatting('_', '_', selectedText);
                        break;
                    case 'link':
                        if (selectedText) {
                            const url = prompt('Enter URL:', 'https://');
                            if (url) {
                                const linkText = `[${selectedText}](${url})`;
                                replyMessage.value = beforeText + linkText + afterText;
                                replyMessage.focus();
                                replyMessage.setSelectionRange(
                                    beforeText.length + linkText.length,
                                    beforeText.length + linkText.length
                                );
                            }
                        } else {
                            const url = prompt('Enter URL:', 'https://');
                            const linkText = prompt('Enter link text:', 'Link text');
                            if (url && linkText) {
                                const fullLink = `[${linkText}](${url})`;
                                replyMessage.value = beforeText + fullLink + afterText;
                                replyMessage.focus();
                                replyMessage.setSelectionRange(
                                    beforeText.length + fullLink.length,
                                    beforeText.length + fullLink.length
                                );
                            }
                        }
                        break;
                    case 'template':
                        // Insert a template response with variables
                        const template = `Dear {contact_name},\n\nThank you for contacting us. We have received your message and will respond to your inquiry as soon as possible.\n\nBest regards,\n{admin_name}\nManage Inc. Team`;
                        
                        // Get contact name from the page
                        const contactNameElement = document.querySelector('.contact-info-content div');
                        const contactName = contactNameElement ? contactNameElement.textContent.trim() : '{contact_name}';
                        
                        // Replace variables with actual values
                        let processedTemplate = template
                            .replace(/{contact_name}/g, contactName)
                            .replace(/{admin_name}/g, document.querySelector('.admin-user-menu-toggle span').textContent.trim());
                        
                        replyMessage.value = processedTemplate;
                        replyMessage.focus();
                        updateCharCount();
                        break;
                }
                
                // Update character count after action
                updateCharCount();
            });
        });
    }
    
    // Variables dropdown functionality
    if (variablesBtn && variablesDropdown) {
        // Toggle variables dropdown
        variablesBtn.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            variablesDropdown.classList.toggle('show');
        });
        
        // Close dropdown when clicking outside
        document.addEventListener('click', function(e) {
            if (!variablesBtn.contains(e.target) && !variablesDropdown.contains(e.target)) {
                variablesDropdown.classList.remove('show');
            }
        });
    }
    
    // Variable insertion
    if (variableLinks.length > 0 && replyMessage) {
        variableLinks.forEach(link => {
            link.addEventListener('click', function(e) {
                e.preventDefault();
                
                const variable = this.getAttribute('data-variable');
                const selStart = replyMessage.selectionStart;
                const selEnd = replyMessage.selectionEnd;
                const beforeText = replyMessage.value.substring(0, selStart);
                const afterText = replyMessage.value.substring(selEnd);
                
                replyMessage.value = beforeText + variable + afterText;
                replyMessage.focus();
                replyMessage.setSelectionRange(
                    selStart + variable.length,
                    selStart + variable.length
                );
                
                // Update character count
                updateCharCount();
                
                // Close dropdown
                variablesDropdown.classList.remove('show');
            });
        });
    }
    
    // Preview functionality
    if (previewBtn && replyMessage) {
        // Create modal if it doesn't exist
        if (!document.getElementById('previewModal')) {
            createPreviewModal();
        }
        
        const previewModal = document.getElementById('previewModal');
        const previewSubject = document.getElementById('previewSubject');
        const previewContent = document.getElementById('previewContent');
        const closePreviewBtn = document.getElementById('closePreviewBtn');
        
        previewBtn.addEventListener('click', function(e) {
            e.preventDefault();
            
            if (!replyMessage.value.trim()) {
                alert('Please enter a message before previewing.');
                replyMessage.focus();
                return;
            }
            
            // Set preview content
            previewSubject.textContent = replySubject.value;
            
            // Convert markdown to HTML for preview
            let messageHtml = replyMessage.value
                .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>') // Bold
                .replace(/_(.*?)_/g, '<em>$1</em>') // Italic
                .replace(/\[(.*?)\]\((.*?)\)/g, '<a href="$2" target="_blank">$1</a>') // Links
                .replace(/\n/g, '<br>'); // Line breaks
            
            previewContent.innerHTML = messageHtml;
            
            // Show modal
            previewModal.classList.add('show');
            document.body.classList.add('modal-open');
        });
        
        // Close preview modal
        if (closePreviewBtn) {
            closePreviewBtn.addEventListener('click', function() {
                previewModal.classList.remove('show');
                document.body.classList.remove('modal-open');
            });
        }
    }
    
    // Form submission
    if (replyForm && sendBtn) {
        sendBtn.addEventListener('click', function(e) {
            if (!replyMessage.value.trim()) {
                e.preventDefault();
                alert('Please enter a message before sending.');
                replyMessage.focus();
                return;
            }
            
            // Add loading state
            this.disabled = true;
            this.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Sending...';
            
            // Submit form
            replyForm.submit();
        });
    }
    
    // Helper functions
    function updateCharCount() {
        if (replyMessage && charCount) {
            const count = replyMessage.value.length;
            charCount.textContent = count;
            
            // Add warning class if count is too high
            if (count > 5000) {
                charCount.classList.add('warning');
            } else {
                charCount.classList.remove('warning');
            }
        }
    }
    
    function insertFormatting(prefix, suffix, selectedText) {
        if (!replyMessage) return;
        
        const selStart = replyMessage.selectionStart;
        const selEnd = replyMessage.selectionEnd;
        const beforeText = replyMessage.value.substring(0, selStart);
        const afterText = replyMessage.value.substring(selEnd);
        
        if (selectedText) {
            replyMessage.value = beforeText + prefix + selectedText + suffix + afterText;
            replyMessage.focus();
            replyMessage.setSelectionRange(
                selStart + prefix.length + selectedText.length + suffix.length,
                selStart + prefix.length + selectedText.length + suffix.length
            );
        } else {
            replyMessage.value = beforeText + prefix + suffix + afterText;
            replyMessage.focus();
            replyMessage.setSelectionRange(
                selStart + prefix.length,
                selStart + prefix.length
            );
        }
    }
    
    function createPreviewModal() {
        const modal = document.createElement('div');
        modal.id = 'previewModal';
        modal.className = 'admin-modal';
        modal.innerHTML = `
            <div class="admin-modal-content">
                <div class="admin-modal-header">
                    <h3><i class="fas fa-eye"></i> Preview Reply</h3>
                    <button type="button" class="admin-modal-close" id="closePreviewBtn">&times;</button>
                </div>
                <div class="admin-modal-body">
                    <div class="preview-email">
                        <div class="preview-email-header">
                            <div class="preview-email-subject">
                                <strong>Subject:</strong> <span id="previewSubject"></span>
                            </div>
                        </div>
                        <div class="preview-email-content" id="previewContent"></div>
                    </div>
                </div>
                <div class="admin-modal-footer">
                    <button type="button" class="admin-btn outline" id="closePreviewBtn2">Close</button>
                </div>
            </div>
        `;
        
        document.body.appendChild(modal);
        
        // Add event listener to second close button
        document.getElementById('closePreviewBtn2').addEventListener('click', function() {
            modal.classList.remove('show');
            document.body.classList.remove('modal-open');
        });
        
        // Close modal when clicking outside
        modal.addEventListener('click', function(e) {
            if (e.target === modal) {
                modal.classList.remove('show');
                document.body.classList.remove('modal-open');
            }
        });
    }
});
