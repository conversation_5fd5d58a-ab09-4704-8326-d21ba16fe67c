/**
 * Standardized Modal Forms
 *
 * This file contains JavaScript functionality for all modal forms across the admin panel.
 * It provides consistent behavior for opening, closing, and submitting modal forms.
 */

document.addEventListener('DOMContentLoaded', function() {
    // Initialize all modals
    initModals();
});

/**
 * Initialize all modals on the page
 */
function initModals() {
    // Modal triggers
    const modalTriggers = document.querySelectorAll('[data-toggle="admin-modal"]');
    modalTriggers.forEach(trigger => {
        trigger.addEventListener('click', function(e) {
            e.preventDefault();
            const targetModalId = this.getAttribute('data-target');
            openModal(targetModalId);
        });
    });

    // Modal close buttons
    const modalClosers = document.querySelectorAll('.admin-modal-close, [data-dismiss="admin-modal"]');
    modalClosers.forEach(closer => {
        closer.addEventListener('click', function(e) {
            e.preventDefault();
            const modal = this.closest('.admin-modal');
            if (modal) {
                closeModal(modal.id);
            }
        });
    });

    // Close modal when clicking outside
    const modals = document.querySelectorAll('.admin-modal');
    modals.forEach(modal => {
        modal.addEventListener('click', function(e) {
            if (e.target === this) {
                closeModal(this.id);
            }
        });
    });

    // Make modals draggable by header
    initDraggableModals();

    // Initialize tabs in modals
    initModalTabs();
}

/**
 * Make modals draggable by their headers
 */
function initDraggableModals() {
    const modalHeaders = document.querySelectorAll('.admin-modal-header');

    modalHeaders.forEach(header => {
        const dialog = header.closest('.admin-modal-dialog');
        if (!dialog) return;

        // Set cursor style to indicate draggable
        header.style.cursor = 'move';

        let isDragging = false;
        let offsetX, offsetY;

        // Mouse down event to start dragging
        header.addEventListener('mousedown', function(e) {
            // Only handle left mouse button
            if (e.button !== 0) return;

            // Prevent text selection during drag
            e.preventDefault();

            // Get the initial position
            const dialogRect = dialog.getBoundingClientRect();

            // Calculate the offset from the mouse position to the dialog position
            offsetX = e.clientX - dialogRect.left;
            offsetY = e.clientY - dialogRect.top;

            isDragging = true;

            // Add dragging class for visual feedback
            dialog.classList.add('dragging');
        });

        // Mouse move event to handle dragging
        document.addEventListener('mousemove', function(e) {
            if (!isDragging) return;

            // Calculate new position
            const x = e.clientX - offsetX;
            const y = e.clientY - offsetY;

            // Apply new position
            dialog.style.position = 'absolute';
            dialog.style.margin = '0';
            dialog.style.left = `${x}px`;
            dialog.style.top = `${y}px`;
            dialog.style.transform = 'none';
        });

        // Mouse up event to stop dragging
        document.addEventListener('mouseup', function() {
            if (!isDragging) return;

            isDragging = false;

            // Remove dragging class
            dialog.classList.remove('dragging');
        });
    });
}

/**
 * Open a modal by ID
 * @param {string} modalId - The ID of the modal to open
 */
function openModal(modalId) {
    const modal = document.getElementById(modalId);
    if (!modal) return;

    // Add body class to prevent scrolling
    document.body.classList.add('modal-open');

    // Reset any previous positioning
    const dialog = modal.querySelector('.admin-modal-dialog');
    if (dialog) {
        dialog.style.position = '';
        dialog.style.top = '';
        dialog.style.left = '';
        dialog.style.margin = '';
        dialog.style.transform = '';
    }

    // Show modal
    modal.style.display = 'flex';

    // Trigger reflow to enable transition
    void modal.offsetWidth;

    // Add show class for animation
    modal.classList.add('show');

    // Center the modal in the viewport
    centerModalInViewport(modal);

    // Focus first input
    setTimeout(() => {
        const firstInput = modal.querySelector('input:not([type="hidden"]), select, textarea');
        if (firstInput) {
            firstInput.focus();
        }
    }, 300);

    // Trigger event
    const event = new CustomEvent('modal:opened', { detail: { modalId } });
    document.dispatchEvent(event);
}

/**
 * Center a modal in the viewport
 * @param {HTMLElement} modal - The modal element to center
 */
function centerModalInViewport(modal) {
    const dialog = modal.querySelector('.admin-modal-dialog');
    if (!dialog) return;

    // Get viewport dimensions
    const viewportWidth = window.innerWidth;
    const viewportHeight = window.innerHeight;

    // Get dialog dimensions
    const dialogRect = dialog.getBoundingClientRect();
    const dialogWidth = dialogRect.width;
    const dialogHeight = dialogRect.height;

    // Calculate center position
    const left = Math.max(0, (viewportWidth - dialogWidth) / 2);
    const top = Math.max(0, (viewportHeight - dialogHeight) / 2);

    // Apply centered position
    dialog.style.position = 'absolute';
    dialog.style.margin = '0';
    dialog.style.left = `${left}px`;
    dialog.style.top = `${top}px`;
    dialog.style.transform = 'none';
}

/**
 * Close a modal by ID
 * @param {string} modalId - The ID of the modal to close
 */
function closeModal(modalId) {
    const modal = document.getElementById(modalId);
    if (!modal) return;

    // Remove show class for animation
    modal.classList.remove('show');

    // Hide modal after animation
    setTimeout(() => {
        modal.style.display = 'none';

        // Remove body class
        const openModals = document.querySelectorAll('.admin-modal.show');
        if (openModals.length === 0) {
            document.body.classList.remove('modal-open');
        }

        // Trigger event
        const event = new CustomEvent('modal:closed', { detail: { modalId } });
        document.dispatchEvent(event);
    }, 300);
}

/**
 * Initialize tabs in modals
 */
function initModalTabs() {
    const tabButtons = document.querySelectorAll('.admin-modal-tab');
    tabButtons.forEach(button => {
        button.addEventListener('click', function() {
            // Get tab group
            const tabGroup = this.closest('.admin-modal-tabs');
            if (!tabGroup) return;

            // Get tab content container
            const modal = this.closest('.admin-modal');
            if (!modal) return;

            // Get tab ID
            const tabId = this.getAttribute('data-tab');
            if (!tabId) return;

            // Remove active class from all tabs in this group
            const tabs = tabGroup.querySelectorAll('.admin-modal-tab');
            tabs.forEach(tab => tab.classList.remove('active'));

            // Add active class to clicked tab
            this.classList.add('active');

            // Hide all tab content
            const tabContents = modal.querySelectorAll('.admin-modal-tab-content');
            tabContents.forEach(content => content.classList.remove('active'));

            // Show selected tab content
            const selectedContent = modal.querySelector(`.admin-modal-tab-content[data-tab="${tabId}"]`);
            if (selectedContent) {
                selectedContent.classList.add('active');
            }

            // Trigger event
            const event = new CustomEvent('modal:tabChanged', {
                detail: {
                    modalId: modal.id,
                    tabId: tabId
                }
            });
            document.dispatchEvent(event);
        });
    });
}

/**
 * Create a modal dynamically
 * @param {Object} options - Modal options
 * @param {string} options.id - Modal ID
 * @param {string} options.title - Modal title
 * @param {string} options.content - Modal content HTML
 * @param {Array} options.buttons - Array of button objects
 * @param {string} options.size - Modal size (small, medium, large)
 * @returns {HTMLElement} The created modal element
 */
function createModal(options) {
    // Default options
    const defaults = {
        id: 'dynamic-modal-' + Date.now(),
        title: 'Modal Title',
        content: '',
        buttons: [
            {
                text: 'Close',
                type: 'secondary',
                action: 'close'
            }
        ],
        size: 'medium'
    };

    // Merge options
    const settings = Object.assign({}, defaults, options);

    // Create modal element
    const modal = document.createElement('div');
    modal.id = settings.id;
    modal.className = 'admin-modal';

    // Set size class
    if (settings.size === 'small') {
        modal.classList.add('admin-modal-sm');
    } else if (settings.size === 'large') {
        modal.classList.add('admin-modal-lg');
    }

    // Create modal HTML
    let buttonsHtml = '';
    settings.buttons.forEach(button => {
        const type = button.type || 'secondary';
        const action = button.action || '';
        const actionAttr = action ? `data-action="${action}"` : '';
        const dismissAttr = action === 'close' ? 'data-dismiss="admin-modal"' : '';

        buttonsHtml += `
            <button type="button" class="admin-modal-btn admin-modal-btn-${type}" ${actionAttr} ${dismissAttr}>
                ${button.text}
            </button>
        `;
    });

    modal.innerHTML = `
        <div class="admin-modal-dialog">
            <div class="admin-modal-content">
                <div class="admin-modal-header">
                    <h3>${settings.title}</h3>
                    <button type="button" class="admin-modal-close" data-dismiss="admin-modal">&times;</button>
                </div>
                <div class="admin-modal-body">
                    ${settings.content}
                </div>
                <div class="admin-modal-footer">
                    ${buttonsHtml}
                </div>
            </div>
        </div>
    `;

    // Add to document
    document.body.appendChild(modal);

    // Initialize modal
    initModals();

    // Return modal element
    return modal;
}

// Export functions
window.AdminModal = {
    open: openModal,
    close: closeModal,
    create: createModal
};
