<?php
/**
 * CollaborativeEditing Class
 * 
 * Manages collaborative editing features for the frontend editor
 */
class CollaborativeEditing {
    private $conn;
    private $user_id;
    private $lock_timeout = 300; // 5 minutes in seconds
    
    /**
     * Constructor
     * 
     * @param mysqli $conn Database connection
     * @param int $user_id User ID
     */
    public function __construct($conn, $user_id) {
        $this->conn = $conn;
        $this->user_id = $user_id;
        
        // Clean up expired locks
        $this->cleanupExpiredLocks();
    }
    
    /**
     * Clean up expired locks
     */
    private function cleanupExpiredLocks() {
        $sql = "DELETE FROM file_locks WHERE expires_at < NOW()";
        $this->conn->query($sql);
    }
    
    /**
     * Acquire a lock on a file
     * 
     * @param string $file_path File path
     * @return array Lock status information
     */
    public function acquireLock($file_path) {
        // Check if file is already locked
        $current_lock = $this->getLock($file_path);
        
        if ($current_lock) {
            // If locked by current user, extend the lock
            if ($current_lock['user_id'] == $this->user_id) {
                return $this->extendLock($file_path);
            }
            
            // File is locked by another user
            return [
                'success' => false,
                'message' => 'File is locked by another user',
                'locked_by' => $current_lock['username'],
                'locked_until' => $current_lock['expires_at']
            ];
        }
        
        // Create a new lock
        $expires_at = date('Y-m-d H:i:s', time() + $this->lock_timeout);
        
        $sql = "INSERT INTO file_locks (file_path, user_id, expires_at) 
                VALUES (?, ?, ?)
                ON DUPLICATE KEY UPDATE 
                user_id = VALUES(user_id), 
                locked_at = NOW(), 
                expires_at = VALUES(expires_at)";
        
        $stmt = $this->conn->prepare($sql);
        $stmt->bind_param("sis", $file_path, $this->user_id, $expires_at);
        
        if ($stmt->execute()) {
            return [
                'success' => true,
                'message' => 'Lock acquired successfully',
                'expires_at' => $expires_at
            ];
        }
        
        return [
            'success' => false,
            'message' => 'Failed to acquire lock: ' . $this->conn->error
        ];
    }
    
    /**
     * Extend an existing lock
     * 
     * @param string $file_path File path
     * @return array Lock status information
     */
    public function extendLock($file_path) {
        $expires_at = date('Y-m-d H:i:s', time() + $this->lock_timeout);
        
        $sql = "UPDATE file_locks 
                SET expires_at = ? 
                WHERE file_path = ? AND user_id = ?";
        
        $stmt = $this->conn->prepare($sql);
        $stmt->bind_param("ssi", $expires_at, $file_path, $this->user_id);
        
        if ($stmt->execute()) {
            return [
                'success' => true,
                'message' => 'Lock extended successfully',
                'expires_at' => $expires_at
            ];
        }
        
        return [
            'success' => false,
            'message' => 'Failed to extend lock: ' . $this->conn->error
        ];
    }
    
    /**
     * Release a lock on a file
     * 
     * @param string $file_path File path
     * @return array Lock status information
     */
    public function releaseLock($file_path) {
        $sql = "DELETE FROM file_locks 
                WHERE file_path = ? AND user_id = ?";
        
        $stmt = $this->conn->prepare($sql);
        $stmt->bind_param("si", $file_path, $this->user_id);
        
        if ($stmt->execute()) {
            return [
                'success' => true,
                'message' => 'Lock released successfully'
            ];
        }
        
        return [
            'success' => false,
            'message' => 'Failed to release lock: ' . $this->conn->error
        ];
    }
    
    /**
     * Get the current lock on a file
     * 
     * @param string $file_path File path
     * @return array|null Lock information or null if not locked
     */
    public function getLock($file_path) {
        $sql = "SELECT fl.*, u.username 
                FROM file_locks fl
                JOIN users u ON fl.user_id = u.id
                WHERE fl.file_path = ?";
        
        $stmt = $this->conn->prepare($sql);
        $stmt->bind_param("s", $file_path);
        $stmt->execute();
        $result = $stmt->get_result();
        
        if ($result->num_rows > 0) {
            return $result->fetch_assoc();
        }
        
        return null;
    }
    
    /**
     * Check if a file is locked by the current user
     * 
     * @param string $file_path File path
     * @return bool True if locked by current user, false otherwise
     */
    public function isLockedByCurrentUser($file_path) {
        $lock = $this->getLock($file_path);
        
        return $lock && $lock['user_id'] == $this->user_id;
    }
    
    /**
     * Force release a lock (admin only)
     * 
     * @param string $file_path File path
     * @return array Lock status information
     */
    public function forceReleaseLock($file_path) {
        // Check if user is admin
        $sql = "SELECT is_admin FROM users WHERE id = ?";
        $stmt = $this->conn->prepare($sql);
        $stmt->bind_param("i", $this->user_id);
        $stmt->execute();
        $result = $stmt->get_result();
        $user = $result->fetch_assoc();
        
        if (!$user || $user['is_admin'] != 1) {
            return [
                'success' => false,
                'message' => 'Only administrators can force release locks'
            ];
        }
        
        $sql = "DELETE FROM file_locks WHERE file_path = ?";
        $stmt = $this->conn->prepare($sql);
        $stmt->bind_param("s", $file_path);
        
        if ($stmt->execute()) {
            return [
                'success' => true,
                'message' => 'Lock force released successfully'
            ];
        }
        
        return [
            'success' => false,
            'message' => 'Failed to force release lock: ' . $this->conn->error
        ];
    }
    
    /**
     * Get all active locks
     * 
     * @return array Array of active locks
     */
    public function getAllActiveLocks() {
        $sql = "SELECT fl.*, u.username 
                FROM file_locks fl
                JOIN users u ON fl.user_id = u.id
                WHERE fl.expires_at > NOW()
                ORDER BY fl.locked_at DESC";
        
        $result = $this->conn->query($sql);
        
        $locks = [];
        while ($row = $result->fetch_assoc()) {
            $locks[] = $row;
        }
        
        return $locks;
    }
}
?>
