<?php
/**
 * Email Functions
 * Unified email system using mail() by default and SMTP when enabled
 */

// Ensure we have access to the get_setting function
if (!function_exists('get_setting')) {
    /**
     * Get setting value from database
     * Fallback function if not already defined
     */
    function get_setting($key, $default = '') {
        global $conn;

        if (!$conn) {
            // If no database connection, try to get from config constants
            $constant_name = strtoupper($key);
            if (defined($constant_name)) {
                return constant($constant_name);
            }
            return $default;
        }

        try {
            $stmt = $conn->prepare("SELECT setting_value FROM system_settings WHERE setting_key = ? LIMIT 1");
            if (!$stmt) {
                return $default;
            }

            $stmt->bind_param("s", $key);
            $stmt->execute();
            $result = $stmt->get_result();

            if ($result && $result->num_rows > 0) {
                $row = $result->fetch_assoc();
                return $row['setting_value'];
            }

            $stmt->close();
        } catch (Exception $e) {
            error_log("Error getting setting '$key': " . $e->getMessage());
        }

        return $default;
    }
}

// Ensure we have access to other helper functions
if (!function_exists('get_site_url')) {
    /**
     * Get site URL
     * Fallback function if not already defined
     */
    function get_site_url($path = '') {
        $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
        $host = $_SERVER['HTTP_HOST'];
        $script_dir = dirname($_SERVER['SCRIPT_NAME']);

        // Remove /admin from the path if present
        $script_dir = str_replace('/admin', '', $script_dir);

        $base_url = $protocol . '://' . $host . $script_dir;

        if (!empty($path)) {
            $base_url .= '/' . ltrim($path, '/');
        }

        return $base_url;
    }
}

if (!function_exists('get_admin_logo_path')) {
    /**
     * Get admin logo path
     * Fallback function if not already defined
     */
    function get_admin_logo_path() {
        return get_setting('admin_logo', 'images/logo.png');
    }
}

if (!function_exists('sanitize_input')) {
    /**
     * Sanitize input
     * Fallback function if not already defined
     */
    function sanitize_input($input) {
        if (is_array($input)) {
            foreach ($input as $key => $value) {
                $input[$key] = sanitize_input($value);
            }
            return $input;
        }

        if (is_string($input)) {
            return htmlspecialchars(trim($input), ENT_QUOTES, 'UTF-8');
        }

        return $input;
    }
}

/**
 * Send email using unified system
 * Uses mail() by default, SMTP only when use_smtp is enabled
 *
 * @param string $to Recipient email
 * @param string $subject Email subject
 * @param string $content Email content (HTML or plain text)
 * @param array $attachments Email attachments (not supported with mail())
 * @return bool True on success, false on failure
 */
function send_email($to, $subject, $content, $attachments = []) {
    global $conn;

    // Get email settings
    $use_smtp = get_setting('use_smtp', '0');

    if ($use_smtp === '1') {
        return send_email_smtp($to, $subject, $content, $attachments);
    } else {
        return send_email_mail($to, $subject, $content);
    }
}

/**
 * Send email using PHP mail() function
 *
 * @param string $to Recipient email
 * @param string $subject Email subject
 * @param string $content Email content
 * @return bool True on success, false on failure
 */
function send_email_mail($to, $subject, $content) {
    // Get email settings from database or config
    $from_email = get_setting('from_email', defined('FROM_EMAIL') ? FROM_EMAIL : 'noreply@' . $_SERVER['HTTP_HOST']);
    $from_name = get_setting('from_name', defined('FROM_NAME') ? FROM_NAME : get_setting('site_name', 'Website'));

    // Build headers like in installation process
    $headers = "From: " . $from_name . " <" . $from_email . ">\r\n";
    $headers .= "Reply-To: " . $from_email . "\r\n";
    $headers .= "X-Mailer: PHP/" . phpversion() . "\r\n";

    // Detect if content is HTML
    if (strpos($content, '<html>') !== false || strpos($content, '<body>') !== false || strpos($content, '<br>') !== false || strpos($content, '<p>') !== false) {
        $headers .= "MIME-Version: 1.0\r\n";
        $headers .= "Content-Type: text/html; charset=UTF-8\r\n";
    } else {
        $headers .= "MIME-Version: 1.0\r\n";
        $headers .= "Content-Type: text/plain; charset=UTF-8\r\n";
    }

    // Send email using PHP mail() function
    $result = mail($to, $subject, $content, $headers);

    if ($result) {
        error_log("Email sent successfully to $to using PHP mail()");
    } else {
        error_log("Failed to send email to $to using PHP mail()");
    }

    return $result;
}

/**
 * Send email using SMTP
 *
 * @param string $to Recipient email
 * @param string $subject Email subject
 * @param string $content Email content
 * @param array $attachments Email attachments (optional)
 * @return bool True on success, false on failure
 */
function send_email_smtp($to, $subject, $content, $attachments = []) {
    // Get SMTP settings
    $smtp_host = get_setting('smtp_host', '');
    $smtp_port = get_setting('smtp_port', '587');
    $smtp_username = get_setting('smtp_username', '');
    $smtp_password = get_setting('smtp_password', '');
    $smtp_security = get_setting('smtp_security', 'tls');
    $from_email = get_setting('from_email', defined('FROM_EMAIL') ? FROM_EMAIL : 'noreply@' . $_SERVER['HTTP_HOST']);
    $from_name = get_setting('from_name', defined('FROM_NAME') ? FROM_NAME : get_setting('site_name', 'Website'));

    // Validate SMTP settings
    if (empty($smtp_host) || empty($smtp_username) || empty($smtp_password)) {
        error_log("SMTP settings incomplete, falling back to PHP mail()");
        return send_email_mail($to, $subject, $content);
    }

    try {
        // Use SimpleMailer for SMTP
        require_once __DIR__ . '/../lib/SimpleMailer.php';
        $mailer = new SimpleMailer(null); // Pass null since we'll set settings manually

        // Set SMTP settings
        $mailer->setSettings([
            'use_smtp' => true,
            'smtp_host' => $smtp_host,
            'smtp_port' => $smtp_port,
            'smtp_username' => $smtp_username,
            'smtp_password' => $smtp_password,
            'smtp_security' => $smtp_security,
            'from_email' => $from_email,
            'from_name' => $from_name
        ]);

        $result = $mailer->send($to, $subject, $content, $attachments);
        return $result['success'] ?? false;

    } catch (Exception $e) {
        error_log("SMTP sending failed: " . $e->getMessage() . ", falling back to PHP mail()");
        return send_email_mail($to, $subject, $content);
    }
}

/**
 * Send email using template
 *
 * @param string $template_slug Template slug
 * @param string $to Recipient email
 * @param array $variables Variables to replace
 * @return bool True on success, false on failure
 */
function send_email_template($template_slug, $to, $variables = []) {
    // Get template
    $template = get_email_template_by_slug($template_slug);

    if (!$template) {
        // If template doesn't exist, create a simple fallback email
        error_log("Email template '$template_slug' not found, using fallback");

        $subject = "Notification from " . get_setting('site_name', 'Your Website');
        $content = "This is a notification email.\n\n";

        // Add variables to content if provided
        if (!empty($variables)) {
            foreach ($variables as $key => $value) {
                $clean_key = str_replace(['{{', '}}'], '', $key);
                $content .= ucfirst(str_replace('_', ' ', $clean_key)) . ": " . $value . "\n";
            }
        }

        $content .= "\nBest regards,\n" . get_setting('site_name', 'Your Website');

        return send_email($to, $subject, $content);
    }

    // Add general variables with proper email URLs
    $site_url = get_site_url();
    $logo_path = get_setting('logo', 'images/logo.png');
    $admin_logo_path = get_admin_logo_path();

    // Clean admin logo path for email delivery
    if (strpos($admin_logo_path, 'admin/') === 0) {
        $admin_logo_path = substr($admin_logo_path, 6); // Remove 'admin/' prefix
    }

    $variables = array_merge([
        '{{site_name}}' => get_setting('site_name', 'Your Website'),
        '{{site_url}}' => $site_url,
        '{{admin_email}}' => get_setting('admin_email', ''),
        '{{current_year}}' => date('Y'),
        '{{logo}}' => $site_url . '/' . ltrim($logo_path, '/'),
        '{{admin_logo}}' => $site_url . '/' . ltrim($admin_logo_path, '/'),
        '{{primary_color}}' => get_setting('primary_color', '#3c3c45'),
        '{{secondary_color}}' => get_setting('secondary_color', '#f1ca2f')
    ], $variables);

    // Parse template
    $subject = parse_template($template['subject'], $variables);
    $content = parse_template($template['content'], $variables);

    // Send email
    return send_email($to, $subject, $content);
}

/**
 * Parse template with variables
 *
 * @param string $template Template content
 * @param array $variables Variables to replace
 * @return string Parsed template
 */
function parse_template($template, $variables) {
    foreach ($variables as $key => $value) {
        $template = str_replace($key, $value, $template);
    }

    return $template;
}

/**
 * Get email templates
 *
 * @return array Array of email templates
 */
function get_email_templates() {
    global $conn;

    $result = $conn->query("SELECT * FROM email_templates ORDER BY name");

    $templates = [];

    while ($row = $result->fetch_assoc()) {
        $templates[] = $row;
    }

    return $templates;
}

/**
 * Get email template by ID
 *
 * @param int $id Template ID
 * @return array|null Template data or null if not found
 */
function get_email_template($id) {
    global $conn;

    $stmt = $conn->prepare("SELECT * FROM email_templates WHERE id = ?");
    $stmt->bind_param("i", $id);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result->num_rows === 0) {
        return null;
    }

    return $result->fetch_assoc();
}

/**
 * Get email template by slug
 *
 * @param string $slug Template slug
 * @return array|null Template data or null if not found
 */
function get_email_template_by_slug($slug) {
    global $conn;

    if (!$conn) {
        return null;
    }

    try {
        // First check if slug column exists
        $check_column = $conn->query("SHOW COLUMNS FROM email_templates LIKE 'slug'");

        if ($check_column && $check_column->num_rows > 0) {
            // Slug column exists, use it
            $stmt = $conn->prepare("SELECT * FROM email_templates WHERE slug = ?");
            $stmt->bind_param("s", $slug);
        } else {
            // Slug column doesn't exist, try to match by template_key or name
            $stmt = $conn->prepare("SELECT * FROM email_templates WHERE template_key = ? OR name = ?");
            $stmt->bind_param("ss", $slug, $slug);
        }

        $stmt->execute();
        $result = $stmt->get_result();

        if ($result->num_rows === 0) {
            return null;
        }

        return $result->fetch_assoc();
    } catch (Exception $e) {
        error_log("Error getting email template by slug '$slug': " . $e->getMessage());
        return null;
    }
}

/**
 * Update email template
 *
 * @param int $id Template ID
 * @param array $data Template data
 * @return bool True on success, false on failure
 */
function update_email_template($id, $data) {
    global $conn;

    $stmt = $conn->prepare("UPDATE email_templates SET subject = ?, content = ?, updated_at = NOW() WHERE id = ?");
    $stmt->bind_param("ssi", $data['subject'], $data['content'], $id);

    return $stmt->execute();
}

/**
 * Reset email template to default
 *
 * @param int $id Template ID
 * @return bool True on success, false on failure
 */
function reset_email_template($id) {
    global $conn;

    $stmt = $conn->prepare("UPDATE email_templates SET subject = default_subject, content = default_content, updated_at = NOW() WHERE id = ?");
    $stmt->bind_param("i", $id);

    return $stmt->execute();
}

/**
 * Get template variables
 *
 * @param string $type Template type
 * @return array Array of template variables
 */
function get_template_variables($type = '') {
    $variables = [
        'general' => [
            '{{site_name}}' => 'Name of the website',
            '{{site_url}}' => 'URL of the website',
            '{{admin_email}}' => 'Admin email address',
            '{{current_year}}' => 'Current year',
            '{{logo}}' => 'Website logo URL',
            '{{admin_logo}}' => 'Admin panel logo URL',
            '{{primary_color}}' => 'Primary theme color',
            '{{secondary_color}}' => 'Secondary theme color'
        ],
        'email' => [
            '{{user_name}}' => 'Name of the user',
            '{{user_email}}' => 'Email address of the user',
            '{{verification_link}}' => 'Email verification link',
            '{{reset_link}}' => 'Password reset link',
            '{{login_link}}' => 'Login page link',
            '{{contact_name}}' => 'Name of the contact form submitter',
            '{{contact_email}}' => 'Email of the contact form submitter',
            '{{contact_subject}}' => 'Subject of the contact form',
            '{{contact_message}}' => 'Message from the contact form'
        ],
        'news' => [
            '{{news_title}}' => 'Title of the news article',
            '{{news_excerpt}}' => 'Excerpt of the news article',
            '{{news_content}}' => 'Content of the news article',
            '{{news_image}}' => 'Image of the news article',
            '{{news_date}}' => 'Publication date of the news article',
            '{{news_author}}' => 'Author of the news article',
            '{{news_category}}' => 'Category of the news article',
            '{{news_url}}' => 'URL of the news article'
        ]
    ];

    if ($type && isset($variables[$type])) {
        return $variables[$type];
    }

    return $variables;
}

/**
 * Send test email using unified system
 *
 * @param string $test_email Email address to send test to
 * @return bool True on success, false on failure
 */
function send_test_email($test_email = null) {
    if (!$test_email) {
        $test_email = get_setting('admin_email', get_setting('from_email', ''));
    }

    if (empty($test_email)) {
        return false;
    }

    $subject = 'Test Email from ' . get_setting('site_name', 'Your Website');
    $message = 'This is a test email from your website. If you received this email, your email settings are configured correctly.';

    return send_email($test_email, $subject, $message);
}

/**
 * Send verification email
 *
 * @param string $to Recipient email
 * @param string $name Recipient name
 * @param string $token Verification token
 * @return bool True on success, false on failure
 */
function send_verification_email($to, $name, $token) {
    $verification_link = get_site_url('verify.php?token=' . $token);

    $variables = [
        '{{user_name}}' => $name,
        '{{user_email}}' => $to,
        '{{verification_link}}' => $verification_link
    ];

    return send_email_template('email-verification', $to, $variables);
}

/**
 * Send password reset email
 *
 * @param string $to Recipient email
 * @param string $name Recipient name
 * @param string $token Reset token
 * @return bool True on success, false on failure
 */
function send_password_reset_email($to, $name, $token) {
    $reset_link = get_site_url('reset-password.php?token=' . $token);

    $variables = [
        '{{user_name}}' => $name,
        '{{user_email}}' => $to,
        '{{reset_link}}' => $reset_link
    ];

    return send_email_template('password-reset', $to, $variables);
}

/**
 * Send welcome email
 *
 * @param string $to Recipient email
 * @param string $name Recipient name
 * @return bool True on success, false on failure
 */
function send_welcome_email($to, $name) {
    $login_link = get_site_url('login.php');

    $variables = [
        '{{user_name}}' => $name,
        '{{user_email}}' => $to,
        '{{login_link}}' => $login_link
    ];

    return send_email_template('welcome', $to, $variables);
}

/**
 * Send contact form email
 *
 * @param string $name Sender name
 * @param string $email Sender email
 * @param string $subject Email subject
 * @param string $message Email message
 * @return bool True on success, false on failure
 */
function send_contact_form_email($name, $email, $subject, $message) {
    $admin_email = get_setting('admin_email', '');

    if (empty($admin_email)) {
        return false;
    }

    $variables = [
        '{{contact_name}}' => $name,
        '{{contact_email}}' => $email,
        '{{contact_subject}}' => $subject,
        '{{contact_message}}' => $message
    ];

    return send_email_template('contact-form', $admin_email, $variables);
}

/**
 * Send notification email
 *
 * @param string $to Recipient email
 * @param string $subject Email subject
 * @param string $message Email message
 * @return bool True on success, false on failure
 */
function send_notification_email($to, $subject, $message) {
    $variables = [
        '{{notification_subject}}' => $subject,
        '{{notification_message}}' => $message
    ];

    return send_email_template('notification', $to, $variables);
}

/**
 * Get email settings
 *
 * @return array Email settings
 */
function get_email_settings() {
    $settings = [
        'use_smtp' => get_setting('use_smtp', '0'),
        'smtp_host' => get_setting('smtp_host', ''),
        'smtp_port' => get_setting('smtp_port', '587'),
        'smtp_username' => get_setting('smtp_username', ''),
        'smtp_password' => get_setting('smtp_password', ''),
        'smtp_security' => get_setting('smtp_security', 'tls'),
        'from_email' => get_setting('from_email', ''),
        'from_name' => get_setting('from_name', ''),
        'admin_email' => get_setting('admin_email', '')
    ];

    return $settings;
}
