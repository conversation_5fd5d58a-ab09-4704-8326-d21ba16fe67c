/**
 * All News Page CSS - Modern Overhaul
 *
 * This file contains styles specific to the all news page.
 */

/* News Container */
.news-container {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

/* News Filters */
.admin-table-actions {
  background-color: #fff;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  padding: 20px;
  margin-bottom: 24px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 16px;
}

.admin-table-filters {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  flex: 1;
}

.admin-table-filter {
  display: flex;
  align-items: center;
  gap: 8px;
}

.admin-table-filter label {
  font-weight: 500;
  color: #333;
  font-size: 14px;
  white-space: nowrap;
}

.admin-table-filter select {
  min-width: 150px;
  padding: 10px 14px;
  border-radius: 8px;
  border: 1px solid #e0e0e0;
  background-color: #f9f9f9;
  font-size: 14px;
  color: #333;
  cursor: pointer;
  transition: all 0.2s ease;
  appearance: none;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%23333' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: right 10px center;
  padding-right: 30px;
}

.admin-table-filter select:hover {
  border-color: #ccc;
}

.admin-table-filter select:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(var(--primary-rgb), 0.1);
  outline: none;
}

.admin-table-bulk-actions {
  display: flex;
  align-items: center;
  gap: 12px;
}

.admin-table-bulk-actions select {
  min-width: 150px;
  padding: 10px 14px;
  border-radius: 8px;
  border: 1px solid #e0e0e0;
  background-color: #f9f9f9;
  font-size: 14px;
  color: #333;
  cursor: pointer;
  transition: all 0.2s ease;
  appearance: none;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%23333' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: right 10px center;
  padding-right: 30px;
}

.admin-table-bulk-actions select:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(var(--primary-rgb), 0.1);
  outline: none;
}

/* News Table */
.admin-table-responsive {
  background-color: #fff;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  overflow: hidden;
}

.admin-table {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
}

.admin-table th {
  background-color: #f5f7fa;
  padding: 16px 20px;
  font-weight: 600;
  color: #333;
  text-align: left;
  font-size: 14px;
  border-bottom: 1px solid #e0e0e0;
  position: sticky;
  top: 0;
  z-index: 10;
}

.admin-table td {
  padding: 16px 20px;
  border-bottom: 1px solid #f0f0f0;
  vertical-align: middle;
}

.admin-table tbody tr {
  transition: all 0.2s ease;
}

.admin-table tbody tr:hover {
  background-color: #f9f9f9;
}

.admin-table tbody tr:last-child td {
  border-bottom: none;
}

/* Table Columns */
.check-column {
  width: 40px;
  text-align: center;
}

.check-column input[type="checkbox"] {
  width: 18px;
  height: 18px;
  cursor: pointer;
  border-radius: 4px;
  border: 1px solid #ccc;
  position: relative;
  appearance: none;
  background-color: #fff;
  transition: all 0.2s ease;
}

.check-column input[type="checkbox"]:checked {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
}

.check-column input[type="checkbox"]:checked::after {
  content: "";
  position: absolute;
  left: 6px;
  top: 2px;
  width: 5px;
  height: 10px;
  border: solid white;
  border-width: 0 2px 2px 0;
  transform: rotate(45deg);
}

.title-column {
  min-width: 300px;
}

.category-column {
  width: 150px;
}

.date-column {
  width: 150px;
}

.actions-column {
  width: 120px;
  text-align: right;
}

/* Post Title Area */
.post-title-area {
  display: flex;
  align-items: center;
  gap: 16px;
}

.post-thumbnail {
  width: 60px;
  height: 60px;
  border-radius: 8px;
  overflow: hidden;
  flex-shrink: 0;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
}

.post-thumbnail img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.post-title-area:hover .post-thumbnail img {
  transform: scale(1.05);
}

.post-info {
  flex: 1;
  min-width: 0;
}

.post-info strong {
  display: block;
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 6px;
  transition: color 0.2s ease;
}

.post-title-area:hover .post-info strong {
  color: var(--primary-color);
}

.row-actions {
  display: flex;
  gap: 8px;
  font-size: 13px;
  color: #666;
  flex-wrap: wrap;
}

.row-actions span {
  display: inline-flex;
  align-items: center;
}

.row-actions a {
  color: #666;
  text-decoration: none;
  transition: color 0.2s ease;
}

.row-actions a:hover {
  color: var(--primary-color);
  text-decoration: underline;
}

.row-actions .trash a:hover {
  color: var(--danger-color);
}

.row-actions .view a:hover {
  color: var(--info-color);
}

/* Category Column */
.category-column {
  font-size: 14px;
  color: #555;
}

.no-category {
  color: #999;
  font-style: italic;
}

/* Date Column */
.post-date {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.date-display {
  font-weight: 500;
  color: #333;
  font-size: 14px;
}

.time-display {
  color: #666;
  font-size: 13px;
}

/* Actions Column */
.news-actions {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
}

.news-actions .admin-btn {
  width: 36px;
  height: 36px;
  padding: 0;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border-radius: 8px;
  background-color: #f5f5f5;
  color: #555;
  border: none;
  cursor: pointer;
  transition: all 0.2s ease;
}

.news-actions .admin-btn:hover {
  background-color: #e0e0e0;
}

.news-actions .admin-btn.primary:hover {
  background-color: var(--primary-color);
  color: white;
}

.news-actions .admin-btn.secondary:hover {
  background-color: var(--info-color);
  color: white;
}

.news-actions .admin-btn.danger:hover {
  background-color: var(--danger-color);
  color: white;
}

/* Empty Table State */
.admin-table-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;
  background-color: #fff;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.admin-table-empty i {
  font-size: 48px;
  color: #ccc;
  margin-bottom: 20px;
}

.admin-table-empty p {
  font-size: 16px;
  color: #666;
  max-width: 400px;
  margin: 0 auto;
}

/* Pagination */
.admin-table-pagination {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 16px;
  margin-top: 24px;
  padding: 16px 20px;
  background-color: #fff;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.admin-table-pagination-info {
  color: #666;
  font-size: 14px;
}

.admin-table-pagination-per-page {
  display: flex;
  align-items: center;
  gap: 8px;
}

.admin-table-pagination-per-page label {
  color: #666;
  font-size: 14px;
}

.admin-table-pagination-per-page select {
  padding: 8px 12px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  background-color: #f9f9f9;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
  appearance: none;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%23333' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: right 8px center;
  padding-right: 30px;
}

.admin-table-pagination-controls {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.admin-table-pagination-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-width: 36px;
  height: 36px;
  padding: 0 12px;
  background-color: #fff;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  color: #333;
  font-size: 14px;
  text-decoration: none;
  cursor: pointer;
  transition: all 0.2s ease;
}

.admin-table-pagination-button:hover {
  background-color: #f5f5f5;
}

.admin-table-pagination-button.active {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
  color: white;
  font-weight: 600;
}

.admin-table-pagination-button.disabled {
  opacity: 0.5;
  cursor: not-allowed;
  pointer-events: none;
}

/* Page-specific styles that don't conflict with component styles */
@media (max-width: 992px) {
  /* Only keep styles that are specific to this page and don't override component styles */
  .news-container {
    gap: 16px;
  }
}

@media (max-width: 768px) {
  /* Only keep styles that are specific to this page and don't override component styles */
  .post-info strong {
    font-size: 14px;
  }

  .post-title-area {
    gap: 12px;
  }
}
