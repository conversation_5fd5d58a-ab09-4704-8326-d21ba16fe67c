!function(e){"object"==typeof exports&&"object"==typeof module?e(require("../../lib/codemirror"),require("../fold/xml-fold")):"function"==typeof define&&define.amd?define(["../../lib/codemirror","../fold/xml-fold"],e):e(CodeMirror)}(function(b){b.defineOption("autoCloseTags",!1,function(e,t,n){n!=b.Init&&n&&e.removeKeyMap("autoCloseTags"),t&&(n={name:"autoCloseTags"},"object"==typeof t&&!1===t.whenClosing||(n["'/'"]=function(e){return(e=e).getOption("disableInput")?b.Pass:o(e,!0)}),"object"==typeof t&&!1===t.whenOpening||(n["'>'"]=function(e){var t=e;if(t.getOption("disableInput"))return b.Pass;for(var n=t.listSelections(),o=[],r=t.getOption("autoCloseTags"),i=0;i<n.length;i++){if(!n[i].empty())return b.Pass;var a=n[i].head,l=t.getTokenAt(a),s=b.innerMode(t.getMode(),l.state),d=s.state,c=s.mode.xmlCurrentTag&&s.mode.xmlCurrentTag(d),f=c&&c.name;if(!f)return b.Pass;var g="html"==s.mode.configuration,u="object"==typeof r&&r.dontCloseTags||g&&y,g="object"==typeof r&&r.indentTags||g&&x,m=(f=l.end>a.ch?f.slice(0,f.length-l.end+a.ch):f).toLowerCase();if(!f||"string"==l.type&&(l.end!=a.ch||!/[\"\']/.test(l.string.charAt(l.string.length-1))||1==l.string.length)||"tag"==l.type&&c.close||l.string.indexOf("/")==a.ch-l.start-1||u&&-1<v(u,m)||P(t,s.mode.xmlCurrentContext&&s.mode.xmlCurrentContext(d)||[],f,a,!0))return b.Pass;c="object"==typeof r&&r.emptyTags;c&&-1<v(c,f)?o[i]={text:"/>",newPos:b.Pos(a.line,a.ch+2)}:(l=g&&-1<v(g,m),o[i]={indent:l,text:">"+(l?"\n\n":"")+"</"+f+">",newPos:l?b.Pos(a.line+1,0):b.Pos(a.line,a.ch+1)})}for(var h="object"==typeof r&&r.dontIndentOnAutoClose,i=n.length-1;0<=i;i--){var p=o[i],C=(t.replaceRange(p.text,n[i].head,n[i].anchor,"+insert"),t.listSelections().slice(0));C[i]={head:p.newPos,anchor:p.newPos},t.setSelections(C),!h&&p.indent&&(t.indentLine(p.newPos.line,null,!0),t.indentLine(p.newPos.line+1,null,!0))}}),e.addKeyMap(n))});var y=["area","base","br","col","command","embed","hr","img","input","keygen","link","meta","param","source","track","wbr"],x=["applet","blockquote","body","button","div","dl","fieldset","form","frameset","h1","h2","h3","h4","h5","h6","head","html","iframe","layer","legend","object","ol","p","select","table","ul"];function o(e,t){for(var n=e.listSelections(),o=[],r=t?"/":"</",i=e.getOption("autoCloseTags"),i="object"==typeof i&&i.dontIndentOnSlash,a=0;a<n.length;a++){if(!n[a].empty())return b.Pass;var l=n[a].head,s=e.getTokenAt(l),d=b.innerMode(e.getMode(),s.state),c=d.state;if(t&&("string"==s.type||"<"!=s.string.charAt(0)||s.start!=l.ch-1))return b.Pass;var f,g="xml"!=d.mode.name&&"htmlmixed"==e.getMode().name;if(g&&"javascript"==d.mode.name)f=r+"script";else if(g&&"css"==d.mode.name)f=r+"style";else{g=d.mode.xmlCurrentContext&&d.mode.xmlCurrentContext(c),d=g.length?g[g.length-1]:"";if(!g||g.length&&P(e,g,d,l))return b.Pass;f=r+d}">"!=e.getLine(l.line).charAt(s.end)&&(f+=">"),o[a]=f}if(e.replaceSelections(o),n=e.listSelections(),!i)for(a=0;a<n.length;a++)(a==n.length-1||n[a].head.line<n[a+1].head.line)&&e.indentLine(n[a].head.line)}function v(e,t){if(e.indexOf)return e.indexOf(t);for(var n=0,o=e.length;n<o;++n)if(e[n]==t)return n;return-1}function P(e,t,n,o,r){if(b.scanForClosingTag){var i=Math.min(e.lastLine()+1,o.line+500),a=b.scanForClosingTag(e,o,null,i);if(a&&a.tag==n){for(var l=r?1:0,s=t.length-1;0<=s&&t[s]==n;s--)++l;o=a.to;for(s=1;s<l;s++){var d=b.scanForClosingTag(e,o,null,i);if(!d||d.tag!=n)return;o=d.to}return 1}}}b.commands.closeTag=function(e){return o(e)}});