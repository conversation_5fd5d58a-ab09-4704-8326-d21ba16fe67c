/**
 * News Module JavaScript
 * Functionality for the news management module
 * Consolidated from multiple JS files
 */

// Initialize news module when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    // Initialize news module
    NewsModule.init();
});

// News Module Namespace
const NewsModule = {
    // Initialize news module
    init: function() {
        this.initNewsEditor();
        this.initNewsImageUpload();
        this.initNewsCategorySelect();
        this.initNewsDatePicker();
        this.initNewsStatusToggle();
        this.initNewsDelete();
        this.initNewsSearch();
        this.initNewsFilter();
        this.initNewsSort();
        this.initNewsViewToggle();
        this.initNewsBulkActions();
        this.initNewsPreview();
    },

    // Initialize news editor
    initNewsEditor: function() {
        const newsEditor = document.querySelector('#news-content');
        
        if (newsEditor && typeof ClassicEditor !== 'undefined') {
            ClassicEditor
                .create(newsEditor, {
                    toolbar: [
                        'heading', '|',
                        'bold', 'italic', 'link', 'bulletedList', 'numberedList', '|',
                        'indent', 'outdent', '|',
                        'imageUpload', 'blockQuote', 'insertTable', 'mediaEmbed', '|',
                        'undo', 'redo'
                    ],
                    image: {
                        toolbar: [
                            'imageStyle:full',
                            'imageStyle:side',
                            '|',
                            'imageTextAlternative'
                        ]
                    }
                })
                .then(editor => {
                    // Store editor instance for later use
                    window.newsEditor = editor;
                    
                    // Auto-save functionality
                    const autoSaveInterval = 30000; // 30 seconds
                    let autoSaveTimer;
                    
                    editor.model.document.on('change:data', () => {
                        clearTimeout(autoSaveTimer);
                        autoSaveTimer = setTimeout(() => {
                            const newsId = document.querySelector('#news-id').value;
                            const content = editor.getData();
                            
                            if (newsId) {
                                // Save content via AJAX
                                fetch('ajax/save_news_content.php', {
                                    method: 'POST',
                                    headers: {
                                        'Content-Type': 'application/x-www-form-urlencoded',
                                    },
                                    body: `id=${newsId}&content=${encodeURIComponent(content)}`
                                })
                                .then(response => response.json())
                                .then(data => {
                                    if (data.success) {
                                        console.log('News content auto-saved');
                                    }
                                })
                                .catch(error => {
                                    console.error('Error auto-saving news content:', error);
                                });
                            }
                        }, autoSaveInterval);
                    });
                })
                .catch(error => {
                    console.error(error);
                });
        }
    },

    // Initialize news image upload
    initNewsImageUpload: function() {
        const imageUpload = document.querySelector('#news-image-upload');
        const imagePreview = document.querySelector('#news-image-preview');
        const imageRemove = document.querySelector('#news-image-remove');
        const imageInput = document.querySelector('#news-image');
        
        if (imageUpload && imagePreview) {
            // Show preview on file select
            imageUpload.addEventListener('change', function() {
                if (this.files && this.files[0]) {
                    const reader = new FileReader();
                    
                    reader.onload = function(e) {
                        imagePreview.src = e.target.result;
                        imagePreview.style.display = 'block';
                        
                        if (imageRemove) {
                            imageRemove.style.display = 'inline-block';
                        }
                    };
                    
                    reader.readAsDataURL(this.files[0]);
                }
            });
            
            // Remove image
            if (imageRemove) {
                imageRemove.addEventListener('click', function(e) {
                    e.preventDefault();
                    
                    imagePreview.src = '';
                    imagePreview.style.display = 'none';
                    imageUpload.value = '';
                    
                    if (imageInput) {
                        imageInput.value = '';
                    }
                    
                    this.style.display = 'none';
                });
            }
        }
    },

    // Initialize news category select
    initNewsCategorySelect: function() {
        const categorySelect = document.querySelector('#news-category');
        const addCategoryButton = document.querySelector('#add-category');
        
        if (categorySelect && addCategoryButton) {
            addCategoryButton.addEventListener('click', function(e) {
                e.preventDefault();
                
                // Show modal to add new category
                const modal = document.querySelector('#add-category-modal');
                
                if (modal) {
                    modal.classList.add('active');
                    
                    // Focus category name input
                    const categoryNameInput = modal.querySelector('#category-name');
                    if (categoryNameInput) {
                        categoryNameInput.focus();
                    }
                    
                    // Handle form submission
                    const form = modal.querySelector('form');
                    if (form) {
                        form.addEventListener('submit', function(e) {
                            e.preventDefault();
                            
                            const categoryName = form.querySelector('#category-name').value;
                            const categorySlug = form.querySelector('#category-slug').value;
                            const categoryDescription = form.querySelector('#category-description').value;
                            
                            // Save category via AJAX
                            fetch('ajax/add_category.php', {
                                method: 'POST',
                                headers: {
                                    'Content-Type': 'application/x-www-form-urlencoded',
                                },
                                body: `name=${encodeURIComponent(categoryName)}&slug=${encodeURIComponent(categorySlug)}&description=${encodeURIComponent(categoryDescription)}`
                            })
                            .then(response => response.json())
                            .then(data => {
                                if (data.success) {
                                    // Add new category to select
                                    const option = document.createElement('option');
                                    option.value = data.id;
                                    option.textContent = categoryName;
                                    option.selected = true;
                                    categorySelect.appendChild(option);
                                    
                                    // Close modal
                                    modal.classList.remove('active');
                                    
                                    // Reset form
                                    form.reset();
                                    
                                    // Show success message
                                    AdminCore.showNotification('Category added successfully.', 'success');
                                } else {
                                    AdminCore.showNotification(data.message || 'Error adding category.', 'error');
                                }
                            })
                            .catch(error => {
                                console.error('Error adding category:', error);
                                AdminCore.showNotification('Error adding category.', 'error');
                            });
                        });
                    }
                }
            });
        }
    },

    // Initialize news date picker
    initNewsDatePicker: function() {
        const datePickers = document.querySelectorAll('.news-date-picker');
        
        if (datePickers.length > 0 && typeof $.fn.datepicker !== 'undefined') {
            datePickers.forEach(function(picker) {
                $(picker).datepicker({
                    format: 'yyyy-mm-dd',
                    autoclose: true,
                    todayHighlight: true,
                    todayBtn: 'linked'
                });
            });
        }
    },

    // Initialize news status toggle
    initNewsStatusToggle: function() {
        const statusToggle = document.querySelector('#news-status');
        const scheduledSection = document.querySelector('#scheduled-section');
        
        if (statusToggle && scheduledSection) {
            // Initial state
            updateScheduledSection();
            
            // Update on change
            statusToggle.addEventListener('change', updateScheduledSection);
            
            function updateScheduledSection() {
                if (statusToggle.value === 'scheduled') {
                    scheduledSection.style.display = 'block';
                } else {
                    scheduledSection.style.display = 'none';
                }
            }
        }
    },

    // Initialize news delete
    initNewsDelete: function() {
        const deleteButtons = document.querySelectorAll('.delete-news');
        
        deleteButtons.forEach(function(button) {
            button.addEventListener('click', function(e) {
                e.preventDefault();
                
                const newsId = this.getAttribute('data-id');
                const newsTitle = this.getAttribute('data-title');
                
                if (confirm(`Are you sure you want to delete "${newsTitle}"? This action cannot be undone.`)) {
                    // Delete news via AJAX
                    fetch('ajax/delete_news.php', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/x-www-form-urlencoded',
                        },
                        body: `id=${newsId}`
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            // Remove news item from list
                            const newsItem = this.closest('.news-item, tr');
                            if (newsItem) {
                                newsItem.remove();
                            }
                            
                            // Show success message
                            AdminCore.showNotification('News deleted successfully.', 'success');
                        } else {
                            AdminCore.showNotification(data.message || 'Error deleting news.', 'error');
                        }
                    })
                    .catch(error => {
                        console.error('Error deleting news:', error);
                        AdminCore.showNotification('Error deleting news.', 'error');
                    });
                }
            });
        });
    },

    // Initialize news search
    initNewsSearch: function() {
        const searchForm = document.querySelector('#news-search-form');
        const searchInput = document.querySelector('#news-search');
        const newsList = document.querySelector('#news-list');
        
        if (searchForm && searchInput && newsList) {
            searchForm.addEventListener('submit', function(e) {
                e.preventDefault();
                
                const query = searchInput.value.trim();
                
                // Search news via AJAX
                fetch(`ajax/search_news.php?q=${encodeURIComponent(query)}`)
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            // Update news list
                            newsList.innerHTML = data.html;
                            
                            // Re-initialize delete buttons
                            NewsModule.initNewsDelete();
                        } else {
                            AdminCore.showNotification(data.message || 'Error searching news.', 'error');
                        }
                    })
                    .catch(error => {
                        console.error('Error searching news:', error);
                        AdminCore.showNotification('Error searching news.', 'error');
                    });
            });
        }
    },

    // Initialize news filter
    initNewsFilter: function() {
        const filterForm = document.querySelector('#news-filter-form');
        const categoryFilter = document.querySelector('#filter-category');
        const statusFilter = document.querySelector('#filter-status');
        const dateFilter = document.querySelector('#filter-date');
        const newsList = document.querySelector('#news-list');
        
        if (filterForm && newsList) {
            // Apply filters on change
            [categoryFilter, statusFilter, dateFilter].forEach(function(filter) {
                if (filter) {
                    filter.addEventListener('change', applyFilters);
                }
            });
            
            function applyFilters() {
                const category = categoryFilter ? categoryFilter.value : '';
                const status = statusFilter ? statusFilter.value : '';
                const date = dateFilter ? dateFilter.value : '';
                
                // Filter news via AJAX
                fetch(`ajax/filter_news.php?category=${category}&status=${status}&date=${date}`)
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            // Update news list
                            newsList.innerHTML = data.html;
                            
                            // Re-initialize delete buttons
                            NewsModule.initNewsDelete();
                        } else {
                            AdminCore.showNotification(data.message || 'Error filtering news.', 'error');
                        }
                    })
                    .catch(error => {
                        console.error('Error filtering news:', error);
                        AdminCore.showNotification('Error filtering news.', 'error');
                    });
            }
        }
    },

    // Initialize news sort
    initNewsSort: function() {
        const sortSelect = document.querySelector('#news-sort');
        const newsList = document.querySelector('#news-list');
        
        if (sortSelect && newsList) {
            sortSelect.addEventListener('change', function() {
                const sort = this.value;
                
                // Sort news via AJAX
                fetch(`ajax/sort_news.php?sort=${sort}`)
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            // Update news list
                            newsList.innerHTML = data.html;
                            
                            // Re-initialize delete buttons
                            NewsModule.initNewsDelete();
                        } else {
                            AdminCore.showNotification(data.message || 'Error sorting news.', 'error');
                        }
                    })
                    .catch(error => {
                        console.error('Error sorting news:', error);
                        AdminCore.showNotification('Error sorting news.', 'error');
                    });
            });
        }
    },

    // Initialize news view toggle
    initNewsViewToggle: function() {
        const viewToggle = document.querySelector('#news-view-toggle');
        const listView = document.querySelector('#news-list-view');
        const gridView = document.querySelector('#news-grid-view');
        
        if (viewToggle && listView && gridView) {
            viewToggle.addEventListener('click', function(e) {
                e.preventDefault();
                
                const currentView = this.getAttribute('data-view');
                const newView = currentView === 'list' ? 'grid' : 'list';
                
                // Update toggle
                this.setAttribute('data-view', newView);
                this.innerHTML = newView === 'list' ? 
                    '<i class="fas fa-th-large"></i> Grid View' : 
                    '<i class="fas fa-list"></i> List View';
                
                // Update view
                if (newView === 'list') {
                    listView.style.display = 'block';
                    gridView.style.display = 'none';
                } else {
                    listView.style.display = 'none';
                    gridView.style.display = 'block';
                }
                
                // Save preference
                localStorage.setItem('news_view', newView);
            });
            
            // Load saved preference
            const savedView = localStorage.getItem('news_view');
            if (savedView) {
                viewToggle.setAttribute('data-view', savedView === 'list' ? 'grid' : 'list');
                viewToggle.click();
            }
        }
    },

    // Initialize news bulk actions
    initNewsBulkActions: function() {
        const bulkActionForm = document.querySelector('#bulk-action-form');
        const bulkActionSelect = document.querySelector('#bulk-action');
        const bulkActionApply = document.querySelector('#apply-bulk-action');
        const selectAllCheckbox = document.querySelector('#select-all-news');
        const newsCheckboxes = document.querySelectorAll('.news-checkbox');
        
        if (bulkActionForm && bulkActionSelect && bulkActionApply) {
            bulkActionForm.addEventListener('submit', function(e) {
                e.preventDefault();
                
                const action = bulkActionSelect.value;
                if (!action) return;
                
                const selectedNews = Array.from(document.querySelectorAll('.news-checkbox:checked')).map(cb => cb.value);
                if (selectedNews.length === 0) {
                    AdminCore.showNotification('Please select at least one news item.', 'warning');
                    return;
                }
                
                // Confirm action
                let confirmMessage = 'Are you sure you want to perform this action on the selected news items?';
                if (action === 'delete') {
                    confirmMessage = 'Are you sure you want to delete the selected news items? This action cannot be undone.';
                }
                
                if (confirm(confirmMessage)) {
                    // Perform bulk action via AJAX
                    fetch('ajax/bulk_news_action.php', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/x-www-form-urlencoded',
                        },
                        body: `action=${action}&ids=${selectedNews.join(',')}`
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            // Reload page
                            window.location.reload();
                        } else {
                            AdminCore.showNotification(data.message || 'Error performing bulk action.', 'error');
                        }
                    })
                    .catch(error => {
                        console.error('Error performing bulk action:', error);
                        AdminCore.showNotification('Error performing bulk action.', 'error');
                    });
                }
            });
            
            // Select all functionality
            if (selectAllCheckbox) {
                selectAllCheckbox.addEventListener('change', function() {
                    newsCheckboxes.forEach(function(checkbox) {
                        checkbox.checked = selectAllCheckbox.checked;
                    });
                });
                
                // Update select all when individual checkboxes change
                newsCheckboxes.forEach(function(checkbox) {
                    checkbox.addEventListener('change', function() {
                        const allChecked = Array.from(newsCheckboxes).every(function(cb) {
                            return cb.checked;
                        });
                        
                        const someChecked = Array.from(newsCheckboxes).some(function(cb) {
                            return cb.checked;
                        });
                        
                        selectAllCheckbox.checked = allChecked;
                        selectAllCheckbox.indeterminate = someChecked && !allChecked;
                    });
                });
            }
        }
    },

    // Initialize news preview
    initNewsPreview: function() {
        const previewButton = document.querySelector('#preview-news');
        const newsForm = document.querySelector('#news-form');
        
        if (previewButton && newsForm) {
            previewButton.addEventListener('click', function(e) {
                e.preventDefault();
                
                // Get form data
                const formData = new FormData(newsForm);
                
                // Add editor content
                if (window.newsEditor) {
                    formData.append('content', window.newsEditor.getData());
                }
                
                // Open preview in new window
                const previewWindow = window.open('', 'newsPreview', 'width=1024,height=768');
                
                // Create form to post data to preview page
                const form = document.createElement('form');
                form.action = 'news_preview.php';
                form.method = 'post';
                form.target = 'newsPreview';
                
                // Add form data as hidden inputs
                for (const [key, value] of formData.entries()) {
                    const input = document.createElement('input');
                    input.type = 'hidden';
                    input.name = key;
                    input.value = value;
                    form.appendChild(input);
                }
                
                // Submit form
                document.body.appendChild(form);
                form.submit();
                document.body.removeChild(form);
            });
        }
    }
};
