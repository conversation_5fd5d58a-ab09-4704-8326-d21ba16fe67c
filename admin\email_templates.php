<?php
session_start();
require_once 'config.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header('Location: index.php');
    exit;
}

// Include permissions class
require_once 'lib/Permissions.php';

// Initialize permission system
$permissions = new Permissions($conn, $_SESSION['user_id']);

// Check if user has permission to manage email settings
if (!$permissions->hasPermission('manage_email_settings')) {
    $_SESSION['error_message'] = "You do not have permission to manage email templates.";
    header('Location: dashboard.php');
    exit;
}

// Include the EmailTemplates class
require_once 'lib/EmailTemplates.php';

// Initialize EmailTemplates
$emailTemplates = new EmailTemplates($conn);

// Handle form submission
$success_message = '';
$error_message = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    if ($_POST['action'] === 'update_template') {
        $template_id = (int)$_POST['template_id'];
        $subject = $_POST['subject'] ?? '';
        $content = $_POST['content'] ?? '';

        if ($emailTemplates->updateTemplate($template_id, $subject, $content)) {
            $success_message = "Template updated successfully!";

            // Log the activity
            require_once 'includes/admin-functions.php';
            $template = $emailTemplates->getTemplateById($template_id);
            $template_name = $template ? $template['template_name'] : 'Unknown Template';
            log_activity('update', 'Updated email template: ' . $template_name, $_SESSION['user_id']);
        } else {
            $error_message = "Failed to update template. Please try again.";
        }
    }
}

// Get all templates
$templates = $emailTemplates->getAllTemplates();

// Page configuration
$page_title = 'Email Templates';
$page_subtitle = 'Manage email templates for automated messages';
$page_icon = 'fas fa-envelope';

// Include header
include 'includes/header.php';
?>

<div class="admin-container">
    <div class="admin-content-header">
        <div class="admin-content-title-group">
            <h2 class="admin-content-title">
                <i class="<?php echo $page_icon; ?>"></i> <?php echo $page_title; ?>
            </h2>
            <p class="admin-content-subtitle"><?php echo $page_subtitle; ?></p>
        </div>
    </div>

    <?php if ($success_message): ?>
    <div class="alert alert-success">
        <i class="fas fa-check-circle"></i> <?php echo $success_message; ?>
    </div>
    <?php endif; ?>

    <?php if ($error_message): ?>
    <div class="alert alert-danger">
        <i class="fas fa-exclamation-circle"></i> <?php echo $error_message; ?>
    </div>
    <?php endif; ?>

    <div class="admin-content-body">
        <div class="templates-grid">
            <?php if (empty($templates)): ?>
                <div class="no-templates">
                    <div class="no-templates-icon">
                        <i class="fas fa-envelope"></i>
                    </div>
                    <h3>No Email Templates Found</h3>
                    <p>No email templates are currently configured.</p>
                </div>
            <?php else: ?>
                <?php foreach ($templates as $template): ?>
                <div class="template-card">
                    <div class="template-card-header">
                        <h3 class="template-title">
                            <i class="fas fa-envelope"></i>
                            <?php echo htmlspecialchars($template['template_name']); ?>
                        </h3>
                        <div class="template-status">
                            <?php if ($template['is_active']): ?>
                                <span class="status-badge active">Active</span>
                            <?php else: ?>
                                <span class="status-badge inactive">Inactive</span>
                            <?php endif; ?>
                        </div>
                    </div>

                    <div class="template-card-body">
                        <div class="template-info">
                            <div class="template-field">
                                <label>Template Key:</label>
                                <code><?php echo htmlspecialchars($template['template_key']); ?></code>
                            </div>
                            <div class="template-field">
                                <label>Subject:</label>
                                <span><?php echo htmlspecialchars($template['subject']); ?></span>
                            </div>
                            <?php if (!empty($template['variables'])): ?>
                            <div class="template-field">
                                <label>Available Variables:</label>
                                <div class="variables-list">
                                    <?php
                                    $variables = explode(', ', $template['variables']);
                                    foreach ($variables as $variable):
                                    ?>
                                        <span class="variable-tag"><?php echo htmlspecialchars($variable); ?></span>
                                    <?php endforeach; ?>
                                </div>
                            </div>
                            <?php endif; ?>
                        </div>
                    </div>

                    <div class="template-card-footer">
                        <button type="button" class="admin-btn admin-btn-primary" onclick="editTemplate(<?php echo $template['id']; ?>)">
                            <i class="fas fa-edit"></i> Edit Template
                        </button>
                    </div>
                </div>
                <?php endforeach; ?>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- Edit Template Modal -->
<div id="editTemplateModal" class="modal" style="display: none;">
    <div class="modal-content">
        <div class="modal-header">
            <h3><i class="fas fa-edit"></i> Edit Email Template</h3>
            <button type="button" class="modal-close" onclick="closeEditModal()">&times;</button>
        </div>
        <form method="post" id="editTemplateForm">
            <input type="hidden" name="action" value="update_template">
            <input type="hidden" name="template_id" id="edit_template_id">

            <div class="modal-body">
                <div class="form-group">
                    <label for="edit_template_name">Template Name:</label>
                    <input type="text" id="edit_template_name" class="form-control" readonly>
                </div>

                <div class="form-group">
                    <label for="edit_subject">Subject:</label>
                    <input type="text" id="edit_subject" name="subject" class="form-control" required>
                </div>

                <div class="form-group">
                    <label for="edit_content">Content:</label>
                    <textarea id="edit_content" name="content" class="form-control" rows="15" required></textarea>
                </div>

                <div class="form-group">
                    <label>Available Variables:</label>
                    <div id="edit_variables" class="variables-list"></div>
                </div>
            </div>

            <div class="modal-footer">
                <button type="button" class="admin-btn admin-btn-secondary" onclick="closeEditModal()">Cancel</button>
                <button type="submit" class="admin-btn admin-btn-primary">
                    <i class="fas fa-save"></i> Save Changes
                </button>
            </div>
        </form>
    </div>
</div>





<style>
.templates-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
    gap: 25px;
}

.template-card {
    background: #fff;
    border-radius: 12px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    overflow: hidden;
    transition: all 0.3s ease;
}

.template-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
}

.template-card-header {
    padding: 20px;
    background: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.template-title {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    color: #333;
    display: flex;
    align-items: center;
    gap: 10px;
}

.template-title i {
    color: #f1ca2f;
}

.status-badge {
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 500;
}

.status-badge.active {
    background: #d4edda;
    color: #155724;
}

.status-badge.inactive {
    background: #f8d7da;
    color: #721c24;
}

.template-card-body {
    padding: 20px;
}

.template-field {
    margin-bottom: 15px;
}

.template-field label {
    display: block;
    font-weight: 600;
    color: #333;
    margin-bottom: 5px;
    font-size: 14px;
}

.template-field code {
    background: #f8f9fa;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 13px;
    color: #e83e8c;
}

.variables-list {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
}

.variable-tag {
    background: #e3f2fd;
    color: #1976d2;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-family: monospace;
}

.template-card-footer {
    padding: 20px;
    background: #f8f9fa;
    border-top: 1px solid #e9ecef;
    display: flex;
    gap: 10px;
}

.no-templates {
    grid-column: 1 / -1;
    text-align: center;
    padding: 60px 20px;
    color: #666;
}

.no-templates-icon {
    font-size: 48px;
    color: #f1ca2f;
    margin-bottom: 20px;
}

.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 9999;
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-content {
    background: #fff;
    border-radius: 12px;
    max-width: 800px;
    width: 90%;
    max-height: 90vh;
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

.modal-header {
    padding: 20px;
    background: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h3 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    color: #333;
    display: flex;
    align-items: center;
    gap: 10px;
}

.modal-close {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: #666;
    padding: 0;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.modal-close:hover {
    background: #e9ecef;
    color: #333;
}

.modal-body {
    padding: 20px;
    flex: 1;
    overflow-y: auto;
}

.modal-footer {
    padding: 20px;
    background: #f8f9fa;
    border-top: 1px solid #e9ecef;
    display: flex;
    justify-content: flex-end;
    gap: 10px;
}

.preview-section {
    margin-bottom: 25px;
}

.preview-section h4 {
    margin: 0 0 10px 0;
    font-size: 14px;
    font-weight: 600;
    color: #333;
}

.preview-content {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 6px;
    border: 1px solid #e9ecef;
    white-space: pre-wrap;
    font-family: monospace;
    font-size: 13px;
    line-height: 1.5;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    font-weight: 600;
    color: #333;
    margin-bottom: 8px;
    font-size: 14px;
}

.form-control {
    width: 100%;
    padding: 10px 12px;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 14px;
    transition: border-color 0.3s ease;
}

.form-control:focus {
    outline: none;
    border-color: #f1ca2f;
    box-shadow: 0 0 0 2px rgba(241, 202, 47, 0.2);
}

.form-control[readonly] {
    background-color: #f8f9fa;
    color: #666;
}

@media (max-width: 768px) {
    .templates-grid {
        grid-template-columns: 1fr;
    }

    .template-card-footer {
        flex-direction: column;
    }

    .modal-content {
        width: 95%;
        margin: 20px;
    }
}
</style>

<script>
// Template data for JavaScript
const templatesData = <?php echo json_encode($templates); ?>;
console.log('Templates loaded:', templatesData);

function editTemplate(templateId) {
    console.log('editTemplate called with ID:', templateId);

    // Redirect to dedicated edit page
    window.location.href = 'edit_template.php?id=' + templateId;
}

function closeEditModal() {
    document.getElementById('editTemplateModal').style.display = 'none';
}



// Close modals when clicking outside
document.addEventListener('click', function(e) {
    if (e.target.classList.contains('modal')) {
        e.target.style.display = 'none';
    }
});

// Close modals with Escape key
document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape') {
        closeEditModal();
    }
});

// Wait for admin header to be loaded before initializing
document.addEventListener('DOMContentLoaded', function() {
    function initializeEmailTemplates() {
        console.log('Email templates page initialized');

        // Ensure all buttons are properly bound
        const editButtons = document.querySelectorAll('button[onclick*="editTemplate"]');

        console.log('Found', editButtons.length, 'edit buttons');

        // Re-bind edit buttons
        editButtons.forEach(button => {
            const onclickAttr = button.getAttribute('onclick');
            if (onclickAttr) {
                const match = onclickAttr.match(/editTemplate\((\d+)\)/);
                if (match) {
                    const templateId = match[1];
                    button.removeAttribute('onclick');
                    button.addEventListener('click', function() {
                        editTemplate(templateId);
                    });
                }
            }
        });


    }

    // Check if admin header is loaded
    if (document.body.classList.contains('admin-header-loaded')) {
        initializeEmailTemplates();
    } else {
        // Listen for the admin header loaded event
        document.addEventListener('adminHeaderLoaded', initializeEmailTemplates);

        // Fallback: initialize after a delay if event doesn't fire
        setTimeout(initializeEmailTemplates, 1000);
    }
});
</script>

<?php include 'includes/footer.php'; ?>
