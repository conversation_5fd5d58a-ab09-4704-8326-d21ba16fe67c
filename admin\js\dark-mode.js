/**
 * Enhanced Dark Mode Toggle
 *
 * This script handles the dark mode toggle functionality.
 * It saves the user's preference in localStorage and applies the appropriate theme.
 * It also syncs the preference with the server and handles system preference changes.
 */

document.addEventListener('DOMContentLoaded', function() {
    // Get the dark mode toggle button
    const darkModeToggle = document.getElementById('toggle-dark-mode');
    const darkModeText = document.getElementById('dark-mode-text');

    // Get user settings from the hidden container
    let userSettings = {};
    const userSettingsElement = document.getElementById('user-settings-data');
    if (userSettingsElement && userSettingsElement.dataset.settings) {
        try {
            userSettings = JSON.parse(userSettingsElement.dataset.settings);
        } catch (e) {
            console.error('Error parsing user settings:', e);
        }
    }

    // Initialize theme based on user settings, localStorage, or system preference
    initializeTheme();

    // Add click event to the dark mode toggle
    if (darkModeToggle) {
        darkModeToggle.addEventListener('click', function(e) {
            e.preventDefault();

            // Toggle dark mode
            const isDarkMode = document.body.classList.toggle('dark-mode');

            // Update data-theme attribute
            document.documentElement.setAttribute('data-theme', isDarkMode ? 'dark' : 'light');

            // Save preference to localStorage
            localStorage.setItem('theme', isDarkMode ? 'dark' : 'light');

            // Update text
            updateDarkModeText(isDarkMode);

            // Save preference to user settings via AJAX
            saveUserThemePreference(isDarkMode ? 'dark' : 'light');
        });
    }

    // Listen for system preference changes
    if (window.matchMedia) {
        const colorSchemeQuery = window.matchMedia('(prefers-color-scheme: dark)');

        // Add change listener
        try {
            // Chrome & Firefox
            colorSchemeQuery.addEventListener('change', handleSystemPreferenceChange);
        } catch (e1) {
            try {
                // Safari
                colorSchemeQuery.addListener(handleSystemPreferenceChange);
            } catch (e2) {
                console.error('Could not add listener for color scheme changes:', e2);
            }
        }
    }

    /**
     * Initialize theme based on user settings, localStorage, or system preference
     */
    function initializeTheme() {
        // Get theme preference from various sources
        const savedTheme = localStorage.getItem('theme');
        const userTheme = userSettings.theme;
        const prefersDarkMode = window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches;

        let themeToApply = 'light';

        // Determine which theme to apply
        if (userTheme === 'dark') {
            // User has explicitly set dark theme in profile
            themeToApply = 'dark';
        } else if (userTheme === 'auto') {
            // User wants to follow system preference
            themeToApply = prefersDarkMode ? 'dark' : 'light';
        } else if (savedTheme) {
            // Use localStorage preference if available
            themeToApply = savedTheme;
        } else if (prefersDarkMode) {
            // Fall back to system preference
            themeToApply = 'dark';
        }

        // Apply the theme
        applyTheme(themeToApply);
    }

    /**
     * Apply the specified theme
     *
     * @param {string} theme - The theme to apply ('dark' or 'light')
     */
    function applyTheme(theme) {
        const isDarkMode = theme === 'dark';

        // Update HTML attribute
        document.documentElement.setAttribute('data-theme', theme);

        // Update body class
        if (isDarkMode) {
            document.body.classList.add('dark-mode');
        } else {
            document.body.classList.remove('dark-mode');
        }

        // Update toggle button text if it exists
        updateDarkModeText(isDarkMode);

        // Log theme change
        console.log('Theme applied:', theme);
    }

    /**
     * Handle system preference change
     *
     * @param {MediaQueryListEvent} e - The media query change event
     */
    function handleSystemPreferenceChange(e) {
        // Only apply system preference if user has set theme to 'auto'
        if (userSettings.theme === 'auto') {
            const newTheme = e.matches ? 'dark' : 'light';
            applyTheme(newTheme);
        }
    }

    /**
     * Update the dark mode toggle text
     *
     * @param {boolean} isDarkMode - Whether dark mode is active
     */
    function updateDarkModeText(isDarkMode) {
        if (!darkModeText || !darkModeToggle) return;

        if (isDarkMode) {
            darkModeText.textContent = 'Light Mode';
            const icon = darkModeToggle.querySelector('i');
            if (icon) {
                icon.classList.remove('fa-moon');
                icon.classList.add('fa-sun');
            }
        } else {
            darkModeText.textContent = 'Dark Mode';
            const icon = darkModeToggle.querySelector('i');
            if (icon) {
                icon.classList.remove('fa-sun');
                icon.classList.add('fa-moon');
            }
        }
    }

    /**
     * Save user theme preference to the server
     *
     * @param {string} theme - The theme preference to save
     */
    function saveUserThemePreference(theme) {
        fetch('ajax/save_user_setting.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: 'setting=theme&value=' + theme
        })
        .then(response => response.json())
        .then(data => {
            console.log('Theme preference saved:', data);
            // Update local user settings
            if (userSettings) {
                userSettings.theme = theme;
            }
        })
        .catch(error => {
            console.error('Error saving theme preference:', error);
        });
    }
});
