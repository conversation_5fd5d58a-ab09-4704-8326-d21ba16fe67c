<?php
/**
 * Get All Settings AJAX Handler
 *
 * This script retrieves all settings from all categories for backup purposes.
 */

// Start output buffering to prevent any output before JSON
ob_start();

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Set proper JSON headers
header('Content-Type: application/json');
header('Cache-Control: no-store, no-cache, must-revalidate, max-age=0');
header('Cache-Control: post-check=0, pre-check=0', false);
header('Pragma: no-cache');

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    echo json_encode([
        'success' => false,
        'message' => 'You must be logged in to perform this action'
    ]);
    exit;
}

// Include database connection
require_once '../config.php';

// Include permissions class
require_once '../lib/Permissions.php';

// Initialize permission system
$permissions = new Permissions($conn, $_SESSION['user_id']);

// Check if user has permission to manage settings
if (!$permissions->hasPermission('manage_email_settings')) {
    echo json_encode([
        'success' => false,
        'message' => 'You do not have permission to manage system settings'
    ]);
    exit;
}

// Function to get all settings
function getAllSettings($conn) {
    // Get all settings from the database
    $sql = "SELECT * FROM system_settings ORDER BY category, order_index";
    $result = $conn->query($sql);

    if (!$result) {
        return [
            'success' => false,
            'message' => 'Failed to retrieve settings: ' . $conn->error
        ];
    }

    // Group settings by category
    $settings = [];
    while ($row = $result->fetch_assoc()) {
        $category = $row['category'];

        if (!isset($settings[$category])) {
            $settings[$category] = [];
        }

        $settings[$category][$row['setting_key']] = [
            'value' => $row['setting_value'],
            'type' => $row['setting_type'],
            'display_name' => $row['display_name'],
            'description' => $row['description'],
            'options' => $row['options'],
            'is_required' => $row['is_required'],
            'order_index' => $row['order_index']
        ];
    }

    // Get email templates if available
    try {
        require_once '../email_templates.php';
        $emailTemplates = new EmailTemplates($conn);
        $templates = $emailTemplates->getAllTemplates();

        // Add templates to settings
        $settings['email_templates'] = [];
        foreach ($templates as $template) {
            $settings['email_templates'][$template['template_key']] = [
                'subject' => $template['subject'],
                'content' => $template['content'],
                'description' => $template['description']
            ];
        }
    } catch (Exception $e) {
        // Email templates might not be available, continue without them
        error_log('Error getting email templates: ' . $e->getMessage());
    }

    return [
        'success' => true,
        'settings' => $settings
    ];
}

// Get all settings
$result = getAllSettings($conn);

// Sanitize the settings to ensure they can be encoded as JSON
function sanitizeForJson($data) {
    if (is_array($data)) {
        foreach ($data as $key => $value) {
            $data[$key] = sanitizeForJson($value);
        }
        return $data;
    } elseif (is_string($data)) {
        // Remove any invalid UTF-8 sequences
        $data = mb_convert_encoding($data, 'UTF-8', 'UTF-8');

        // Replace any control characters that might cause JSON encoding issues
        $data = preg_replace('/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/u', '', $data);

        return $data;
    } else {
        return $data;
    }
}

// Sanitize the result before encoding
if ($result['success'] && isset($result['settings'])) {
    $result['settings'] = sanitizeForJson($result['settings']);
}

// Return the result with proper error handling
try {
    echo json_encode($result, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
} catch (Exception $e) {
    // Clear any previous output
    ob_clean();

    // Return a simplified error response
    echo json_encode([
        'success' => false,
        'message' => 'JSON encoding error: ' . $e->getMessage()
    ]);
}

// Check for JSON encoding errors
if (json_last_error() !== JSON_ERROR_NONE) {
    // Clear any previous output
    ob_clean();

    // Return a simplified error response
    echo json_encode([
        'success' => false,
        'message' => 'JSON encoding error: ' . json_last_error_msg()
    ]);
}

// End output buffering and flush
ob_end_flush();
exit;
