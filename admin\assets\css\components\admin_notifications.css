/**
 * Admin Notifications CSS
 *
 * This file contains styles for notifications and toasts in the admin panel.
 */

/* Notifications Container */
.notifications-container {
  position: fixed;
  top: var(--spacing-4);
  right: var(--spacing-4);
  z-index: var(--z-index-tooltip);
  display: flex;
  flex-direction: column;
  gap: var(--spacing-2);
  max-width: 350px;
  pointer-events: none;
}

.notifications-container > * {
  pointer-events: auto;
}

/* Toast Notification */
.toast {
  background-color: var(--white);
  border-radius: var(--radius-md);
  box-shadow: var(--shadow-lg);
  border: 1px solid var(--border-color);
  overflow: hidden;
  width: 100%;
  opacity: 0;
  transform: translateX(100%);
  transition: all var(--transition-normal) ease;
}

.toast.show {
  opacity: 1;
  transform: translateX(0);
}

/* Toast Header */
.toast-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-3) var(--spacing-4);
  border-bottom: 1px solid var(--border-light);
  background-color: var(--white);
}

.toast-title {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-semibold);
  color: var(--text-dark);
  margin: 0;
}

.toast-close {
  background-color: transparent;
  border: none;
  font-size: var(--font-size-base);
  line-height: 1;
  color: var(--text-muted);
  cursor: pointer;
  padding: var(--spacing-1);
  margin: calc(-1 * var(--spacing-1));
  border-radius: var(--radius-sm);
  transition: all var(--transition-fast) ease;
}

.toast-close:hover {
  color: var(--text-dark);
  background-color: var(--gray-100);
}

/* Toast Body */
.toast-body {
  padding: var(--spacing-3) var(--spacing-4);
  font-size: var(--font-size-sm);
  color: var(--text-color);
}

/* Toast with Icon */
.toast-with-icon {
  display: flex;
  align-items: flex-start;
  gap: var(--spacing-3);
}

.toast-icon {
  font-size: var(--font-size-lg);
  flex-shrink: 0;
}

.toast-content {
  flex: 1;
}

/* Toast Variants */
.toast-primary {
  border-left: 4px solid var(--primary-color);
}

.toast-primary .toast-icon {
  color: var(--primary-color);
}

.toast-secondary {
  border-left: 4px solid var(--secondary-color);
}

.toast-secondary .toast-icon {
  color: var(--secondary-color);
}

.toast-success {
  border-left: 4px solid var(--success-color);
}

.toast-success .toast-icon {
  color: var(--success-color);
}

.toast-danger {
  border-left: 4px solid var(--danger-color);
}

.toast-danger .toast-icon {
  color: var(--danger-color);
}

.toast-warning {
  border-left: 4px solid var(--warning-color);
}

.toast-warning .toast-icon {
  color: var(--warning-color);
}

.toast-info {
  border-left: 4px solid var(--info-color);
}

.toast-info .toast-icon {
  color: var(--info-color);
}

/* Toast Positions */
.notifications-top-right {
  top: var(--spacing-4);
  right: var(--spacing-4);
  bottom: auto;
  left: auto;
}

.notifications-top-left {
  top: var(--spacing-4);
  left: var(--spacing-4);
  bottom: auto;
  right: auto;
}

.notifications-bottom-right {
  bottom: var(--spacing-4);
  right: var(--spacing-4);
  top: auto;
  left: auto;
}

.notifications-bottom-left {
  bottom: var(--spacing-4);
  left: var(--spacing-4);
  top: auto;
  right: auto;
}

.notifications-top-center {
  top: var(--spacing-4);
  left: 50%;
  transform: translateX(-50%);
  bottom: auto;
  right: auto;
}

.notifications-bottom-center {
  bottom: var(--spacing-4);
  left: 50%;
  transform: translateX(-50%);
  top: auto;
  right: auto;
}

/* Notification Center */
.notification-center {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  width: 320px;
  background-color: var(--white);
  box-shadow: var(--shadow-xl);
  z-index: var(--z-index-modal);
  transform: translateX(100%);
  transition: transform var(--transition-normal) ease;
  display: flex;
  flex-direction: column;
}

.notification-center.show {
  transform: translateX(0);
}

/* Notifications Dropdown */
.notifications-dropdown {
  position: absolute;
  top: calc(100% + 5px);
  right: 10px;
  z-index: 9999 !important;
  width: 280px;
  background-color: var(--white);
  border-radius: var(--radius-md);
  box-shadow: var(--shadow-lg);
  border: 1px solid var(--border-color);
  display: none;
  transform: translateY(10px);
  opacity: 0;
  transition: transform var(--transition-fast) ease, opacity var(--transition-fast) ease;
  overflow: hidden;
}

.notifications-dropdown.show {
  display: block !important;
  transform: translateY(0) !important;
  opacity: 1;
  animation: dropdownFadeIn 0.2s ease-out;
  z-index: 9999 !important;
}

@keyframes dropdownFadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@media (max-width: 768px) {
  .notifications-dropdown {
    position: fixed !important;
    top: calc(var(--topbar-height) * 2 + 15px) !important; /* Add more space to ensure it's below the topbar */
    right: 10px !important;
    width: 280px !important;
    max-width: calc(100vw - 20px) !important;
    box-shadow: var(--shadow-xl) !important;
    z-index: 9999 !important; /* Higher z-index to ensure it's above everything */
    transform: none !important; /* Override any transform that might affect positioning */
  }

  .notifications-dropdown.show {
    display: block !important;
    opacity: 1 !important;
    transform: none !important;
    z-index: 9999 !important;
  }
}

/* Notification Center Header */
.notification-center-header {
  padding: var(--spacing-4);
  border-bottom: 1px solid var(--border-color);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.notification-center-title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--text-dark);
  margin: 0;
}

.notification-center-close {
  background-color: transparent;
  border: none;
  font-size: var(--font-size-xl);
  line-height: 1;
  color: var(--text-muted);
  cursor: pointer;
  padding: var(--spacing-1);
  margin: calc(-1 * var(--spacing-1));
  border-radius: var(--radius-sm);
  transition: all var(--transition-fast) ease;
}

.notification-center-close:hover {
  color: var(--text-dark);
  background-color: var(--gray-100);
}

/* Notification Center Tabs */
.notification-center-tabs {
  display: flex;
  border-bottom: 1px solid var(--border-color);
}

.notification-center-tab {
  flex: 1;
  padding: var(--spacing-3);
  font-size: var(--font-size-sm);
  color: var(--text-light);
  text-align: center;
  cursor: pointer;
  transition: all var(--transition-fast) ease;
  border-bottom: 2px solid transparent;
}

.notification-center-tab:hover {
  color: var(--text-dark);
}

.notification-center-tab.active {
  color: var(--primary-color);
  border-bottom-color: var(--primary-color);
  font-weight: var(--font-weight-medium);
}

/* Notification Center Content */
.notification-center-content {
  flex: 1;
  overflow-y: auto;
}

.notification-center-list {
  list-style: none;
  margin: 0;
  padding: 0;
}

/* Notification Item */
.notification-item {
  display: flex;
  align-items: flex-start;
  gap: var(--spacing-3);
  padding: var(--spacing-3) var(--spacing-4);
  border-bottom: 1px solid var(--border-light);
  transition: background-color var(--transition-fast) ease;
  cursor: pointer;
}

.notification-item:hover {
  background-color: var(--gray-50);
}

.notification-item.unread {
  background-color: var(--primary-very-light);
}

.notification-item.unread:hover {
  background-color: rgba(241, 202, 47, 0.15);
}

.notification-icon {
  width: 40px;
  height: 40px;
  border-radius: var(--radius-full);
  background-color: var(--primary-very-light);
  color: var(--primary-color);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--font-size-base);
  flex-shrink: 0;
}

.notification-icon.success {
  background-color: rgba(16, 185, 129, 0.1);
  color: var(--success-color);
}

.notification-icon.warning {
  background-color: rgba(245, 158, 11, 0.1);
  color: var(--warning-color);
}

.notification-icon.danger {
  background-color: rgba(239, 68, 68, 0.1);
  color: var(--danger-color);
}

.notification-icon.info {
  background-color: rgba(59, 130, 246, 0.1);
  color: var(--info-color);
}

.notification-content {
  flex: 1;
}

.notification-title {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--text-dark);
  margin-bottom: var(--spacing-1);
}

.notification-text {
  font-size: var(--font-size-xs);
  color: var(--text-light);
  margin-bottom: var(--spacing-1);
}

.notification-time {
  font-size: var(--font-size-xs);
  color: var(--text-muted);
}

.notification-actions {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  margin-top: var(--spacing-2);
}

/* Notification Center Footer */
.notification-center-footer {
  padding: var(--spacing-3) var(--spacing-4);
  border-top: 1px solid var(--border-color);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.notification-center-mark-all {
  font-size: var(--font-size-sm);
  color: var(--primary-color);
  background-color: transparent;
  border: none;
  cursor: pointer;
  padding: 0;
  transition: color var(--transition-fast) ease;
}

.notification-center-mark-all:hover {
  color: var(--primary-dark);
  text-decoration: underline;
}

.notification-center-settings {
  font-size: var(--font-size-sm);
  color: var(--text-light);
  background-color: transparent;
  border: none;
  cursor: pointer;
  padding: 0;
  transition: color var(--transition-fast) ease;
}

.notification-center-settings:hover {
  color: var(--text-dark);
}

/* Notification Badge */
.notification-badge {
  position: absolute;
  top: 0;
  right: 0;
  min-width: 18px;
  height: 18px;
  padding: 0 var(--spacing-1);
  font-size: 10px;
  font-weight: var(--font-weight-bold);
  line-height: 1;
  border-radius: var(--radius-full);
  background-color: var(--danger-color);
  color: var(--white);
  display: flex;
  align-items: center;
  justify-content: center;
  transform: translate(50%, -50%);
}

/* Notification Overlay */
.notification-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: calc(var(--z-index-modal) - 1);
  opacity: 0;
  visibility: hidden;
  transition: opacity var(--transition-normal) ease, visibility var(--transition-normal) ease;
}

.notification-overlay.show {
  opacity: 1;
  visibility: visible;
}

/* Responsive Styles */
@media (max-width: 576px) {
  .notifications-container {
    left: var(--spacing-3);
    right: var(--spacing-3);
    max-width: none;
  }

  .notifications-top-right,
  .notifications-top-left,
  .notifications-top-center {
    left: var(--spacing-3);
    right: var(--spacing-3);
    transform: none;
  }

  .notifications-bottom-right,
  .notifications-bottom-left,
  .notifications-bottom-center {
    left: var(--spacing-3);
    right: var(--spacing-3);
    transform: none;
  }

  .notification-center {
    width: 100%;
  }
}
