# Enable PHP processing
AddType application/x-httpd-php .php

# Set default character set
AddDefaultCharset UTF-8

# Prevent directory listing
Options -Indexes

# Protect .htaccess file
<Files .htaccess>
    Order Allow,Deny
    Deny from all
</Files>

# Protect config.php file
<Files config.php>
    Order Allow,Deny
    Deny from all
</Files>

# News page is now handled directly by news.php

# Ensure index.html is loaded by default
DirectoryIndex index.html

# Enable URL rewriting
RewriteEngine On

# Handle clean URLs for articles with .html extension
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^([a-zA-Z0-9-]+)\.html$ article-handler.php [L,QSA]

# Redirect old format to new format
RewriteCond %{QUERY_STRING} ^slug=([^&]+)$
RewriteRule ^news-detail\.html$ $1.html? [R=301,L]

# Allow access to admin/install.php
RewriteCond %{REQUEST_URI} ^/manageinc/admin/install\.php [NC]
RewriteRule .* - [L]

# Redirect to index.html if no file is specified in root directory
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteCond %{REQUEST_URI} ^/manageinc/?$
RewriteRule ^(.*)$ /manageinc/index.html [L]

# Disable caching for PHP files only (safer approach)
<FilesMatch "\.(php)$">
  <IfModule mod_headers.c>
    Header set Cache-Control "no-store, no-cache, must-revalidate, max-age=0"
    Header set Pragma "no-cache"
    Header set Expires "Mon, 26 Jul 1997 05:00:00 GMT"
  </IfModule>
</FilesMatch>

# Alternative method to disable caching for PHP files (if mod_headers is not available)
php_flag session.cache_limiter "nocache"
