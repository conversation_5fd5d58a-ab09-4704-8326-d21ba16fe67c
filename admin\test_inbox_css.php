<?php
/**
 * Test Inbox CSS Loading
 * 
 * This script tests if the modern inbox CSS is loading correctly
 */

session_start();
require_once 'config.php';

// Check if user is logged in and is admin
if (!isset($_SESSION['user_id'])) {
    die('Access denied. Please log in as admin.');
}

// Define CSS version for cache busting (aggressive cache busting)
$css_version = '6.0.0';
$cache_buster = time() . '_' . rand(1000, 9999);
$full_css_version = $css_version . '.' . $cache_buster;

// Add CSS for the inbox page with aggressive cache busting
$extra_css = '<link rel="stylesheet" href="assets/css/pages/inbox.css?v=' . $full_css_version . '&t=' . time() . '" data-version="modern-inbox">';

// Add no-cache headers for CSS
header("Cache-Control: no-store, no-cache, must-revalidate, max-age=0");
header("Cache-Control: post-check=0, pre-check=0", false);
header("Pragma: no-cache");

$page_title = 'Test Inbox CSS';
$body_class = 'inbox-page';
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo htmlspecialchars($page_title); ?></title>
    
    <!-- Base Admin CSS -->
    <link rel="stylesheet" href="assets/css/admin.css?v=<?php echo $full_css_version; ?>">
    
    <!-- Modern Inbox CSS -->
    <?php echo $extra_css; ?>
    
    <style>
        .test-container {
            max-width: 800px;
            margin: 40px auto;
            padding: 20px;
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }
        .test-item {
            padding: 15px;
            margin: 10px 0;
            border-radius: 8px;
            border: 1px solid #ddd;
        }
        .test-success {
            background: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
        .test-error {
            background: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
        .css-url {
            font-family: monospace;
            background: #f8f9fa;
            padding: 5px;
            border-radius: 4px;
            word-break: break-all;
        }
    </style>
</head>
<body class="<?php echo htmlspecialchars($body_class); ?>">
    <div class="test-container">
        <h1>🧪 Inbox CSS Loading Test</h1>
        
        <div class="test-item test-success">
            <h3>✅ CSS Version Information</h3>
            <p><strong>CSS Version:</strong> <?php echo htmlspecialchars($css_version); ?></p>
            <p><strong>Cache Buster:</strong> <?php echo htmlspecialchars($cache_buster); ?></p>
            <p><strong>Full Version:</strong> <?php echo htmlspecialchars($full_css_version); ?></p>
        </div>
        
        <div class="test-item">
            <h3>📄 CSS File URL</h3>
            <div class="css-url">
                assets/css/pages/inbox.css?v=<?php echo htmlspecialchars($full_css_version); ?>&t=<?php echo time(); ?>
            </div>
        </div>
        
        <div class="test-item">
            <h3>🔍 CSS Loading Test</h3>
            <p>If the modern inbox CSS is loading correctly, you should see:</p>
            <ul>
                <li>A green indicator in the top-right corner saying "MODERN INBOX CSS LOADED v6.0.0"</li>
                <li>This page should have the inbox-page body class</li>
                <li>The background should be light gray (#f8f9fa)</li>
            </ul>
        </div>
        
        <div class="test-item">
            <h3>🎯 Test Modern Message View</h3>
            <div class="modern-message-view">
                <div class="message-nav-bar">
                    <div class="nav-left">
                        <a href="#" class="nav-back-btn">
                            <i class="fas fa-arrow-left"></i>
                            <span>Back to Inbox</span>
                        </a>
                        <div class="message-nav-info">
                            <h1 class="message-nav-title">Test Message</h1>
                            <span class="message-nav-meta">
                                <i class="fas fa-calendar"></i>
                                Test Date
                            </span>
                        </div>
                    </div>
                </div>
                
                <div class="message-content-grid">
                    <div class="contact-info-panel">
                        <div class="contact-card">
                            <div class="contact-avatar-section">
                                <div class="contact-avatar-circle">
                                    <span class="avatar-initials">TU</span>
                                </div>
                                <div class="contact-info">
                                    <h2 class="contact-name">Test User</h2>
                                    <p class="contact-source">via Test Form</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="message-content-panel">
                        <div class="original-message-card">
                            <div class="message-header">
                                <div class="message-header-left">
                                    <h3 class="message-title">
                                        <i class="fas fa-comment-alt"></i>
                                        Test Message
                                    </h3>
                                </div>
                            </div>
                            <div class="message-content">
                                <div class="message-text">
                                    This is a test message to verify the modern inbox CSS is working correctly.
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="test-item">
            <h3>🔗 Quick Actions</h3>
            <a href="inbox.php" class="btn" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 12px 24px; border-radius: 8px; text-decoration: none; display: inline-block; margin: 5px;">
                📧 Go to Inbox
            </a>
            <a href="create_contact_replies_table.php" class="btn" style="background: #6c757d; color: white; padding: 12px 24px; border-radius: 8px; text-decoration: none; display: inline-block; margin: 5px;">
                🔧 Create Tables
            </a>
            <a href="insert_sample_data.php" class="btn" style="background: #28a745; color: white; padding: 12px 24px; border-radius: 8px; text-decoration: none; display: inline-block; margin: 5px;">
                📥 Insert Sample Data
            </a>
        </div>
        
        <div class="test-item">
            <h3>📋 Debug Information</h3>
            <p><strong>Body Class:</strong> <?php echo htmlspecialchars($body_class); ?></p>
            <p><strong>Page Title:</strong> <?php echo htmlspecialchars($page_title); ?></p>
            <p><strong>Current Time:</strong> <?php echo date('Y-m-d H:i:s'); ?></p>
            <p><strong>CSS File Exists:</strong> <?php echo file_exists('assets/css/pages/inbox.css') ? 'Yes' : 'No'; ?></p>
        </div>
    </div>
    
    <script>
        // Check if CSS is loaded by looking for the debug indicator
        document.addEventListener('DOMContentLoaded', function() {
            const body = document.body;
            const computedStyle = window.getComputedStyle(body, '::before');
            const content = computedStyle.getPropertyValue('content');
            
            if (content.includes('MODERN INBOX CSS LOADED')) {
                console.log('✅ Modern Inbox CSS is loaded successfully!');
            } else {
                console.log('❌ Modern Inbox CSS may not be loaded properly.');
            }
        });
    </script>
</body>
</html>
