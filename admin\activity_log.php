<?php
session_start();
require_once 'config.php';
require_once 'includes/functions.php';
require_once 'includes/security.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header('Location: login.php');
    exit();
}

$user_id = $_SESSION['user_id'];

// Pagination
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$per_page = 20;
$offset = ($page - 1) * $per_page;

// Get total count for pagination
$count_sql = "SELECT COUNT(*) as total FROM activity_log WHERE user_id = ?";
$count_stmt = $conn->prepare($count_sql);
$count_stmt->bind_param("i", $user_id);
$count_stmt->execute();
$count_result = $count_stmt->get_result();
$total_records = $count_result->fetch_assoc()['total'];
$total_pages = ceil($total_records / $per_page);

// Get activity log entries
$sql = "SELECT * FROM activity_log WHERE user_id = ? ORDER BY created_at DESC LIMIT ? OFFSET ?";
$stmt = $conn->prepare($sql);
$stmt->bind_param("iii", $user_id, $per_page, $offset);
$stmt->execute();
$result = $stmt->get_result();
$activities = $result->fetch_all(MYSQLI_ASSOC);

// Function to get activity icon
function getActivityIcon($action) {
    switch (strtolower($action)) {
        case 'login':
            return 'fas fa-sign-in-alt';
        case 'logout':
            return 'fas fa-sign-out-alt';
        case 'create':
            return 'fas fa-plus-circle';
        case 'update':
        case 'edit':
            return 'fas fa-edit';
        case 'delete':
            return 'fas fa-trash';
        case 'view':
            return 'fas fa-eye';
        case 'upload':
            return 'fas fa-upload';
        case 'download':
            return 'fas fa-download';
        default:
            return 'fas fa-info-circle';
    }
}

// Function to get activity color
function getActivityColor($action) {
    switch (strtolower($action)) {
        case 'login':
            return 'success';
        case 'logout':
            return 'info';
        case 'create':
            return 'primary';
        case 'update':
        case 'edit':
            return 'warning';
        case 'delete':
            return 'danger';
        case 'view':
            return 'info';
        case 'upload':
        case 'download':
            return 'secondary';
        default:
            return 'light';
    }
}
// Page title and metadata
$page_title = "Activity Log";
$page_icon = "fas fa-history";
$page_subtitle = "View your recent activities and actions";
?>

<?php include 'includes/header.php'; ?>

    <div class="admin-container">
        <div class="admin-content-header">
            <div class="admin-content-title-group">
                <h2 class="admin-content-title">
                    <i class="fas fa-history"></i>
                    Activity Log
                </h2>
                <p class="admin-content-subtitle">View your recent activities and actions</p>
            </div>
        </div>

        <div class="admin-card">
            <div class="admin-card-header">
                <h3 class="admin-card-title">
                    <i class="fas fa-list"></i>
                    Recent Activities
                </h3>
                <div class="admin-card-actions">
                    <span class="badge badge-info"><?php echo $total_records; ?> total entries</span>
                </div>
            </div>
            <div class="admin-card-body">
                    <?php if (empty($activities)): ?>
                        <div class="empty-state">
                            <i class="fas fa-history"></i>
                            <h3>No Activity Found</h3>
                            <p>Your activity log is empty. Start using the admin panel to see your activities here.</p>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="admin-table">
                                <thead>
                                    <tr>
                                        <th>Action</th>
                                        <th>Description</th>
                                        <th>Date & Time</th>
                                        <th>IP Address</th>
                                        <th>User Agent</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($activities as $activity): ?>
                                        <tr>
                                            <td>
                                                <span class="activity-badge <?php echo getActivityColor($activity['action']); ?>">
                                                    <i class="<?php echo getActivityIcon($activity['action']); ?>"></i>
                                                    <?php echo ucfirst($activity['action']); ?>
                                                </span>
                                            </td>
                                            <td>
                                                <div class="activity-description">
                                                    <?php echo htmlspecialchars($activity['description']); ?>
                                                </div>
                                            </td>
                                            <td>
                                                <div class="activity-time">
                                                    <div class="time-primary"><?php echo date('M j, Y', strtotime($activity['created_at'])); ?></div>
                                                    <div class="time-secondary"><?php echo date('g:i A', strtotime($activity['created_at'])); ?></div>
                                                </div>
                                            </td>
                                            <td>
                                                <span class="activity-ip"><?php echo htmlspecialchars($activity['ip_address'] ?? 'N/A'); ?></span>
                                            </td>
                                            <td>
                                                <div class="activity-user-agent" title="<?php echo htmlspecialchars($activity['user_agent'] ?? 'N/A'); ?>">
                                                    <?php
                                                    $user_agent = $activity['user_agent'] ?? 'N/A';
                                                    echo htmlspecialchars(strlen($user_agent) > 50 ? substr($user_agent, 0, 50) . '...' : $user_agent);
                                                    ?>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>

                        <?php if ($total_pages > 1): ?>
                            <div class="pagination-wrapper">
                                <nav class="pagination">
                                    <?php if ($page > 1): ?>
                                        <a href="?page=<?php echo $page - 1; ?>" class="pagination-link">
                                            <i class="fas fa-chevron-left"></i> Previous
                                        </a>
                                    <?php endif; ?>

                                    <?php for ($i = 1; $i <= $total_pages; $i++): ?>
                                        <a href="?page=<?php echo $i; ?>" class="pagination-link <?php echo $i === $page ? 'active' : ''; ?>">
                                            <?php echo $i; ?>
                                        </a>
                                    <?php endfor; ?>

                                    <?php if ($page < $total_pages): ?>
                                        <a href="?page=<?php echo $page + 1; ?>" class="pagination-link">
                                            Next <i class="fas fa-chevron-right"></i>
                                        </a>
                                    <?php endif; ?>
                                </nav>
                            </div>
                        <?php endif; ?>
                <?php endif; ?>
            </div>
        </div>
    </div>

<style>
/* Activity Log Table Styles */
.activity-badge {
    display: inline-flex;
    align-items: center;
    gap: 5px;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
    text-transform: uppercase;
}

.activity-badge.success {
    background-color: #d4edda;
    color: #155724;
}

.activity-badge.info {
    background-color: #d1ecf1;
    color: #0c5460;
}

.activity-badge.primary {
    background-color: #cce7ff;
    color: #004085;
}

.activity-badge.warning {
    background-color: #fff3cd;
    color: #856404;
}

.activity-badge.danger {
    background-color: #f8d7da;
    color: #721c24;
}

.activity-badge.secondary {
    background-color: #e2e3e5;
    color: #383d41;
}

.activity-badge.light {
    background-color: #f8f9fa;
    color: #495057;
}

.activity-description {
    font-weight: 500;
    color: #333;
}

.activity-time {
    text-align: center;
}

.time-primary {
    font-weight: 500;
    color: #333;
}

.time-secondary {
    font-size: 12px;
    color: #666;
    margin-top: 2px;
}

.activity-ip {
    font-family: monospace;
    font-size: 12px;
    color: #666;
}

.activity-user-agent {
    font-size: 12px;
    color: #666;
    max-width: 200px;
    word-break: break-all;
}

/* Responsive table */
@media (max-width: 768px) {
    .admin-table th:nth-child(4),
    .admin-table td:nth-child(4),
    .admin-table th:nth-child(5),
    .admin-table td:nth-child(5) {
        display: none;
    }
}
</style>

<?php include 'includes/footer.php'; ?>
