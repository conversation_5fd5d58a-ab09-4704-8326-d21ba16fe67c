<?php
/**
 * Mark Notification as Read AJAX Handler
 */

// Start output buffering to prevent any output before JSON
ob_start();

// Enable error logging but disable display
error_reporting(E_ALL);
ini_set('display_errors', 0);
ini_set('log_errors', 1);

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Set proper JSON headers
header('Content-Type: application/json');
header('Cache-Control: no-store, no-cache, must-revalidate, max-age=0');
header('Cache-Control: post-check=0, pre-check=0', false);
header('Pragma: no-cache');

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    echo json_encode([
        'success' => false,
        'message' => 'You must be logged in to perform this action'
    ]);
    exit;
}

try {
    // Include database connection
    require_once '../config.php';
    require_once '../lib/Notifications.php';

    // Initialize notification system
    $notifications = new Notifications($conn);
    
    // Get the action type
    $action = $_POST['action'] ?? '';
    
    if ($action === 'mark_all_read') {
        // Mark all notifications as read for the current user
        $success = $notifications->markAllAsRead($_SESSION['user_id']);
        
        if ($success) {
            echo json_encode([
                'success' => true,
                'message' => 'All notifications marked as read'
            ]);
        } else {
            echo json_encode([
                'success' => false,
                'message' => 'Failed to mark all notifications as read'
            ]);
        }
    } elseif ($action === 'mark_read') {
        // Mark a specific notification as read
        $notification_id = isset($_POST['notification_id']) ? (int)$_POST['notification_id'] : 0;
        
        if ($notification_id <= 0) {
            echo json_encode([
                'success' => false,
                'message' => 'Invalid notification ID'
            ]);
            exit;
        }
        
        $success = $notifications->markAsRead($notification_id, $_SESSION['user_id']);
        
        if ($success) {
            echo json_encode([
                'success' => true,
                'message' => 'Notification marked as read'
            ]);
        } else {
            echo json_encode([
                'success' => false,
                'message' => 'Failed to mark notification as read'
            ]);
        }
    } else {
        echo json_encode([
            'success' => false,
            'message' => 'Invalid action'
        ]);
    }
    
} catch (Exception $e) {
    // Log the error
    error_log('Error in mark_notification_read.php: ' . $e->getMessage());
    
    // Return a generic error message
    echo json_encode([
        'success' => false,
        'message' => 'An unexpected error occurred'
    ]);
}

// End output buffering and flush
ob_end_flush();
exit;
