/**
 * Google Fonts API Integration
 *
 * This file provides functionality to load and use Google Fonts in the editor
 */

class GoogleFontsLoader {
    constructor() {
        // Get API key from settings if available
        this.apiKey = '';

        // Try to get API key from the settings data attribute
        const settingsElement = document.getElementById('google-fonts-settings');
        if (settingsElement && settingsElement.dataset.apiKey) {
            this.apiKey = settingsElement.dataset.apiKey;
        }

        // Fallback to default key if none is provided
        if (!this.apiKey) {
            this.apiKey = 'AIzaSyAOES8EmKhuJEnsn9kS1XKBpxxp-TgN8Jc'; // This is a restricted API key for Google Fonts only
        }

        this.apiUrl = `https://www.googleapis.com/webfonts/v1/webfonts?key=${this.apiKey}&sort=popularity`;
        this.fonts = [];
        this.popularFonts = [
            'Open Sans',
            'Roboto',
            'Lato',
            'Mont<PERSON><PERSON>',
            '<PERSON>',
            'Source Sans Pro',
            '<PERSON><PERSON><PERSON>',
            '<PERSON><PERSON><PERSON>',
            '<PERSON><PERSON><PERSON><PERSON>',
            '<PERSON><PERSON>weather',
            'Playfair Display',
            '<PERSON><PERSON><PERSON>',
            '<PERSON><PERSON><PERSON>',
            'Work Sans',
            'PT Sans'
        ];
        this.loadedFonts = new Set();
    }

    /**
     * Load fonts from Google Fonts API
     * @returns {Promise} Promise that resolves when fonts are loaded
     */
    async loadFonts() {
        try {
            const response = await fetch(this.apiUrl);
            const data = await response.json();
            this.fonts = data.items;
            return this.fonts;
        } catch (error) {
            console.error('Error loading Google Fonts:', error);
            // Fallback to popular fonts if API fails
            return this.getPopularFonts();
        }
    }

    /**
     * Get popular fonts as a fallback
     * @returns {Array} Array of popular font objects
     */
    getPopularFonts() {
        return this.popularFonts.map(fontName => ({
            family: fontName,
            variants: ['regular', 'italic', '700', '700italic'],
            category: 'sans-serif'
        }));
    }

    /**
     * Get all available fonts
     * @returns {Promise} Promise that resolves with all fonts
     */
    async getAllFonts() {
        if (this.fonts.length === 0) {
            await this.loadFonts();
        }
        return this.fonts;
    }

    /**
     * Get popular fonts (top 15)
     * @returns {Promise} Promise that resolves with popular fonts
     */
    async getPopularFontsList() {
        if (this.fonts.length === 0) {
            await this.loadFonts();
        }

        // If API loaded, return top fonts
        if (this.fonts.length > 0) {
            return this.fonts.slice(0, 15);
        }

        // Fallback
        return this.getPopularFonts();
    }

    /**
     * Load a specific font for use
     * @param {string} fontFamily Font family name
     */
    loadFont(fontFamily) {
        if (this.loadedFonts.has(fontFamily)) {
            return; // Font already loaded
        }

        const formattedFamily = fontFamily.replace(/ /g, '+');
        const link = document.createElement('link');
        link.rel = 'stylesheet';
        link.href = `https://fonts.googleapis.com/css2?family=${formattedFamily}:wght@400;700&display=swap`;
        document.head.appendChild(link);

        this.loadedFonts.add(fontFamily);
    }

    /**
     * Search fonts by name
     * @param {string} query Search query
     * @returns {Promise} Promise that resolves with matching fonts
     */
    async searchFonts(query) {
        if (this.fonts.length === 0) {
            await this.loadFonts();
        }

        const lowerQuery = query.toLowerCase();
        return this.fonts.filter(font =>
            font.family.toLowerCase().includes(lowerQuery)
        ).slice(0, 10); // Limit to 10 results
    }

    /**
     * Get font by category
     * @param {string} category Font category (serif, sans-serif, display, handwriting, monospace)
     * @returns {Promise} Promise that resolves with fonts in the category
     */
    async getFontsByCategory(category) {
        if (this.fonts.length === 0) {
            await this.loadFonts();
        }

        return this.fonts.filter(font =>
            font.category === category
        ).slice(0, 10); // Limit to 10 results
    }
}

// Create a global instance
window.googleFontsLoader = new GoogleFontsLoader();
