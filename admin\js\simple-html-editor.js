/**
 * Simple HTML Editor
 * A lightweight alternative to CodeMirror/Quill
 * Uses native browser APIs for syntax highlighting and editing
 */

class SimpleHTMLEditor {
    constructor(container, options = {}) {
        this.container = container;
        this.options = {
            theme: 'dark',
            lineNumbers: true,
            autoIndent: true,
            tabSize: 2,
            ...options
        };
        
        this.init();
    }
    
    init() {
        this.createEditor();
        this.setupEventListeners();
        this.applySyntaxHighlighting();
    }
    
    createEditor() {
        this.container.innerHTML = `
            <div class="simple-editor">
                <div class="editor-toolbar">
                    <button type="button" class="btn-code active" data-mode="code">
                        <i class="fas fa-code"></i> Code
                    </button>
                    <button type="button" class="btn-visual" data-mode="visual">
                        <i class="fas fa-eye"></i> Visual
                    </button>
                    <button type="button" class="btn-format">
                        <i class="fas fa-magic"></i> Format
                    </button>
                </div>
                <div class="editor-content">
                    <div class="code-editor">
                        <div class="line-numbers"></div>
                        <textarea class="code-textarea" spellcheck="false"></textarea>
                        <div class="syntax-overlay"></div>
                    </div>
                    <div class="visual-editor" contenteditable="true" style="display: none;">
                        <p>Start editing your content...</p>
                    </div>
                </div>
            </div>
        `;
        
        this.codeTextarea = this.container.querySelector('.code-textarea');
        this.visualEditor = this.container.querySelector('.visual-editor');
        this.syntaxOverlay = this.container.querySelector('.syntax-overlay');
        this.lineNumbers = this.container.querySelector('.line-numbers');
        
        this.currentMode = 'code';
    }
    
    setupEventListeners() {
        // Mode switching
        this.container.querySelectorAll('[data-mode]').forEach(btn => {
            btn.addEventListener('click', (e) => {
                this.switchMode(e.target.dataset.mode);
            });
        });
        
        // Format button
        this.container.querySelector('.btn-format').addEventListener('click', () => {
            this.formatCode();
        });
        
        // Code textarea events
        this.codeTextarea.addEventListener('input', () => {
            this.updateSyntaxHighlighting();
            this.updateLineNumbers();
        });
        
        this.codeTextarea.addEventListener('scroll', () => {
            this.syncScroll();
        });
        
        this.codeTextarea.addEventListener('keydown', (e) => {
            this.handleKeydown(e);
        });
        
        // Visual editor events
        this.visualEditor.addEventListener('input', () => {
            if (this.currentMode === 'visual') {
                this.updateCodeFromVisual();
            }
        });
    }
    
    switchMode(mode) {
        const codeBtn = this.container.querySelector('.btn-code');
        const visualBtn = this.container.querySelector('.btn-visual');
        const codeEditor = this.container.querySelector('.code-editor');
        const visualEditor = this.container.querySelector('.visual-editor');
        
        if (mode === 'code') {
            if (this.currentMode === 'visual') {
                this.updateCodeFromVisual();
            }
            
            codeBtn.classList.add('active');
            visualBtn.classList.remove('active');
            codeEditor.style.display = 'block';
            visualEditor.style.display = 'none';
        } else {
            this.updateVisualFromCode();
            
            visualBtn.classList.add('active');
            codeBtn.classList.remove('active');
            codeEditor.style.display = 'none';
            visualEditor.style.display = 'block';
        }
        
        this.currentMode = mode;
    }
    
    updateSyntaxHighlighting() {
        const code = this.codeTextarea.value;
        const highlighted = this.highlightHTML(code);
        this.syntaxOverlay.innerHTML = highlighted;
    }
    
    highlightHTML(code) {
        return code
            .replace(/&/g, '&amp;')
            .replace(/</g, '&lt;')
            .replace(/>/g, '&gt;')
            .replace(/(&lt;\/?)([\w-]+)/g, '$1<span class="tag">$2</span>')
            .replace(/([\w-]+)(=)/g, '<span class="attr">$1</span>$2')
            .replace(/=("[^"]*")/g, '=<span class="string">$1</span>')
            .replace(/(&lt;!--[\s\S]*?--&gt;)/g, '<span class="comment">$1</span>')
            .replace(/\n/g, '<br>');
    }
    
    updateLineNumbers() {
        const lines = this.codeTextarea.value.split('\n').length;
        let lineNumbersHTML = '';
        for (let i = 1; i <= lines; i++) {
            lineNumbersHTML += `<div>${i}</div>`;
        }
        this.lineNumbers.innerHTML = lineNumbersHTML;
    }
    
    syncScroll() {
        this.syntaxOverlay.scrollTop = this.codeTextarea.scrollTop;
        this.lineNumbers.scrollTop = this.codeTextarea.scrollTop;
    }
    
    handleKeydown(e) {
        if (e.key === 'Tab') {
            e.preventDefault();
            const start = this.codeTextarea.selectionStart;
            const end = this.codeTextarea.selectionEnd;
            const spaces = ' '.repeat(this.options.tabSize);
            
            this.codeTextarea.value = 
                this.codeTextarea.value.substring(0, start) + 
                spaces + 
                this.codeTextarea.value.substring(end);
            
            this.codeTextarea.selectionStart = this.codeTextarea.selectionEnd = start + this.options.tabSize;
            this.updateSyntaxHighlighting();
        }
    }
    
    formatCode() {
        if (typeof html_beautify !== 'undefined') {
            const formatted = html_beautify(this.codeTextarea.value, {
                indent_size: this.options.tabSize,
                wrap_line_length: 120,
                preserve_newlines: true
            });
            this.setValue(formatted);
        }
    }
    
    updateVisualFromCode() {
        const code = this.codeTextarea.value;
        // Convert relative paths to absolute for display
        const processedCode = this.convertPathsForDisplay(code);
        
        // Extract body content
        const bodyMatch = processedCode.match(/<body[^>]*>([\s\S]*?)<\/body>/i);
        const content = bodyMatch ? bodyMatch[1] : processedCode;
        
        this.visualEditor.innerHTML = content || '<p>Start editing your content...</p>';
    }
    
    updateCodeFromVisual() {
        let content = this.visualEditor.innerHTML;
        if (content === '<p>Start editing your content...</p>') {
            content = '';
        }
        
        // Convert absolute paths back to relative
        content = this.convertPathsForSaving(content);
        
        // Update only body content if it exists
        const originalCode = this.codeTextarea.value;
        const bodyMatch = originalCode.match(/<body[^>]*>([\s\S]*?)<\/body>/i);
        
        if (bodyMatch) {
            const updatedCode = originalCode.replace(bodyMatch[1], content);
            this.codeTextarea.value = updatedCode;
        } else {
            this.codeTextarea.value = content;
        }
        
        this.updateSyntaxHighlighting();
        this.updateLineNumbers();
    }
    
    convertPathsForDisplay(code) {
        if (!window.siteBaseUrl) return code;
        
        const baseUrl = window.siteBaseUrl.replace(/\/$/, '');
        
        return code.replace(/\b(src|href)="(?!https?:\/\/|\/\/|data:|mailto:|tel:|#)([^"]+)"/gi, (match, attr, path) => {
            if (path.startsWith('/')) {
                return `${attr}="${window.location.origin}${path}"`;
            }
            return `${attr}="${baseUrl}/${path}"`;
        });
    }
    
    convertPathsForSaving(code) {
        if (!window.siteBaseUrl) return code;
        
        const baseUrl = window.siteBaseUrl.replace(/\/$/, '');
        const baseUrlEscaped = baseUrl.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
        const originEscaped = window.location.origin.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
        
        return code
            .replace(new RegExp(`\\b(src|href)="${baseUrlEscaped}/([^"]*)"`, 'gi'), '$1="$2"')
            .replace(new RegExp(`\\b(src|href)="${originEscaped}/([^"]*)"`, 'gi'), '$1="/$2"');
    }
    
    getValue() {
        return this.codeTextarea.value;
    }
    
    setValue(value) {
        this.codeTextarea.value = value;
        this.updateSyntaxHighlighting();
        this.updateLineNumbers();
    }
    
    applySyntaxHighlighting() {
        this.updateSyntaxHighlighting();
        this.updateLineNumbers();
    }
}

// CSS for the simple editor
const editorCSS = `
.simple-editor {
    border: 1px solid #ddd;
    border-radius: 4px;
    overflow: hidden;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}

.editor-toolbar {
    background: #f5f5f5;
    padding: 8px;
    border-bottom: 1px solid #ddd;
    display: flex;
    gap: 8px;
}

.editor-toolbar button {
    padding: 6px 12px;
    border: 1px solid #ccc;
    background: white;
    border-radius: 3px;
    cursor: pointer;
    font-size: 12px;
}

.editor-toolbar button.active {
    background: #007cba;
    color: white;
    border-color: #005a87;
}

.editor-content {
    position: relative;
    min-height: 400px;
}

.code-editor {
    position: relative;
    display: flex;
    min-height: 400px;
}

.line-numbers {
    background: #f8f8f8;
    color: #666;
    padding: 10px 8px;
    font-size: 12px;
    line-height: 1.5;
    text-align: right;
    border-right: 1px solid #ddd;
    user-select: none;
    overflow: hidden;
}

.code-textarea {
    flex: 1;
    border: none;
    outline: none;
    padding: 10px;
    font-family: inherit;
    font-size: 14px;
    line-height: 1.5;
    resize: none;
    background: transparent;
    color: transparent;
    caret-color: #333;
    z-index: 2;
    position: relative;
}

.syntax-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    padding: 10px;
    font-family: inherit;
    font-size: 14px;
    line-height: 1.5;
    pointer-events: none;
    z-index: 1;
    overflow: auto;
    white-space: pre-wrap;
    word-wrap: break-word;
}

.visual-editor {
    min-height: 400px;
    padding: 15px;
    outline: none;
    line-height: 1.6;
}

/* Syntax highlighting */
.tag { color: #e06c75; }
.attr { color: #d19a66; }
.string { color: #98c379; }
.comment { color: #5c6370; font-style: italic; }
`;

// Inject CSS
if (!document.getElementById('simple-editor-css')) {
    const style = document.createElement('style');
    style.id = 'simple-editor-css';
    style.textContent = editorCSS;
    document.head.appendChild(style);
}

// Export for use
window.SimpleHTMLEditor = SimpleHTMLEditor;
