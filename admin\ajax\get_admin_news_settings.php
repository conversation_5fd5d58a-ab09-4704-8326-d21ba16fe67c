<?php
/**
 * Get Admin News Settings
 *
 * This file returns the admin news settings from the database.
 */

// Start output buffering to prevent any output before JSON
ob_start();

// Include database configuration
require_once '../config.php';

// Check if user is logged in
session_start();

// Set proper JSON headers
header('Content-Type: application/json');
header('Cache-Control: no-store, no-cache, must-revalidate, max-age=0');
header('Cache-Control: post-check=0, pre-check=0', false);
header('Pragma: no-cache');

if (!isset($_SESSION['user_id'])) {
    echo json_encode([
        'success' => false,
        'message' => 'Unauthorized'
    ], JSO<PERSON>_PRETTY_PRINT | JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
    ob_end_flush();
    exit;
}

// Get admin news settings
$settings = getAdminNewsSettings($conn);

// Return settings as JSON
echo json_encode([
    'success' => true,
    'settings' => $settings
], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);

// Check for JSON encoding errors
if (json_last_error() !== JSON_ERROR_NONE) {
    // Clear any previous output
    ob_clean();

    // Return a simplified error response
    echo json_encode([
        'success' => false,
        'message' => 'JSON encoding error: ' . json_last_error_msg()
    ], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
}

// End output buffering and flush
ob_end_flush();
exit;

/**
 * Get admin news settings from the database
 *
 * @param mysqli $conn Database connection
 * @return array Admin news settings
 */
function getAdminNewsSettings($conn) {
    // Initialize settings array
    $settings = [];

    // Get admin_news settings
    $news_query = "SELECT setting_key, setting_value FROM system_settings WHERE category = 'admin_news'";
    $news_result = $conn->query($news_query);

    if ($news_result && $news_result->num_rows > 0) {
        while ($row = $news_result->fetch_assoc()) {
            $settings[$row['setting_key']] = $row['setting_value'];
        }
    }

    // Set default values for missing settings
    $default_settings = [
        'admin_news_per_page' => '10',
        'admin_show_news_date' => '1',
        'admin_show_news_category' => '1',
        'admin_news_excerpt_length' => '100',
        'generate_slugs' => '1',
        'slug_separator' => '-',
        'admin_items_per_page_options' => '10,25,50,100',
        'admin_default_sort_order' => 'newest'
    ];

    // Merge default settings with database settings
    $settings = array_merge($default_settings, $settings);

    // If no admin settings found, try to get legacy news settings
    if (count($settings) <= count($default_settings)) {
        $legacy_query = "SELECT setting_key, setting_value FROM system_settings WHERE category = 'news'";
        $legacy_result = $conn->query($legacy_query);

        if ($legacy_result && $legacy_result->num_rows > 0) {
            while ($row = $legacy_result->fetch_assoc()) {
                // Map legacy settings to new settings
                switch ($row['setting_key']) {
                    case 'news_per_page':
                        $settings['admin_news_per_page'] = $row['setting_value'];
                        break;
                    case 'show_news_date':
                        $settings['admin_show_news_date'] = $row['setting_value'];
                        break;
                    case 'show_news_category':
                        $settings['admin_show_news_category'] = $row['setting_value'];
                        break;
                    case 'news_excerpt_length':
                        $settings['admin_news_excerpt_length'] = $row['setting_value'];
                        break;
                    case 'items_per_page_options':
                        $settings['admin_items_per_page_options'] = $row['setting_value'];
                        break;
                    case 'default_sort_order':
                        $settings['admin_default_sort_order'] = $row['setting_value'];
                        break;
                    case 'generate_slugs':
                    case 'slug_separator':
                        // These settings are shared between admin and frontend
                        $settings[$row['setting_key']] = $row['setting_value'];
                        break;
                }
            }
        }
    }

    return $settings;
}
?>
