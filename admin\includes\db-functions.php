<?php
/**
 * Database Functions
 * Functions for database operations
 * Consolidated from multiple PHP files
 */

/**
 * Connect to database
 * 
 * @return mysqli Database connection
 */
function db_connect() {
    // Include database configuration
    require_once __DIR__ . '/../../config.php';
    
    // Create connection
    $conn = new mysqli(DB_HOST, DB_USER, DB_PASS, DB_NAME);
    
    // Check connection
    if ($conn->connect_error) {
        die("Connection failed: " . $conn->connect_error);
    }
    
    // Set charset
    $conn->set_charset("utf8mb4");
    
    return $conn;
}

/**
 * Close database connection
 * 
 * @param mysqli $conn Database connection
 * @return void
 */
function db_close($conn) {
    $conn->close();
}

/**
 * Execute query
 * 
 * @param string $query SQL query
 * @param array $params Query parameters
 * @param string $types Parameter types (i: integer, d: double, s: string, b: blob)
 * @return mysqli_result|bool Query result
 */
function db_query($query, $params = [], $types = '') {
    global $conn;
    
    $stmt = $conn->prepare($query);
    
    if (!$stmt) {
        return false;
    }
    
    if (!empty($params)) {
        if (empty($types)) {
            // Auto-detect parameter types
            $types = '';
            foreach ($params as $param) {
                if (is_int($param)) {
                    $types .= 'i';
                } elseif (is_float($param)) {
                    $types .= 'd';
                } elseif (is_string($param)) {
                    $types .= 's';
                } else {
                    $types .= 'b';
                }
            }
        }
        
        $stmt->bind_param($types, ...$params);
    }
    
    $stmt->execute();
    
    return $stmt->get_result();
}

/**
 * Get single row
 * 
 * @param string $query SQL query
 * @param array $params Query parameters
 * @param string $types Parameter types
 * @return array|null Row data or null if not found
 */
function db_get_row($query, $params = [], $types = '') {
    $result = db_query($query, $params, $types);
    
    if (!$result) {
        return null;
    }
    
    return $result->fetch_assoc();
}

/**
 * Get multiple rows
 * 
 * @param string $query SQL query
 * @param array $params Query parameters
 * @param string $types Parameter types
 * @return array Array of rows
 */
function db_get_rows($query, $params = [], $types = '') {
    $result = db_query($query, $params, $types);
    
    if (!$result) {
        return [];
    }
    
    $rows = [];
    
    while ($row = $result->fetch_assoc()) {
        $rows[] = $row;
    }
    
    return $rows;
}

/**
 * Get single value
 * 
 * @param string $query SQL query
 * @param array $params Query parameters
 * @param string $types Parameter types
 * @return mixed|null Value or null if not found
 */
function db_get_var($query, $params = [], $types = '') {
    $result = db_query($query, $params, $types);
    
    if (!$result) {
        return null;
    }
    
    $row = $result->fetch_row();
    
    return $row ? $row[0] : null;
}

/**
 * Insert data
 * 
 * @param string $table Table name
 * @param array $data Data to insert (column => value)
 * @return int|bool Inserted ID on success, false on failure
 */
function db_insert($table, $data) {
    global $conn;
    
    $columns = array_keys($data);
    $values = array_values($data);
    
    $placeholders = array_fill(0, count($values), '?');
    
    $query = "INSERT INTO $table (" . implode(', ', $columns) . ") VALUES (" . implode(', ', $placeholders) . ")";
    
    $stmt = $conn->prepare($query);
    
    if (!$stmt) {
        return false;
    }
    
    // Auto-detect parameter types
    $types = '';
    foreach ($values as $value) {
        if (is_int($value)) {
            $types .= 'i';
        } elseif (is_float($value)) {
            $types .= 'd';
        } elseif (is_string($value)) {
            $types .= 's';
        } else {
            $types .= 'b';
        }
    }
    
    $stmt->bind_param($types, ...$values);
    
    if ($stmt->execute()) {
        return $conn->insert_id;
    }
    
    return false;
}

/**
 * Update data
 * 
 * @param string $table Table name
 * @param array $data Data to update (column => value)
 * @param array $where Where conditions (column => value)
 * @return bool True on success, false on failure
 */
function db_update($table, $data, $where) {
    global $conn;
    
    $set = [];
    foreach ($data as $column => $value) {
        $set[] = "$column = ?";
    }
    
    $where_clauses = [];
    foreach ($where as $column => $value) {
        $where_clauses[] = "$column = ?";
    }
    
    $query = "UPDATE $table SET " . implode(', ', $set) . " WHERE " . implode(' AND ', $where_clauses);
    
    $stmt = $conn->prepare($query);
    
    if (!$stmt) {
        return false;
    }
    
    $values = array_merge(array_values($data), array_values($where));
    
    // Auto-detect parameter types
    $types = '';
    foreach ($values as $value) {
        if (is_int($value)) {
            $types .= 'i';
        } elseif (is_float($value)) {
            $types .= 'd';
        } elseif (is_string($value)) {
            $types .= 's';
        } else {
            $types .= 'b';
        }
    }
    
    $stmt->bind_param($types, ...$values);
    
    return $stmt->execute();
}

/**
 * Delete data
 * 
 * @param string $table Table name
 * @param array $where Where conditions (column => value)
 * @return bool True on success, false on failure
 */
function db_delete($table, $where) {
    global $conn;
    
    $where_clauses = [];
    foreach ($where as $column => $value) {
        $where_clauses[] = "$column = ?";
    }
    
    $query = "DELETE FROM $table WHERE " . implode(' AND ', $where_clauses);
    
    $stmt = $conn->prepare($query);
    
    if (!$stmt) {
        return false;
    }
    
    $values = array_values($where);
    
    // Auto-detect parameter types
    $types = '';
    foreach ($values as $value) {
        if (is_int($value)) {
            $types .= 'i';
        } elseif (is_float($value)) {
            $types .= 'd';
        } elseif (is_string($value)) {
            $types .= 's';
        } else {
            $types .= 'b';
        }
    }
    
    $stmt->bind_param($types, ...$values);
    
    return $stmt->execute();
}

/**
 * Count rows
 * 
 * @param string $table Table name
 * @param array $where Where conditions (column => value)
 * @return int Number of rows
 */
function db_count($table, $where = []) {
    global $conn;
    
    $query = "SELECT COUNT(*) FROM $table";
    
    if (!empty($where)) {
        $where_clauses = [];
        foreach ($where as $column => $value) {
            $where_clauses[] = "$column = ?";
        }
        
        $query .= " WHERE " . implode(' AND ', $where_clauses);
    }
    
    $stmt = $conn->prepare($query);
    
    if (!$stmt) {
        return 0;
    }
    
    if (!empty($where)) {
        $values = array_values($where);
        
        // Auto-detect parameter types
        $types = '';
        foreach ($values as $value) {
            if (is_int($value)) {
                $types .= 'i';
            } elseif (is_float($value)) {
                $types .= 'd';
            } elseif (is_string($value)) {
                $types .= 's';
            } else {
                $types .= 'b';
            }
        }
        
        $stmt->bind_param($types, ...$values);
    }
    
    $stmt->execute();
    $result = $stmt->get_result();
    $row = $result->fetch_row();
    
    return $row ? (int)$row[0] : 0;
}

/**
 * Check if table exists
 * 
 * @param string $table Table name
 * @return bool True if table exists, false otherwise
 */
function db_table_exists($table) {
    global $conn;
    
    $result = $conn->query("SHOW TABLES LIKE '$table'");
    
    return $result->num_rows > 0;
}

/**
 * Get table columns
 * 
 * @param string $table Table name
 * @return array Array of column names
 */
function db_get_columns($table) {
    global $conn;
    
    $result = $conn->query("SHOW COLUMNS FROM $table");
    
    if (!$result) {
        return [];
    }
    
    $columns = [];
    
    while ($row = $result->fetch_assoc()) {
        $columns[] = $row['Field'];
    }
    
    return $columns;
}

/**
 * Escape string
 * 
 * @param string $string String to escape
 * @return string Escaped string
 */
function db_escape($string) {
    global $conn;
    
    return $conn->real_escape_string($string);
}

/**
 * Begin transaction
 * 
 * @return bool True on success, false on failure
 */
function db_begin_transaction() {
    global $conn;
    
    return $conn->begin_transaction();
}

/**
 * Commit transaction
 * 
 * @return bool True on success, false on failure
 */
function db_commit() {
    global $conn;
    
    return $conn->commit();
}

/**
 * Rollback transaction
 * 
 * @return bool True on success, false on failure
 */
function db_rollback() {
    global $conn;
    
    return $conn->rollback();
}

/**
 * Get last error
 * 
 * @return string Last error message
 */
function db_error() {
    global $conn;
    
    return $conn->error;
}

/**
 * Get last inserted ID
 * 
 * @return int Last inserted ID
 */
function db_last_id() {
    global $conn;
    
    return $conn->insert_id;
}

/**
 * Get affected rows
 * 
 * @return int Number of affected rows
 */
function db_affected_rows() {
    global $conn;
    
    return $conn->affected_rows;
}

/**
 * Backup database
 * 
 * @param string $tables Tables to backup (comma-separated)
 * @return string|bool SQL dump on success, false on failure
 */
function db_backup($tables = '*') {
    global $conn;
    
    // Get all tables if not specified
    if ($tables === '*') {
        $tables = [];
        $result = $conn->query('SHOW TABLES');
        while ($row = $result->fetch_row()) {
            $tables[] = $row[0];
        }
    } else {
        $tables = explode(',', $tables);
    }
    
    $output = '';
    
    // Process each table
    foreach ($tables as $table) {
        $result = $conn->query("SELECT * FROM $table");
        $num_fields = $result->field_count;
        $num_rows = $result->num_rows;
        
        $output .= "DROP TABLE IF EXISTS $table;\n";
        
        $row2 = $conn->query("SHOW CREATE TABLE $table")->fetch_row();
        $output .= $row2[1] . ";\n\n";
        
        if ($num_rows > 0) {
            $output .= "INSERT INTO $table VALUES ";
            
            $row_count = 0;
            while ($row = $result->fetch_row()) {
                $output .= "(";
                for ($j = 0; $j < $num_fields; $j++) {
                    $row[$j] = addslashes($row[$j]);
                    $row[$j] = str_replace("\n", "\\n", $row[$j]);
                    
                    if (isset($row[$j])) {
                        $output .= "'" . $row[$j] . "'";
                    } else {
                        $output .= "NULL";
                    }
                    
                    if ($j < ($num_fields - 1)) {
                        $output .= ",";
                    }
                }
                
                $row_count++;
                if ($row_count < $num_rows) {
                    $output .= "),\n";
                } else {
                    $output .= ");\n\n";
                }
            }
        }
    }
    
    return $output;
}

/**
 * Restore database from backup
 * 
 * @param string $sql SQL dump
 * @return bool True on success, false on failure
 */
function db_restore($sql) {
    global $conn;
    
    // Begin transaction
    $conn->begin_transaction();
    
    try {
        // Split SQL by semicolon
        $queries = explode(';', $sql);
        
        foreach ($queries as $query) {
            $query = trim($query);
            
            if (!empty($query)) {
                $conn->query($query);
                
                if ($conn->error) {
                    throw new Exception($conn->error);
                }
            }
        }
        
        // Commit transaction
        $conn->commit();
        
        return true;
    } catch (Exception $e) {
        // Rollback transaction
        $conn->rollback();
        
        error_log('Error restoring database: ' . $e->getMessage());
        
        return false;
    }
}
