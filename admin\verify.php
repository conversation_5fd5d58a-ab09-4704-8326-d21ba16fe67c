<?php
session_start();
require_once 'config.php';

$message = '';
$status = '';

// Check if token is provided
if (isset($_GET['token']) && !empty($_GET['token'])) {
    $token = sanitize($_GET['token']);

    // First, check if token exists in the users table (direct verification method)
    $user_sql = "SELECT id, username, email, is_verified FROM users WHERE verification_token = ? AND is_verified = 0";
    $user_stmt = $conn->prepare($user_sql);
    $user_stmt->bind_param("s", $token);
    $user_stmt->execute();
    $user_result = $user_stmt->get_result();

    if ($user_result && $user_result->num_rows > 0) {
        // Token found in users table
        $user = $user_result->fetch_assoc();

        // Update user as verified
        $update_sql = "UPDATE users SET is_verified = 1, verification_token = NULL WHERE id = ?";
        $update_stmt = $conn->prepare($update_sql);
        $update_stmt->bind_param("i", $user['id']);

        if ($update_stmt->execute()) {
            $message = "Your account has been verified successfully. You can now login.";
            $status = "success";
        } else {
            $message = "An error occurred. Please try again later.";
            $status = "error";
        }
    } else {
        // Check if tokens table exists (token table verification method)
        $check_table = $conn->query("SHOW TABLES LIKE 'verification_tokens'");
        if ($check_table->num_rows > 0) {
            // Check if token exists and is valid in the verification_tokens table
            $sql = "SELECT vt.*, u.id as user_id, u.username, u.email, u.is_verified
                    FROM verification_tokens vt
                    JOIN users u ON vt.user_id = u.id
                    WHERE vt.token = ? AND vt.expiry > NOW()";
            $stmt = $conn->prepare($sql);
            $stmt->bind_param("s", $token);
            $stmt->execute();
            $result = $stmt->get_result();

            if ($result && $result->num_rows > 0) {
                $data = $result->fetch_assoc();

                // Check if user is already verified
                if (isset($data['is_verified']) && $data['is_verified'] == 1) {
                    $message = "Your account is already verified. You can login now.";
                    $status = "info";
                } else {
                    // Update user verification status
                    $update_sql = "UPDATE users SET is_verified = 1 WHERE id = ?";
                    $update_stmt = $conn->prepare($update_sql);
                    $update_stmt->bind_param("i", $data['user_id']);

                    if ($update_stmt->execute()) {
                        // Delete used token
                        $delete_token = "DELETE FROM verification_tokens WHERE token = ?";
                        $delete_stmt = $conn->prepare($delete_token);
                        $delete_stmt->bind_param("s", $token);
                        $delete_stmt->execute();

                        $message = "Your account has been verified successfully. You can now login.";
                        $status = "success";
                    } else {
                        $message = "Failed to verify your account. Please try again later.";
                        $status = "error";
                    }
                }
            } else {
                // Check if token exists but is expired
                $expired_sql = "SELECT vt.*, u.id as user_id, u.username, u.email, u.is_verified
                        FROM verification_tokens vt
                        JOIN users u ON vt.user_id = u.id
                        WHERE vt.token = ? AND vt.expiry <= NOW()";
                $expired_stmt = $conn->prepare($expired_sql);
                $expired_stmt->bind_param("s", $token);
                $expired_stmt->execute();
                $expired_result = $expired_stmt->get_result();

                if ($expired_result && $expired_result->num_rows > 0) {
                    $expired_data = $expired_result->fetch_assoc();
                    $message = "Your verification link has expired. Please request a new verification email.";
                    $status = "error";

                    // Store user email in session for potential admin resend
                    $_SESSION['expired_token_email'] = $expired_data['email'];
                    $_SESSION['expired_token_user_id'] = $expired_data['user_id'];
                } else {
                    $message = "Invalid verification link. Please request a new verification email.";
                    $status = "error";
                }
            }
        } else {
            $message = "Invalid verification link.";
            $status = "error";
        }
    }
} else {
    $message = "Invalid verification link.";
    $status = "error";
}

// Set page title
$page_title = "Account Verification";
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> | Manage Inc.</title>
    <link rel="stylesheet" href="../css/style.css?v=<?php echo time(); ?>">
    <link rel="stylesheet" href="css/admin-style.css?v=<?php echo time(); ?>">
    <link href="https://fonts.googleapis.com/css2?family=Open+Sans:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <style>
        body {
            background-color: #f5f5f5;
            background-image: linear-gradient(135deg, #f5f5f5 0%, #e0e0e0 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            min-height: 100vh;
            padding: 20px;
            margin: 0;
            font-family: 'Open Sans', sans-serif;
        }

        .verification-container {
            max-width: 500px;
            width: 100%;
            padding: 40px;
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            position: relative;
            overflow: hidden;
        }

        .verification-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 5px;
            background: #f1ca2f;
        }

        .verification-header {
            text-align: center;
            margin-bottom: 35px;
        }

        .verification-header img {
            max-width: 200px;
            margin-bottom: 25px;
        }

        .verification-header h2 {
            color: #3c3c45;
            font-weight: 600;
            margin-bottom: 10px;
            font-size: 24px;
        }

        .admin-alert {
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 25px;
            font-size: 14px;
        }

        .admin-alert.error {
            background-color: #ffebee;
            color: #c62828;
            border-left: 4px solid #c62828;
        }

        .admin-alert.success {
            background-color: #e8f5e9;
            color: #2e7d32;
            border-left: 4px solid #2e7d32;
        }

        .admin-alert.info {
            background-color: #e3f2fd;
            color: #1565c0;
            border-left: 4px solid #1565c0;
        }

        .verification-actions {
            text-align: center;
            margin-top: 30px;
        }

        .verification-actions a {
            display: inline-block;
            padding: 12px 24px;
            background-color: #3c3c45;
            color: #fff;
            border-radius: 4px;
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .verification-actions a:hover {
            background-color: #4a4a52;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }

        .verification-actions .admin-action {
            margin-top: 10px;
            background-color: #f1ca2f;
            color: #333;
        }

        .verification-actions .admin-action:hover {
            background-color: #e0bc20;
        }

        .verification-footer {
            text-align: center;
            margin-top: 35px;
            color: #777;
            font-size: 13px;
            border-top: 1px solid #eee;
            padding-top: 20px;
        }
    </style>
</head>
<body>
    <div class="verification-container">
        <div class="verification-header">
            <img src="../images/login-logo.jpg" alt="Manage Incorporated">
            <h2>Account Verification</h2>
        </div>

        <div class="admin-alert <?php echo $status; ?>">
            <?php if ($status == 'error'): ?>
                <i class="fas fa-exclamation-circle"></i>
            <?php elseif ($status == 'success'): ?>
                <i class="fas fa-check-circle"></i>
            <?php else: ?>
                <i class="fas fa-info-circle"></i>
            <?php endif; ?>
            <?php echo $message; ?>
        </div>

        <div class="verification-actions">
            <a href="index.php">Go to Login</a>

            <?php if (isset($_SESSION['expired_token_email']) && isset($_SESSION['user_id']) && isset($_SESSION['is_admin']) && $_SESSION['is_admin'] == 1): ?>
                <a href="admin-resend-verification.php?user_id=<?php echo $_SESSION['expired_token_user_id']; ?>" class="admin-action">
                    <i class="fas fa-paper-plane"></i> Admin: Resend Verification Email
                </a>
            <?php endif; ?>
        </div>

        <div class="verification-footer">
            <p>&copy; <?php echo date('Y'); ?> Manage Inc. All Rights Reserved.</p>
        </div>
    </div>
</body>
</html>
