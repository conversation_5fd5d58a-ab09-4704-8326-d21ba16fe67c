/**
 * Collaborative Editing JavaScript
 * 
 * Handles collaborative editing functionality
 */

// Acquire a lock on a file
function acquireLock(filePath) {
    return fetch('ajax/frontend_editor.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
            'Accept': 'application/json'
        },
        body: 'action=acquire_lock&file_path=' + encodeURIComponent(filePath)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Update UI to show lock status
            updateLockStatus(true, null);
            return true;
        } else {
            // Show locked by message
            updateLockStatus(false, data.locked_by);
            return false;
        }
    })
    .catch(error => {
        console.error('Error acquiring lock:', error);
        return false;
    });
}

// Extend an existing lock
function extendLock(filePath) {
    return fetch('ajax/frontend_editor.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
            'Accept': 'application/json'
        },
        body: 'action=extend_lock&file_path=' + encodeURIComponent(filePath)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Lock extended successfully
            return true;
        } else {
            console.error('Failed to extend lock:', data.message);
            return false;
        }
    })
    .catch(error => {
        console.error('Error extending lock:', error);
        return false;
    });
}

// Release a lock on a file
function releaseLock(filePath) {
    return fetch('ajax/frontend_editor.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
            'Accept': 'application/json'
        },
        body: 'action=release_lock&file_path=' + encodeURIComponent(filePath)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Update UI to show lock released
            updateLockStatus(false, null);
            return true;
        } else {
            console.error('Failed to release lock:', data.message);
            return false;
        }
    })
    .catch(error => {
        console.error('Error releasing lock:', error);
        return false;
    });
}

// Force release a lock (admin only)
function forceReleaseLock(filePath) {
    return fetch('ajax/frontend_editor.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
            'Accept': 'application/json'
        },
        body: 'action=force_release_lock&file_path=' + encodeURIComponent(filePath)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Update UI to show lock released
            updateLockStatus(false, null);
            return true;
        } else {
            console.error('Failed to force release lock:', data.message);
            return false;
        }
    })
    .catch(error => {
        console.error('Error force releasing lock:', error);
        return false;
    });
}

// Check if a file is locked
function checkLock(filePath) {
    return fetch('ajax/frontend_editor.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
            'Accept': 'application/json'
        },
        body: 'action=check_lock&file_path=' + encodeURIComponent(filePath)
    })
    .then(response => response.json())
    .then(data => {
        if (data.locked) {
            // File is locked
            if (data.locked_by_current_user) {
                // Locked by current user
                updateLockStatus(true, null);
            } else {
                // Locked by another user
                updateLockStatus(false, data.locked_by);
            }
            return data;
        } else {
            // File is not locked
            updateLockStatus(false, null);
            return null;
        }
    })
    .catch(error => {
        console.error('Error checking lock:', error);
        return null;
    });
}

// Update lock status in UI
function updateLockStatus(isLockedByCurrentUser, lockedBy) {
    const releaseLockBtn = document.getElementById('releaseLockBtn');
    const forceUnlockBtn = document.getElementById('forceUnlockBtn');
    const saveButton = document.querySelector('button[name="save_file"]');
    
    if (isLockedByCurrentUser) {
        // Locked by current user
        if (releaseLockBtn) {
            releaseLockBtn.style.display = 'inline-block';
        }
        
        if (forceUnlockBtn) {
            forceUnlockBtn.style.display = 'none';
        }
        
        if (saveButton) {
            saveButton.disabled = false;
        }
    } else if (lockedBy) {
        // Locked by another user
        if (releaseLockBtn) {
            releaseLockBtn.style.display = 'none';
        }
        
        if (forceUnlockBtn) {
            forceUnlockBtn.style.display = 'inline-block';
        }
        
        if (saveButton) {
            saveButton.disabled = true;
        }
        
        // Show lock message
        alert('This file is currently being edited by ' + lockedBy + '. You can view it, but you cannot save changes.');
    } else {
        // Not locked
        if (releaseLockBtn) {
            releaseLockBtn.style.display = 'none';
        }
        
        if (forceUnlockBtn) {
            forceUnlockBtn.style.display = 'none';
        }
        
        if (saveButton) {
            saveButton.disabled = false;
        }
    }
}
