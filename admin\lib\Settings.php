<?php
/**
 * Settings class for handling all system settings
 */
class Settings {
    private $conn;
    private $settings = [];
    private $categories = [
        'general' => 'General Settings',
        'email' => 'Email Settings',
        'fonts' => 'Font Settings',
        'appearance' => 'Appearance Settings'
    ];

    /**
     * Constructor
     *
     * @param object $conn Database connection
     */
    public function __construct($conn) {
        $this->conn = $conn;
        $this->initializeSettings();
    }

    /**
     * Initialize settings
     */
    private function initializeSettings() {
        // Check if settings table exists
        $check_table = "SHOW TABLES LIKE 'system_settings'";
        $table_result = $this->conn->query($check_table);

        if ($table_result->num_rows == 0) {
            // Table doesn't exist, create it
            $this->createSettingsTable();
        }

        // Load settings
        $this->loadSettings();
    }

    /**
     * Create settings table
     */
    private function createSettingsTable() {
        $sql = "CREATE TABLE IF NOT EXISTS `system_settings` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `category` varchar(50) NOT NULL,
            `setting_key` varchar(100) NOT NULL,
            `setting_value` text NOT NULL,
            `setting_type` varchar(20) NOT NULL DEFAULT 'text',
            `display_name` varchar(100) NOT NULL,
            `description` text,
            `options` text,
            `is_required` tinyint(1) NOT NULL DEFAULT 0,
            `order_index` int(11) NOT NULL DEFAULT 0,
            `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
            `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            UNIQUE KEY `unique_setting` (`category`, `setting_key`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;";

        $this->conn->query($sql);

        // Insert default settings
        $this->insertDefaultSettings();
    }

    /**
     * Insert default settings
     */
    private function insertDefaultSettings() {
        // General settings
        $this->insertSetting('general', 'admin_email', '<EMAIL>', 'email', 'Admin Email', 'The main administrator email address', null, 1, 1);
        $this->insertSetting('general', 'timezone', 'UTC', 'select', 'Timezone', 'Default timezone for the site', json_encode($this->getTimezoneOptions()), 1, 2);
        $this->insertSetting('general', 'date_format', 'Y-m-d', 'select', 'Date Format', 'Default date format', json_encode([
            'Y-m-d' => 'YYYY-MM-DD',
            'd-m-Y' => 'DD-MM-YYYY',
            'm/d/Y' => 'MM/DD/YYYY',
            'd/m/Y' => 'DD/MM/YYYY',
            'F j, Y' => 'Month Day, Year'
        ]), 1, 5);

        // Email settings (migrate from email_settings table)
        $this->migrateEmailSettings();

        // Font settings
        $this->insertSetting('fonts', 'google_fonts_api_key', '', 'text', 'Google Fonts API Key', 'API key for Google Fonts (optional)', null, 0, 1);
        $this->insertSetting('fonts', 'enable_google_fonts', '0', 'checkbox', 'Enable Google Fonts', 'Enable Google Fonts integration', null, 0, 2);
        $this->insertSetting('fonts', 'heading_font', 'Arial, sans-serif', 'font', 'Heading Font', 'Font for headings', null, 0, 3);
        $this->insertSetting('fonts', 'body_font', 'Arial, sans-serif', 'font', 'Body Font', 'Font for body text', null, 0, 4);

        // Appearance settings
        $this->insertSetting('appearance', 'primary_color', '#f1ca2f', 'color', 'Primary Color', 'Main color for the site', null, 0, 1);
        $this->insertSetting('appearance', 'secondary_color', '#3c3c45', 'color', 'Secondary Color', 'Secondary color for the site', null, 0, 2);
        $this->insertSetting('appearance', 'enable_dark_mode', '0', 'checkbox', 'Enable Dark Mode', 'Allow users to switch to dark mode', null, 0, 3);


    }

    /**
     * Migrate email settings from the old table
     */
    private function migrateEmailSettings() {
        // Check if email_settings table exists
        $check_table = "SHOW TABLES LIKE 'email_settings'";
        $table_result = $this->conn->query($check_table);

        if ($table_result->num_rows > 0) {
            // Table exists, migrate settings
            $sql = "SELECT * FROM email_settings LIMIT 1";
            $result = $this->conn->query($sql);

            if ($result->num_rows > 0) {
                $email_settings = $result->fetch_assoc();

                // Insert email settings
                $this->insertSetting('email', 'use_smtp', $email_settings['use_smtp'], 'checkbox', 'Use SMTP', 'Use SMTP to send emails instead of PHP mail()', null, 0, 1);
                $this->insertSetting('email', 'smtp_host', $email_settings['smtp_host'], 'text', 'SMTP Host', 'SMTP server hostname', null, 0, 2);
                $this->insertSetting('email', 'smtp_port', $email_settings['smtp_port'], 'number', 'SMTP Port', 'SMTP server port', null, 0, 3);
                $this->insertSetting('email', 'smtp_username', $email_settings['smtp_username'], 'text', 'SMTP Username', 'SMTP username', null, 0, 4);
                $this->insertSetting('email', 'smtp_password', $email_settings['smtp_password'], 'password', 'SMTP Password', 'SMTP password', null, 0, 5);
                $this->insertSetting('email', 'smtp_encryption', $email_settings['smtp_encryption'], 'select', 'SMTP Encryption', 'SMTP encryption method', json_encode([
                    'none' => 'None',
                    'tls' => 'TLS',
                    'ssl' => 'SSL'
                ]), 0, 6);
                $this->insertSetting('email', 'from_email', $email_settings['from_email'], 'email', 'From Email', 'Default sender email address', null, 1, 7);
                $this->insertSetting('email', 'from_name', $email_settings['from_name'], 'text', 'From Name', 'Default sender name', null, 1, 8);
                $this->insertSetting('email', 'reply_to', $email_settings['reply_to'], 'email', 'Reply-To Email', 'Default reply-to email address', null, 0, 9);

                return;
            }
        }

        // If no email settings table or no settings found, insert defaults
        $this->insertSetting('email', 'use_smtp', '0', 'checkbox', 'Use SMTP', 'Use SMTP to send emails instead of PHP mail()', null, 0, 1);
        $this->insertSetting('email', 'smtp_host', 'smtp.example.com', 'text', 'SMTP Host', 'SMTP server hostname', null, 0, 2);
        $this->insertSetting('email', 'smtp_port', '587', 'number', 'SMTP Port', 'SMTP server port', null, 0, 3);
        $this->insertSetting('email', 'smtp_username', '<EMAIL>', 'text', 'SMTP Username', 'SMTP username', null, 0, 4);
        $this->insertSetting('email', 'smtp_password', 'password', 'password', 'SMTP Password', 'SMTP password', null, 0, 5);
        $this->insertSetting('email', 'smtp_encryption', 'tls', 'select', 'SMTP Encryption', 'SMTP encryption method', json_encode([
            'none' => 'None',
            'tls' => 'TLS',
            'ssl' => 'SSL'
        ]), 0, 6);
        $this->insertSetting('email', 'from_email', '<EMAIL>', 'email', 'From Email', 'Default sender email address', null, 1, 7);
        $this->insertSetting('email', 'from_name', 'Manage Inc.', 'text', 'From Name', 'Default sender name', null, 1, 8);
        $this->insertSetting('email', 'reply_to', '<EMAIL>', 'email', 'Reply-To Email', 'Default reply-to email address', null, 0, 9);
    }

    /**
     * Insert a setting
     *
     * @param string $category Setting category
     * @param string $key Setting key
     * @param string $value Setting value
     * @param string $type Setting type (text, textarea, checkbox, select, etc.)
     * @param string $display_name Display name
     * @param string $description Description
     * @param string $options JSON-encoded options for select/radio types
     * @param bool $is_required Whether the setting is required
     * @param int $order_index Order index for display
     * @return bool Success status
     */
    private function insertSetting($category, $key, $value, $type, $display_name, $description, $options, $is_required, $order_index) {
        $category = $this->conn->real_escape_string($category);
        $key = $this->conn->real_escape_string($key);
        $value = $this->conn->real_escape_string($value);
        $type = $this->conn->real_escape_string($type);
        $display_name = $this->conn->real_escape_string($display_name);
        $description = $this->conn->real_escape_string($description);
        $options = $this->conn->real_escape_string($options);
        $is_required = (int)$is_required;
        $order_index = (int)$order_index;

        // Check if setting already exists
        $check_sql = "SELECT id FROM system_settings WHERE category = '$category' AND setting_key = '$key'";
        $check_result = $this->conn->query($check_sql);

        if ($check_result->num_rows > 0) {
            // Setting exists, update it
            $sql = "UPDATE system_settings SET
                    setting_value = '$value',
                    setting_type = '$type',
                    display_name = '$display_name',
                    description = '$description',
                    options = '$options',
                    is_required = $is_required,
                    order_index = $order_index
                    WHERE category = '$category' AND setting_key = '$key'";
        } else {
            // Setting doesn't exist, insert it
            $sql = "INSERT INTO system_settings
                    (category, setting_key, setting_value, setting_type, display_name, description, options, is_required, order_index)
                    VALUES
                    ('$category', '$key', '$value', '$type', '$display_name', '$description', '$options', $is_required, $order_index)";
        }

        return $this->conn->query($sql);
    }

    /**
     * Load all settings
     */
    private function loadSettings() {
        $sql = "SELECT * FROM system_settings ORDER BY category, order_index";
        $result = $this->conn->query($sql);

        if ($result->num_rows > 0) {
            while ($row = $result->fetch_assoc()) {
                $category = $row['category'];
                $key = $row['setting_key'];

                if (!isset($this->settings[$category])) {
                    $this->settings[$category] = [];
                }

                $this->settings[$category][$key] = $row;
            }
        }
    }

    /**
     * Get all settings
     *
     * @return array All settings
     */
    public function getAllSettings() {
        return $this->settings;
    }

    /**
     * Get settings by category
     *
     * @param string $category Category name
     * @return array Settings in the category
     */
    public function getSettingsByCategory($category) {
        return isset($this->settings[$category]) ? $this->settings[$category] : [];
    }

    /**
     * Get a specific setting value
     *
     * @param string $category Category name
     * @param string $key Setting key
     * @param mixed $default Default value if setting not found
     * @return mixed Setting value
     */
    public function getSetting($category, $key, $default = null) {
        if (isset($this->settings[$category][$key])) {
            return $this->settings[$category][$key]['setting_value'];
        }

        return $default;
    }

    /**
     * Update a setting
     *
     * @param string $category Category name
     * @param string $key Setting key
     * @param string $value New value
     * @return bool Success status
     */
    public function updateSetting($category, $key, $value) {
        $category = $this->conn->real_escape_string($category);
        $key = $this->conn->real_escape_string($key);
        $value = $this->conn->real_escape_string($value);

        // Check if setting exists
        $check_sql = "SELECT id FROM system_settings WHERE category = '$category' AND setting_key = '$key'";
        $check_result = $this->conn->query($check_sql);

        if ($check_result && $check_result->num_rows > 0) {
            // Setting exists, update it
            $sql = "UPDATE system_settings SET setting_value = '$value' WHERE category = '$category' AND setting_key = '$key'";
            $result = $this->conn->query($sql);

            if ($result) {
                // Update local cache
                if (isset($this->settings[$category][$key])) {
                    $this->settings[$category][$key]['setting_value'] = $value;
                }
                return true;
            }
        } else {
            // Setting doesn't exist, insert it with default values
            $sql = "INSERT INTO system_settings
                    (category, setting_key, setting_value, setting_type, display_name, description, is_required, order_index)
                    VALUES
                    ('$category', '$key', '$value', 'text', '$key', '', 0, 99)";
            $result = $this->conn->query($sql);

            if ($result) {
                // Add to local cache
                if (!isset($this->settings[$category])) {
                    $this->settings[$category] = [];
                }

                $this->settings[$category][$key] = [
                    'id' => $this->conn->insert_id,
                    'category' => $category,
                    'setting_key' => $key,
                    'setting_value' => $value,
                    'setting_type' => 'text',
                    'display_name' => $key,
                    'description' => '',
                    'options' => null,
                    'is_required' => 0,
                    'order_index' => 99
                ];

                return true;
            }
        }

        return false;
    }

    /**
     * Update multiple settings
     *
     * @param array $settings Array of settings [category => [key => value]]
     * @return bool Success status
     */
    public function updateSettings($settings) {
        $success = true;
        $errors = [];

        foreach ($settings as $category => $category_settings) {
            foreach ($category_settings as $key => $value) {
                try {
                    $result = $this->updateSetting($category, $key, $value);
                    if (!$result) {
                        $success = false;
                        $errors[] = "Failed to update setting: $category.$key";
                    }
                } catch (Exception $e) {
                    $success = false;
                    $errors[] = "Error updating setting $category.$key: " . $e->getMessage();
                }
            }
        }

        if (!empty($errors)) {
            error_log("Settings update errors: " . implode(", ", $errors));
        }

        return $success;
    }

    /**
     * Get all categories
     *
     * @return array Categories
     */
    public function getCategories() {
        return $this->categories;
    }

    /**
     * Get timezone options
     *
     * @return array Timezone options
     */
    private function getTimezoneOptions() {
        $timezones = [
            'UTC' => 'UTC',
            'America/New_York' => 'Eastern Time (US & Canada)',
            'America/Chicago' => 'Central Time (US & Canada)',
            'America/Denver' => 'Mountain Time (US & Canada)',
            'America/Los_Angeles' => 'Pacific Time (US & Canada)',
            'America/Anchorage' => 'Alaska',
            'America/Adak' => 'Hawaii',
            'Europe/London' => 'London',
            'Europe/Paris' => 'Paris',
            'Europe/Berlin' => 'Berlin',
            'Europe/Moscow' => 'Moscow',
            'Asia/Tokyo' => 'Tokyo',
            'Asia/Shanghai' => 'Shanghai',
            'Australia/Sydney' => 'Sydney',
            'Pacific/Auckland' => 'Auckland'
        ];

        return $timezones;
    }
