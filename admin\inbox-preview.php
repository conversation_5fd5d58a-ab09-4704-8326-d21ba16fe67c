<?php
// Include header
$page_title = 'Inbox Message View Preview';
$body_class = 'inbox-page';
include 'includes/header.php';

// Add CSS for the inbox page
$extra_css = '<link rel="stylesheet" href="assets/css/pages/inbox.css?v=' . $css_version . '">';

// Mock data for preview
$mock_submission = [
    'id' => 2,
    'name' => 'Gufran Farooqui',
    'email' => '<EMAIL>',
    'phone' => '+****************',
    'message' => 'Hello, I am interested in your services. Could you please provide more information about your pricing and availability? I would like to discuss a potential project with your team.',
    'source' => 'Footer Form',
    'is_read' => 0,
    'created_at' => '2025-05-25 08:36:00'
];
?>

<div class="admin-container">
    <!-- Content Header -->
    <div class="content-header">
        <div class="content-header-left">
            <div class="content-header-icon">
                <i class="fas fa-envelope"></i>
            </div>
            <div class="content-header-text">
                <h1>View Message</h1>
                <p>Preview of the new modern inbox message view design</p>
            </div>
        </div>
        <div class="content-header-actions">
            <a href="inbox.php" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Back to Inbox
            </a>
        </div>
    </div>

    <div class="inbox-wrapper">
        <!-- New Message View Layout -->
        <div class="message-view-layout">
            <div class="message-view-header">
                <div class="message-view-actions">
                    <a href="inbox.php" class="btn btn-primary">
                        <i class="fas fa-arrow-left"></i> Back to Inbox
                    </a>

                    <div class="message-action-buttons">
                        <a href="#" class="btn btn-secondary">
                            <i class="fas fa-envelope-open"></i> Mark as Read
                        </a>
                        <a href="#" class="btn btn-warning">
                            <i class="fas fa-folder"></i> Email Directly
                        </a>
                        <a href="#" class="btn btn-danger" onclick="return confirm('Are you sure you want to delete this message?');">
                            <i class="fas fa-trash"></i> Delete
                        </a>
                    </div>
                </div>
            </div>

            <div class="message-view-container">
                <div class="message-view-sidebar">
                    <div class="message-contact-card">
                        <div class="contact-header">
                            <div class="contact-avatar">
                                <i class="fas fa-user"></i>
                            </div>
                            <div class="contact-name">
                                <h3><?php echo htmlspecialchars($mock_submission['name']); ?></h3>
                                <span class="contact-source"><?php echo htmlspecialchars($mock_submission['source']); ?></span>
                            </div>
                        </div>

                        <div class="contact-details">
                            <div class="contact-detail-item">
                                <i class="fas fa-envelope"></i>
                                <a href="mailto:<?php echo htmlspecialchars($mock_submission['email']); ?>">
                                    <?php echo htmlspecialchars($mock_submission['email']); ?>
                                </a>
                            </div>

                            <div class="contact-detail-item">
                                <i class="fas fa-phone"></i>
                                <a href="tel:<?php echo htmlspecialchars($mock_submission['phone']); ?>">
                                    <?php echo htmlspecialchars($mock_submission['phone']); ?>
                                </a>
                            </div>

                            <div class="contact-detail-item">
                                <i class="fas fa-calendar"></i>
                                <span><?php echo date('F j, Y, g:i a', strtotime($mock_submission['created_at'])); ?></span>
                            </div>

                            <div class="contact-detail-item">
                                <i class="fas fa-tag"></i>
                                <span>
                                    <?php if ($mock_submission['is_read']): ?>
                                        <span class="status-badge read">Read</span>
                                    <?php else: ?>
                                        <span class="status-badge unread">Unread</span>
                                    <?php endif; ?>
                                </span>
                            </div>
                        </div>

                        <div class="contact-actions">
                            <a href="mailto:<?php echo htmlspecialchars($mock_submission['email']); ?>" class="btn btn-primary full-width">
                                <i class="fas fa-envelope"></i> Email Directly
                            </a>
                            <a href="tel:<?php echo htmlspecialchars($mock_submission['phone']); ?>" class="btn btn-secondary full-width">
                                <i class="fas fa-phone"></i> Call
                            </a>
                        </div>
                    </div>
                </div>

                <div class="message-view-content">
                    <div class="message-card">
                        <div class="message-card-header">
                            <h3>Message</h3>
                            <span class="message-date"><?php echo date('F j, Y, g:i a', strtotime($mock_submission['created_at'])); ?></span>
                        </div>

                        <div class="message-card-body">
                            <div class="message-text">
                                <?php echo nl2br(htmlspecialchars($mock_submission['message'])); ?>
                            </div>
                        </div>
                    </div>

                    <!-- Reply Form -->
                    <div class="reply-card">
                        <div class="reply-card-header">
                            <h3><i class="fas fa-reply"></i> Quick Reply</h3>
                        </div>

                        <div class="reply-card-body">
                            <form method="post" action="#" id="reply-form">
                                <div class="form-group">
                                    <label for="reply_subject">Subject:</label>
                                    <input type="text" id="reply_subject" name="reply_subject" class="form-control" value="Re: Contact Form Submission" required>
                                </div>

                                <div class="form-group">
                                    <label for="reply_message">Message:</label>
                                    <div class="reply-toolbar">
                                        <button type="button" class="reply-toolbar-btn" data-action="bold" title="Bold"><i class="fas fa-bold"></i></button>
                                        <button type="button" class="reply-toolbar-btn" data-action="italic" title="Italic"><i class="fas fa-italic"></i></button>
                                        <button type="button" class="reply-toolbar-btn" data-action="link" title="Insert Link"><i class="fas fa-link"></i></button>
                                        <button type="button" class="reply-toolbar-btn" data-action="template" title="Use Template"><i class="fas fa-file-alt"></i></button>
                                    </div>
                                    <textarea id="reply_message" name="reply_message" class="form-control" rows="6" placeholder="Type your reply here..." required></textarea>
                                </div>

                                <div class="form-actions">
                                    <button type="button" class="btn btn-secondary preview-reply-btn">
                                        <i class="fas fa-eye"></i> Preview
                                    </button>
                                    <button type="submit" class="btn btn-primary" id="send-reply-btn">
                                        <i class="fas fa-paper-plane"></i> Send Reply
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>

                    <!-- Mock Reply History -->
                    <div class="reply-history-card">
                        <div class="reply-history-header">
                            <h3><i class="fas fa-history"></i> Reply History</h3>
                            <span class="reply-count">2 Replies</span>
                        </div>

                        <div class="reply-history-body">
                            <div class="reply-item">
                                <div class="reply-item-header">
                                    <div class="reply-user-info">
                                        <span class="reply-user-name">Admin User</span>
                                        <span class="reply-date">May 24, 2025, 2:30 PM</span>
                                    </div>
                                    <div class="reply-subject">
                                        Re: Contact Form Submission
                                    </div>
                                </div>
                                <div class="reply-item-content">
                                    Thank you for your inquiry. We'll get back to you within 24 hours with detailed information about our services.
                                </div>
                            </div>

                            <div class="reply-item">
                                <div class="reply-item-header">
                                    <div class="reply-user-info">
                                        <span class="reply-user-name">Support Team</span>
                                        <span class="reply-date">May 23, 2025, 10:15 AM</span>
                                    </div>
                                    <div class="reply-subject">
                                        Welcome to Manage Inc.
                                    </div>
                                </div>
                                <div class="reply-item-content">
                                    Welcome! We've received your message and assigned it to our team. You can expect a response soon.
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Preview Modal -->
        <div id="preview-modal" class="admin-modal">
            <div class="admin-modal-content">
                <div class="admin-modal-header">
                    <h3><i class="fas fa-eye"></i> Preview Reply</h3>
                    <button type="button" class="admin-modal-close">&times;</button>
                </div>
                <div class="admin-modal-body">
                    <div class="preview-subject">
                        <strong>Subject:</strong> <span id="preview-subject"></span>
                    </div>
                    <div class="preview-message">
                        <div id="preview-message-content"></div>
                    </div>
                </div>
                <div class="admin-modal-footer">
                    <button type="button" class="btn btn-secondary admin-modal-close">
                        <i class="fas fa-times"></i> Close
                    </button>
                    <button type="button" class="btn btn-primary" id="send-from-preview">
                        <i class="fas fa-paper-plane"></i> Send Reply
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Reply toolbar functionality
    const replyToolbarButtons = document.querySelectorAll('.reply-toolbar-btn');
    const replyMessage = document.getElementById('reply_message');
    const previewModal = document.getElementById('preview-modal');
    const previewSubject = document.getElementById('preview-subject');
    const previewMessageContent = document.getElementById('preview-message-content');
    const sendFromPreviewBtn = document.getElementById('send-from-preview');
    const replyForm = document.getElementById('reply-form');
    const replySubject = document.getElementById('reply_subject');
    const previewReplyBtn = document.querySelector('.preview-reply-btn');
    const modalCloseButtons = document.querySelectorAll('.admin-modal-close');

    // Preview functionality
    if (previewReplyBtn && previewModal) {
        previewReplyBtn.addEventListener('click', function() {
            if (replyMessage.value.trim() === '') {
                alert('Please enter a message before previewing.');
                return;
            }

            // Set preview content
            previewSubject.textContent = replySubject.value;
            previewMessageContent.innerHTML = nl2br(replyMessage.value);

            // Show modal
            previewModal.style.display = 'flex';
        });

        // Close modal when clicking close button
        modalCloseButtons.forEach(button => {
            button.addEventListener('click', function() {
                previewModal.style.display = 'none';
            });
        });

        // Close modal when clicking outside
        window.addEventListener('click', function(e) {
            if (e.target === previewModal) {
                previewModal.style.display = 'none';
            }
        });

        // Send from preview
        if (sendFromPreviewBtn) {
            sendFromPreviewBtn.addEventListener('click', function() {
                alert('This is a preview - reply functionality would work in the actual inbox.');
                previewModal.style.display = 'none';
            });
        }
    }

    // Toolbar buttons
    if (replyToolbarButtons.length && replyMessage) {
        replyToolbarButtons.forEach(button => {
            button.addEventListener('click', function(e) {
                e.preventDefault();
                const action = this.getAttribute('data-action');
                if (!action) return;

                // Get cursor position
                const startPos = replyMessage.selectionStart;
                const endPos = replyMessage.selectionEnd;
                const selectedText = replyMessage.value.substring(startPos, endPos);

                switch (action) {
                    case 'bold':
                        wrapText(replyMessage, '**', '**', selectedText, startPos, endPos);
                        break;
                    case 'italic':
                        wrapText(replyMessage, '_', '_', selectedText, startPos, endPos);
                        break;
                    case 'link':
                        const url = prompt('Enter URL:', 'https://');
                        if (url) {
                            wrapText(replyMessage, '[', `](${url})`, selectedText || 'link text', startPos, endPos);
                        }
                        break;
                    case 'template':
                        const template = `Dear ${<?php echo json_encode(htmlspecialchars($mock_submission['name'])); ?>},\n\nThank you for contacting us. We have received your message and will respond to your inquiry as soon as possible.\n\nBest regards,\nAdmin Team\nManage Inc.`;
                        replyMessage.value = template;
                        break;
                }

                // Add active state to button
                this.classList.add('active');
                setTimeout(() => {
                    this.classList.remove('active');
                }, 300);
            });
        });
    }

    // Helper function to wrap text with formatting
    function wrapText(textarea, before, after, selectedText, startPos, endPos) {
        const newText = before + selectedText + after;
        textarea.value =
            textarea.value.substring(0, startPos) +
            newText +
            textarea.value.substring(endPos);

        // Set selection to the wrapped text
        textarea.focus();
        textarea.selectionStart = startPos + before.length;
        textarea.selectionEnd = startPos + before.length + selectedText.length;
    }

    // Helper function to convert newlines to <br> tags
    function nl2br(str) {
        return str.replace(/\n/g, '<br>');
    }

    // Form submission
    replyForm.addEventListener('submit', function(e) {
        e.preventDefault();
        alert('This is a preview - reply functionality would work in the actual inbox.');
    });
});
</script>

<?php include 'includes/footer.php'; ?>
