/* ========================================
   MODERN INBOX PAGE STYLES - VERSION 6.0.0
   AGGRESSIVE CACHE BUSTING ENABLED
   ======================================== */

/* DEBUG: Add visible indicator that modern CSS is loaded */
body.inbox-page::before {
    content: "MODERN INBOX CSS LOADED v6.0.0" !important;
    position: fixed !important;
    top: 0 !important;
    right: 0 !important;
    background: #28a745 !important;
    color: white !important;
    padding: 5px 10px !important;
    font-size: 12px !important;
    z-index: 99999 !important;
    border-radius: 0 0 0 5px !important;
}

/* FORCE OVERRIDE ALL EXISTING STYLES */
.admin-content .message-view-layout,
.admin-content .message-view-container,
.admin-content .message-view-sidebar,
.admin-content .message-view-content {
    display: none !important;
}

/* Base Inbox Styles */
.inbox-page {
    background-color: #f8f9fa !important;
}

.inbox-wrapper {
    max-width: 100% !important;
    margin: 0 auto !important;
}

/* Override admin-container max-width for inbox page */
.inbox-page .admin-container,
body.inbox-page .admin-container {
    max-width: none !important;
    width: 100% !important;
    padding: var(--spacing-4) !important;
}

/* Ensure inbox wrapper uses full width */
.inbox-page .inbox-wrapper,
body.inbox-page .inbox-wrapper {
    max-width: none !important;
    width: 100% !important;
    margin: 0 !important;
}

/* ========================================
   MODERN MESSAGE VIEW LAYOUT
   ======================================== */

.modern-message-view {
    background: #ffffff !important;
    border-radius: 12px !important;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08) !important;
    overflow: hidden !important;
    margin: 20px 0 !important;
    display: block !important;
    position: relative !important;
    z-index: 1000 !important;
}

/* Navigation Bar */
.message-nav-bar {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px 30px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.nav-left {
    display: flex;
    align-items: center;
    gap: 20px;
}

.nav-back-btn {
    display: flex;
    align-items: center;
    gap: 8px;
    color: white;
    text-decoration: none;
    padding: 8px 16px;
    border-radius: 8px;
    background: rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
    font-weight: 500;
}

.nav-back-btn:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateX(-2px);
}

.message-nav-info {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.message-nav-title {
    font-size: 1.4rem;
    font-weight: 600;
    margin: 0;
    color: white;
}

.message-nav-meta {
    font-size: 0.9rem;
    color: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    gap: 6px;
}

.nav-right {
    display: flex;
    align-items: center;
    gap: 15px;
}

.message-status-indicator .status-badge {
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 0.85rem;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 6px;
}

.status-badge.status-read {
    background: rgba(40, 167, 69, 0.2);
    color: #28a745;
    border: 1px solid rgba(40, 167, 69, 0.3);
}

.status-badge.status-unread {
    background: rgba(255, 193, 7, 0.2);
    color: #ffc107;
    border: 1px solid rgba(255, 193, 7, 0.3);
}

/* Actions Dropdown */
.message-actions-dropdown {
    position: relative;
}

.actions-dropdown-btn {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: white;
    padding: 8px 12px;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.actions-dropdown-btn:hover {
    background: rgba(255, 255, 255, 0.2);
}

.actions-dropdown-menu {
    position: absolute;
    top: 100%;
    right: 0;
    background: white;
    border-radius: 8px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    min-width: 200px;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all 0.3s ease;
    margin-top: 8px;
}

.actions-dropdown-menu.show {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.dropdown-item {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 12px 16px;
    color: #333;
    text-decoration: none;
    transition: background-color 0.2s ease;
    font-size: 0.9rem;
}

.dropdown-item:hover {
    background: #f8f9fa;
}

.dropdown-item.danger {
    color: #dc3545;
}

.dropdown-item.danger:hover {
    background: #fff5f5;
}

.dropdown-divider {
    height: 1px;
    background: #e9ecef;
    margin: 8px 0;
}

/* ========================================
   MESSAGE CONTENT GRID
   ======================================== */

.message-content-grid {
    display: grid;
    grid-template-columns: 350px 1fr;
    gap: 30px;
    padding: 30px;
    background: #f8f9fa;
}

/* Contact Information Panel */
.contact-info-panel {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
    height: fit-content;
    position: sticky;
    top: 20px;
}

.contact-card {
    padding: 25px;
}

.contact-avatar-section {
    display: flex;
    align-items: center;
    gap: 15px;
    margin-bottom: 25px;
    padding-bottom: 20px;
    border-bottom: 1px solid #e9ecef;
}

.contact-avatar-circle {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 600;
    font-size: 1.2rem;
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.contact-info {
    flex: 1;
}

.contact-name {
    font-size: 1.3rem;
    font-weight: 600;
    color: #2c3e50;
    margin: 0 0 4px 0;
}

.contact-source {
    color: #6c757d;
    font-size: 0.9rem;
    margin: 0;
}

.contact-details-grid {
    display: flex;
    flex-direction: column;
    gap: 15px;
    margin-bottom: 25px;
}

.detail-item {
    display: flex;
    align-items: flex-start;
    gap: 12px;
    padding: 12px;
    background: #f8f9fa;
    border-radius: 8px;
    transition: all 0.2s ease;
}

.detail-item:hover {
    background: #e9ecef;
}

.detail-icon {
    width: 36px;
    height: 36px;
    border-radius: 8px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 0.9rem;
    flex-shrink: 0;
}

.detail-content {
    flex: 1;
    min-width: 0;
}

.detail-content label {
    display: block;
    font-size: 0.8rem;
    font-weight: 600;
    color: #6c757d;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: 4px;
}

.detail-link {
    color: #667eea;
    text-decoration: none;
    font-weight: 500;
    word-break: break-all;
    transition: color 0.2s ease;
}

.detail-link:hover {
    color: #764ba2;
    text-decoration: underline;
}

.detail-text {
    color: #495057;
    font-weight: 500;
}

.contact-quick-actions {
    display: flex;
    gap: 10px;
}

.quick-action-btn {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    padding: 12px 16px;
    border-radius: 8px;
    text-decoration: none;
    font-weight: 500;
    font-size: 0.9rem;
    transition: all 0.3s ease;
}

.quick-action-btn.primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.quick-action-btn.primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.quick-action-btn.secondary {
    background: #f8f9fa;
    color: #495057;
    border: 1px solid #dee2e6;
}

.quick-action-btn.secondary:hover {
    background: #e9ecef;
    transform: translateY(-1px);
}

/* Inbox Header */
.inbox-header {
    background: #fff;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.inbox-stats {
    display: flex;
    gap: 20px;
    margin-top: 15px;
}

.inbox-stat {
    background: #f8f9fa;
    padding: 10px 15px;
    border-radius: 6px;
    text-align: center;
    min-width: 100px;
}

.inbox-stat-number {
    font-size: 24px;
    font-weight: bold;
    color: #333;
}

.inbox-stat-label {
    font-size: 12px;
    color: #666;
    text-transform: uppercase;
}

/* Search Input Group Styling */
.search-input-group {
    display: flex;
    align-items: center;
    gap: 5px;
    flex-wrap: nowrap;
}

.search-input-group input {
    min-width: 200px;
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
    flex: 1;
}

.search-input-group .admin-btn {
    padding: 8px 12px;
    min-width: auto;
    flex-shrink: 0;
}

.search-input-group .admin-btn.secondary {
    background: #6c757d;
    color: #fff;
    border-color: #6c757d;
}

.search-input-group .admin-btn.secondary:hover {
    background: #5a6268;
    border-color: #545b62;
}

/* Fix for admin-table-actions layout */
.admin-table-actions {
    display: flex !important;
    justify-content: space-between !important;
    align-items: center !important;
    flex-wrap: wrap !important;
    gap: 16px !important;
    background: #fff;
    padding: 20px;
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
    margin-bottom: 24px;
}

.admin-table-filters {
    display: flex !important;
    flex-wrap: wrap !important;
    gap: 16px !important;
    flex: 1 !important;
    align-items: center !important;
}

.admin-table-filter {
    display: flex !important;
    align-items: center !important;
    gap: 8px !important;
    white-space: nowrap !important;
}

.admin-table-bulk-actions {
    display: flex !important;
    align-items: center !important;
    gap: 10px !important;
    flex-shrink: 0 !important;
}

.filter-label {
    font-weight: 500;
    color: #333;
    font-size: 14px;
}

/* Message List */
.message-list {
    background: #fff;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.message-item {
    border-bottom: 1px solid #e9ecef;
    padding: 15px 20px;
    transition: background-color 0.2s ease;
    cursor: pointer;
}

.message-item:hover {
    background-color: #f8f9fa;
}

.message-item.unread {
    background-color: #fff3cd;
    border-left: 4px solid #ffc107;
}

.message-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.message-sender {
    font-weight: 600;
    color: #333;
}

.message-email {
    color: #666;
    font-size: 14px;
    margin-left: 10px;
}

.message-date {
    color: #666;
    font-size: 13px;
}

.message-subject {
    font-weight: 500;
    color: #333;
    margin-bottom: 5px;
}

.message-preview {
    color: #666;
    font-size: 14px;
    line-height: 1.4;
    max-height: 40px;
    overflow: hidden;
}

/* Message Actions */
.message-actions {
    display: flex;
    gap: 10px;
    margin-top: 10px;
}

.message-action {
    padding: 4px 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
    background: #fff;
    color: #666;
    text-decoration: none;
    font-size: 12px;
    transition: all 0.2s ease;
}

.message-action:hover {
    background: #f8f9fa;
    color: #333;
}

.message-action.primary {
    background: #007bff;
    color: #fff;
    border-color: #007bff;
}

.message-action.primary:hover {
    background: #0056b3;
    border-color: #0056b3;
}

.message-action.danger {
    background: #dc3545;
    color: #fff;
    border-color: #dc3545;
}

.message-action.danger:hover {
    background: #c82333;
    border-color: #c82333;
}

/* ========================================
   MODERN MESSAGE VIEW LAYOUT
   ======================================== */

/* Message View Layout Container */
.message-view-layout {
    display: flex !important;
    flex-direction: column !important;
    gap: 1.5rem !important;
    max-width: 100% !important;
    margin: 0 auto !important;
    padding: 0 !important;
}

/* Fix for message view page body class */
body.message-view-page {
    background-color: #f8f9fa !important;
}

body.message-view-page .admin-container {
    max-width: none !important;
    width: 100% !important;
    padding: 1.5rem !important;
}

/* Content Header Styling for Message View */
body.message-view-page .admin-content-header {
    background: #ffffff !important;
    border-radius: 12px !important;
    padding: 1.5rem !important;
    margin-bottom: 1.5rem !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05) !important;
    border: 1px solid #e9ecef !important;
    display: flex !important;
    justify-content: space-between !important;
    align-items: center !important;
    flex-wrap: wrap !important;
    gap: 1rem !important;
}

body.message-view-page .admin-content-title-group {
    display: flex !important;
    flex-direction: column !important;
    gap: 0.5rem !important;
}

body.message-view-page .admin-content-title {
    margin: 0 !important;
    font-size: 1.5rem !important;
    font-weight: 600 !important;
    color: #212529 !important;
    display: flex !important;
    align-items: center !important;
    gap: 0.75rem !important;
}

body.message-view-page .admin-content-title i {
    color: #f1ca2f !important;
    font-size: 1.25rem !important;
}

body.message-view-page .admin-content-subtitle {
    margin: 0 !important;
    color: #6c757d !important;
    font-size: 0.875rem !important;
    font-weight: 500 !important;
}

body.message-view-page .admin-btn.secondary {
    background: #6c757d !important;
    color: #ffffff !important;
    border: 1px solid #6c757d !important;
    padding: 0.75rem 1.5rem !important;
    border-radius: 8px !important;
    text-decoration: none !important;
    font-weight: 500 !important;
    transition: all 0.15s ease !important;
    display: inline-flex !important;
    align-items: center !important;
    gap: 0.5rem !important;
}

body.message-view-page .admin-btn.secondary:hover {
    background: #5a6268 !important;
    border-color: #545b62 !important;
    transform: translateY(-1px) !important;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15) !important;
}

/* Message View Header */
.message-view-header {
    background: #ffffff !important;
    border-radius: 12px !important;
    padding: 1.5rem !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05) !important;
    border: 1px solid #e9ecef !important;
}

.message-view-actions {
    display: flex !important;
    justify-content: space-between !important;
    align-items: center !important;
    flex-wrap: wrap !important;
    gap: 1rem !important;
}

.message-action-buttons {
    display: flex !important;
    gap: 0.75rem !important;
    flex-wrap: wrap !important;
}

/* Message View Container */
.message-view-container {
    display: grid !important;
    grid-template-columns: 320px 1fr !important;
    gap: 1.5rem !important;
    align-items: start !important;
}

/* Responsive design for message view */
@media (max-width: 1024px) {
    .message-view-container {
        grid-template-columns: 280px 1fr !important;
        gap: 1rem !important;
    }
}

@media (max-width: 768px) {
    .message-view-container {
        grid-template-columns: 1fr !important;
        gap: 1rem !important;
    }

    .message-view-sidebar {
        position: static !important;
        order: 2 !important;
    }

    .message-view-content {
        order: 1 !important;
    }

    body.message-view-page .admin-content-header {
        flex-direction: column !important;
        align-items: flex-start !important;
        text-align: left !important;
    }

    body.message-view-page .admin-container {
        padding: 1rem !important;
    }
}

/* Message View Sidebar */
.message-view-sidebar {
    position: sticky !important;
    top: 1.5rem !important;
}

.message-contact-card {
    background: #ffffff !important;
    border-radius: 12px !important;
    padding: 1.5rem !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05) !important;
    border: 1px solid #e9ecef !important;
}

.contact-header {
    display: flex !important;
    align-items: center !important;
    gap: 1rem !important;
    margin-bottom: 1.5rem !important;
    padding-bottom: 1.5rem !important;
    border-bottom: 1px solid #f0f0f0 !important;
}

.contact-avatar {
    width: 60px !important;
    height: 60px !important;
    background: linear-gradient(135deg, #f1ca2f, #e6b82a) !important;
    border-radius: 50% !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    color: #ffffff !important;
    font-size: 1.5rem !important;
    font-weight: 600 !important;
    box-shadow: 0 4px 8px rgba(241, 202, 47, 0.3) !important;
    flex-shrink: 0 !important;
}

.contact-name h3 {
    margin: 0 0 0.25rem 0 !important;
    font-size: 1.125rem !important;
    font-weight: 600 !important;
    color: #212529 !important;
    line-height: 1.3 !important;
}

.contact-source {
    font-size: 0.75rem !important;
    color: #6c757d !important;
    background: #f8f9fa !important;
    padding: 0.25rem 0.5rem !important;
    border-radius: 12px !important;
    text-transform: uppercase !important;
    font-weight: 500 !important;
    letter-spacing: 0.5px !important;
}

.contact-details {
    margin-bottom: 1.5rem !important;
}

.contact-detail-item {
    display: flex !important;
    align-items: center !important;
    gap: 0.75rem !important;
    padding: 0.75rem 0 !important;
    border-bottom: 1px solid #f8f9fa !important;
}

.contact-detail-item:last-child {
    border-bottom: none !important;
}

.contact-detail-item i {
    width: 20px !important;
    height: 20px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    color: #6c757d !important;
    font-size: 0.875rem !important;
    flex-shrink: 0 !important;
}

.contact-detail-item a {
    color: #212529 !important;
    text-decoration: none !important;
    font-size: 0.875rem !important;
    transition: color 0.15s ease !important;
}

.contact-detail-item a:hover {
    color: #f1ca2f !important;
}

.contact-detail-item span {
    font-size: 0.875rem !important;
    color: #495057 !important;
}

.status-badge {
    padding: 0.25rem 0.75rem !important;
    border-radius: 12px !important;
    font-size: 0.75rem !important;
    font-weight: 500 !important;
    text-transform: uppercase !important;
    letter-spacing: 0.5px !important;
}

.status-badge.read {
    background: #d4edda !important;
    color: #155724 !important;
}

.status-badge.unread {
    background: #fff3cd !important;
    color: #856404 !important;
}

.contact-actions {
    display: flex !important;
    flex-direction: column !important;
    gap: 0.5rem !important;
}

.contact-actions .admin-btn.full-width {
    width: 100% !important;
    justify-content: center !important;
    padding: 0.75rem 1rem !important;
    font-size: 0.875rem !important;
}

/* Message View Content */
.message-view-content {
    display: flex !important;
    flex-direction: column !important;
    gap: 1.5rem !important;
}

.message-card {
    background: #ffffff !important;
    border-radius: 12px !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05) !important;
    border: 1px solid #e9ecef !important;
    overflow: hidden !important;
}

.message-card-header {
    background: #f8f9fa !important;
    padding: 1.5rem !important;
    border-bottom: 1px solid #e9ecef !important;
    display: flex !important;
    justify-content: space-between !important;
    align-items: center !important;
}

.message-card-header h3 {
    margin: 0 !important;
    font-size: 1.125rem !important;
    font-weight: 600 !important;
    color: #212529 !important;
}

.message-date {
    font-size: 0.875rem !important;
    color: #6c757d !important;
    font-weight: 500 !important;
}

.message-card-body {
    padding: 1.5rem !important;
}

.message-text {
    font-size: 0.9375rem !important;
    line-height: 1.6 !important;
    color: #495057 !important;
    background: #f8f9fa !important;
    padding: 1.5rem !important;
    border-radius: 8px !important;
    border-left: 4px solid #f1ca2f !important;
    white-space: pre-wrap !important;
    word-wrap: break-word !important;
}

/* Reply Card */
.reply-card {
    background: #ffffff !important;
    border-radius: 12px !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05) !important;
    border: 1px solid #e9ecef !important;
    overflow: hidden !important;
}

.reply-card-header {
    background: linear-gradient(135deg, #f1ca2f, #e6b82a) !important;
    padding: 1.5rem !important;
    color: #212529 !important;
}

.reply-card-header h3 {
    margin: 0 !important;
    font-size: 1.125rem !important;
    font-weight: 600 !important;
    display: flex !important;
    align-items: center !important;
    gap: 0.5rem !important;
}

.reply-card-body {
    padding: 1.5rem !important;
}

.reply-toolbar {
    display: flex !important;
    gap: 0.5rem !important;
    margin-bottom: 1rem !important;
    padding: 0.75rem !important;
    background: #f8f9fa !important;
    border-radius: 8px !important;
    border: 1px solid #e9ecef !important;
}

.reply-toolbar-btn {
    padding: 0.5rem !important;
    background: #ffffff !important;
    border: 1px solid #dee2e6 !important;
    border-radius: 6px !important;
    color: #495057 !important;
    cursor: pointer !important;
    transition: all 0.15s ease !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    width: 36px !important;
    height: 36px !important;
}

.reply-toolbar-btn:hover {
    background: #f1ca2f !important;
    color: #212529 !important;
    border-color: #f1ca2f !important;
    transform: translateY(-1px) !important;
}

.reply-toolbar-btn.active {
    background: #f1ca2f !important;
    color: #212529 !important;
    border-color: #f1ca2f !important;
}

.reply-card .form-group {
    margin-bottom: 1.5rem !important;
}

.reply-card .form-group label {
    display: block !important;
    margin-bottom: 0.5rem !important;
    font-weight: 600 !important;
    color: #212529 !important;
    font-size: 0.875rem !important;
}

.reply-card .form-group input,
.reply-card .form-group textarea {
    width: 100% !important;
    padding: 0.75rem !important;
    border: 1px solid #dee2e6 !important;
    border-radius: 8px !important;
    font-size: 0.875rem !important;
    transition: border-color 0.15s ease, box-shadow 0.15s ease !important;
}

.reply-card .form-group input:focus,
.reply-card .form-group textarea:focus {
    outline: none !important;
    border-color: #f1ca2f !important;
    box-shadow: 0 0 0 3px rgba(241, 202, 47, 0.1) !important;
}

.reply-card .form-group textarea {
    resize: vertical !important;
    min-height: 120px !important;
    font-family: inherit !important;
}

.form-actions {
    display: flex !important;
    gap: 1rem !important;
    justify-content: flex-end !important;
    align-items: center !important;
    flex-wrap: wrap !important;
}

/* Reply History Card */
.reply-history-card {
    background: #ffffff !important;
    border-radius: 12px !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05) !important;
    border: 1px solid #e9ecef !important;
    overflow: hidden !important;
}

.reply-history-header {
    background: #f8f9fa !important;
    padding: 1.5rem !important;
    border-bottom: 1px solid #e9ecef !important;
    display: flex !important;
    justify-content: space-between !important;
    align-items: center !important;
}

.reply-history-header h3 {
    margin: 0 !important;
    font-size: 1.125rem !important;
    font-weight: 600 !important;
    color: #212529 !important;
    display: flex !important;
    align-items: center !important;
    gap: 0.5rem !important;
}

.reply-count {
    background: #f1ca2f !important;
    color: #212529 !important;
    padding: 0.25rem 0.75rem !important;
    border-radius: 12px !important;
    font-size: 0.75rem !important;
    font-weight: 600 !important;
}

.reply-history-body {
    padding: 1.5rem !important;
}

.reply-item {
    padding: 1rem !important;
    border: 1px solid #f0f0f0 !important;
    border-radius: 8px !important;
    margin-bottom: 1rem !important;
    background: #fafafa !important;
}

.reply-item:last-child {
    margin-bottom: 0 !important;
}

.reply-item-header {
    display: flex !important;
    justify-content: space-between !important;
    align-items: flex-start !important;
    margin-bottom: 0.75rem !important;
    flex-wrap: wrap !important;
    gap: 0.5rem !important;
}

.reply-user-info {
    display: flex !important;
    flex-direction: column !important;
    gap: 0.25rem !important;
}

.reply-user-name {
    font-weight: 600 !important;
    color: #212529 !important;
    font-size: 0.875rem !important;
}

.reply-date {
    font-size: 0.75rem !important;
    color: #6c757d !important;
}

.reply-subject {
    font-weight: 500 !important;
    color: #495057 !important;
    font-size: 0.875rem !important;
    text-align: right !important;
}

.reply-item-content {
    font-size: 0.875rem !important;
    line-height: 1.5 !important;
    color: #495057 !important;
    white-space: pre-wrap !important;
    word-wrap: break-word !important;
}

/* Error View */
.message-error-view {
    text-align: center !important;
    padding: 3rem 2rem !important;
    background: #ffffff !important;
    border-radius: 12px !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05) !important;
    border: 1px solid #e9ecef !important;
}

.error-icon {
    font-size: 4rem !important;
    color: #dc3545 !important;
    margin-bottom: 1.5rem !important;
}

.message-error-view h2 {
    margin: 0 0 1rem 0 !important;
    font-size: 1.5rem !important;
    font-weight: 600 !important;
    color: #212529 !important;
}

.message-error-view p {
    margin: 0 0 2rem 0 !important;
    color: #6c757d !important;
    font-size: 1rem !important;
}

.error-actions {
    display: flex !important;
    gap: 1rem !important;
    justify-content: center !important;
    flex-wrap: wrap !important;
}

/* Bulk Actions */
.bulk-actions {
    background: #fff;
    padding: 15px 20px;
    border-bottom: 1px solid #e9ecef;
    display: flex;
    align-items: center;
    gap: 15px;
}

.bulk-select {
    display: flex;
    align-items: center;
    gap: 10px;
}

/* Pagination */
.pagination-wrapper {
    background: #fff;
    padding: 15px 20px;
    border-top: 1px solid #e9ecef;
    display: flex;
    justify-content: center;
}

/* Empty State */
.empty-inbox {
    text-align: center;
    padding: 60px 20px;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.empty-inbox-icon {
    font-size: 48px;
    color: #ddd;
    margin-bottom: 20px;
}

.empty-inbox h3 {
    color: #333;
    margin-bottom: 10px;
}

.empty-inbox p {
    color: #666;
    font-size: 14px;
}

/* ========================================
   MODAL STYLES
   ======================================== */

.admin-modal {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    width: 100% !important;
    height: 100% !important;
    background: rgba(0, 0, 0, 0.5) !important;
    display: none !important;
    z-index: 10000 !important;
    align-items: center !important;
    justify-content: center !important;
    padding: 1rem !important;
}

.admin-modal-content {
    background: #ffffff !important;
    border-radius: 12px !important;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15) !important;
    max-width: 600px !important;
    width: 100% !important;
    max-height: 90vh !important;
    overflow: hidden !important;
    display: flex !important;
    flex-direction: column !important;
}

.admin-modal-header {
    padding: 1.5rem !important;
    border-bottom: 1px solid #e9ecef !important;
    display: flex !important;
    justify-content: space-between !important;
    align-items: center !important;
    background: #f8f9fa !important;
}

.admin-modal-header h3 {
    margin: 0 !important;
    font-size: 1.25rem !important;
    font-weight: 600 !important;
    color: #212529 !important;
    display: flex !important;
    align-items: center !important;
    gap: 0.5rem !important;
}

.admin-modal-close {
    background: none !important;
    border: none !important;
    font-size: 1.5rem !important;
    color: #6c757d !important;
    cursor: pointer !important;
    padding: 0.5rem !important;
    border-radius: 6px !important;
    transition: all 0.15s ease !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    width: 36px !important;
    height: 36px !important;
}

.admin-modal-close:hover {
    background: #f8f9fa !important;
    color: #dc3545 !important;
}

.admin-modal-body {
    padding: 1.5rem !important;
    flex: 1 !important;
    overflow-y: auto !important;
}

.admin-modal-footer {
    padding: 1.5rem !important;
    border-top: 1px solid #e9ecef !important;
    display: flex !important;
    gap: 1rem !important;
    justify-content: flex-end !important;
    background: #f8f9fa !important;
}

.preview-subject {
    margin-bottom: 1rem !important;
    padding: 1rem !important;
    background: #f8f9fa !important;
    border-radius: 8px !important;
    border-left: 4px solid #f1ca2f !important;
}

.preview-subject strong {
    color: #212529 !important;
    font-weight: 600 !important;
}

.preview-message {
    background: #ffffff !important;
    border: 1px solid #e9ecef !important;
    border-radius: 8px !important;
    padding: 1.5rem !important;
    min-height: 120px !important;
}

#preview-message-content {
    line-height: 1.6 !important;
    color: #495057 !important;
    white-space: pre-wrap !important;
    word-wrap: break-word !important;
}

/* ========================================
   RESPONSIVE DESIGN
   ======================================== */

@media (max-width: 1024px) {
    /* Message view container becomes single column on tablets */
    .message-view-container {
        grid-template-columns: 1fr !important;
        gap: 1rem !important;
    }

    .message-view-sidebar {
        position: static !important;
        order: -1 !important;
    }

    .message-contact-card {
        padding: 1rem !important;
    }

    .contact-header {
        flex-direction: column !important;
        text-align: center !important;
        gap: 0.75rem !important;
    }

    .contact-details {
        margin-bottom: 1rem !important;
    }
}

@media (max-width: 768px) {
    /* Search input group responsive */
    .search-input-group {
        flex-wrap: wrap;
        gap: 8px;
    }

    .search-input-group input {
        min-width: auto;
        flex: 1;
        width: 100%;
    }

    /* Message layout responsive */
    .message-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 5px;
    }

    .message-actions {
        flex-wrap: wrap;
    }

    /* Message view responsive */
    .message-view-header {
        padding: 1rem !important;
    }

    .message-view-actions {
        flex-direction: column !important;
        align-items: stretch !important;
        gap: 0.75rem !important;
    }

    .message-action-buttons {
        justify-content: center !important;
        flex-wrap: wrap !important;
    }

    .message-card,
    .reply-card,
    .reply-history-card {
        margin: 0 -0.5rem !important;
        border-radius: 8px !important;
    }

    .message-card-header,
    .reply-card-header,
    .reply-history-header {
        padding: 1rem !important;
    }

    .message-card-body,
    .reply-card-body,
    .reply-history-body {
        padding: 1rem !important;
    }

    .message-text {
        padding: 1rem !important;
        font-size: 0.875rem !important;
    }

    .contact-header {
        padding-bottom: 1rem !important;
        margin-bottom: 1rem !important;
    }

    .contact-avatar {
        width: 50px !important;
        height: 50px !important;
        font-size: 1.25rem !important;
    }

    .contact-name h3 {
        font-size: 1rem !important;
    }

    .reply-toolbar {
        flex-wrap: wrap !important;
        gap: 0.25rem !important;
        padding: 0.5rem !important;
    }

    .reply-toolbar-btn {
        width: 32px !important;
        height: 32px !important;
    }

    .form-actions {
        flex-direction: column !important;
        align-items: stretch !important;
    }

    .form-actions .admin-btn {
        width: 100% !important;
        justify-content: center !important;
    }

    /* Modal responsive */
    .admin-modal {
        padding: 0.5rem !important;
    }

    .admin-modal-content {
        max-height: 95vh !important;
        border-radius: 8px !important;
    }

    .admin-modal-header,
    .admin-modal-body,
    .admin-modal-footer {
        padding: 1rem !important;
    }

    .admin-modal-footer {
        flex-direction: column !important;
    }

    .admin-modal-footer .admin-btn {
        width: 100% !important;
        justify-content: center !important;
    }
}

@media (max-width: 480px) {
    .search-input-group input {
        font-size: 16px; /* Prevent zoom on iOS */
    }
}

/* Dark Mode Support */
body.dark-mode .inbox-page {
    background-color: #1a1a1a;
}

body.dark-mode .inbox-header,
body.dark-mode .message-list,
body.dark-mode .message-detail,
body.dark-mode .empty-inbox {
    background-color: #2d2d2d;
    color: #e0e0e0;
}

body.dark-mode .search-input-group input {
    background-color: #3a3a3a;
    border-color: #404040;
    color: #e0e0e0;
}

body.dark-mode .search-input-group .admin-btn.secondary {
    background-color: #6c757d;
    border-color: #6c757d;
}

body.dark-mode .search-input-group .admin-btn.secondary:hover {
    background-color: #5a6268;
    border-color: #545b62;
}

body.dark-mode .message-item {
    border-bottom-color: #404040;
}

body.dark-mode .message-item:hover {
    background-color: #3a3a3a;
}

body.dark-mode .message-item.unread {
    background-color: #3d3520;
    border-left-color: #ffc107;
}

body.dark-mode .message-content {
    background-color: #3a3a3a;
    border-color: #404040;
}

body.dark-mode .message-detail-header {
    background-color: #3a3a3a;
    border-bottom-color: #404040;
}
