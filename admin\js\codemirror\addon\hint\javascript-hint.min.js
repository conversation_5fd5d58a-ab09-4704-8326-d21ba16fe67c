!function(t){"object"==typeof exports&&"object"==typeof module?t(require("../../lib/codemirror")):"function"==typeof define&&define.amd?define(["../../lib/codemirror"],t):t(CodeMirror)}(function(f){var c=f.Pos;function g(t,e){for(var r=0,n=t.length;r<n;++r)e(t[r])}function r(t,e,r,n){var i=t.getCursor(),o=r(t,i);if(!/\b(?:string|comment)\b/.test(o.type)){var s=f.innerMode(t.getMode(),o.state);if("json"!==s.mode.helperType){o.state=s.state,/^[\w$_]*$/.test(o.string)?o.end>i.ch&&(o.end=i.ch,o.string=o.string.slice(0,i.ch-o.start)):o={start:i.ch,end:i.ch,string:"",state:o.state,type:"."==o.string?"property":null};for(var a=o;"property"==a.type;){if("."!=(a=r(t,c(i.line,a.start))).string)return;var a=r(t,c(i.line,a.start)),l=l||[];l.push(a)}return{list:function(t,e,r,n){var i=[],o=t.string,s=n&&n.globalScope||window;function a(t){0!=t.lastIndexOf(o,0)||function(t,e){if(Array.prototype.indexOf)return-1!=t.indexOf(e);for(var r=t.length;r--;)if(t[r]===e)return 1}(i,t)||i.push(t)}function l(t){"string"==typeof t?g(y,a):t instanceof Array?g(h,a):t instanceof Function&&g(v,a);var e=a;if(Object.getOwnPropertyNames&&Object.getPrototypeOf)for(var r=t;r;r=Object.getPrototypeOf(r))Object.getOwnPropertyNames(r).forEach(e);else for(var n in t)e(n)}if(e&&e.length){var f,c=e.pop();for(c.type&&0===c.type.indexOf("variable")?(n&&n.additionalContext&&(f=n.additionalContext[c.string]),n&&!1===n.useGlobalScope||(f=f||s[c.string])):"string"==c.type?f="":"atom"==c.type?f=1:"function"==c.type&&(null==s.jQuery||"$"!=c.string&&"jQuery"!=c.string||"function"!=typeof s.jQuery?null!=s._&&"_"==c.string&&"function"==typeof s._&&(f=s._()):f=s.jQuery());null!=f&&e.length;)f=f[e.pop().string];null!=f&&l(f)}else{for(var p=t.state.localVars;p;p=p.next)a(p.name);for(var u=t.state.context;u;u=u.prev)for(p=u.vars;p;p=p.next)a(p.name);for(p=t.state.globalVars;p;p=p.next)a(p.name);if(n&&null!=n.additionalContext)for(var d in n.additionalContext)a(d);n&&!1===n.useGlobalScope||l(s),g(r,a)}return i}(o,l,e,n),from:c(i.line,o.start),to:c(i.line,o.end)}}}}function n(t,e){t=t.getTokenAt(e);return e.ch==t.start+1&&"."==t.string.charAt(0)?(t.end=t.start,t.string=".",t.type="property"):/^\.[\w$_]*$/.test(t.string)&&(t.type="property",t.start++,t.string=t.string.replace(/\./,"")),t}f.registerHelper("hint","javascript",function(t,e){return r(t,i,function(t,e){return t.getTokenAt(e)},e)}),f.registerHelper("hint","coffeescript",function(t,e){return r(t,o,n,e)});var y="charAt charCodeAt indexOf lastIndexOf substring substr slice trim trimLeft trimRight toUpperCase toLowerCase split concat match replace search".split(" "),h="length concat join splice push pop shift unshift slice reverse sort indexOf lastIndexOf every some filter forEach map reduce reduceRight ".split(" "),v="prototype apply call bind".split(" "),i="break case catch class const continue debugger default delete do else export extends false finally for function if in import instanceof new null return super switch this throw true try typeof var void while with yield".split(" "),o="and break catch class continue delete do else extends false finally for if in instanceof isnt new no not null of off on or return switch then throw true try typeof until void while with yes".split(" ")});