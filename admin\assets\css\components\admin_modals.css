/**
 * Admin Modals CSS
 * 
 * This file contains styles for modal dialogs in the admin panel.
 */

/* Modal Backdrop */
.modal-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: var(--z-index-modal-backdrop);
  display: none;
  opacity: 0;
  transition: opacity var(--transition-normal) ease;
}

.modal-backdrop.show {
  display: block;
  opacity: 1;
}

/* Modal Container */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: var(--z-index-modal);
  display: none;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-4);
  overflow-x: hidden;
  overflow-y: auto;
  opacity: 0;
  transition: opacity var(--transition-normal) ease;
}

.modal.show {
  display: flex;
  opacity: 1;
}

/* Modal Dialog */
.modal-dialog {
  position: relative;
  width: 100%;
  max-width: 500px;
  margin: auto;
  background-color: var(--white);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-xl);
  transform: translateY(-20px);
  transition: transform var(--transition-normal) ease;
  overflow: hidden;
}

.modal.show .modal-dialog {
  transform: translateY(0);
}

/* Modal Sizes */
.modal-sm .modal-dialog {
  max-width: 300px;
}

.modal-lg .modal-dialog {
  max-width: 800px;
}

.modal-xl .modal-dialog {
  max-width: 1140px;
}

/* Modal Fullscreen */
.modal-fullscreen .modal-dialog {
  max-width: none;
  width: 100%;
  height: 100%;
  margin: 0;
  border-radius: 0;
}

/* Modal Header */
.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-4);
  border-bottom: 1px solid var(--border-color);
}

.modal-title {
  margin: 0;
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--text-dark);
}

.modal-close {
  background-color: transparent;
  border: none;
  font-size: var(--font-size-xl);
  line-height: 1;
  color: var(--text-muted);
  cursor: pointer;
  padding: var(--spacing-1);
  margin: calc(-1 * var(--spacing-1));
  border-radius: var(--radius-sm);
  transition: all var(--transition-fast) ease;
}

.modal-close:hover {
  color: var(--text-dark);
  background-color: var(--gray-100);
}

/* Modal Body */
.modal-body {
  padding: var(--spacing-4);
  max-height: calc(100vh - 200px);
  overflow-y: auto;
}

.modal-fullscreen .modal-body {
  max-height: calc(100vh - 120px);
}

/* Modal Footer */
.modal-footer {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: var(--spacing-2);
  padding: var(--spacing-4);
  border-top: 1px solid var(--border-color);
}

/* Modal with Scrollable Content */
.modal-scrollable .modal-body {
  max-height: 400px;
  overflow-y: auto;
}

/* Modal with Tabs */
.modal-tabs {
  display: flex;
  border-bottom: 1px solid var(--border-color);
  margin-bottom: var(--spacing-4);
}

.modal-tab {
  padding: var(--spacing-3) var(--spacing-4);
  font-size: var(--font-size-sm);
  color: var(--text-light);
  border-bottom: 2px solid transparent;
  cursor: pointer;
  transition: all var(--transition-fast) ease;
}

.modal-tab:hover {
  color: var(--text-dark);
}

.modal-tab.active {
  color: var(--primary-color);
  border-bottom-color: var(--primary-color);
  font-weight: var(--font-weight-medium);
}

/* Modal with Form */
.modal-form .form-group:last-child {
  margin-bottom: 0;
}

/* Modal with Alert */
.modal-alert {
  margin-bottom: var(--spacing-4);
}

/* Modal with Divider */
.modal-divider {
  margin: var(--spacing-4) 0;
  border-top: 1px solid var(--border-color);
}

/* Modal with Icon */
.modal-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 64px;
  height: 64px;
  margin: 0 auto var(--spacing-4);
  border-radius: 50%;
  font-size: var(--font-size-2xl);
}

.modal-icon-primary {
  background-color: var(--primary-very-light);
  color: var(--primary-color);
}

.modal-icon-success {
  background-color: rgba(16, 185, 129, 0.1);
  color: var(--success-color);
}

.modal-icon-danger {
  background-color: rgba(239, 68, 68, 0.1);
  color: var(--danger-color);
}

.modal-icon-warning {
  background-color: rgba(245, 158, 11, 0.1);
  color: var(--warning-color);
}

.modal-icon-info {
  background-color: rgba(59, 130, 246, 0.1);
  color: var(--info-color);
}

/* Modal Centered Text */
.modal-text-center {
  text-align: center;
}

/* Modal with Image */
.modal-image {
  width: 100%;
  height: auto;
  margin-bottom: var(--spacing-4);
  border-radius: var(--radius-md);
}

/* Modal Slide Animations */
.modal-slide-up .modal-dialog {
  transform: translateY(100px);
}

.modal-slide-up.show .modal-dialog {
  transform: translateY(0);
}

.modal-slide-down .modal-dialog {
  transform: translateY(-100px);
}

.modal-slide-down.show .modal-dialog {
  transform: translateY(0);
}

.modal-slide-left .modal-dialog {
  transform: translateX(100px);
}

.modal-slide-left.show .modal-dialog {
  transform: translateX(0);
}

.modal-slide-right .modal-dialog {
  transform: translateX(-100px);
}

.modal-slide-right.show .modal-dialog {
  transform: translateX(0);
}

/* Modal Zoom Animations */
.modal-zoom .modal-dialog {
  transform: scale(0.9);
}

.modal-zoom.show .modal-dialog {
  transform: scale(1);
}

/* Modal Side Panel */
.modal-side {
  align-items: stretch;
  justify-content: flex-end;
}

.modal-side .modal-dialog {
  height: 100%;
  max-width: 400px;
  margin: 0;
  border-radius: 0;
  transform: translateX(100%);
}

.modal-side.show .modal-dialog {
  transform: translateX(0);
}

.modal-side-left {
  justify-content: flex-start;
}

.modal-side-left .modal-dialog {
  transform: translateX(-100%);
}

.modal-side-left.show .modal-dialog {
  transform: translateX(0);
}

/* Responsive Styles */
@media (max-width: 576px) {
  .modal-dialog {
    max-width: 100%;
    margin: var(--spacing-2);
  }
  
  .modal-lg .modal-dialog,
  .modal-xl .modal-dialog {
    max-width: 100%;
  }
  
  .modal-side .modal-dialog {
    max-width: 80%;
  }
  
  .modal-footer {
    flex-direction: column;
    align-items: stretch;
  }
  
  .modal-footer .btn {
    margin-left: 0;
    margin-top: var(--spacing-2);
  }
}
