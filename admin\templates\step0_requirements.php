<style>
/* Help link styling */
.help-link {
    text-align: center;
    margin: 15px 0;
}

.help-link a {
    display: inline-flex;
    align-items: center;
    background-color: #f8f9fa;
    color: #2c3e50;
    padding: 8px 15px;
    border-radius: 4px;
    text-decoration: none;
    font-weight: 500;
    border: 1px solid #dee2e6;
    transition: all 0.3s ease;
}

.help-link a:hover {
    background-color: #e9ecef;
    color: #f1ca2f;
    border-color: #f1ca2f;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}

.help-link i {
    margin-right: 8px;
    color: #f1ca2f;
}
</style>

<h2>System Requirements Check</h2>
<p>Before proceeding with the installation, let's check if your system meets the requirements:</p>
<p class="help-link"><a href="../install-help.html" target="_blank"><i class="fas fa-question-circle"></i> Need help? View the Installation Guide</a></p>

<?php $requirements = check_requirements(); ?>

<div class="requirements-check">
    <table class="requirements-table">
        <thead>
            <tr>
                <th>Requirement</th>
                <th>Required</th>
                <th>Current</th>
                <th>Status</th>
            </tr>
        </thead>
        <tbody>
            <?php
            $requirements_met = true;
            foreach ($requirements as $req):
            ?>
                <tr class="<?php echo $req['status'] ? 'requirement-met' : ($req['critical'] ? 'requirement-critical' : 'requirement-warning'); ?>">
                    <td><?php echo $req['name']; ?></td>
                    <td><?php echo $req['required']; ?></td>
                    <td><?php echo $req['current']; ?></td>
                    <td>
                        <?php if ($req['status']): ?>
                            <i class="fas fa-check-circle" style="color: #28a745;"></i>
                        <?php else: ?>
                            <i class="fas fa-times-circle" style="color: <?php echo $req['critical'] ? '#dc3545' : '#ffc107'; ?>;"></i>
                        <?php endif; ?>
                    </td>
                </tr>
                <?php if (!$req['status'] && $req['critical']): $requirements_met = false; endif; ?>
            <?php endforeach; ?>
        </tbody>
    </table>
</div>

<div class="form-actions">
    <?php if ($requirements_met): ?>
        <a href="javascript:void(0)" onclick="updateStepIndicators(1); loadStepContent(1);" class="btn">Continue to Installation</a>
    <?php else: ?>
        <div class="alert alert-danger">
            <strong>Critical requirements not met!</strong> Please fix the issues above before continuing.
        </div>
        <button type="button" onclick="window.location.reload();" class="btn-secondary">Refresh</button>
    <?php endif; ?>
</div>
