<h2>Database Setup</h2>
<p>Enter your database connection details below:</p>

<div style="position: relative; margin-bottom: 20px; text-align: left;">
    <span class="connection-info-icon" style="display: inline-block; position: relative; cursor: help;">
        <i class="fas fa-check-circle" style="font-size: 24px; color: #4CAF50;"></i>
        <span style="margin-left: 5px; font-size: 16px; font-weight: bold; color: #2E7D32;">Connection Test Successful!</span>
        <span style="margin-left: 5px; font-size: 14px; color: #666;">Click for connection details</span>
        <div class="connection-tooltip" style="visibility: hidden; width: 450px; background-color: #fff; color: #333; text-align: left; border-radius: 6px; padding: 15px; position: absolute; z-index: 1; top: 30px; left: 0; opacity: 0; transition: opacity 0.3s; box-shadow: 0 2px 8px rgba(0,0,0,0.15); border: 1px solid #ddd; max-height: 80vh; overflow-y: auto;">
            <!-- Arrow pointing to the help icon -->
            <div style="position: absolute; top: -10px; left: 10px; width: 0; height: 0; border-left: 10px solid transparent; border-right: 10px solid transparent; border-bottom: 10px solid #fff; filter: drop-shadow(0 -2px 2px rgba(0,0,0,0.1));"></div>
            <h4 style="margin-top: 0; color: #2E7D32;"><i class="fas fa-check-circle"></i> Connection Test Successful!</h4>
            <p>Our diagnostic tool has detected that your database server is accessible using multiple methods.</p>
            <p><strong>Recommended connection settings:</strong></p>
            <ul style="margin-top: 5px; padding-left: 20px;">
                <li>Use <strong>127.0.0.1</strong> as the database host (already set as default)</li>
                <li>Default port (3306) works correctly</li>
                <li>Make sure to use the exact database name and username from your hosting control panel</li>
                <li>Verify that the database user has been properly assigned to the database with ALL PRIVILEGES</li>
            </ul>
            <p style="margin-top: 10px;">
                <a href="db-test.php" target="_blank" style="display: inline-block; padding: 5px 10px; background-color: #f1ca2f; color: #333; text-decoration: none; border-radius: 3px; font-weight: bold;">
                    <i class="fas fa-database"></i> Run Advanced Connection Test
                </a>
                <span style="margin-left: 5px; font-size: 0.9em; color: #555;">If you're still having issues, use this tool to diagnose connection problems.</span>
            </p>
        </div>
    </span>
</div>

<?php
// Success messages are still displayed at the template level
if (isset($success) && !empty($success)):
?>
<div class="success-message" style="padding: 15px; margin-bottom: 20px; border-radius: 4px; background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb;">
    <?php echo $success; ?>
</div>
<?php endif; ?>

<div style="position: relative; margin-bottom: 20px; text-align: right;">
    <span class="help-icon" style="display: inline-block; position: relative; cursor: help;">
        <i class="fas fa-question-circle" style="font-size: 24px; color: #f1ca2f;"></i>
        <span style="margin-left: 5px; font-size: 14px; color: #666;">Hover for important information</span>
        <div class="tooltip" style="visibility: hidden; width: 450px; background-color: #fff; color: #333; text-align: left; border-radius: 6px; padding: 15px; position: absolute; z-index: 1; bottom: 125%; right: 0; opacity: 0; transition: opacity 0.3s; box-shadow: 0 2px 8px rgba(0,0,0,0.15); border: 1px solid #ddd; max-height: 80vh; overflow-y: auto;">
            <!-- Arrow pointing to the help icon -->
            <div style="position: absolute; bottom: -10px; right: 10px; width: 0; height: 0; border-left: 10px solid transparent; border-right: 10px solid transparent; border-top: 10px solid #fff; filter: drop-shadow(0 2px 2px rgba(0,0,0,0.1));"></div>
            <h4 style="margin-top: 0; color: #333;"><i class="fas fa-exclamation-triangle" style="color: #f1ca2f;"></i> Important Notice</h4>
            <p><strong>This installer requires an existing database.</strong></p>
            <p>Please follow these steps:</p>
            <ol style="margin-top: 10px; margin-bottom: 5px;">
                <li>Log in to your hosting control panel (cPanel, Plesk, Hpanel, etc.)</li>
                <li>Navigate to the MySQL/Database section</li>
                <li>Create a new database with the name you want to use</li>
                <li>Create a database user or use an existing one</li>
                <li>Assign the user to the database with ALL PRIVILEGES</li>
                <li>Return here and enter the database credentials you created</li>
            </ol>
            <p style="margin-top: 10px;"><strong>ProxySQL Users:</strong> If your hosting uses ProxySQL, make sure to:</p>
            <ol style="margin-top: 5px; margin-bottom: 5px;">
                <li>Use the exact database name and username as provided by your hosting provider</li>
                <li>Double-check that the user has been properly assigned to the database</li>
                <li>Try using '127.0.0.1' instead of 'localhost' if you encounter connection issues</li>
            </ol>
        </div>
    </span>
</div>

<form method="post" action="" id="db-setup-form">
    <input type="hidden" name="csrf_token" value="<?php echo $_SESSION['csrf_token']; ?>">

    <div class="form-group">
        <label for="db_host">Database Host</label>
        <input type="text" id="db_host" name="db_host" value="<?php echo isset($_POST['db_host']) ? htmlspecialchars($_POST['db_host']) : '127.0.0.1'; ?>" required>
        <div class="form-help">
            <span style="display: block; margin-bottom: 5px;">
                <span style="color: #2E7D32;"><i class="fas fa-check-circle"></i> Both '127.0.0.1' and 'localhost' work in your environment.</span>
                We recommend using '127.0.0.1' for better compatibility with ProxySQL.
            </span>
            <div style="display: flex; margin-top: 5px;">
                <button type="button" class="host-option" onclick="document.getElementById('db_host').value='127.0.0.1'" style="flex: 1; padding: 5px; margin-right: 5px; background-color: #4CAF50; color: white; border: none; border-radius: 3px; cursor: pointer;">Use 127.0.0.1 (Recommended)</button>
                <button type="button" class="host-option" onclick="document.getElementById('db_host').value='localhost'" style="flex: 1; padding: 5px; background-color: #f5f5f5; border: 1px solid #ddd; border-radius: 3px; cursor: pointer;">Use localhost</button>
            </div>
        </div>
    </div>

    <!-- Always use existing database -->
    <input type="hidden" name="db_option" value="existing">

    <div class="form-group">
        <label for="db_name">Database Name</label>
        <input type="text" id="db_name" name="db_name" required pattern="[a-zA-Z0-9_\-]+" title="Only letters, numbers, underscores and hyphens are allowed" value="<?php echo isset($_POST['db_name']) ? htmlspecialchars($_POST['db_name']) : ''; ?>">
        <div class="form-help">Only letters, numbers, underscores and hyphens are allowed</div>
    </div>

    <div class="form-group">
        <label for="db_user">Database Username</label>
        <input type="text" id="db_user" name="db_user" required pattern="[a-zA-Z0-9_\-]+" title="Only letters, numbers, underscores and hyphens are allowed" value="<?php echo isset($_POST['db_user']) ? htmlspecialchars($_POST['db_user']) : ''; ?>">
        <div class="form-help">Only letters, numbers, underscores and hyphens are allowed</div>
    </div>

    <div class="form-group">
        <label for="db_pass">Database Password</label>
        <div class="password-container" style="position: relative;">
            <input type="password" id="db_pass" name="db_pass" value="<?php echo isset($_POST['db_pass']) ? htmlspecialchars($_POST['db_pass']) : ''; ?>">
            <button type="button" class="password-toggle" onclick="togglePasswordVisibility('db_pass')" style="position: absolute; right: 40px; top: 50%; transform: translateY(-50%); background: none; border: none; cursor: pointer; padding: 5px;">
                <i class="fas fa-eye"></i>
            </button>
            <button type="button" class="password-generate" onclick="generatePassword('db_pass')" style="position: absolute; right: 10px; top: 50%; transform: translateY(-50%); background: none; border: none; cursor: pointer; padding: 5px;">
                <i class="fas fa-key"></i>
            </button>
        </div>
        <div class="form-help">May be empty for some database configurations</div>
    </div>

    <div class="security-note" style="text-align: right; margin-top: 15px;">
        <span class="security-help-icon" style="display: inline-block; position: relative; cursor: help;">
            <i class="fas fa-shield-alt" style="font-size: 20px; color: #4CAF50;"></i>
            <span style="margin-left: 5px; font-size: 14px; color: #666;">Security information</span>
            <div class="security-tooltip" style="visibility: hidden; width: 350px; background-color: #fff; color: #333; text-align: left; border-radius: 6px; padding: 15px; position: absolute; z-index: 1; bottom: 125%; right: 0; opacity: 0; transition: opacity 0.3s; box-shadow: 0 2px 8px rgba(0,0,0,0.15); border: 1px solid #ddd;">
                <!-- Arrow pointing to the help icon -->
                <div style="position: absolute; bottom: -10px; right: 10px; width: 0; height: 0; border-left: 10px solid transparent; border-right: 10px solid transparent; border-top: 10px solid #fff; filter: drop-shadow(0 2px 2px rgba(0,0,0,0.1));"></div>
                <h4 style="margin-top: 0; color: #333;"><i class="fas fa-shield-alt" style="color: #4CAF50;"></i> Security Note</h4>
                <p>The database credentials will be stored securely in your configuration file.</p>
                <p>Best practices for database security:</p>
                <ul>
                    <li>Create a dedicated database user for this application</li>
                    <li>Grant only the necessary permissions to this user</li>
                    <li>Use a strong, unique password</li>
                    <li>Do not use the root user for web applications</li>
                </ul>
            </div>
        </span>
    </div>

    <div class="form-actions">
        <button type="submit" name="setup_db" id="setup-database-btn" class="btn-primary">Next</button>
    </div>
</form>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Form elements
    const dbForm = document.getElementById('db-setup-form');
    const dbHostInput = document.getElementById('db_host');
    const dbNameInput = document.getElementById('db_name');
    const dbUserInput = document.getElementById('db_user');

    // Function to handle tooltip functionality
    function setupTooltip(iconSelector, tooltipSelector) {
        const icon = document.querySelector(iconSelector);
        const tooltip = document.querySelector(tooltipSelector);

        if (icon && tooltip) {
            icon.addEventListener('mouseenter', function() {
                // Hide all other tooltips first
                document.querySelectorAll('.tooltip, .security-tooltip, .connection-tooltip').forEach(t => {
                    t.style.visibility = 'hidden';
                    t.style.opacity = '0';
                });

                // Show this tooltip
                tooltip.style.visibility = 'visible';
                tooltip.style.opacity = '1';
            });

            icon.addEventListener('mouseleave', function() {
                tooltip.style.visibility = 'hidden';
                tooltip.style.opacity = '0';
            });

            // Also handle click for mobile devices
            icon.addEventListener('click', function(e) {
                e.preventDefault();

                // Hide all other tooltips first
                document.querySelectorAll('.tooltip, .security-tooltip, .connection-tooltip').forEach(t => {
                    if (t !== tooltip) {
                        t.style.visibility = 'hidden';
                        t.style.opacity = '0';
                    }
                });

                // Toggle this tooltip
                if (tooltip.style.visibility === 'visible') {
                    tooltip.style.visibility = 'hidden';
                    tooltip.style.opacity = '0';
                } else {
                    tooltip.style.visibility = 'visible';
                    tooltip.style.opacity = '1';
                }
            });
        }
    }

    // Setup tooltips
    setupTooltip('.help-icon', '.tooltip');
    setupTooltip('.security-help-icon', '.security-tooltip');
    setupTooltip('.connection-info-icon', '.connection-tooltip');

    // Close tooltips when clicking outside
    document.addEventListener('click', function(e) {
        if (!e.target.closest('.help-icon') && !e.target.closest('.security-help-icon') && !e.target.closest('.connection-info-icon')) {
            document.querySelectorAll('.tooltip, .security-tooltip, .connection-tooltip').forEach(tooltip => {
                tooltip.style.visibility = 'hidden';
                tooltip.style.opacity = '0';
            });
        }
    });

    // ProxySQL detection and automatic host suggestion
    function checkForProxySQLPattern() {
        const dbName = dbNameInput.value;
        const dbUser = dbUserInput.value;

        // If both database name and username contain underscores, it's likely a ProxySQL environment
        if (dbName.includes('_') && dbUser.includes('_') && dbHostInput.value === 'localhost') {
            // Show a suggestion to use 127.0.0.1 instead of localhost
            const proxyNotice = document.createElement('div');
            proxyNotice.id = 'proxy-notice';
            proxyNotice.style.padding = '10px';
            proxyNotice.style.marginTop = '5px';
            proxyNotice.style.backgroundColor = '#fff3cd';
            proxyNotice.style.border = '1px solid #ffeeba';
            proxyNotice.style.borderRadius = '4px';
            proxyNotice.style.color = '#856404';
            proxyNotice.innerHTML = '<strong>ProxySQL Environment Detected:</strong> For better compatibility, we recommend using <a href="#" id="use-ip">127.0.0.1</a> instead of localhost.';

            // Only add if not already present
            if (!document.getElementById('proxy-notice')) {
                dbHostInput.parentNode.appendChild(proxyNotice);

                // Add click handler for the suggestion link
                document.getElementById('use-ip').addEventListener('click', function(e) {
                    e.preventDefault();
                    dbHostInput.value = '127.0.0.1';
                    proxyNotice.remove();
                });
            }
        } else {
            // Remove the notice if it exists and conditions no longer apply
            const existingNotice = document.getElementById('proxy-notice');
            if (existingNotice) {
                existingNotice.remove();
            }
        }
    }

    // Add event listeners
    dbNameInput.addEventListener('input', checkForProxySQLPattern);
    dbUserInput.addEventListener('input', checkForProxySQLPattern);
    dbHostInput.addEventListener('input', checkForProxySQLPattern);

    // Check on page load in case fields are pre-filled
    checkForProxySQLPattern();
});
</script>
