<div class="completed-message">
    <i class="fas fa-check-circle"></i>
    <h2>Installation Complete!</h2>
    <p>Your admin panel has been successfully set up with the following features:</p>
    <ul style="text-align: left; max-width: 500px; margin: 20px auto; line-height: 1.8;">
        <li><i class="fas fa-check text-success"></i> <strong>Content Management:</strong> News, categories, and contact form</li>
        <li><i class="fas fa-check text-success"></i> <strong>User Management:</strong> User accounts with roles and permissions</li>
        <li><i class="fas fa-check text-success"></i> <strong>Frontend Editor:</strong> Edit HTML, CSS, and JavaScript files</li>
        <li><i class="fas fa-check text-success"></i> <strong>File Versioning:</strong> Track changes to files with version history</li>
        <li><i class="fas fa-check text-success"></i> <strong>CodeMirror Integration:</strong> Syntax highlighting and code editing</li>
        <li><i class="fas fa-check text-success"></i> <strong>Collaborative Editing:</strong> File locking for team collaboration</li>
        <li><i class="fas fa-check text-success"></i> <strong>Separate Admin/Frontend Settings:</strong> Dedicated settings for both admin panel and frontend</li>
    </ul>

    <div style="margin-top: 30px;">
        <a href="index.php" class="admin-btn" style="background-color: #f1ca2f; color: #333; text-decoration: none; padding: 12px 25px; border-radius: 4px; font-weight: bold; display: inline-block;">
            <i class="fas fa-sign-in-alt"></i> Go to Admin Login
        </a>
    </div>

    <script>
        // Automatically redirect to index.php after 3 seconds
        setTimeout(function() {
            window.location.href = 'index.php';
        }, 3000);
    </script>

    <?php if (isset($config_updated) && !$config_updated): ?>
    <div style="margin: 20px 0; padding: 15px; background-color: #fff9e6; border-left: 4px solid #f1ca2f; text-align: left;">
        <h3 style="margin-top: 0;">Manual Configuration Update</h3>
        <p>The installation wizard was unable to automatically update your configuration file. Please manually update your <strong>config.php</strong> file in the admin directory with the following content:</p>

        <div style="background: #f5f5f5; padding: 15px; border-radius: 4px; margin: 15px 0; overflow-x: auto; font-family: monospace; font-size: 12px; white-space: pre-wrap;">
<?php echo htmlspecialchars($config_content); ?>
        </div>

        <p>After updating the file, your website will be fully functional.</p>
    </div>
    <?php endif; ?>

 </div>
