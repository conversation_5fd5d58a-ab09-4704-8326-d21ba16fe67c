/* Manage Inc. Website Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Open Sans', Arial, sans-serif;
    font-size: 16px;
    line-height: 1.6;
    color: #333;
    background-color: #fff;
    /* Prevent pull-to-refresh in Chrome mobile */
    overscroll-behavior-y: none;
}

a {
    text-decoration: none;
    color: inherit;
}

img {
    max-width: 100%;
    height: auto;
}

.container {
    width: 95%;
    max-width: 1400px;
    margin: 0 auto;
}

/* Header Styles */
.main-header {
    background-color: #3c3c45;
    padding: 15px 0;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    z-index: 1000;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    /* Fix for Chrome pull-to-refresh */
    transform: translateZ(0);
    -webkit-transform: translateZ(0);
    will-change: transform;
    -webkit-overflow-scrolling: touch;
    -webkit-backface-visibility: hidden;
    backface-visibility: hidden;
}

.main-header .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
}

.logo img {
    height: 60px;
}

.main-nav ul {
    display: flex;
    list-style: none;
}

.main-nav li {
    margin-left: 25px;
}

.main-nav a {
    color: #fff;
    font-size: 14px;
    font-weight: 600;
    transition: color 0.3s;
}

.main-nav a:hover {
    color: #f1ca2f;
}

.main-nav .active a {
    color: #f1ca2f;
}

/* Hero Section */
.hero-section {
    background-image: url('../images/hero-bg.jpg');
    background-size: cover;
    background-position: center;
    padding: 0;
    position: relative;
    overflow: hidden;
    height: 500px; /* Increased height for desktop */
    margin-top: 70px; /* Add space for fixed header */
}

/* Mobile-only slider indicators */
.mobile-slider-indicators {
    display: none; /* Hidden by default on desktop */
    position: absolute;
    bottom: 20px;
    left: 0;
    right: 0;
    text-align: center;
    z-index: 100;
    width: 100%;
}

@media (max-width: 768px) {
    .et_pb_slide_description {
        padding-bottom: 5px !important;
    }

    .et_pb_button_wrapper {
        margin-bottom: 5px !important;
    }

    /* Mobile-specific controller styles */
    .et-pb-controllers {
        bottom: 60px !important;
        position: relative !important;
        margin-top: 0px !important;
        display: flex !important;
        visibility: visible !important;
        opacity: 1 !important;
        z-index: 200 !important;
    }
}

.mobile-slider-indicators a {
    display: inline-block;
    width: 15px;
    height: 6px;
    background-color: rgba(0, 0, 0, 0.5);
    margin: 0 5px;
    cursor: pointer;
    text-indent: -9999px;
    border-radius: 0 !important;
    transition: all 0.1s;
    text-decoration: none;
    opacity: 1;
    visibility: visible;
    overflow: hidden;
    border: 1px solid rgba(255, 255, 255, 0.5);
}

.mobile-slider-indicators a.et-pb-active-control {
    background-color: #f1ca2f;
    width: 30px;
    border-color: #f1ca2f;
}

/* Divi-style slider */
.tl_slider_hero80 {
    position: relative;
}

.et_pb_slider {
    position: relative;
    height: 100%;
}

.et_pb_slides {
    height: 100%;
    width: 100%;
    position: relative;
}

.et_pb_slide {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    visibility: hidden;
    transition: opacity 1.2s ease-in-out, visibility 1.2s ease-in-out;
    z-index: 1;
    display: none;
}

.et_pb_slide.active {
    opacity: 1;
    visibility: visible;
    z-index: 2;
    display: block;
}

.et_pb_slide_overlay_container {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(241, 202, 47, 0.4);
    /* No ::before pseudo-element */
}

.et_pb_container {
    height: 100%;
    width: 100%;
    max-width: 1400px;
    margin: 0 auto;
    position: relative;
    display: flex;
    align-items: center;
}

.et_pb_slider_container_inner {
    width: 100%;
}

.et_pb_slide_description {
    max-width: 1200px;
    margin: 0 auto;
    text-align: center;
    padding: 60px 10px 70px;
    background-color: transparent;
}

.et_pb_slide_title {
    color: #000;
    font-size: 32px;
    font-weight: 600;
    margin-bottom: 10px;
    text-transform: uppercase;
    letter-spacing: 1px;
    white-space: nowrap;
    text-shadow: 1px 1px 3px rgba(255, 255, 255, 0.7);
}

.et_pb_slide_content h2 {
    color: #000;
    font-size: 44px;
    font-weight: 700;
    margin-top: 5px;
    margin-bottom: 10px;
    line-height: 1.2;
    text-transform: uppercase;
    white-space: nowrap;
    width: 100%;
    display: block;
    text-shadow: 1px 1px 3px rgba(255, 255, 255, 0.7);
}

.et_pb_slide_content p {
    color: #000;
    margin-bottom: 15px;
    font-size: 22px;
    line-height: 1.5;
    max-width: 1200px;
    margin-left: auto;
    margin-right: auto;
    text-align: center;
    padding-top: 10px;
    min-height: 120px; /* Minimum height for 3 lines */
    text-shadow: 1px 1px 3px rgba(255, 255, 255, 0.7);
}

.et_pb_button {
    display: inline-block;
    background-color: transparent;
    color: #000;
    padding: 10px 25px;
    font-size: 18px;
    font-weight: 600;
    border: 1px solid #000;
    cursor: pointer;
    text-transform: uppercase;
    transition: all 0.3s;
    text-decoration: none;
    letter-spacing: 1px;
    line-height: 1.7;
    margin-top: 0;
    position: relative;
    z-index: 10;
}

.et_pb_button:hover {
    background-color: #f1ca2f;
    border-color: #f1ca2f;
    padding: 10px 25px !important;
}

.et-pb-controllers {
    position: absolute;
    bottom: 20px;
    left: 0;
    right: 0;
    text-align: center;
    z-index: 100;
    display: flex;
    justify-content: center;
    width: 100%;
    /* Ensure visibility in all browsers */
    opacity: 1 !important;
    visibility: visible !important;
}

.et-pb-controllers a {
    display: inline-block;
    width: 15px;
    height: 6px;
    background-color: rgba(0, 0, 0, 0.5);
    margin: 0 5px;
    cursor: pointer;
    text-indent: -9999px;
    border-radius: 0 !important;
    transition: all 0.1s;
    text-decoration: none;
    opacity: 1;
    visibility: visible;
    overflow: hidden;
    border: 1px solid rgba(255, 255, 255, 0.5);
}

.et-pb-controllers a.et-pb-active-control {
    background-color: #f1ca2f !important;
    width: 30px !important;
    border-color: #f1ca2f !important;
    border-width: 1px !important;
}

/* Animation */
.tl_slider_hero80 .et_pb_slide .et_pb_slide_description {
    text-shadow: 0 0 0 rgba(0,0,0,0) !important;
    animation-name: none !important;
}

/* Desktop-specific controller styles */
@media (min-width: 769px) {
    .et-pb-controllers {
        bottom: 20px !important;
        position: absolute !important;
        display: flex !important;
        justify-content: center !important;
        visibility: visible !important;
        opacity: 1 !important;
        z-index: 200 !important;
    }

    .et-pb-controllers a {
        display: inline-block !important;
        width: 15px !important;
        height: 8px !important;
        background-color: rgba(0, 0, 0, 0.5) !important;
        margin: 0 5px !important;
        border-radius: 0 !important;
        opacity: 1 !important;
        visibility: visible !important;
        text-indent: -9999px !important;
        overflow: hidden !important;
        border: 1px solid rgba(255, 255, 255, 0.5) !important;
    }

    .et-pb-controllers a.et-pb-active-control {
        background-color: #f1ca2f !important;
        width: 30px !important;
        border-color: #f1ca2f !important;
    }
}

.tl_slider_hero80 .et_pb_slide .et_pb_slide_title,
.tl_slider_hero80 .et_pb_slide .et_pb_slide_content h2 {
    animation-duration: 1.2s;
    animation-delay: 0.1s;
    animation-timing-function: ease-in-out;
    animation-fill-mode: both;
    animation-name: fadeLeft;
    opacity: 0;
}

.tl_slider_hero80 .et_pb_slide .et_pb_slide_content p,
.tl_slider_hero80 .et_pb_slide .et_pb_button_wrapper {
    animation-duration: 1.2s;
    animation-delay: 0.9s;
    animation-timing-function: ease-in-out;
    animation-fill-mode: both;
    animation-name: fadeLeft;
    opacity: 0;
}

.et_pb_slide.active .et_pb_slide_title,
.et_pb_slide.active .et_pb_slide_content h2,
.et_pb_slide.active .et_pb_slide_content p,
.et_pb_slide.active .et_pb_button_wrapper {
    opacity: 1;
}

@keyframes fadeLeft {
    0% {
        opacity: 0;
        transform: translateX(-10%);
    }
    100% {
        opacity: 1;
        transform: translateX(0);
    }
}

/* About Section */
.about-section {
    padding: 30px 0;
    background-color: #fff;
    margin-top: 0;
}

.about-text {
    text-align: center;
    max-width: 1200px;
    margin: 0 auto 20px;
    line-height: 1.6;
    font-size: 15px;
    color: #666;
}

.about-text strong {
    font-weight: 600;
    color: #3c3c45;
}

/* Services Section */
.services-section {
    padding: 30px 0;
    background-color: #f5f5f5;
}

.section-title {
    text-align: center;
    font-size: 38px;
    font-weight: 700;
    margin-bottom: 25px;
    color: #3c3c45;
    position: relative;
    padding-bottom: 15px;
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 4px;
    background-color: #f1ca2f;
}

.services-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 20px;
    max-width: 1200px;
    margin: 0 auto;
}

.service-card {
    background-color: #fff;
    padding: 25px 15px;
    text-align: center;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
    transition: all 0.3s;
    border-radius: 0;
    border: 1px solid #eee;
}

.service-card:hover {
    transform: scale(1.03);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    cursor: pointer;
}

.service-card:hover .service-icon {
    border-color: #f1ca2f;
    background-color: #fffdf5;
}

.service-icon {
    margin-bottom: 20px;
    display: flex;
    justify-content: center;
    align-items: center;
    height: 80px;
    width: 80px;
    margin-left: auto;
    margin-right: auto;
    border: 1px solid #3c3c45;
    border-radius: 10px;
    background-color: #fafafa;
    padding: 12px;
    transition: all 0.3s;
}

.service-icon img {
    height: 50px;
    width: 50px;
}

.service-card h3 {
    font-size: 16px;
    font-weight: 700;
    margin-bottom: 15px;
    color: #3c3c45;
}

.read-more {
    display: inline-block;
    color: #f1ca2f;
    font-weight: 600;
    margin-top: 15px;
    transition: color 0.3s;
    font-size: 14px;
    text-decoration: none;
}

.read-more:hover {
    color: #e0bc20;
}

.read-more::after {
    content: " →";
}

/* Partners Section */
.partners-section {
    padding: 30px 0;
    background-color: #fff;
}

.partners-carousel {
    position: relative;
    padding: 0 40px;
    max-width: 1200px;
    margin: 0 auto;
}

.partners-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
}

.partner {
    padding: 0 20px;
    text-align: center;
    margin-bottom: 20px;
    width: 16.66%;
    box-sizing: border-box;
}

.partner img {
    max-height: 60px;
    max-width: 100%;
    filter: grayscale(0);
    opacity: 1;
    transition: all 0.3s;
}

.partner img:hover {
    filter: grayscale(100%);
    opacity: 0.7;
}

.carousel-prev,
.carousel-next {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    background-color: transparent;
    border: none;
    font-size: 24px;
    color: #3c3c45;
    cursor: pointer;
    z-index: 10;
}

.carousel-prev {
    left: 0;
}

.carousel-next {
    right: 0;
}

/* Footer Styles */
.footer {
    color: #fff;
    background-color: #3c3c45;
}

/* Footer Top Section */
.footer-top {
    padding: 40px 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.footer-top-content {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 50px;
}

.footer-info {
    width: 45%;
}

.footer-info h2 {
    font-size: 24px;
    font-weight: 700;
    margin-bottom: 20px;
    color: #fff;
    text-transform: uppercase;
}

.footer-info p {
    font-size: 16px;
    line-height: 1.6;
    color: #fff;
    opacity: 0.9;
}

.footer-form {
    width: 50%;
}

.footer-form .form-row {
    display: flex;
    gap: 15px;
    margin-bottom: 15px;
}

.footer-form input,
.footer-form textarea {
    width: 100%;
    padding: 12px;
    background-color: rgba(255, 255, 255, 0.1);
    border: none;
    color: #fff;
    border-radius: 0;
}

.footer-form input::placeholder,
.footer-form textarea::placeholder {
    color: rgba(255, 255, 255, 0.7);
}

.footer-form textarea {
    height: 120px;
    resize: none;
    margin-bottom: 15px;
}

.submit-btn {
    background-color: #f1ca2f;
    color: #333;
    padding: 10px 20px;
    border: none;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s;
    float: right;
}

.submit-btn:hover {
    background-color: #e0bc20;
}

/* Footer Middle Section */
.footer-middle {
    padding: 30px 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.footer-middle-content {
    display: flex;
    justify-content: space-between;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 50px;
}

.footer-logo {
    width: 30%;
    display: flex;
    align-items: flex-start; /* Align to top to allow margin-top to work */
    margin-top: 20px; /* Move logo down */
}

.footer-logo img {
    max-height: 80px;
    margin-right: auto; /* Pushes the logo to the left */
}

.footer-services {
    width: 30%;
}

.footer-services h4,
.footer-contact h4 {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 20px;
    color: #fff;
}

.footer-services ul {
    list-style: none;
    padding: 0;
}

.footer-services li {
    margin-bottom: 10px;
}

.footer-services a {
    color: #ccc;
    text-decoration: none;
    transition: color 0.3s;
}

.footer-services a:hover {
    color: #f1ca2f;
}

.footer-contact {
    width: 30%;
}

.footer-contact p {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
    color: #ccc;
}

.footer-contact img {
    width: 16px;
    height: 16px;
    margin-right: 10px;
}

.footer-contact a {
    color: #ccc;
    text-decoration: none;
    transition: color 0.3s;
}

.footer-contact a:hover {
    color: #f1ca2f;
}

/* Footer Bottom Section */
.footer-bottom {
    padding: 15px 0;
    text-align: center;
}

.footer-bottom p {
    font-size: 14px;
    color: #999;
    margin: 0;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 50px;
}

/* Ensure content pushes footer down enough */
body {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

main {
    flex: 1;
    padding-bottom: 30px; /* Reduced padding as requested */
}

.footer {
    margin-top: auto;
}

/* Page Styles */
.page-header {
    background-color: #f5f5f5;
    padding: 40px 0;
    margin-top: 70px;
    text-align: center;
}

.page-header h1 {
    font-size: 36px;
    font-weight: 700;
    color: #3c3c45;
    margin: 0;
}

/* Page Banner - Standardized based on contact-us page */
.page-banner,
.cloud-page-banner,
.managed-services-page-banner,
.infrastructure-page-banner,
.services-page-banner,
.contact-us-page-banner,
.news-page-banner {
    background-size: cover;
    background-position: center;
    color: #333;
    padding: 40px 0;
    text-align: center;
    margin-top: 90px;
    position: relative;
    min-height: 120px; /* Standardized height based on contact page */
    display: flex;
    align-items: center;
    justify-content: center;
}

.page-banner::before,
.cloud-page-banner::before,
.managed-services-page-banner::before,
.infrastructure-page-banner::before,
.services-page-banner::before,
.contact-us-page-banner::before,
.news-page-banner::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(241, 202, 47, 0.7);
    z-index: 1;
}

.page-banner h1,
.cloud-page-banner h1,
.managed-services-page-banner h1,
.infrastructure-page-banner h1,
.services-page-banner h1,
.contact-us-page-banner h1,
.news-page-banner h1 {
    margin: 0;
    font-size: 36px;
    font-weight: 600;
    color: #333;
    position: relative;
    z-index: 5;
    text-align: center;
    width: 100%;
}

/* Page Submenu */
.page-submenu {
    padding: 15px 0;
    text-align: center;
    border-bottom: 1px solid #eee;
}

.submenu {
    display: flex;
    list-style: none;
    margin: 0;
    padding: 0;
    justify-content: center;
}

.submenu li {
    margin: 0 10px;
}

.submenu a {
    display: inline-block;
    color: #fff;
    text-decoration: none;
    font-weight: 600;
    padding: 8px 15px;
    transition: all 0.3s;
    background-color: #4a4a52;
    border-radius: 4px;
}

.submenu a:hover {
    background-color: #5a5a62;
}

.submenu a.active {
    background-color: #f1ca2f;
    color: #333;
}

/* Cloud Services Grid */
.cloud-services-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 25px;
    margin: 40px 0;
}

.cloud-service {
    padding: 30px;
    color: #333;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    min-height: 280px;
    display: flex;
    flex-direction: column;
    border: 1px solid #eee;
}

.cloud-service:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    border-color: #f1ca2f;
}

.cloud-service::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 3px;
    background-color: #f1ca2f;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.cloud-service:hover::before {
    opacity: 1;
}

.cloud-service-icon {
    width: 60px;
    height: 60px;
    margin: 0 auto 15px;
    background-color: rgba(241, 202, 47, 0.1);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.cloud-service:hover .cloud-service-icon {
    background-color: rgba(241, 202, 47, 0.2);
    transform: scale(1.05);
}

.cloud-service-icon img {
    width: 30px;
    height: 30px;
}

.cloud-service h3 {
    margin-top: 0;
    text-align: center;
    font-size: 20px;
    font-weight: 700;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid #eee;
    letter-spacing: 0.5px;
    color: #3c3c45;
}

.cloud-service ul {
    margin: 0;
    padding-left: 20px;
    flex-grow: 1;
}

.cloud-service li {
    margin-bottom: 10px;
    font-size: 15px;
    line-height: 1.5;
    color: #666;
}

.cloud-service li:last-child {
    margin-bottom: 0;
}

@media (max-width: 992px) {
    .cloud-services-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 20px;
    }

    .cloud-service {
        min-height: 250px;
    }
}

@media (max-width: 576px) {
    .cloud-services-grid {
        grid-template-columns: 1fr;
    }

    .cloud-service {
        min-height: auto;
        padding: 20px;
    }
}

.page-content {
    padding: 0px 0;
    padding-top: 0px;
}

.page-content .container {
    padding: 0 40px;
    max-width: 1200px;
}

.content-image {
    margin-bottom: 30px;
}

.content-image img {
    max-width: 100%;
    height: auto;
    display: block;
    margin: 0 auto;
    width: 100%;
}

/* Special styling for the first cloud image */
.page-content .content-image:first-of-type img {
    width: 100%;
    max-width: 1200px;
}

.facilities-image {
    max-width: 626px !important;
    height: auto;
    display: block;
    margin: 0 auto;
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
}

.managed-services-diagram {
    max-width: 800px !important;
    height: auto;
    display: block;
    margin: 0 auto;
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    border-radius: 4px;
}

.managed-services-taas {
    max-width: 700px !important;
    height: auto;
    display: block;
    margin: 0 auto;
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    border-radius: 4px;
}

.services-industry-img {
    max-width: 700px !important;
    height: auto;
    display: block;
    margin: 0 auto;
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    border-radius: 4px;
}

.content-text {
    margin-bottom: 30px;
    line-height: 1.6;
}

.content-section {
    margin-bottom: 40px;
}

.content-section h3 {
    font-size: 24px;
    font-weight: 600;
    margin-bottom: 20px;
    color: #3c3c45;
}

.content-section h4 {
    font-size: 20px;
    font-weight: 600;
    margin-bottom: 15px;
    margin-top: 30px;
    color: #3c3c45;
}

.content-section ul,
.content-section ol {
    padding-left: 20px;
    margin-bottom: 20px;
}

.content-section li {
    margin-bottom: 10px;
    line-height: 1.6;
}

/* Specific Page Banner Backgrounds */
.cloud-page-banner {
    background-image: url('../images/banners/cloud-heading-bg.jpg');
}

.managed-services-page-banner {
    background-image: url('../images/banners/managed-services-heading-bg.jpg');
}

.infrastructure-page-banner {
    background-image: url('../images/banners/infrastructure-heading-bg.jpg');
}

.services-page-banner {
    background-image: url('../images/banners/services-heading-bg.jpg');
}

.contact-us-page-banner {
    background-image: url('../images/banners/contact-us-heading-bg.jpg');
}

.news-page-banner {
    background-image: url('../images/banners/news-heading-bg.jpg');
}

.page-content {
    padding: 0px 0;
    padding-top: 0px;
}

.news-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 30px;
    margin: 50px 0;
}

.news-item {
    background-color: #fff;
    border-radius: 0;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    border: 1px solid #eee;
    display: flex;
    flex-direction: column;
    height: 100%;
}

.news-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 12px 30px rgba(0, 0, 0, 0.1);
    border-color: #f1ca2f;
}

.news-image {
    height: 280px;
    overflow: hidden;
    position: relative;
}

.news-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.5s ease;
}

.news-item:hover .news-image img {
    transform: scale(1.05);
}

.news-content {
    padding: 30px;
    flex-grow: 1;
    display: flex;
    flex-direction: column;
    min-height: 280px;
}

.news-content h2 {
    font-size: 20px;
    font-weight: 600;
    margin-top: 0;
    margin-bottom: 12px;
    color: #3c3c45;
    line-height: 1.4;
    transition: color 0.3s ease;
}

.news-item:hover .news-content h2 {
    color: #1e73be;
}

.news-category {
    display: inline-block;
    color: #f1ca2f;
    font-size: 13px;
    font-weight: 600;
    margin-bottom: 15px;
    text-transform: uppercase;
}

.news-content p {
    color: #666;
    margin-bottom: 20px;
    line-height: 1.6;
    flex-grow: 1;
    font-size: 14px;
}

.read-more-btn {
    display: inline-block;
    color: #f1ca2f;
    font-weight: 600;
    text-decoration: none;
    transition: all 0.3s;
    margin-top: auto;
    text-transform: lowercase;
    font-size: 14px;
}

.read-more-btn:hover {
    color: #1e73be;
}

/* News Detail Page */
.news-detail-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 40px 0;
    background-color: #fff;
    box-shadow: 0 5px 30px rgba(0, 0, 0, 0.05);
    border-radius: 8px;
    overflow: hidden;
}

.news-detail-header {
    margin-bottom: 0px;
    padding: 0px;
    border-bottom: 1px solid #eee;
}

.news-detail-header h1 {
    font-size: 36px;
    color: #3c3c45;
    margin-bottom: 15px;
    line-height: 1.3;
    font-weight: 700;
}

.news-detail-meta {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    gap: 20px;
    margin-bottom: 10px;
}

.news-date, .news-category {
    color: #666;
    font-size: 14px;
    display: flex;
    align-items: center;
}

.news-calendar-icon, .news-tag-icon {
    display: inline-block;
    width: 16px;
    height: 16px;
    margin-right: 8px;
    position: relative;
}

.news-calendar-icon::before {
    content: '';
    position: absolute;
    width: 14px;
    height: 14px;
    border: 2px solid #f1ca2f;
    border-radius: 2px;
    top: 0;
    left: 0;
}

.news-calendar-icon::after {
    content: '';
    position: absolute;
    width: 10px;
    height: 2px;
    background-color: #f1ca2f;
    top: 6px;
    left: 3px;
}

.news-tag-icon::before {
    content: '';
    position: absolute;
    width: 12px;
    height: 12px;
    border: 2px solid #f1ca2f;
    transform: rotate(45deg);
    top: 2px;
    left: 2px;
}

.news-category {
    background-color: rgba(241, 202, 47, 0.1);
    padding: 4px 12px;
    border-radius: 20px;
    color: #e0bc20;
    font-weight: 600;
}

.news-detail-image {
    margin-bottom: 30px;
    overflow: hidden;
    max-height: 500px;
}

.news-detail-image img {
    width: 100%;
    height: auto;
    display: block;
    object-fit: cover;
}

.news-detail-content {
    line-height: 1.8;
    color: #333;
    padding: 0px;
    font-size: 16px;
}

.news-detail-content p {
    margin-bottom: 20px;
    font-size: 16px;
}

.news-detail-content h2 {
    font-size: 28px;
    margin: 30px 0 15px;
    color: #3c3c45;
}

.news-detail-content h3 {
    font-size: 22px;
    margin: 25px 0 15px;
    color: #3c3c45;
}

.news-detail-content ul, .news-detail-content ol {
    margin-bottom: 20px;
    padding-left: 25px;
}

.news-detail-content li {
    margin-bottom: 10px;
}

.news-detail-content a {
    color: #1e73be;
    text-decoration: none;
    border-bottom: 1px solid rgba(30, 115, 190, 0.3);
    transition: all 0.3s;
}

.news-detail-content a:hover {
    color: #f1ca2f;
    border-bottom-color: rgba(241, 202, 47, 0.5);
}

.news-detail-content img {
    max-width: 100%;
    height: auto;
    margin: 20px 0;
    border-radius: 4px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.news-detail-content blockquote {
    border-left: 4px solid #f1ca2f;
    padding: 15px 20px;
    margin: 20px 0;
    background-color: #f9f9f9;
    font-style: italic;
}

.back-to-news {
    display: inline-flex;
    align-items: center;
    margin-top: 40px;
    padding: 12px 24px;
    background-color: #f1ca2f;
    color: #333;
    text-decoration: none;
    border-radius: 6px;
    font-weight: 600;
    transition: all 0.3s;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.back-to-news:hover {
    background-color: #e0bc20;
    transform: translateY(-2px);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
}

.back-to-news::before {
    content: '←';
    margin-right: 8px;
}

@media (max-width: 992px) {
    .news-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .news-detail-header h1 {
        font-size: 30px;
    }

    .news-detail-container {
        padding: 30px;
    }

    .news-detail-content h2 {
        font-size: 26px;
        margin-top: 35px;
        margin-bottom: 18px;
    }

    .news-detail-content h3 {
        font-size: 20px;
        margin-top: 25px;
    }
}

@media (max-width: 768px) {
    .news-detail-container {
        padding: 20px;
    }

    .news-detail-header h1 {
        font-size: 28px;
        margin-bottom: 15px;
    }

    .news-detail-image {
        margin-bottom: 25px;
    }

    .news-detail-meta {
        margin-bottom: 20px;
    }

    .news-detail-content h2 {
        font-size: 24px;
        margin-top: 30px;
        margin-bottom: 15px;
    }

    .news-detail-content h3 {
        font-size: 20px;
        margin-top: 25px;
        margin-bottom: 12px;
    }

    .image-wrapper {
        margin: 20px 0;
        overflow: hidden;
    }

    .responsive-image {
        box-shadow: 0 1px 5px rgba(0, 0, 0, 0.1);
        border-radius: 2px;
    }

    .news-detail-footer {
        margin-top: 30px;
        padding-top: 15px;
    }

    .back-to-news {
        margin: 0;
        display: block;
        text-align: center;
        width: 100%;
    }
}

@media (max-width: 576px) {
    .news-grid {
        grid-template-columns: 1fr;
    }

    .news-detail-header h1 {
        font-size: 24px;
    }

    .news-detail-container {
        padding: 15px;
    }

    .news-detail-content {
        font-size: 15px;
    }

    .news-detail-content h2 {
        font-size: 22px;
    }

    .news-detail-content h3 {
        font-size: 18px;
    }

    .image-wrapper {
        margin: 15px 0;
    }

    .responsive-image {
        box-shadow: none;
        border: 1px solid #eee;
    }

    .news-detail-footer {
        margin-top: 25px;
        padding-top: 10px;
    }
}

/* Contact Page Styles */
.contact-layout {
    display: flex;
    flex-wrap: wrap;
    gap: 40px;
    margin: 40px 0;
}

.contact-form-side {
    flex: 1;
    min-width: 300px;
    background-color: #fff;
    padding: 40px;
    border-radius: 0;
    box-shadow: 0 0 0 rgba(0, 0, 0, 0);
}

.contact-form-side h2 {
    font-size: 24px;
    font-weight: 600;
    margin-bottom: 30px;
    color: #333;
}

.contact-form .form-group {
    margin-bottom: 20px;
}

.contact-form input,
.contact-form textarea {
    width: 100%;
    padding: 12px 15px;
    border: 1px solid #ddd;
    border-radius: 0;
    font-size: 15px;
    transition: border-color 0.3s;
}

.contact-form input:focus,
.contact-form textarea:focus {
    border-color: #f1ca2f;
    outline: none;
}

.contact-form textarea {
    height: 120px;
    resize: none;
}

.contact-form .submit-btn {
    background-color: #f1ca2f;
    color: #333;
    padding: 12px 25px;
    border: none;
    border-radius: 0;
    font-weight: 600;
    font-size: 16px;
    cursor: pointer;
    transition: all 0.3s;
    float: right;
    display: inline-block;
}

.contact-form .submit-btn:hover {
    background-color: #e0bc20;
}

.contact-info-side {
    flex: 1;
    min-width: 300px;
    padding: 40px;
    background-color: #3c3c45; /* Changed to match header background color */
    color: #fff; /* Changed to white for better contrast */
    border-radius: 0;
}

.contact-info-side h3 {
    font-size: 18px;
    font-weight: 700;
    margin-bottom: 30px;
    color: #fff; /* Changed to white for better contrast */
}

.contact-info-item {
    display: flex;
    align-items: flex-start;
    margin-bottom: 20px;
}

.contact-icon {
    margin-right: 15px;
    min-width: 24px;
}

.contact-icon img {
    width: 20px;
    height: 20px;
}

.contact-info-item p {
    margin: 0;
    line-height: 1.5;
    color: #fff; /* Changed to white for better contrast */
}

.contact-info-item a {
    color: #fff; /* Changed to white for better contrast */
    text-decoration: none;
    transition: color 0.3s;
}

.contact-info-item a:hover {
    color: #f1ca2f;
}

/* Hide footer form on contact page */
.contact-page .footer-top {
    display: block;
}

/* News Detail Page Styles */
.news-detail-container {
    max-width: 1400px; /* Increased from 1000px to match other pages */
    margin: 0 auto;
    padding: 40px;
}

.news-detail-image {
    margin-bottom: 30px;
}

.news-detail-image img {
    width: 100%;
    height: auto;
    border-radius: 0;
}

.news-detail-header {
    margin-bottom: 0px;
}

.news-detail-header h1 {
    font-size: 36px;
    color: #3c3c45;
    margin-bottom: 20px;
    font-weight: 600;
    line-height: 1.2;
}

.news-detail-meta {
    color: #666;
    font-size: 15px;
    margin-bottom: 30px;
    display: flex;
    align-items: center;
}

.news-detail-meta::before {
    content: '';
    display: inline-block;
    width: 6px;
    height: 6px;
    background-color: #f1ca2f;
    border-radius: 50%;
    margin-right: 10px;
}

.news-detail-content {
    line-height: 1.8;
    color: #333;
    font-size: 16px;
}

.news-detail-content p {
    margin-bottom: 24px;
}

.news-detail-content h2 {
    font-size: 28px;
    margin-top: 40px;
    margin-bottom: 20px;
    color: #3c3c45;
    font-weight: 600;
}

.news-detail-content h3 {
    font-size: 22px;
    margin-top: 30px;
    margin-bottom: 15px;
    color: #3c3c45;
}

.image-wrapper {
    margin: 25px 0;
    text-align: center;
}

.responsive-image {
    max-width: 100%;
    height: auto;
    display: inline-block;
    border-radius: 4px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.news-detail-footer {
    margin-top: 40px;
    padding-top: 20px;
    border-top: 1px solid #eee;
}

.back-to-news {
    display: inline-flex;
    align-items: center;
    margin: 40px 0 20px 40px;
    padding: 12px 24px;
    background-color: #f1ca2f;
    color: #333;
    text-decoration: none;
    border-radius: 30px;
    font-weight: 600;
    transition: all 0.3s ease;
    box-shadow: 0 4px 10px rgba(241, 202, 47, 0.3);
}

.back-to-news:hover {
    background-color: #e0bc20;
    transform: translateX(-5px);
    box-shadow: 0 6px 15px rgba(241, 202, 47, 0.4);
}

/* Remove ::before content to fix duplicate arrow */
.back-to-news::before {
    content: none;
}

.back-arrow {
    display: inline-block;
    width: 16px;
    height: 16px;
    margin-right: 10px;
    position: relative;
}

.back-arrow::before {
    content: '';
    position: absolute;
    width: 12px;
    height: 2px;
    background-color: #333;
    top: 7px;
    left: 0;
}

.back-arrow::after {
    content: '';
    position: absolute;
    width: 6px;
    height: 6px;
    border-left: 2px solid #333;
    border-bottom: 2px solid #333;
    transform: rotate(45deg);
    top: 5px;
    left: 0;
}

/* Responsive Styles */
@media (max-width: 992px) {
    .services-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .footer-top-content,
    .footer-middle-content {
        flex-direction: column;
    }

    .footer-info,
    .footer-form,
    .footer-logo,
    .footer-services,
    .footer-contact {
        width: 100%;
        margin-bottom: 30px;
    }

    .contact-container {
        flex-direction: column;
    }

    .contact-cards {
        flex-direction: column;
        align-items: center;
    }

    .contact-card {
        max-width: 100%;
        width: 100%;
    }
}

@media (max-width: 768px) {
    .main-header .container {
        flex-direction: column;
    }

    .main-nav {
        margin-top: 20px;
    }

    .main-nav ul {
        flex-wrap: wrap;
        justify-content: center;
    }

    .main-nav li {
        margin: 5px 10px;
    }

    .hero-content h1 {
        font-size: 28px;
    }

    .hero-content h2 {
        font-size: 20px;
    }

    .footer-form .form-row {
        flex-direction: column;
        gap: 10px;
    }

    .submit-btn {
        float: none;
        width: 100%;
    }
}

@media (max-width: 576px) {
    .services-grid {
        grid-template-columns: 1fr;
    }

    .partners-container {
        flex-wrap: wrap;
    }

    .partner {
        width: 50%;
        margin-bottom: 20px;
    }
}

/* Page-specific banners */
/* Set margin-top for all page banners */
.cloud-page-banner,
.managed-services-page-banner,
.infrastructure-page-banner,
.services-page-banner,
.contact-us-page-banner,
.news-page-banner {
    margin-top: 90px;
}

/* Cloud page specific banner */
.cloud-page-banner {
    background-image: url('../images/banners/cloud-heading-bg.jpg');
    background-size: cover;
    background-position: center;
    color: #333;
    margin-top: 90px;
    min-height: 120px;
    padding: 40px 0;
    text-align: center;
}

/* Removed ::before pseudo-element */

/* Managed Services page specific banner */
.managed-services-page-banner {
    background-image: url('../images/banners/managed-services-heading-bg.jpg');
    background-size: cover;
    background-position: center;
    color: #333;
    margin-top: 90px;
    min-height: 120px;
    padding: 40px 0;
    text-align: center;
}

/* Removed ::before pseudo-element */

/* Infrastructure page specific banner */
.infrastructure-page-banner {
    background-image: url('../images/banners/infrastructure-heading-bg.jpg');
    background-size: cover;
    background-position: center;
    color: #333;
    margin-top: 90px;
    min-height: 120px;
    padding: 40px 0;
    text-align: center;
}

/* Removed ::before pseudo-element */

/* Contact Us page specific banner */
.contact-us-page-banner {
    background-image: url('../images/banners/contact-us-heading-bg.jpg');
    background-size: cover;
    background-position: center;
    color: #333;
    margin-top: 90px;
    min-height: 120px;
    padding: 40px 0;
    text-align: center;
}

/* Removed ::before pseudo-element */

/* Services page specific banner */
.services-page-banner {
    background-image: url('../images/banners/services-heading-bg.jpg');
    background-size: cover;
    background-position: center;
    color: #333;
    margin-top: 90px;
    min-height: 120px;
    padding: 40px 0;
    text-align: center;
}

/* Removed ::before pseudo-element */

/* News page specific banner */
.news-page-banner {
    background-image: url('../images/banners/cloud-heading-bg.jpg');
    background-size: cover;
    background-position: center;
    color: #333;
    margin-top: 90px;
    min-height: 120px;
    padding: 40px 0;
    text-align: center;
}

/* Removed ::before pseudo-element */

/* Page-specific banner headings */
.page-banner h1,
.cloud-page-banner h1,
.managed-services-page-banner h1,
.infrastructure-page-banner h1,
.contact-us-page-banner h1,
.services-page-banner h1,
.news-page-banner h1 {
    margin: 0;
    font-size: 42px;
    font-weight: 700;
    color: #333;
    text-shadow: 1px 1px 3px rgba(255, 255, 255, 0.7);
    position: relative;
    z-index: 5;
    letter-spacing: 0.5px;
    text-transform: none;
}

/* Mobile Menu Toggle */
.mobile-menu-toggle {
    display: none;
    cursor: pointer;
    width: 30px;
    height: 25px;
    position: relative;
    z-index: 1001;
    margin-left: auto;
    margin-right: 10px;
}

.mobile-menu-toggle span {
    display: block;
    position: absolute;
    height: 3px;
    width: 100%;
    background: #fff;
    border-radius: 3px;
    opacity: 1;
    left: 0;
    transform: rotate(0deg);
    transition: .25s ease-in-out;
}

.mobile-menu-toggle span:nth-child(1) {
    top: 0px;
}

.mobile-menu-toggle span:nth-child(2) {
    top: 10px;
}

.mobile-menu-toggle span:nth-child(3) {
    top: 20px;
}

.mobile-menu-toggle.active span:nth-child(1) {
    top: 10px;
    transform: rotate(135deg);
}

.mobile-menu-toggle.active span:nth-child(2) {
    opacity: 0;
    left: -60px;
}

.mobile-menu-toggle.active span:nth-child(3) {
    top: 10px;
    transform: rotate(-135deg);
}

/* Mobile Responsiveness */
@media (max-width: 768px) {
    .main-header {
        padding: 0;
        position: fixed !important;
        top: 0 !important;
        /* Additional Chrome mobile fixes */
        -webkit-transform: translate3d(0,0,0);
        transform: translate3d(0,0,0);
    }

    .main-header .container {
        padding: 5px 15px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        position: relative;
        height: 60px; /* Fixed height to match other pages */
    }

    /* Hero section mobile styles */
    .hero-section {
        height: auto;
        min-height: 450px; /* Increased height for mobile */
        margin-top: 60px; /* Match the banner margin */
        clear: both; /* Ensure proper clearing */
        display: block; /* Ensure block display */
        overflow: visible; /* Allow controllers to be visible outside */
        margin-bottom: 20px; /* Reduced space after hero section */
        position: relative; /* Ensure proper positioning context */
    }

    /* Show mobile indicators on mobile */
    .mobile-slider-indicators {
        display: flex !important;
        justify-content: center !important;
        position: absolute !important;
        bottom: 20px !important; /* Position inside the slider */
        left: 0 !important;
        right: 0 !important;
        width: 100% !important;
        padding: 0 !important;
        margin: 0 !important;
        z-index: 1000 !important; /* Higher z-index to ensure visibility */
        background-color: transparent !important;
    }

    /* Make mobile indicators highly visible */
    .mobile-slider-indicators a {
        width: 30px !important;
        height: 10px !important;
        margin: 0 6px !important;
        background-color: rgba(0, 0, 0, 0.7) !important;
        border: 2px solid rgba(255, 255, 255, 0.9) !important;
        opacity: 1 !important;
        visibility: visible !important;
        text-indent: -9999px !important;
        overflow: hidden !important;
        display: block !important;
        position: relative !important;
        box-shadow: 0 0 3px rgba(0, 0, 0, 0.3) !important;
    }

    .mobile-slider-indicators a.et-pb-active-control {
        width: 50px !important;
        background-color: #f1ca2f !important;
        border-color: #f1ca2f !important;
        box-shadow: 0 0 8px rgba(241, 202, 47, 0.9) !important;
    }

    /* Hide desktop controllers on mobile */
    .et-pb-controllers {
        display: none !important;
    }

    .et_pb_slider,
    .et_pb_slides {
        height: 100%;
        min-height: 450px; /* Increased height for mobile */
    }

    .et_pb_slide {
        position: relative;
        min-height: 450px; /* Increased height for mobile */
        top: 0;
        left: 0;
    }

    .et_pb_slide.active {
        position: relative;
        display: block;
    }

    .et_pb_slide_description {
        padding: 20px 15px;
        width: 90%;
        max-width: 500px;
        margin: 0 auto;
        background-color: transparent;
        border-radius: 0;
        box-shadow: none;
        overflow: hidden; /* Prevent content overflow */
        position: relative; /* Ensure proper stacking */
    }

    .et_pb_slide_title,
    .et_pb_slide_content h2 {
        white-space: normal;
        font-size: 20px;
        text-shadow: none;
    }

    .et_pb_slide_content p {
        font-size: 16px;
        min-height: auto;
        text-shadow: none;
    }

    .logo {
        flex: 0 0 auto;
        margin-right: auto;
    }

    .mobile-menu-toggle {
        position: absolute;
        right: 15px;
        top: 50%;
        transform: translateY(-50%);
    }

    .logo img {
        height: 50px;
    }

    .main-nav {
        display: none;
        position: absolute;
        top: 70px;
        left: 0;
        width: 100%;
        background-color: #3c3c45;
        padding: 20px 0;
        box-shadow: 0 5px 10px rgba(0,0,0,0.1);
        z-index: 1000;
    }

    .main-nav.active {
        display: block;
    }

    .main-nav ul {
        display: flex;
        flex-direction: column;
        align-items: center;
        padding: 0;
        margin: 0;
    }

    .main-nav li {
        margin: 10px 0;
        width: 100%;
        text-align: center;
    }

    .main-nav a {
        display: block;
        padding: 10px 0;
        font-size: 16px;
    }

    .mobile-menu-toggle {
        display: block;
        flex: 0 0 auto;
    }

    /* Hero section styles moved to inline styles in index.html */

    .services-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .partner {
        width: 33.33%;
    }

    .footer-top-content,
    .footer-middle-content {
        flex-direction: column;
        padding: 0 20px;
    }

    .footer-info,
    .footer-form,
    .footer-logo,
    .footer-services,
    .footer-contact {
        width: 100%;
        margin-bottom: 30px;
    }

    .footer-logo {
        display: flex;
        justify-content: flex-start; /* Ensures logo stays left-aligned on mobile */
        align-items: flex-start; /* Keep top alignment on mobile */
        margin-top: 15px; /* Slightly less margin on mobile */
    }

    .section-title {
        font-size: 28px;
    }

    /* Fix spacing between hero and about section */
    .about-section {
        padding-top: 30px; /* Increased padding for better spacing */
        margin-top: 0; /* No negative margin */
        position: relative; /* Ensure proper stacking context */
        z-index: 5; /* Higher than hero section */
        background-color: #fff; /* Ensure background is solid */
        border-top: 1px solid #f1f1f1; /* Subtle separator */
    }

    .about-text {
        padding: 0 15px;
        margin-top: 0;
    }

    /* Ensure hero section doesn't overlap */
    .hero-section {
        position: relative;
        z-index: 1;
        overflow: visible;
        margin-bottom: 30px; /* Increased bottom margin for better spacing */
    }

    /* Page banners for mobile - Standardized based on contact-us page */
    .page-banner,
    .cloud-page-banner,
    .managed-services-page-banner,
    .infrastructure-page-banner,
    .services-page-banner,
    .contact-us-page-banner,
    .news-page-banner {
        margin-top: 60px !important; /* Reduced margin to eliminate white space */
        padding: 20px 0 !important; /* Consistent padding */
        min-height: 120px !important; /* Consistent height */
        text-align: center;
        position: relative;
    }

    /* Fix for header gap in mobile */
    .main-header {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        z-index: 1000;
        height: 60px; /* Fixed height to match margin-top of banners */
    }

    .page-banner h1,
    .cloud-page-banner h1,
    .managed-services-page-banner h1,
    .infrastructure-page-banner h1,
    .services-page-banner h1,
    .contact-us-page-banner h1,
    .news-page-banner h1 {
        font-size: 28px !important;
        text-align: center !important;
        width: 100%;
        display: block;
        margin: 0 auto;
        padding: 0;
        font-weight: 600;
    }

    /* Consistent banner height on mobile */
    .page-banner,
    .cloud-page-banner,
    .managed-services-page-banner,
    .infrastructure-page-banner,
    .services-page-banner,
    .contact-us-page-banner,
    .news-page-banner {
        min-height: 100px !important;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    /* Adjust page content to account for banner positioning */
    .page-content {
        position: relative;
        margin-top: 20px;
        margin-bottom: 20px;
    }

    .cloud-page-banner .container,
    .managed-services-page-banner .container,
    .infrastructure-page-banner .container,
    .services-page-banner .container,
    .contact-us-page-banner .container,
    .news-page-banner .container {
        text-align: center !important;
    }

    /* Content images for mobile */
    .content-image {
        margin: 20px 0;
    }

    /* Form elements for mobile */
    input, textarea, button {
        width: 100%;
    }

    .form-row {
        flex-direction: column;
    }

    .form-row input {
        margin-bottom: 10px;
    }
}

/* Extra small devices */
@media (max-width: 480px) {
    .hero-section,
    .et_pb_slider,
    .et_pb_slides,
    .et_pb_slide,
    .et_pb_slide.active {
        min-height: 400px; /* Increased height for smaller devices, but still less than regular mobile */
    }

    /* Further adjust spacing for extra small devices */
    .about-section {
        margin-top: 0; /* No negative margin */
        padding-top: 25px; /* Increased padding for better spacing */
    }

    /* Ensure hero section has proper spacing on extra small devices */
    .hero-section {
        margin-bottom: 25px; /* Proper spacing after hero section */
    }

    /* Adjust banner height and padding for extra small devices */
    .page-banner,
    .cloud-page-banner,
    .managed-services-page-banner,
    .infrastructure-page-banner,
    .services-page-banner,
    .contact-us-page-banner,
    .news-page-banner {
        min-height: 100px !important;
        padding: 20px 0 !important;
    }

    .page-banner h1,
    .cloud-page-banner h1,
    .managed-services-page-banner h1,
    .infrastructure-page-banner h1,
    .services-page-banner h1,
    .contact-us-page-banner h1,
    .news-page-banner h1 {
        font-size: 24px !important;
    }

    .et_pb_slide_description {
        padding: 15px 10px;
        width: 95%;
    }

    .et_pb_slide_title,
    .et_pb_slide_content h2 {
        font-size: 18px;
        line-height: 1.3;
        margin-bottom: 8px;
    }

    .et_pb_slide_content p {
        font-size: 14px;
        line-height: 1.4;
        margin-bottom: 12px;
    }

    .et_pb_more_button {
        font-size: 14px;
        padding: 6px 15px;
        margin-bottom: 30px; /* Add bottom margin for space before indicators */
    }

    .et_pb_button_wrapper {
        margin-bottom: 30px; /* Add bottom margin for space before indicators */
    }

    /* Mobile-specific controller styles for extra small devices */
    .et-pb-controllers {
        bottom: 60px !important;
        position: relative !important;
        margin-top: 0px !important;
        display: flex !important;
        visibility: visible !important;
        opacity: 1 !important;
        z-index: 200 !important;
    }

    .et-pb-controllers a {
        width: 20px !important;
        height: 6px !important;
        background-color: rgba(0, 0, 0, 0.5) !important;
        border: 1px solid rgba(255, 255, 255, 0.5) !important;
        opacity: 1 !important;
        visibility: visible !important;
        margin: 0 4px !important;
    }

    .et-pb-controllers a.et-pb-active-control {
        background-color: #f1ca2f !important;
        width: 35px !important;
        border-color: #f1ca2f !important;
    }

    /* Style mobile slider indicators for extra small devices */
    .mobile-slider-indicators a {
        width: 20px !important;
        height: 6px !important;
        background-color: rgba(0, 0, 0, 0.5) !important;
        border: 1px solid rgba(255, 255, 255, 0.5) !important;
        opacity: 1 !important;
        visibility: visible !important;
        margin: 0 4px !important;
    }

    .mobile-slider-indicators a.et-pb-active-control {
        background-color: #f1ca2f !important;
        width: 35px !important;
        border-color: #f1ca2f !important;
    }
}