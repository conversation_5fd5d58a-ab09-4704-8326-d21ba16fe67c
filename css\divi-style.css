/* Divi Theme Styles */
body {
    font-family: 'Open Sans', Arial, sans-serif;
    font-size: 14px;
    color: #666;
    background-color: #fff;
    line-height: 1.7em;
    font-weight: 500;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

.container {
    width: 80%;
    max-width: 1080px;
    margin: auto;
}

/* Header Styles */
#main-header {
    background-color: #fff;
    box-shadow: 0 1px 0 rgba(0, 0, 0, 0.1);
    position: relative;
    z-index: 99999;
    top: 0;
    width: 100%;
    transition: all 0.4s ease-in-out;
}

.logo_container {
    height: 100%;
    position: relative;
    float: left;
}

.logo_helper {
    display: inline-block;
    height: 100%;
    vertical-align: middle;
    width: 0;
}

#logo {
    max-height: 54%;
    display: inline-block;
    float: none;
    margin-bottom: 0;
    vertical-align: middle;
    transition: all 0.4s ease-in-out;
    transform: translate3d(0, 0, 0);
}

#et-top-navigation {
    float: right;
    padding-top: 33px;
}

#top-menu-nav {
    float: left;
    padding-bottom: 10px;
}

#top-menu {
    line-height: 0;
}

#top-menu li {
    display: inline-block;
    padding-right: 22px;
    font-size: 14px;
}

#top-menu a {
    color: #666;
    text-decoration: none;
    display: block;
    position: relative;
    transition: all 0.4s ease-in-out;
}

#top-menu a:hover {
    color: #2ea3f2;
}

#top-menu .current_page_item > a {
    color: #2ea3f2;
}

/* Main Content Styles */
#main-content {
    background-color: #fff;
}

.et_pb_section {
    padding: 54px 0;
    position: relative;
}

.et_pb_row {
    position: relative;
    width: 80%;
    max-width: 1080px;
    margin: auto;
    padding: 30px 0;
}

.et_pb_column {
    float: left;
    position: relative;
    z-index: 9;
    padding-bottom: 30px;
}

.et_pb_column_4_4 {
    width: 100%;
}

.et_pb_column_1_2 {
    width: 47.25%;
    margin-right: 5.5%;
}

.et_pb_column_1_3 {
    width: 29.666%;
    margin-right: 5.5%;
}

.et_pb_column_1_4 {
    width: 20.875%;
    margin-right: 5.5%;
}

.et_pb_column:last-child {
    margin-right: 0 !important;
}

.et_pb_module {
    margin-bottom: 30px;
}

.et_pb_text {
    word-wrap: break-word;
}

.et_pb_text_inner {
    position: relative;
}

.section-title {
    font-size: 30px;
    color: #333;
    padding-bottom: 10px;
    line-height: 1em;
    font-weight: 500;
    text-align: center;
}

/* Slider Styles */
.et_pb_fullwidth_section {
    padding: 0;
}

.et_pb_fullwidth_slider {
    position: relative;
    overflow: hidden;
}

.et_pb_slide {
    padding: 0 0 10%;
    position: relative;
    background-color: #2ea3f2;
}

.et_pb_slide_description {
    padding: 16% 8%;
    width: 100%;
    text-align: center;
}

.et_pb_slide_title {
    font-size: 46px;
    font-weight: 300;
    padding-bottom: 20px;
    color: #fff;
}

.et_pb_slide_content {
    font-size: 18px;
    font-weight: 400;
    color: #fff;
}

.et_pb_button {
    display: inline-block;
    color: #fff;
    background-color: transparent;
    border: 2px solid #fff;
    border-radius: 3px;
    padding: 0.3em 1em;
    font-size: 20px;
    font-weight: 500;
    line-height: 1.7em;
    transition: all 0.2s;
    text-decoration: none;
}

.et_pb_button:hover {
    background-color: rgba(255, 255, 255, 0.2);
    padding: 0.3em 2em 0.3em 1em;
    border: 2px solid transparent;
}

/* Blurb Styles */
.et_pb_blurb {
    position: relative;
}

.et_pb_blurb_content {
    position: relative;
    max-width: 550px;
    margin: 0 auto;
}

.et_pb_main_blurb_image {
    text-align: center;
    margin-bottom: 30px;
}

.et_pb_main_blurb_image img {
    max-width: 100%;
    height: auto;
}

.et_pb_blurb_container {
    text-align: center;
}

.et_pb_module_header {
    padding-bottom: 10px;
    color: #333;
    font-weight: 500;
    line-height: 1em;
    font-size: 22px;
}

/* Testimonial Styles */
.et_pb_testimonial {
    position: relative;
    padding: 30px;
    line-height: 1.5;
    background-color: #f5f5f5;
    border-radius: 5px;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.et_pb_testimonial_description {
    position: relative;
}

.et_pb_testimonial_description_inner {
    position: relative;
}

.et_pb_testimonial_author {
    display: block;
    margin-top: 15px;
    font-weight: 700;
    color: #333;
}

.et_pb_testimonial_meta {
    color: #666;
    padding-top: 4px;
    font-size: 12px;
}

/* Footer Styles */
#main-footer {
    background-color: #222;
    color: #fff;
}

#footer-bottom {
    background-color: #1f1f1f;
    padding: 15px 0 5px;
}

#footer-info {
    float: left;
    padding-bottom: 10px;
    color: #666;
    text-align: left;
}

#footer-nav {
    float: right;
}

.bottom-nav {
    padding: 15px 0;
    line-height: 1;
}

.bottom-nav li {
    display: inline-block;
    padding-right: 22px;
    font-size: 14px;
    font-weight: 600;
}

.bottom-nav a {
    color: #bbb;
    text-decoration: none;
}

.bottom-nav a:hover {
    color: #fff;
    opacity: 0.7;
}

/* Scroll to Top Button */
.et_pb_scroll_top {
    position: fixed;
    right: 20px;
    bottom: 20px;
    background: rgba(0, 0, 0, 0.4);
    width: 40px;
    height: 40px;
    text-align: center;
    line-height: 40px;
    color: #fff;
    cursor: pointer;
    border-radius: 3px;
    display: none;
    font-size: 30px;
    z-index: 99999;
}

/* Responsive Styles */
@media (max-width: 980px) {
    #logo {
        max-width: 50%;
    }
    
    #et-top-navigation {
        padding-top: 20px;
    }
    
    .et_pb_column {
        width: 100% !important;
        margin-right: 0 !important;
    }
}

@media (max-width: 767px) {
    .et_pb_slide_description {
        padding: 10% 8%;
    }
    
    .et_pb_slide_title {
        font-size: 26px;
    }
    
    .et_pb_slide_content {
        font-size: 14px;
    }
}
