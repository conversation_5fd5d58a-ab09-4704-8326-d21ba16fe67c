<?php
/**
 * Content Extractor Class
 *
 * This class provides functionality to extract only the content area from HTML files,
 * excluding header, footer, and menu sections.
 */
class ContentExtractor {
    /**
     * Extract content area from HTML file
     *
     * @param string $html_content The full HTML content
     * @return string The extracted content area
     */
    public static function extractContent($html_content) {
        // First try using regex to extract content (faster and more reliable for known structures)
        $content = self::extractContentWithRegex($html_content);
        $extraction_method = '';

        // If regex extraction worked, return the content
        if (!empty($content)) {
            $extraction_method = 'regex';
            // Add a hidden comment to track extraction method
            $content = "<!-- EXTRACTION_METHOD: regex -->\n" . $content;
            return $content;
        }

        // If regex failed, try DOM-based extraction
        // Use DOMDocument to parse the HTML
        $dom = new DOMDocument();

        // Suppress warnings from malformed HTML
        libxml_use_internal_errors(true);

        // Load the HTML content
        $dom->loadHTML($html_content, LIBXML_HTML_NOIMPLIED | LIBXML_HTML_NODEFDTD);

        // Clear any errors
        libxml_clear_errors();

        // Look for main content containers
        $content = self::findMainContent($dom);

        if (!empty($content)) {
            $extraction_method = 'dom';
            // Add a hidden comment to track extraction method
            $content = "<!-- EXTRACTION_METHOD: dom -->\n" . $content;
            return $content;
        }

        // If no main content found, try to extract content between header and footer
        if (empty($content)) {
            $content = self::extractContentBetweenHeaderAndFooter($html_content);
            if (!empty($content)) {
                $extraction_method = 'header_footer';
                // Add a hidden comment to track extraction method
                $content = "<!-- EXTRACTION_METHOD: header_footer -->\n" . $content;
                return $content;
            }
        }

        // If still no content, return the original but with a warning comment
        if (empty($content)) {
            return "<!-- WARNING: Could not extract content area. Showing full HTML. -->\n" . $html_content;
        }

        return $content;
    }

    /**
     * Extract content using regex patterns specific to the site structure
     *
     * @param string $html_content The full HTML content
     * @return string The extracted content
     */
    private static function extractContentWithRegex($html_content) {
        // Remove everything before the main content
        $patterns = [
            // Pattern to remove header and navigation
            '/^.*?<div class="admin-container">/is',
            '/^.*?<main[^>]*>/is',
            '/^.*?<div[^>]*class="[^"]*main-content[^"]*"[^>]*>/is',
            '/^.*?<div[^>]*id="content"[^>]*>/is',
            '/^.*?<div[^>]*class="[^"]*content[^"]*"[^>]*>/is',
            '/^.*?<article[^>]*>/is',
            '/^.*?<div[^>]*class="[^"]*page-content[^"]*"[^>]*>/is',
            '/^.*?<div[^>]*id="main-content"[^>]*>/is',
            '/^.*?<div[^>]*class="[^"]*container[^"]*"[^>]*>/is',
            // Additional patterns for specific site structures
            '/^.*?<div[^>]*class="[^"]*page-wrapper[^"]*"[^>]*>/is',
            '/^.*?<div[^>]*class="[^"]*site-content[^"]*"[^>]*>/is',
            '/^.*?<div[^>]*class="[^"]*entry-content[^"]*"[^>]*>/is',
            '/^.*?<section[^>]*class="[^"]*content[^"]*"[^>]*>/is',
            '/^.*?<section[^>]*id="[^"]*content[^"]*"[^>]*>/is',
            '/^.*?<div[^>]*class="[^"]*inner-content[^"]*"[^>]*>/is',
            '/^.*?<div[^>]*class="[^"]*page-content-wrapper[^"]*"[^>]*>/is'
        ];

        // Pattern to remove footer and everything after it
        $end_patterns = [
            '/<\/div>\s*<footer.*$/is',
            '/<\/main>\s*.*$/is',
            '/<\/div>\s*<\/div>\s*<footer.*$/is',
            '/<\/article>\s*.*$/is',
            '/<\/div>\s*<\/main>.*$/is',
            '/<\/div>\s*<\/div>\s*<\/main>.*$/is',
            // Additional patterns for specific site structures
            '/<\/section>\s*<footer.*$/is',
            '/<\/section>\s*<\/div>\s*<footer.*$/is',
            '/<\/div>\s*<\/div>\s*<\/div>\s*<footer.*$/is',
            '/<\/div>\s*<\/section>\s*<footer.*$/is',
            '/<\/div>\s*<\/div>\s*<\/section>\s*<footer.*$/is',
            '/<\/div>\s*<\/div>\s*<\/div>\s*<\/main>.*$/is'
        ];

        // Try each pattern
        $content = $html_content;
        $modified = false;

        foreach ($patterns as $pattern) {
            if (preg_match($pattern, $content)) {
                $content = preg_replace($pattern, '', $content);
                $modified = true;
                break;
            }
        }

        foreach ($end_patterns as $pattern) {
            if (preg_match($pattern, $content)) {
                $content = preg_replace($pattern, '', $content);
                $modified = true;
                break;
            }
        }

        // If we couldn't modify the content with our patterns, return empty
        if (!$modified) {
            return '';
        }

        return trim($content);
    }

    /**
     * Find the main content area in the DOM
     *
     * @param DOMDocument $dom The DOM document
     * @return string The extracted content
     */
    private static function findMainContent($dom) {
        // Common content container selectors
        $content_selectors = [
            'main',                  // HTML5 main element
            '.main-content',         // Common class for main content
            '#content',              // Common ID for content
            '.content',              // Common class for content
            'article',               // HTML5 article element
            '.page-content',         // Common class for page content
            '#main-content',         // Common ID for main content
            '.container .row',       // Bootstrap-like structure
            '.container',            // Generic container
            '.page-wrapper',         // Common wrapper class
            '.site-content',         // WordPress common class
            '.entry-content',        // WordPress content class
            'section.content',       // Section with content class
            '#page-content',         // Page content ID
            '.inner-content',        // Inner content container
            '.page-content-wrapper'  // Page content wrapper
        ];

        // Try to find content using each selector
        foreach ($content_selectors as $selector) {
            $content = self::getElementBySelector($dom, $selector);
            if (!empty($content)) {
                return $content;
            }
        }

        return '';
    }

    /**
     * Get element by CSS-like selector
     *
     * @param DOMDocument $dom The DOM document
     * @param string $selector Simple CSS-like selector
     * @return string The element's HTML content
     */
    private static function getElementBySelector($dom, $selector) {
        // Parse the selector
        if (strpos($selector, '#') === 0) {
            // ID selector
            $id = substr($selector, 1);
            $element = $dom->getElementById($id);
            if ($element) {
                return $dom->saveHTML($element);
            }
        } elseif (strpos($selector, '.') === 0) {
            // Class selector
            $class = substr($selector, 1);
            $xpath = new DOMXPath($dom);
            $elements = $xpath->query("//*[contains(@class, '$class')]");
            if ($elements && $elements->length > 0) {
                return $dom->saveHTML($elements->item(0));
            }
        } elseif (strpos($selector, ' ') !== false) {
            // Simple descendant selector (e.g., '.container .row')
            $parts = explode(' ', $selector);
            $parent_selector = $parts[0];
            $child_selector = $parts[1];

            $parent_html = self::getElementBySelector($dom, $parent_selector);
            if (!empty($parent_html)) {
                $parent_dom = new DOMDocument();
                libxml_use_internal_errors(true);
                $parent_dom->loadHTML($parent_html, LIBXML_HTML_NOIMPLIED | LIBXML_HTML_NODEFDTD);
                libxml_clear_errors();

                return self::getElementBySelector($parent_dom, $child_selector);
            }
        } else {
            // Tag selector
            $elements = $dom->getElementsByTagName($selector);
            if ($elements && $elements->length > 0) {
                return $dom->saveHTML($elements->item(0));
            }
        }

        return '';
    }

    /**
     * Extract content between header and footer using regex
     *
     * @param string $html_content The full HTML content
     * @return string The extracted content
     */
    private static function extractContentBetweenHeaderAndFooter($html_content) {
        // Try to find content between header and footer
        $pattern = '/<header.*?<\/header>(.*)<footer.*?<\/footer>/is';
        if (preg_match($pattern, $html_content, $matches)) {
            return trim($matches[1]);
        }

        // Try with div with header/footer classes
        $pattern = '/<div[^>]*class="[^"]*header[^"]*".*?<\/div>(.*)<div[^>]*class="[^"]*footer[^"]*".*?<\/div>/is';
        if (preg_match($pattern, $html_content, $matches)) {
            return trim($matches[1]);
        }

        // Try with common navigation patterns
        $pattern = '/<nav.*?<\/nav>(.*)<footer.*?<\/footer>/is';
        if (preg_match($pattern, $html_content, $matches)) {
            return trim($matches[1]);
        }

        return '';
    }

    /**
     * Save content back to HTML file, preserving the structure
     *
     * @param string $original_html The original HTML content
     * @param string $edited_content The edited content area
     * @return string The full HTML with updated content
     */
    public static function injectContent($original_html, $edited_content) {
        // Check if the edited content contains extraction method information
        $extraction_method = '';
        if (preg_match('/<!-- EXTRACTION_METHOD: ([a-z_]+) -->/i', $edited_content, $matches)) {
            $extraction_method = $matches[1];
            // Remove the extraction method comment from the content
            $edited_content = preg_replace('/<!-- EXTRACTION_METHOD: [a-z_]+ -->\n?/i', '', $edited_content);
        }

        // Log the extraction method for debugging
        error_log("Content extraction method: " . ($extraction_method ?: 'unknown'));

        // If extraction was done with regex, use regex for injection
        if ($extraction_method === 'regex') {
            $result = self::injectContentWithRegex($original_html, $edited_content);
            if ($result !== $original_html) {
                return $result;
            }
        }

        // If extraction was done with DOM, try DOM-based injection first
        if ($extraction_method === 'dom' || $extraction_method === 'header_footer' || empty($extraction_method)) {
            // Use DOMDocument to parse the HTML
            $dom = new DOMDocument();

            // Suppress warnings from malformed HTML
            libxml_use_internal_errors(true);

            // Load the HTML content
            $dom->loadHTML($original_html, LIBXML_HTML_NOIMPLIED | LIBXML_HTML_NODEFDTD);

            // Clear any errors
            libxml_clear_errors();

            // Try to find and replace the main content
            $replaced = self::replaceMainContent($dom, $edited_content);

            if ($replaced) {
                return $dom->saveHTML();
            }
        }

        // If all else fails, try regex-based replacement as a fallback
        $result = self::injectContentWithRegex($original_html, $edited_content);

        // If regex replacement also failed, try a last-resort approach
        if ($result === $original_html) {
            $result = self::injectContentLastResort($original_html, $edited_content);
        }

        return $result;
    }

    /**
     * Replace main content in the DOM
     *
     * @param DOMDocument $dom The DOM document
     * @param string $edited_content The edited content
     * @return bool Whether replacement was successful
     */
    private static function replaceMainContent($dom, $edited_content) {
        // Common content container selectors
        $content_selectors = [
            'main',                  // HTML5 main element
            '.main-content',         // Common class for main content
            '#content',              // Common ID for content
            '.content',              // Common class for content
            'article',               // HTML5 article element
            '.page-content',         // Common class for page content
            '#main-content',         // Common ID for main content
            '.container .row',       // Bootstrap-like structure
            '.container',            // Generic container
            '.page-wrapper',         // Common wrapper class
            '.site-content',         // WordPress common class
            '.entry-content',        // WordPress content class
            'section.content',       // Section with content class
            '#page-content',         // Page content ID
            '.inner-content',        // Inner content container
            '.page-content-wrapper'  // Page content wrapper
        ];

        // Try to find and replace content using each selector
        foreach ($content_selectors as $selector) {
            $element = null;

            // Parse the selector
            if (strpos($selector, '#') === 0) {
                // ID selector
                $id = substr($selector, 1);
                $element = $dom->getElementById($id);
            } elseif (strpos($selector, '.') === 0) {
                // Class selector
                $class = substr($selector, 1);
                $xpath = new DOMXPath($dom);
                $elements = $xpath->query("//*[contains(@class, '$class')]");
                if ($elements && $elements->length > 0) {
                    $element = $elements->item(0);
                }
            } elseif (strpos($selector, ' ') !== false) {
                // Simple descendant selector (e.g., '.container .row')
                // Skip for now, handled by other selectors
            } else {
                // Tag selector
                $elements = $dom->getElementsByTagName($selector);
                if ($elements && $elements->length > 0) {
                    $element = $elements->item(0);
                }
            }

            // If element found, replace its content
            if ($element) {
                // Create a new document for the edited content
                $content_dom = new DOMDocument();
                libxml_use_internal_errors(true);

                // Wrap the content in a container to ensure proper parsing
                $wrapped_content = '<div id="content-wrapper">' . $edited_content . '</div>';
                $content_dom->loadHTML('<!DOCTYPE html><html><head><meta charset="UTF-8"></head><body>' . $wrapped_content . '</body></html>', LIBXML_HTML_NOIMPLIED | LIBXML_HTML_NODEFDTD);
                libxml_clear_errors();

                // Clear the element's content while preserving attributes
                while ($element->hasChildNodes()) {
                    $element->removeChild($element->firstChild);
                }

                // Get the wrapper element
                $xpath = new DOMXPath($content_dom);
                $wrapper = $xpath->query('//div[@id="content-wrapper"]')->item(0);

                if ($wrapper) {
                    // Import and append each child of the wrapper
                    foreach ($wrapper->childNodes as $node) {
                        $imported = $dom->importNode($node, true);
                        $element->appendChild($imported);
                    }
                } else {
                    // Fallback: try to get content from body
                    $body = $content_dom->getElementsByTagName('body')->item(0);
                    if ($body) {
                        foreach ($body->childNodes as $node) {
                            $imported = $dom->importNode($node, true);
                            $element->appendChild($imported);
                        }
                    }
                }

                return true;
            }
        }

        return false;
    }

    /**
     * Inject content using regex
     *
     * @param string $original_html The original HTML content
     * @param string $edited_content The edited content
     * @return string The full HTML with updated content
     */
    private static function injectContentWithRegex($original_html, $edited_content) {
        // Try to find and replace content using regex patterns
        $patterns = [
            // Main content patterns
            '/<main[^>]*>(.*?)<\/main>/is' => function($matches) use ($edited_content) {
                // Extract the opening tag
                preg_match('/<main[^>]*>/is', $matches[0], $openTag);
                // Extract the closing tag
                preg_match('/<\/main>/is', $matches[0], $closeTag);
                // Reconstruct with the edited content
                return $openTag[0] . $edited_content . $closeTag[0];
            },
            '/<div[^>]*class="[^"]*main-content[^"]*"[^>]*>(.*?)<\/div>/is' => function($matches) use ($edited_content) {
                // Extract the opening tag
                preg_match('/<div[^>]*class="[^"]*main-content[^"]*"[^>]*>/is', $matches[0], $openTag);
                // Extract the closing tag
                preg_match('/<\/div>/is', $matches[0], $closeTag);
                // Reconstruct with the edited content
                return $openTag[0] . $edited_content . $closeTag[0];
            },
            '/<div[^>]*id="content"[^>]*>(.*?)<\/div>/is' => function($matches) use ($edited_content) {
                // Extract the opening tag
                preg_match('/<div[^>]*id="content"[^>]*>/is', $matches[0], $openTag);
                // Extract the closing tag
                preg_match('/<\/div>/is', $matches[0], $closeTag);
                // Reconstruct with the edited content
                return $openTag[0] . $edited_content . $closeTag[0];
            },
            '/<div[^>]*class="[^"]*content[^"]*"[^>]*>(.*?)<\/div>/is' => function($matches) use ($edited_content) {
                // Extract the opening tag
                preg_match('/<div[^>]*class="[^"]*content[^"]*"[^>]*>/is', $matches[0], $openTag);
                // Extract the closing tag
                preg_match('/<\/div>/is', $matches[0], $closeTag);
                // Reconstruct with the edited content
                return $openTag[0] . $edited_content . $closeTag[0];
            },
            '/<article[^>]*>(.*?)<\/article>/is' => function($matches) use ($edited_content) {
                // Extract the opening tag
                preg_match('/<article[^>]*>/is', $matches[0], $openTag);
                // Extract the closing tag
                preg_match('/<\/article>/is', $matches[0], $closeTag);
                // Reconstruct with the edited content
                return $openTag[0] . $edited_content . $closeTag[0];
            },
            '/<div[^>]*class="[^"]*page-content[^"]*"[^>]*>(.*?)<\/div>/is' => function($matches) use ($edited_content) {
                // Extract the opening tag
                preg_match('/<div[^>]*class="[^"]*page-content[^"]*"[^>]*>/is', $matches[0], $openTag);
                // Extract the closing tag
                preg_match('/<\/div>/is', $matches[0], $closeTag);
                // Reconstruct with the edited content
                return $openTag[0] . $edited_content . $closeTag[0];
            },
            '/<div[^>]*id="main-content"[^>]*>(.*?)<\/div>/is' => function($matches) use ($edited_content) {
                // Extract the opening tag
                preg_match('/<div[^>]*id="main-content"[^>]*>/is', $matches[0], $openTag);
                // Extract the closing tag
                preg_match('/<\/div>/is', $matches[0], $closeTag);
                // Reconstruct with the edited content
                return $openTag[0] . $edited_content . $closeTag[0];
            },
            '/<div[^>]*class="[^"]*container[^"]*"[^>]*>(.*?)<\/div>/is' => function($matches) use ($edited_content) {
                // Extract the opening tag
                preg_match('/<div[^>]*class="[^"]*container[^"]*"[^>]*>/is', $matches[0], $openTag);
                // Extract the closing tag
                preg_match('/<\/div>/is', $matches[0], $closeTag);
                // Reconstruct with the edited content
                return $openTag[0] . $edited_content . $closeTag[0];
            },

            // Additional patterns for specific site structures
            '/<div[^>]*class="[^"]*page-wrapper[^"]*"[^>]*>(.*?)<\/div>/is' => function($matches) use ($edited_content) {
                // Extract the opening tag
                preg_match('/<div[^>]*class="[^"]*page-wrapper[^"]*"[^>]*>/is', $matches[0], $openTag);
                // Extract the closing tag
                preg_match('/<\/div>/is', $matches[0], $closeTag);
                // Reconstruct with the edited content
                return $openTag[0] . $edited_content . $closeTag[0];
            },
            '/<div[^>]*class="[^"]*site-content[^"]*"[^>]*>(.*?)<\/div>/is' => function($matches) use ($edited_content) {
                // Extract the opening tag
                preg_match('/<div[^>]*class="[^"]*site-content[^"]*"[^>]*>/is', $matches[0], $openTag);
                // Extract the closing tag
                preg_match('/<\/div>/is', $matches[0], $closeTag);
                // Reconstruct with the edited content
                return $openTag[0] . $edited_content . $closeTag[0];
            },
            '/<div[^>]*class="[^"]*entry-content[^"]*"[^>]*>(.*?)<\/div>/is' => function($matches) use ($edited_content) {
                // Extract the opening tag
                preg_match('/<div[^>]*class="[^"]*entry-content[^"]*"[^>]*>/is', $matches[0], $openTag);
                // Extract the closing tag
                preg_match('/<\/div>/is', $matches[0], $closeTag);
                // Reconstruct with the edited content
                return $openTag[0] . $edited_content . $closeTag[0];
            },
            '/<section[^>]*class="[^"]*content[^"]*"[^>]*>(.*?)<\/section>/is' => function($matches) use ($edited_content) {
                // Extract the opening tag
                preg_match('/<section[^>]*class="[^"]*content[^"]*"[^>]*>/is', $matches[0], $openTag);
                // Extract the closing tag
                preg_match('/<\/section>/is', $matches[0], $closeTag);
                // Reconstruct with the edited content
                return $openTag[0] . $edited_content . $closeTag[0];
            },
            '/<div[^>]*id="page-content"[^>]*>(.*?)<\/div>/is' => function($matches) use ($edited_content) {
                // Extract the opening tag
                preg_match('/<div[^>]*id="page-content"[^>]*>/is', $matches[0], $openTag);
                // Extract the closing tag
                preg_match('/<\/div>/is', $matches[0], $closeTag);
                // Reconstruct with the edited content
                return $openTag[0] . $edited_content . $closeTag[0];
            },
            '/<div[^>]*class="[^"]*inner-content[^"]*"[^>]*>(.*?)<\/div>/is' => function($matches) use ($edited_content) {
                // Extract the opening tag
                preg_match('/<div[^>]*class="[^"]*inner-content[^"]*"[^>]*>/is', $matches[0], $openTag);
                // Extract the closing tag
                preg_match('/<\/div>/is', $matches[0], $closeTag);
                // Reconstruct with the edited content
                return $openTag[0] . $edited_content . $closeTag[0];
            },
            '/<div[^>]*class="[^"]*page-content-wrapper[^"]*"[^>]*>(.*?)<\/div>/is' => function($matches) use ($edited_content) {
                // Extract the opening tag
                preg_match('/<div[^>]*class="[^"]*page-content-wrapper[^"]*"[^>]*>/is', $matches[0], $openTag);
                // Extract the closing tag
                preg_match('/<\/div>/is', $matches[0], $closeTag);
                // Reconstruct with the edited content
                return $openTag[0] . $edited_content . $closeTag[0];
            },

            // Header/footer patterns
            '/<header.*?<\/header>(.*)<footer.*?<\/footer>/is' => function($matches) use ($edited_content) {
                // Extract the header
                preg_match('/<header.*?<\/header>/is', $matches[0], $header);
                // Extract the footer
                preg_match('/<footer.*?<\/footer>/is', $matches[0], $footer);
                // Reconstruct with the edited content
                return $header[0] . $edited_content . $footer[0];
            }
        ];

        // Try each pattern
        foreach ($patterns as $pattern => $replacement) {
            if (preg_match($pattern, $original_html)) {
                $result = preg_replace_callback($pattern, $replacement, $original_html);
                if ($result !== null && $result !== $original_html) {
                    return $result;
                }
            }
        }

        // If no pattern matched, return the original HTML
        return $original_html;
    }

    /**
     * Last resort method for injecting content when all other methods fail
     *
     * @param string $original_html The original HTML content
     * @param string $edited_content The edited content
     * @return string The full HTML with updated content
     */
    private static function injectContentLastResort($original_html, $edited_content) {
        // Try to find the <body> tag and inject content there
        if (preg_match('/<body[^>]*>(.*)<\/body>/is', $original_html, $matches)) {
            // Get everything before and after the body content
            $parts = preg_split('/<body[^>]*>(.*)<\/body>/is', $original_html, -1, PREG_SPLIT_DELIM_CAPTURE);

            if (count($parts) >= 3) {
                $before_body = $parts[0];
                $body_tag_start = substr($original_html, strpos($original_html, '<body'), strpos($original_html, '>', strpos($original_html, '<body')) - strpos($original_html, '<body') + 1);
                $after_body = $parts[2];

                // Create a new body content with the edited content wrapped in a div
                $new_body_content = '<div class="content-wrapper">' . $edited_content . '</div>';

                // Reconstruct the HTML
                return $before_body . $body_tag_start . $new_body_content . '</body>' . $after_body;
            }
        }

        // If we can't find the body tag, just add a warning comment and return the original
        error_log("WARNING: Could not find a suitable location to inject content. Returning original HTML.");
        return "<!-- WARNING: Content editing failed. The original structure has been preserved. -->\n" . $original_html;
    }
}
