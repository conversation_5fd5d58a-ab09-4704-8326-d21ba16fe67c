<?php
session_start();
require_once 'config.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header('Location: index.php');
    exit;
}

// Include permissions class
require_once 'lib/Permissions.php';

// Initialize permission system
$permissions = new Permissions($conn, $_SESSION['user_id']);

// Check if user has permission to manage settings
if (!$permissions->hasPermission('manage_settings')) {
    $_SESSION['error_message'] = "You do not have permission to manage system settings.";
    header('Location: dashboard.php');
    exit;
}

// Define settings categories
$categories = [
    'general' => 'General',
    'email' => 'Email',
    'appearance' => 'Appearance',
    'fonts' => 'Fonts',
    'news' => 'News',
    'security' => 'Security'
];

// Define category icons
$category_icons = [
    'general' => 'fa-cog',
    'email' => 'fa-envelope',
    'appearance' => 'fa-palette',
    'fonts' => 'fa-font',
    'news' => 'fa-newspaper',
    'security' => 'fa-shield-alt'
];

// Get active category from URL or default to first category
$active_category = isset($_GET['category']) && array_key_exists($_GET['category'], $categories) ? $_GET['category'] : 'general';

// Initialize variables
$success = '';
$error = '';

// Function to get settings by category
function getSettingsByCategory($conn, $category) {
    $stmt = $conn->prepare("SELECT * FROM system_settings WHERE category = ? ORDER BY order_index");
    $stmt->bind_param("s", $category);
    $stmt->execute();
    $result = $stmt->get_result();

    $settings = [];
    while ($row = $result->fetch_assoc()) {
        $settings[$row['setting_key']] = $row;
    }

    return $settings;
}

// Function to update a setting
function updateSetting($conn, $category, $key, $value) {
    $stmt = $conn->prepare("UPDATE system_settings SET setting_value = ? WHERE category = ? AND setting_key = ?");
    $stmt->bind_param("sss", $value, $category, $key);
    return $stmt->execute();
}

// Function to get category descriptions
function getCategoryDescription($category) {
    $descriptions = [
        'general' => 'Basic site configuration and global settings',
        'email' => 'Email delivery and SMTP configuration',
        'appearance' => 'Visual customization and branding options',
        'fonts' => 'Typography and font settings',
        'news' => 'Content management and publishing options',
        'security' => 'Security policies and access controls'
    ];
    return $descriptions[$category] ?? 'Configure settings for this category';
}

// Function to organize settings by logical sections
function organizeSettingsBySection($settings, $category) {
    $organized = [];

    switch ($category) {
        case 'general':
            $organized['Site Information'] = [];
            $organized['Regional Settings'] = [];
            $organized['System Configuration'] = [];
            break;
        case 'email':
            $organized['Basic Email Settings'] = [];
            $organized['SMTP Configuration'] = [];
            $organized['Email Templates'] = [];
            break;
        case 'appearance':
            $organized['Branding'] = [];
            $organized['Colors & Themes'] = [];
            $organized['Layout Options'] = [];
            break;
        case 'fonts':
            $organized['Typography'] = [];
            $organized['Font Families'] = [];
            $organized['Font Sizes'] = [];
            break;
        case 'news':
            $organized['Publishing'] = [];
            $organized['Content Options'] = [];
            $organized['SEO Settings'] = [];
            break;
        case 'security':
            $organized['Authentication'] = [];
            $organized['Access Control'] = [];
            $organized['Security Policies'] = [];
            break;
        default:
            $organized['General'] = [];
            break;
    }

    // Distribute settings into sections based on their keys
    foreach ($settings as $key => $setting) {
        $section = categorizeSettingByKey($key, $category);
        if (isset($organized[$section])) {
            $organized[$section][$key] = $setting;
        } else {
            // Fallback to first section
            $first_section = array_key_first($organized);
            $organized[$first_section][$key] = $setting;
        }
    }

    // Remove empty sections
    return array_filter($organized, function($section) {
        return !empty($section);
    });
}

// Function to categorize settings by their key
function categorizeSettingByKey($key, $category) {
    $patterns = [
        'general' => [
            'Site Information' => ['site_name', 'site_description', 'site_url', 'admin_email'],
            'Regional Settings' => ['timezone', 'language', 'date_format', 'time_format'],
            'System Configuration' => ['maintenance_mode', 'debug_mode', 'cache_enabled']
        ],
        'email' => [
            'Basic Email Settings' => ['from_email', 'from_name', 'reply_to'],
            'SMTP Configuration' => ['use_smtp', 'smtp_host', 'smtp_port', 'smtp_username', 'smtp_password', 'smtp_security'],
            'Email Templates' => ['email_template', 'notification_template']
        ],
        'appearance' => [
            'Branding' => ['logo', 'favicon', 'brand_color'],
            'Colors & Themes' => ['primary_color', 'secondary_color', 'theme'],
            'Layout Options' => ['layout', 'sidebar_position', 'header_style']
        ],
        'fonts' => [
            'Typography' => ['body_font', 'heading_font'],
            'Font Families' => ['font_family', 'google_fonts'],
            'Font Sizes' => ['font_size', 'heading_size']
        ],
        'news' => [
            'Publishing' => ['auto_publish', 'moderation'],
            'Content Options' => ['excerpt_length', 'comments_enabled'],
            'SEO Settings' => ['meta_description', 'keywords']
        ],
        'security' => [
            'Authentication' => ['password_policy', 'two_factor'],
            'Access Control' => ['login_attempts', 'session_timeout'],
            'Security Policies' => ['ssl_required', 'csrf_protection']
        ]
    ];

    if (isset($patterns[$category])) {
        foreach ($patterns[$category] as $section => $keys) {
            if (in_array($key, $keys) ||
                array_filter($keys, function($pattern) use ($key) {
                    return strpos($key, $pattern) !== false;
                })) {
                return $section;
            }
        }
    }

    // Default section
    return array_key_first($patterns[$category] ?? ['General' => []]) ?: 'General';
}

// Function to get section icons
function getSectionIcon($section) {
    $icons = [
        'Site Information' => 'fa-info-circle',
        'Regional Settings' => 'fa-globe',
        'System Configuration' => 'fa-cogs',
        'Basic Email Settings' => 'fa-envelope',
        'SMTP Configuration' => 'fa-server',
        'Email Templates' => 'fa-file-alt',
        'Branding' => 'fa-paint-brush',
        'Colors & Themes' => 'fa-palette',
        'Layout Options' => 'fa-layout',
        'Typography' => 'fa-font',
        'Font Families' => 'fa-text-height',
        'Font Sizes' => 'fa-text-width',
        'Publishing' => 'fa-newspaper',
        'Content Options' => 'fa-edit',
        'SEO Settings' => 'fa-search',
        'Authentication' => 'fa-key',
        'Access Control' => 'fa-shield-alt',
        'Security Policies' => 'fa-lock'
    ];
    return $icons[$section] ?? 'fa-cog';
}

// Function to get grid class based on number of items
function getGridClass($count) {
    if ($count <= 2) return 'grid-1-col';
    if ($count <= 4) return 'grid-2-col';
    return 'grid-3-col';
}

// Process form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['save_settings'])) {
    $category = $_POST['category'];
    $category_settings = getSettingsByCategory($conn, $category);

    foreach ($category_settings as $key => $setting) {
        if ($setting['setting_type'] === 'checkbox') {
            $value = isset($_POST[$key]) ? '1' : '0';
        } elseif ($setting['setting_type'] === 'file') {
            // Handle file upload
            $value = $setting['setting_value']; // Keep existing value by default

            if (isset($_FILES[$key . '_upload']) && $_FILES[$key . '_upload']['error'] === UPLOAD_ERR_OK) {
                // Validate file upload
                $file_info = pathinfo($_FILES[$key . '_upload']['name']);
                $allowed_extensions = ['jpg', 'jpeg', 'png', 'gif', 'webp'];
                $max_file_size = 5242880; // 5MB

                // Check file extension
                if (!isset($file_info['extension']) || !in_array(strtolower($file_info['extension']), $allowed_extensions)) {
                    $error = "Invalid file type for " . $setting['display_name'] . ". Only JPG, JPEG, PNG, GIF, and WebP files are allowed.";
                    break;
                }

                // Check file size
                if ($_FILES[$key . '_upload']['size'] > $max_file_size) {
                    $error = "File size too large for " . $setting['display_name'] . ". Maximum size is 5MB.";
                    break;
                }

                // Check if it's actually an image
                $image_info = getimagesize($_FILES[$key . '_upload']['tmp_name']);
                if ($image_info === false) {
                    $error = "Invalid image file for " . $setting['display_name'] . ". Please upload a valid image.";
                    break;
                }

                // Create uploads directory structure
                $upload_base_dir = '../uploads/';
                $upload_dir = $upload_base_dir . 'logos/';

                if (!is_dir($upload_base_dir)) {
                    mkdir($upload_base_dir, 0755, true);
                }
                if (!is_dir($upload_dir)) {
                    mkdir($upload_dir, 0755, true);
                }

                // Generate unique filename with timestamp
                $timestamp = date('Y-m-d_H-i-s');
                $new_filename = $key . '-' . $timestamp . '.' . strtolower($file_info['extension']);
                $upload_path = $upload_dir . $new_filename;
                $relative_path = 'uploads/logos/' . $new_filename; // Path relative to website root

                // Move uploaded file
                if (move_uploaded_file($_FILES[$key . '_upload']['tmp_name'], $upload_path)) {
                    $value = $relative_path;
                } else {
                    $error = "Failed to upload " . $setting['display_name'] . ". Please check file permissions.";
                    break;
                }
            }
        } else {
            $value = isset($_POST[$key]) ? trim($_POST[$key]) : '';
        }

        // Validate required fields
        if ($setting['is_required'] && empty($value)) {
            $error = $setting['display_name'] . " is required";
            break;
        }

        // Validate email fields
        if ($setting['setting_type'] === 'email' && !empty($value) && !filter_var($value, FILTER_VALIDATE_EMAIL)) {
            $error = "Invalid email format for " . $setting['display_name'];
            break;
        }

        // Update setting
        if (!updateSetting($conn, $category, $key, $value)) {
            $error = "Failed to save " . $setting['display_name'];
            break;
        }

        // Special handling for logo uploads - ensure login logo is set when admin logo is uploaded
        if ($category === 'appearance' && $key === 'admin_logo' && !empty($value)) {
            // Also set this as the login logo if login logo is not already set
            $login_logo_check = $conn->prepare("SELECT setting_value FROM system_settings WHERE category = 'appearance' AND setting_key = 'login_logo'");
            $login_logo_check->execute();
            $login_logo_result = $login_logo_check->get_result();

            if ($login_logo_result->num_rows === 0 || empty($login_logo_result->fetch_assoc()['setting_value'])) {
                // Login logo is not set, so set it to the same as admin logo
                updateSetting($conn, 'appearance', 'login_logo', $value);
                error_log("Auto-set login_logo to: " . $value);
            }
        }
    }

    if (empty($error)) {
        // Log the activity
        require_once 'includes/admin-functions.php';
        log_activity('update', 'Updated ' . $categories[$category] . ' settings', $_SESSION['user_id']);

        $success = "Settings saved successfully";
    }
}

// Get settings for the active category
$category_settings = getSettingsByCategory($conn, $active_category);

// Set page title and metadata
$page_title = "Settings";
$page_icon = 'fas fa-cog';
$page_subtitle = 'Configure system settings and preferences';

// Add page-specific CSS
$extra_css = '<link rel="stylesheet" href="assets/css/pages/settings.css?v=' . time() . '">';
?>
<?php include 'includes/header.php'; ?>

<div class="admin-container">
    <div class="admin-content-header">
        <div class="admin-content-title-group">
            <h2 class="admin-content-title"><i class="<?php echo $page_icon; ?>"></i> <?php echo $page_title; ?></h2>
            <p class="admin-content-subtitle"><?php echo $page_subtitle; ?></p>
        </div>
    </div>

    <?php if (!empty($success)): ?>
    <div class="alert alert-success">
        <i class="fas fa-check-circle"></i> <?php echo $success; ?>
    </div>
    <?php endif; ?>

    <?php if (!empty($error)): ?>
    <div class="alert alert-danger">
        <i class="fas fa-exclamation-circle"></i> <?php echo $error; ?>
    </div>
    <?php endif; ?>

    <div class="admin-content-body">
        <div class="settings-container">
            <div class="settings-sidebar">
                <div class="settings-sidebar-header">
                    <h3><i class="fas fa-cog"></i> Settings Categories</h3>
                    <p>Configure your system preferences</p>
                </div>

                <ul class="settings-nav">
                    <?php foreach ($categories as $category_key => $category_name): ?>
                    <li class="settings-nav-item <?php echo $active_category === $category_key ? 'active' : ''; ?>">
                        <a href="settings.php?category=<?php echo $category_key; ?>" class="settings-nav-link" data-category="<?php echo $category_key; ?>">
                            <div class="nav-icon">
                                <i class="fas <?php echo $category_icons[$category_key]; ?>"></i>
                            </div>
                            <div class="nav-content">
                                <span class="nav-title"><?php echo $category_name; ?></span>
                                <span class="nav-desc"><?php echo getCategoryDescription($category_key); ?></span>
                            </div>
                            <div class="nav-arrow">
                                <i class="fas fa-chevron-right"></i>
                            </div>
                        </a>
                    </li>
                    <?php endforeach; ?>
                </ul>

                <div class="settings-sidebar-footer">
                    <div class="help-card">
                        <i class="fas fa-question-circle"></i>
                        <h4>Need Help?</h4>
                        <p>Check our documentation for detailed configuration guides.</p>
                        <a href="help.html" class="help-link">View Help <i class="fas fa-external-link-alt"></i></a>
                    </div>
                </div>
            </div>

            <div class="settings-content">
                <!-- Loading overlay -->
                <div class="settings-loading-overlay" id="settingsLoadingOverlay" style="display: none;">
                    <div class="loading-spinner">
                        <i class="fas fa-spinner fa-spin"></i>
                        <span>Loading settings...</span>
                    </div>
                </div>

                <form method="post" action="settings.php?category=<?php echo $active_category; ?>" class="settings-form" enctype="multipart/form-data">
                    <input type="hidden" name="category" value="<?php echo $active_category; ?>">

                    <div class="settings-section active" id="<?php echo $active_category; ?>-settings">
                        <div class="settings-header">
                            <div class="settings-title-group">
                                <h3 class="settings-section-title">
                                    <i class="fas <?php echo $category_icons[$active_category]; ?>"></i>
                                    <?php echo $categories[$active_category]; ?> Settings
                                </h3>
                                <p class="settings-section-desc"><?php echo getCategoryDescription($active_category); ?></p>
                            </div>
                        </div>

                        <?php if (empty($category_settings)): ?>
                            <div class="no-settings">
                                <div class="no-settings-icon">
                                    <i class="fas fa-info-circle"></i>
                                </div>
                                <h4>No Settings Available</h4>
                                <p>No settings are configured for this category yet.</p>
                            </div>
                        <?php else: ?>
                            <!-- Organize settings into logical sections -->
                            <?php
                            $organized_settings = organizeSettingsBySection($category_settings, $active_category);
                            foreach ($organized_settings as $section_name => $section_settings):
                            ?>
                            <div class="settings-subsection">
                                <h4 class="subsection-title">
                                    <i class="fas <?php echo getSectionIcon($section_name); ?>"></i>
                                    <?php echo $section_name; ?>
                                </h4>

                                <div class="form-grid <?php echo getGridClass(count($section_settings)); ?>">
                                    <?php foreach ($section_settings as $key => $setting): ?>
                                        <div class="form-group">
                                            <label for="<?php echo $key; ?>">
                                                <?php echo htmlspecialchars($setting['display_name']); ?>
                                                <?php if ($setting['is_required']): ?>
                                                    <span class="required">*</span>
                                                <?php endif; ?>
                                            </label>

                                            <?php
                                            // Only show descriptions for complex fields that need explanation
                                            $show_description = in_array($setting['setting_type'], ['password', 'select', 'color']) ||
                                                              in_array($key, ['smtp_port', 'smtp_security', 'timezone', 'date_format']);
                                            if (!empty($setting['description']) && $show_description):
                                            ?>
                                                <p class="form-hint"><?php echo htmlspecialchars($setting['description']); ?></p>
                                            <?php endif; ?>

                                            <?php if ($setting['setting_type'] === 'text'): ?>
                                                <input type="text" id="<?php echo $key; ?>" name="<?php echo $key; ?>"
                                                       class="form-control" value="<?php echo htmlspecialchars($setting['setting_value']); ?>"
                                                       <?php echo $setting['is_required'] ? 'required' : ''; ?>>

                                            <?php elseif ($setting['setting_type'] === 'email'): ?>
                                                <input type="email" id="<?php echo $key; ?>" name="<?php echo $key; ?>"
                                                       class="form-control" value="<?php echo htmlspecialchars($setting['setting_value']); ?>"
                                                       <?php echo $setting['is_required'] ? 'required' : ''; ?>>

                                            <?php elseif ($setting['setting_type'] === 'number'): ?>
                                                <input type="number" id="<?php echo $key; ?>" name="<?php echo $key; ?>"
                                                       class="form-control" value="<?php echo htmlspecialchars($setting['setting_value']); ?>"
                                                       <?php echo $setting['is_required'] ? 'required' : ''; ?>>

                                            <?php elseif ($setting['setting_type'] === 'password'): ?>
                                                <div class="password-input-group">
                                                    <input type="password" id="<?php echo $key; ?>" name="<?php echo $key; ?>"
                                                           class="form-control" value="<?php echo htmlspecialchars($setting['setting_value']); ?>">
                                                    <button type="button" class="password-toggle" onclick="togglePassword('<?php echo $key; ?>')">
                                                        <i class="fas fa-eye"></i>
                                                    </button>
                                                </div>

                                            <?php elseif ($setting['setting_type'] === 'textarea'): ?>
                                                <textarea id="<?php echo $key; ?>" name="<?php echo $key; ?>" class="form-control" rows="3"
                                                          <?php echo $setting['is_required'] ? 'required' : ''; ?>><?php echo htmlspecialchars($setting['setting_value']); ?></textarea>

                                            <?php elseif ($setting['setting_type'] === 'checkbox'): ?>
                                                <div class="checkbox-wrapper">
                                                    <label class="checkbox-label">
                                                        <input type="checkbox" name="<?php echo $key; ?>" value="1"
                                                               <?php echo $setting['setting_value'] ? 'checked' : ''; ?>>
                                                        <span class="checkbox-text">Enable this option</span>
                                                    </label>
                                                </div>

                                            <?php elseif ($setting['setting_type'] === 'select'): ?>
                                                <select id="<?php echo $key; ?>" name="<?php echo $key; ?>" class="form-control"
                                                        <?php echo $setting['is_required'] ? 'required' : ''; ?>>
                                                    <?php
                                                    $options = json_decode($setting['options'], true);
                                                    if ($options):
                                                        foreach ($options as $value => $label): ?>
                                                            <option value="<?php echo htmlspecialchars($value); ?>"
                                                                    <?php echo $setting['setting_value'] === $value ? 'selected' : ''; ?>>
                                                                <?php echo htmlspecialchars($label); ?>
                                                            </option>
                                                        <?php endforeach;
                                                    endif; ?>
                                                </select>

                                            <?php elseif ($setting['setting_type'] === 'color'): ?>
                                                <div class="color-input-group">
                                                    <input type="color" id="<?php echo $key; ?>" name="<?php echo $key; ?>"
                                                           class="color-picker" value="<?php echo htmlspecialchars($setting['setting_value']); ?>">
                                                    <input type="text" class="color-text form-control"
                                                           value="<?php echo htmlspecialchars($setting['setting_value']); ?>"
                                                           onchange="document.getElementById('<?php echo $key; ?>').value = this.value">
                                                </div>

                                            <?php elseif ($setting['setting_type'] === 'file'): ?>
                                                <div class="file-upload-group">
                                                    <input type="file" id="<?php echo $key; ?>_upload" name="<?php echo $key; ?>_upload"
                                                           class="form-control" accept="image/*" onchange="previewLogo('<?php echo $key; ?>')">
                                                    <input type="hidden" id="<?php echo $key; ?>" name="<?php echo $key; ?>"
                                                           value="<?php echo htmlspecialchars($setting['setting_value']); ?>">
                                                    <?php if (!empty($setting['setting_value'])): ?>
                                                        <div class="logo-preview-container" id="<?php echo $key; ?>_preview_container">
                                                            <img id="<?php echo $key; ?>_preview" src="../<?php echo htmlspecialchars($setting['setting_value']); ?>"
                                                                 alt="Logo Preview" class="logo-preview">
                                                            <button type="button" class="btn btn-sm btn-danger" onclick="removeLogo('<?php echo $key; ?>')">
                                                                <i class="fas fa-trash"></i> Remove
                                                            </button>
                                                        </div>
                                                    <?php else: ?>
                                                        <div class="logo-preview-container" id="<?php echo $key; ?>_preview_container" style="display: none;">
                                                            <img id="<?php echo $key; ?>_preview" src="" alt="Logo Preview" class="logo-preview">
                                                            <button type="button" class="btn btn-sm btn-danger" onclick="removeLogo('<?php echo $key; ?>')">
                                                                <i class="fas fa-trash"></i> Remove
                                                            </button>
                                                        </div>
                                                    <?php endif; ?>
                                                </div>

                                            <?php endif; ?>
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                            </div>
                            <?php endforeach; ?>
                        <?php endif; ?>

                        <div class="form-actions">
                            <button type="submit" name="save_settings" class="admin-btn admin-btn-primary">
                                <i class="fas fa-save"></i> Save Settings
                            </button>
                            <button type="button" class="admin-btn admin-btn-secondary" onclick="resetForm()">
                                <i class="fas fa-undo"></i> Reset Changes
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
    // Password toggle functionality
    function togglePassword(fieldId) {
        const field = document.getElementById(fieldId);
        const button = field.nextElementSibling;
        const icon = button.querySelector('i');

        if (field.type === 'password') {
            field.type = 'text';
            icon.classList.remove('fa-eye');
            icon.classList.add('fa-eye-slash');
        } else {
            field.type = 'password';
            icon.classList.remove('fa-eye-slash');
            icon.classList.add('fa-eye');
        }
    }

    // Reset form functionality
    function resetForm() {
        if (confirm('Are you sure you want to reset all changes? This will reload the page and lose any unsaved changes.')) {
            window.location.reload();
        }
    }

    // Logo preview function
    function previewLogo(key) {
        const fileInput = document.getElementById(key + '_upload');
        const preview = document.getElementById(key + '_preview');
        const previewContainer = document.getElementById(key + '_preview_container');
        const hiddenInput = document.getElementById(key);

        if (fileInput.files && fileInput.files[0]) {
            const reader = new FileReader();

            reader.onload = function(e) {
                preview.src = e.target.result;
                previewContainer.style.display = 'block';
                // Note: The actual file upload will be handled on form submission
            };

            reader.readAsDataURL(fileInput.files[0]);
        }
    }

    // Remove logo function
    function removeLogo(key) {
        const fileInput = document.getElementById(key + '_upload');
        const preview = document.getElementById(key + '_preview');
        const previewContainer = document.getElementById(key + '_preview_container');
        const hiddenInput = document.getElementById(key);

        fileInput.value = '';
        preview.src = '';
        previewContainer.style.display = 'none';
        hiddenInput.value = '';
    }

    // Color picker synchronization
    document.addEventListener('DOMContentLoaded', function() {
        const colorPickers = document.querySelectorAll('.color-picker');
        colorPickers.forEach(picker => {
            const textInput = picker.parentElement.querySelector('.color-text');

            picker.addEventListener('change', function() {
                textInput.value = this.value;
            });

            textInput.addEventListener('change', function() {
                if (/^#[0-9A-F]{6}$/i.test(this.value)) {
                    picker.value = this.value;
                }
            });
        });

        // Simple checkbox styling
        const checkboxes = document.querySelectorAll('.checkbox-wrapper input[type="checkbox"]');
        checkboxes.forEach(checkbox => {
            checkbox.addEventListener('change', function() {
                const wrapper = this.closest('.checkbox-wrapper');
                const textSpan = wrapper.querySelector('.checkbox-text');
                if (this.checked) {
                    wrapper.classList.add('checked');
                    textSpan.textContent = 'Option enabled';
                } else {
                    wrapper.classList.remove('checked');
                    textSpan.textContent = 'Option disabled';
                }
            });

            // Initialize state
            const wrapper = checkbox.closest('.checkbox-wrapper');
            const textSpan = wrapper.querySelector('.checkbox-text');
            if (checkbox.checked) {
                wrapper.classList.add('checked');
                textSpan.textContent = 'Option enabled';
            } else {
                textSpan.textContent = 'Option disabled';
            }
        });
    });

    // Alert auto-dismissal is handled by the global alert-auto-dismiss.js script
</script>

<style>
/* Logo upload styling */
.file-upload-group {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.logo-preview-container {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 10px;
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    background-color: var(--gray-50);
}

.logo-preview {
    max-width: 150px;
    max-height: 80px;
    border-radius: var(--radius-sm);
    border: 1px solid var(--border-color);
}

.color-input-group {
    display: flex;
    align-items: center;
    gap: 10px;
}

.color-picker {
    width: 50px;
    height: 40px;
    border: none;
    border-radius: var(--radius-sm);
    cursor: pointer;
}

.color-text {
    flex: 1;
}

/* Simple Checkbox Styling */
.checkbox-wrapper {
    display: flex;
    align-items: center;
    gap: 8px;
}

.checkbox-label {
    display: flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;
    font-size: 14px;
    color: #333;
    margin: 0;
}

.checkbox-label input[type="checkbox"] {
    width: 18px;
    height: 18px;
    margin: 0;
    cursor: pointer;
    accent-color: #f1ca2f;
}

.checkbox-text {
    font-size: 14px;
    color: #666;
    font-weight: 500;
    user-select: none;
}

.checkbox-wrapper.checked .checkbox-text {
    color: #333;
    font-weight: 600;
}

.checkbox-label:hover .checkbox-text {
    color: #333;
}

.checkbox-label input[type="checkbox"]:focus {
    outline: 2px solid #f1ca2f;
    outline-offset: 2px;
}

/* Loading Overlay */
.settings-loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(255, 255, 255, 0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    border-radius: 8px;
}

.loading-spinner {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 12px;
    color: #666;
}

.loading-spinner i {
    font-size: 24px;
    color: #f1ca2f;
}

.loading-spinner span {
    font-size: 14px;
    font-weight: 500;
}

/* Ensure settings content has relative positioning */
.settings-content {
    position: relative;
    min-height: 400px;
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Handle category navigation with loading state
    const categoryLinks = document.querySelectorAll('.settings-nav-link');
    const loadingOverlay = document.getElementById('settingsLoadingOverlay');

    categoryLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            // Show loading overlay
            if (loadingOverlay) {
                loadingOverlay.style.display = 'flex';
            }

            // Allow the navigation to proceed normally
            // The loading overlay will be hidden when the new page loads
        });
    });

    // Hide loading overlay when page is fully loaded
    window.addEventListener('load', function() {
        if (loadingOverlay) {
            loadingOverlay.style.display = 'none';
        }
    });

    // Handle form submission loading state
    const settingsForm = document.querySelector('.settings-form');
    if (settingsForm) {
        settingsForm.addEventListener('submit', function() {
            if (loadingOverlay) {
                loadingOverlay.style.display = 'flex';
                loadingOverlay.querySelector('span').textContent = 'Saving settings...';
            }
        });
    }
});
</script>

<?php include 'includes/footer.php'; ?>
