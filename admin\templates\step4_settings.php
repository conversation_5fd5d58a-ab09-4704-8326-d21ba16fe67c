<h2>Site Settings</h2>
<p>Configure your website settings:</p>

<form method="post" action="" enctype="multipart/form-data">
    <input type="hidden" name="csrf_token" value="<?php echo $_SESSION['csrf_token']; ?>">

    <div class="form-group">
        <label for="site_name">Site Name</label>
        <input type="text" id="site_name" name="site_name" value="<?php echo isset($_POST['site_name']) ? htmlspecialchars($_POST['site_name']) : ''; ?>" placeholder="Your Company Name" required>
        <div class="form-help">The name of your website</div>
    </div>

    <div class="form-group">
        <label for="site_description">Site Description</label>
        <input type="text" id="site_description" name="site_description" value="<?php echo isset($_POST['site_description']) ? htmlspecialchars($_POST['site_description']) : ''; ?>" placeholder="A brief description of your business">
        <div class="form-help">A short description of your website</div>
    </div>

    <div class="form-group">
        <label for="admin_email">Admin Email</label>
        <input type="email" id="admin_email" name="admin_email" value="<?php echo isset($_POST['admin_email']) ? htmlspecialchars($_POST['admin_email']) : (isset($_SESSION['admin_email']) ? htmlspecialchars($_SESSION['admin_email']) : ''); ?>" placeholder="<EMAIL>" required>
        <div class="form-help">The main administrator email address</div>
    </div>

    <div class="form-group">
        <label for="from_email">From Email</label>
        <input type="email" id="from_email" name="from_email" value="<?php echo isset($_POST['from_email']) ? htmlspecialchars($_POST['from_email']) : ''; ?>" placeholder="<EMAIL>" required>
        <div class="form-help">Default sender email address for system emails</div>
    </div>

    <div class="form-group">
        <label for="from_name">From Name</label>
        <input type="text" id="from_name" name="from_name" value="<?php echo isset($_POST['from_name']) ? htmlspecialchars($_POST['from_name']) : ''; ?>" placeholder="Your Company Name" required>
        <div class="form-help">Default sender name for system emails</div>
    </div>

    <div class="form-group">
        <label for="reply_to">Reply-To Email</label>
        <input type="email" id="reply_to" name="reply_to" value="<?php echo isset($_POST['reply_to']) ? htmlspecialchars($_POST['reply_to']) : ''; ?>" placeholder="<EMAIL>">
        <div class="form-help">Default reply-to email address for system emails (optional)</div>
    </div>

    <div class="form-group">
        <label for="admin_logo">Admin Panel Logo</label>
        <input type="file" id="admin_logo" name="admin_logo" accept="image/*">
        <div class="form-help">Upload a logo for the admin panel header (JPG, PNG, GIF - Max 2MB)</div>
        <div id="logo-preview" style="margin-top: 10px; display: none;">
            <img id="logo-preview-img" src="" alt="Logo Preview" style="max-width: 200px; max-height: 100px; border: 1px solid #ddd; padding: 5px;">
        </div>
    </div>

    <script>
    document.getElementById('admin_logo').addEventListener('change', function(e) {
        const file = e.target.files[0];
        const preview = document.getElementById('logo-preview');
        const previewImg = document.getElementById('logo-preview-img');

        if (file) {
            // Validate file type
            const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'];
            if (!allowedTypes.includes(file.type)) {
                alert('Please select a valid image file (JPG, PNG, or GIF)');
                e.target.value = '';
                preview.style.display = 'none';
                return;
            }

            // Validate file size (2MB)
            if (file.size > 2097152) {
                alert('File size must be less than 2MB');
                e.target.value = '';
                preview.style.display = 'none';
                return;
            }

            // Show preview
            const reader = new FileReader();
            reader.onload = function(e) {
                previewImg.src = e.target.result;
                preview.style.display = 'block';
            };
            reader.readAsDataURL(file);
        } else {
            preview.style.display = 'none';
        }
    });
    </script>

    <div class="form-actions">
        <a href="install.php?step=3" class="btn-secondary" style="margin-right: 10px;">Previous</a>
        <button type="submit" name="setup_site">Complete Installation</button>
    </div>
</form>
