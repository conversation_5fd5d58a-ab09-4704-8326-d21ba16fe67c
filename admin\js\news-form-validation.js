/**
 * News Form Validation
 * 
 * This script provides client-side validation for the news creation and editing forms.
 * It validates:
 * - Required fields
 * - Image file type and size
 * - Slug format
 */

document.addEventListener('DOMContentLoaded', function() {
    // Get form elements
    const form = document.querySelector('.news-editor-form');
    if (!form) return;

    const titleInput = document.getElementById('title');
    const slugInput = document.getElementById('slug');
    const contentEditor = document.querySelector('.wysiwyg-editor');
    const imageInput = document.getElementById('image');
    const categorySelect = document.getElementById('category_id');
    
    // Add validation styles
    function showError(element, message) {
        // Remove any existing error message
        removeError(element);
        
        // Add error class to the element
        element.classList.add('is-invalid');
        
        // Create error message element
        const errorElement = document.createElement('div');
        errorElement.className = 'invalid-feedback';
        errorElement.textContent = message;
        
        // Add error message after the element
        if (element.nextElementSibling && element.nextElementSibling.className === 'form-hint') {
            element.parentNode.insertBefore(errorElement, element.nextElementSibling.nextSibling);
        } else {
            element.parentNode.insertBefore(errorElement, element.nextSibling);
        }
    }
    
    function showSuccess(element) {
        // Remove any existing error message
        removeError(element);
        
        // Add success class to the element
        element.classList.add('is-valid');
    }
    
    function removeError(element) {
        // Remove error and success classes
        element.classList.remove('is-invalid', 'is-valid');
        
        // Remove any existing error message
        const errorElement = element.nextElementSibling;
        if (errorElement && errorElement.className === 'invalid-feedback') {
            errorElement.parentNode.removeChild(errorElement);
        }
    }
    
    // Validate title
    function validateTitle() {
        if (!titleInput.value.trim()) {
            showError(titleInput, 'Title is required');
            return false;
        } else if (titleInput.value.trim().length < 3) {
            showError(titleInput, 'Title must be at least 3 characters');
            return false;
        } else if (titleInput.value.trim().length > 255) {
            showError(titleInput, 'Title must be less than 255 characters');
            return false;
        } else {
            showSuccess(titleInput);
            return true;
        }
    }
    
    // Validate slug
    function validateSlug() {
        const slugValue = slugInput.value.trim();
        
        // If slug is empty, it will be generated from title, so it's valid
        if (!slugValue) {
            removeError(slugInput);
            return true;
        }
        
        // Check if slug contains only letters, numbers, and hyphens
        const slugRegex = /^[a-z0-9-]+$/;
        if (!slugRegex.test(slugValue)) {
            showError(slugInput, 'Slug can only contain lowercase letters, numbers, and hyphens');
            return false;
        } else if (slugValue.length > 255) {
            showError(slugInput, 'Slug must be less than 255 characters');
            return false;
        } else {
            showSuccess(slugInput);
            return true;
        }
    }
    
    // Validate content
    function validateContent() {
        // Get content from the editor
        let content = '';
        
        // If using a WYSIWYG editor, get content from it
        if (window.SimpleEditor && window.SimpleEditor.instances) {
            const editorInstance = window.SimpleEditor.instances[0];
            if (editorInstance) {
                content = editorInstance.getContent();
            }
        } else {
            // Fallback to textarea value
            const contentTextarea = document.getElementById('content');
            if (contentTextarea) {
                content = contentTextarea.value;
            }
        }
        
        if (!content.trim()) {
            // Show error on the editor container
            const editorContainer = document.querySelector('.wysiwyg-editor');
            showError(editorContainer, 'Content is required');
            return false;
        } else {
            const editorContainer = document.querySelector('.wysiwyg-editor');
            showSuccess(editorContainer);
            return true;
        }
    }
    
    // Validate image
    function validateImage() {
        // Check if this is the edit form with an existing image
        const imagePreview = document.querySelector('.image-preview img');
        const isEditForm = imagePreview !== null;
        
        // If it's the edit form and no new image is selected, it's valid
        if (isEditForm && !imageInput.files.length) {
            removeError(imageInput);
            return true;
        }
        
        // For create form, image is required
        if (!isEditForm && !imageInput.files.length) {
            showError(imageInput, 'Featured image is required');
            return false;
        }
        
        // If a file is selected, validate it
        if (imageInput.files.length) {
            const file = imageInput.files[0];
            
            // Check file type
            const validTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/jpg'];
            if (!validTypes.includes(file.type)) {
                showError(imageInput, 'Only JPG, JPEG, PNG & GIF files are allowed');
                return false;
            }
            
            // Check file size (max 5MB)
            const maxSize = 5 * 1024 * 1024; // 5MB in bytes
            if (file.size > maxSize) {
                showError(imageInput, 'File size must be less than 5MB');
                return false;
            }
            
            showSuccess(imageInput);
            return true;
        }
        
        return true;
    }
    
    // Validate category
    function validateCategory() {
        if (categorySelect.value === '0') {
            showError(categorySelect, 'Please select a category');
            return false;
        } else {
            showSuccess(categorySelect);
            return true;
        }
    }
    
    // Validate form on submit
    form.addEventListener('submit', function(e) {
        // Validate all fields
        const isTitleValid = validateTitle();
        const isSlugValid = validateSlug();
        const isContentValid = validateContent();
        const isImageValid = validateImage();
        const isCategoryValid = validateCategory();
        
        // If any field is invalid, prevent form submission
        if (!isTitleValid || !isSlugValid || !isContentValid || !isImageValid || !isCategoryValid) {
            e.preventDefault();
            
            // Scroll to the first error
            const firstError = document.querySelector('.is-invalid');
            if (firstError) {
                firstError.scrollIntoView({ behavior: 'smooth', block: 'center' });
                firstError.focus();
            }
        }
    });
    
    // Add input event listeners for real-time validation
    if (titleInput) titleInput.addEventListener('input', validateTitle);
    if (slugInput) slugInput.addEventListener('input', validateSlug);
    if (imageInput) imageInput.addEventListener('change', validateImage);
    if (categorySelect) categorySelect.addEventListener('change', validateCategory);
});
