/**
 * Admin Auth Layout CSS
 *
 * This file contains styles for authentication pages layout (login, register, etc.).
 */

/* Auth Page Container */
.auth-page {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-4);
  background: linear-gradient(135deg, var(--secondary-very-light) 0%, var(--background-color) 100%);
  position: relative;
  overflow: hidden;
}

/* Background Pattern */
.auth-page::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image:
    radial-gradient(var(--primary-very-light) 2px, transparent 2px),
    radial-gradient(var(--primary-very-light) 2px, transparent 2px);
  background-size: 50px 50px;
  background-position: 0 0, 25px 25px;
  opacity: 0.5;
  z-index: 0;
}

/* Auth Card */
.auth-card {
  width: 100%;
  max-width: 450px;
  background-color: var(--white);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-lg);
  overflow: hidden;
  border: 1px solid var(--border-color);
  position: relative;
  z-index: 1;
  display: flex;
  flex-direction: column;
}

/* Auth Header */
.auth-header {
  padding: var(--spacing-6) var(--spacing-6) var(--spacing-4);
  text-align: center;
  position: relative;
}

/* Add subtle accent line at the top */
.auth-header::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 3px;
  background: linear-gradient(to right, var(--primary-color), var(--primary-light));
  border-radius: var(--radius-full);
}

.auth-logo {
  margin-bottom: var(--spacing-4);
  display: flex;
  justify-content: center;
}

.auth-logo img {
  height: 70px;
  width: auto;
  border-radius: var(--radius-md);
  box-shadow: var(--shadow-md);
  transition: transform var(--transition-normal) ease;
}

.auth-logo img:hover {
  transform: scale(1.05);
}

.auth-title {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-bold);
  color: var(--text-dark);
  margin-bottom: var(--spacing-2);
  letter-spacing: -0.5px;
}

.auth-subtitle {
  font-size: var(--font-size-sm);
  color: var(--text-light);
  max-width: 300px;
  margin: 0 auto;
  line-height: 1.5;
}

/* Auth Body */
.auth-body {
  padding: var(--spacing-4) var(--spacing-6) var(--spacing-6);
}

/* Auth Form */
.auth-form .form-group {
  margin-bottom: var(--spacing-4);
  position: relative;
}

.auth-form .form-label {
  font-weight: var(--font-weight-medium);
  display: block;
  margin-bottom: var(--spacing-2);
  font-size: var(--font-size-sm);
  color: var(--text-dark);
}

.auth-form .form-control {
  height: 46px;
  padding: var(--spacing-3) var(--spacing-4) var(--spacing-3) 42px;
  font-size: var(--font-size-sm);
  width: 100%;
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  background-color: var(--background-light);
  transition: all var(--transition-fast) ease;
  color: var(--text-dark);
}

.auth-form .form-control:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px var(--primary-very-light);
  outline: none;
  background-color: var(--white);
}

.auth-form .form-text {
  font-size: var(--font-size-xs);
  color: var(--text-light);
  margin-top: var(--spacing-1);
}

.auth-form .input-group {
  position: relative;
}

.auth-form .input-icon {
  position: absolute;
  left: 15px;
  top: 50%;
  transform: translateY(-50%);
  color: var(--text-light);
  font-size: var(--font-size-base);
  pointer-events: none;
  transition: color var(--transition-fast) ease;
}

.auth-form .form-control:focus + .input-icon,
.auth-form .input-group:hover .input-icon {
  color: var(--primary-color);
}

/* Remember Me and Forgot Password */
.auth-options {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--spacing-4);
}

.auth-remember {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  font-size: var(--font-size-sm);
  color: var(--text-color);
}

.auth-remember input[type="checkbox"] {
  width: 16px;
  height: 16px;
  border-radius: var(--radius-sm);
  border: 1px solid var(--border-color);
  background-color: var(--white);
  cursor: pointer;
  appearance: none;
  -webkit-appearance: none;
  position: relative;
  transition: all var(--transition-fast) ease;
}

.auth-remember input[type="checkbox"]:checked {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
}

.auth-remember input[type="checkbox"]:checked::after {
  content: '✓';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: var(--white);
  font-size: 10px;
  font-weight: var(--font-weight-bold);
}

.auth-remember input[type="checkbox"]:focus {
  box-shadow: 0 0 0 2px var(--primary-very-light);
  outline: none;
}

.auth-remember label {
  cursor: pointer;
  user-select: none;
}

.auth-forgot {
  font-size: var(--font-size-sm);
  color: var(--primary-color);
  text-decoration: none;
  transition: all var(--transition-fast) ease;
  padding: var(--spacing-1) var(--spacing-2);
  border-radius: var(--radius-md);
}

.auth-forgot:hover {
  color: var(--primary-dark);
  background-color: var(--primary-very-light);
}

.auth-forgot:focus {
  outline: none;
  box-shadow: 0 0 0 2px var(--primary-very-light);
}

/* Auth Button */
.auth-submit {
  width: 100%;
  height: 46px;
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-semibold);
  margin-bottom: var(--spacing-4);
  background-color: var(--primary-color);
  color: var(--text-dark);
  border: none;
  border-radius: var(--radius-md);
  cursor: pointer;
  transition: all var(--transition-fast) ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-2);
  position: relative;
  overflow: hidden;
}

.auth-submit:hover {
  background-color: var(--primary-dark);
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.auth-submit:active {
  transform: translateY(0);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.auth-submit:focus {
  outline: none;
  box-shadow: 0 0 0 3px var(--primary-very-light);
}

.auth-submit .btn-icon {
  font-size: var(--font-size-base);
}

/* Button ripple effect */
.auth-submit::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 5px;
  height: 5px;
  background: rgba(255, 255, 255, 0.5);
  opacity: 0;
  border-radius: 100%;
  transform: scale(1, 1) translate(-50%, -50%);
  transform-origin: 50% 50%;
}

.auth-submit:focus:not(:active)::after {
  animation: ripple 1s ease-out;
}

@keyframes ripple {
  0% {
    transform: scale(0, 0);
    opacity: 0.5;
  }
  20% {
    transform: scale(25, 25);
    opacity: 0.3;
  }
  100% {
    opacity: 0;
    transform: scale(40, 40);
  }
}

/* Social Login */
.auth-social {
  margin-top: var(--spacing-4);
  text-align: center;
}

.auth-social-title {
  display: flex;
  align-items: center;
  margin-bottom: var(--spacing-4);
  font-size: var(--font-size-sm);
  color: var(--text-light);
}

.auth-social-title::before,
.auth-social-title::after {
  content: "";
  flex: 1;
  height: 1px;
  background-color: var(--border-color);
}

.auth-social-title::before {
  margin-right: var(--spacing-3);
}

.auth-social-title::after {
  margin-left: var(--spacing-3);
}

.auth-social-buttons {
  display: flex;
  gap: var(--spacing-2);
  justify-content: center;
}

.auth-social-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: var(--radius-md);
  background-color: var(--white);
  border: 1px solid var(--border-color);
  color: var(--text-dark);
  transition: all var(--transition-fast) ease;
}

.auth-social-button:hover {
  background-color: var(--gray-50);
  transform: translateY(-2px);
}

.auth-social-button i {
  font-size: var(--font-size-lg);
}

.auth-social-google {
  color: #DB4437;
}

.auth-social-facebook {
  color: #4267B2;
}

.auth-social-twitter {
  color: #1DA1F2;
}

.auth-social-linkedin {
  color: #0077B5;
}

/* Auth Footer */
.auth-footer {
  text-align: center;
  padding: var(--spacing-4) var(--spacing-6);
  border-top: 1px solid var(--border-color);
  font-size: var(--font-size-sm);
  color: var(--text-light);
  background-color: var(--background-light);
  border-radius: 0 0 var(--radius-lg) var(--radius-lg);
  margin-top: auto;
}

.auth-footer p {
  margin: 0 0 var(--spacing-2);
}

.auth-footer a {
  color: var(--primary-color);
  text-decoration: none;
  font-weight: var(--font-weight-medium);
  transition: all var(--transition-fast) ease;
  padding: var(--spacing-1) var(--spacing-2);
  border-radius: var(--radius-md);
  display: inline-block;
}

.auth-footer a:hover {
  color: var(--primary-dark);
  background-color: var(--primary-very-light);
}

.auth-footer a:focus {
  outline: none;
  box-shadow: 0 0 0 2px var(--primary-very-light);
}

/* Auth Links */
.auth-links {
  display: flex;
  justify-content: center;
  gap: var(--spacing-3);
  margin-top: var(--spacing-2);
  flex-wrap: wrap;
}

/* Auth Alert */
.auth-alert {
  margin-bottom: var(--spacing-4);
}

/* Auth with Background Image */
.auth-bg-image {
  background-image: url('../../../images/auth-bg.jpg');
  background-size: cover;
  background-position: center;
  position: relative;
}

.auth-bg-image::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
}

.auth-bg-image .auth-card {
  position: relative;
  z-index: 1;
}

/* Auth Split Layout */
.auth-split {
  display: flex;
  min-height: 100vh;
}

.auth-split-image {
  flex: 1;
  background-image: url('../../../images/auth-bg.jpg');
  background-size: cover;
  background-position: center;
  display: none;
}

.auth-split-content {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-4);
}

.auth-split .auth-card {
  box-shadow: none;
  border: none;
}

/* Auth Centered Layout */
.auth-centered {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
}

.auth-centered-header {
  padding: var(--spacing-4);
  display: flex;
  align-items: center;
  justify-content: center;
}

.auth-centered-logo {
  height: 40px;
  width: auto;
}

.auth-centered-content {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-4);
}

.auth-centered-footer {
  padding: var(--spacing-4);
  text-align: center;
  font-size: var(--font-size-sm);
  color: var(--text-light);
}

/* Auth Boxed Layout */
.auth-boxed {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  padding: var(--spacing-6);
}

.auth-boxed-header {
  margin-bottom: var(--spacing-6);
  text-align: center;
}

.auth-boxed-logo {
  height: 40px;
  width: auto;
}

.auth-boxed-content {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}

.auth-boxed-footer {
  margin-top: var(--spacing-6);
  text-align: center;
  font-size: var(--font-size-sm);
  color: var(--text-light);
}

/* Auth Illustration Layout */
.auth-illustration {
  display: flex;
  min-height: 100vh;
}

.auth-illustration-image {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-6);
  background-color: var(--primary-very-light);
  display: none;
}

.auth-illustration-content {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-4);
}

.auth-illustration-img {
  max-width: 100%;
  height: auto;
}

/* Responsive Styles */
@media (min-width: 992px) {
  .auth-split-image,
  .auth-illustration-image {
    display: block;
  }
}

@media (max-width: 576px) {
  .auth-card {
    max-width: 100%;
  }

  .auth-header {
    padding: var(--spacing-4) var(--spacing-4) var(--spacing-3);
  }

  .auth-body {
    padding: var(--spacing-3) var(--spacing-4) var(--spacing-4);
  }

  .auth-footer {
    padding: var(--spacing-3) var(--spacing-4);
  }

  .auth-options {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-2);
  }

  .auth-boxed {
    padding: var(--spacing-4);
  }
}

/* Dark Mode Styles */
.dark-mode .auth-page {
  background: linear-gradient(135deg, var(--secondary-dark) 0%, var(--secondary-color) 100%);
}

.dark-mode .auth-page::before {
  background-image:
    radial-gradient(rgba(255, 255, 255, 0.1) 2px, transparent 2px),
    radial-gradient(rgba(255, 255, 255, 0.1) 2px, transparent 2px);
}

.dark-mode .auth-card {
  background-color: var(--secondary-dark);
  border-color: rgba(255, 255, 255, 0.1);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.dark-mode .auth-header::after {
  background: linear-gradient(to right, var(--primary-color), var(--primary-dark));
}

.dark-mode .auth-title {
  color: var(--white);
}

.dark-mode .auth-subtitle {
  color: rgba(255, 255, 255, 0.7);
}

.dark-mode .auth-form .form-label {
  color: rgba(255, 255, 255, 0.9);
}

.dark-mode .auth-form .form-control {
  background-color: rgba(255, 255, 255, 0.05);
  border-color: rgba(255, 255, 255, 0.1);
  color: var(--white);
}

.dark-mode .auth-form .form-control:focus {
  background-color: rgba(255, 255, 255, 0.1);
  border-color: var(--primary-color);
}

.dark-mode .auth-form .input-icon {
  color: rgba(255, 255, 255, 0.5);
}

.dark-mode .auth-remember {
  color: rgba(255, 255, 255, 0.7);
}

.dark-mode .auth-remember input[type="checkbox"] {
  background-color: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.2);
}

.dark-mode .auth-forgot {
  color: var(--primary-light);
}

.dark-mode .auth-forgot:hover {
  background-color: rgba(241, 202, 47, 0.15);
}

.dark-mode .auth-submit {
  background-color: var(--primary-color);
  color: var(--secondary-dark);
}

.dark-mode .auth-submit:hover {
  background-color: var(--primary-light);
}

.dark-mode .auth-footer {
  background-color: rgba(0, 0, 0, 0.2);
  border-top-color: rgba(255, 255, 255, 0.1);
  color: rgba(255, 255, 255, 0.6);
}

.dark-mode .auth-footer a {
  color: var(--primary-light);
}

.dark-mode .auth-footer a:hover {
  background-color: rgba(241, 202, 47, 0.15);
  color: var(--primary-color);
}
