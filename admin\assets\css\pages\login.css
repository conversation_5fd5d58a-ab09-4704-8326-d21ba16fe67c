/**
 * Login Page CSS
 *
 * This file contains styles specific to the login page.
 */

/* Login Page Container */
.login-page {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-4);
  background-color: var(--background-color);
  background-image: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.login-container {
  width: 100%;
  max-width: 420px;
  margin: 0 auto;
}

/* Login Card */
.login-card {
  width: 100%;
  background-color: var(--white);
  border-radius: var(--radius-lg);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
  overflow: hidden;
}

/* Login Header */
.login-header {
  padding: 30px 30px 20px;
  text-align: center;
  background-color: var(--white);
  border-bottom: 1px solid var(--border-color-light);
}

.login-logo {
  margin-bottom: 20px;
  display: flex;
  justify-content: center;
}

.login-logo img {
  max-width: 180px;
  height: auto;
}

.login-title {
  font-size: 24px;
  font-weight: 600;
  color: var(--text-dark);
  margin: 0 0 8px;
}

.login-subtitle {
  font-size: 14px;
  color: var(--text-light);
  margin: 0;
}

/* Login Body */
.login-body {
  padding: 30px;
}

/* Login Form */
.login-form {
  margin-top: 10px;
}

.login-form .form-group {
  margin-bottom: 20px;
}

.login-form label {
  display: block;
  font-size: 14px;
  font-weight: 600;
  color: var(--text-dark);
  margin-bottom: 8px;
}

.input-wrapper {
  position: relative;
}

.input-wrapper i {
  position: absolute;
  left: 15px;
  top: 50%;
  transform: translateY(-50%);
  color: var(--text-light);
  font-size: 16px;
}

.input-wrapper input {
  width: 100%;
  padding: 12px 15px 12px 45px;
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  font-size: 14px;
  transition: all 0.3s ease;
  text-indent: 0; /* Prevent text from overlapping with icon */
}

.input-wrapper input:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(241, 202, 47, 0.2);
  outline: none;
}

/* Form Options */
.form-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 25px;
}

.remember-me {
  display: flex;
  align-items: center;
}

.remember-me input[type="checkbox"] {
  margin-right: 8px;
}

.remember-me label {
  font-size: 14px;
  color: var(--text-light);
  cursor: pointer;
  margin-bottom: 0;
}

.forgot-password {
  font-size: 14px;
  color: var(--text-light);
  text-decoration: none;
  transition: color 0.3s ease;
}

.forgot-password:hover {
  color: var(--primary-color);
  text-decoration: underline;
}

/* Login Button */
.login-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  padding: 12px 20px;
  background-color: var(--primary-color);
  color: var(--text-dark);
  border: none;
  border-radius: var(--radius-md);
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.login-button i {
  margin-right: 8px;
}

.login-button:hover {
  background-color: var(--primary-dark);
}

.login-button:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

/* Login Alerts */
.login-alert {
  display: flex;
  align-items: flex-start;
  padding: 15px;
  border-radius: var(--radius-md);
  margin-bottom: 20px;
  font-size: 14px;
}

.login-alert i {
  margin-right: 10px;
  font-size: 16px;
  margin-top: 2px;
}

.login-alert.error {
  background-color: #fff2f2;
  border-left: 4px solid #ff5252;
  color: #d32f2f;
}

.login-alert.success {
  background-color: #f0fff4;
  border-left: 4px solid #4caf50;
  color: #388e3c;
}

.alert-message {
  flex: 1;
}

.alert-action {
  margin-top: 8px;
}

.alert-action a {
  color: inherit;
  text-decoration: underline;
  font-weight: 600;
}

/* Login Footer */
.login-footer {
  padding: 15px 30px;
  text-align: center;
  border-top: 1px solid var(--border-color-light);
  display: flex;
  justify-content: center;
  align-items: center;
}

.login-footer p {
  margin: 0;
  font-size: 12px;
  color: var(--text-light);
  text-align: center;
  width: 100%;
}

/* Responsive Styles */
@media (max-width: 480px) {
  .login-header {
    padding: 20px 20px 15px;
  }

  .login-body {
    padding: 20px;
  }

  .login-logo img {
    max-width: 150px;
  }

  .login-title {
    font-size: 20px;
  }

  .form-options {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }

  .login-footer {
    padding: 12px 20px;
  }
}
