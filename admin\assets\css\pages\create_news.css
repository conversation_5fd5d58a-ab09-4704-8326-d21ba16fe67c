/**
 * Create News Page CSS
 *
 * This file contains styles specific to the create news page.
 */

/* Create News Page Specific Styles */
.page-create_news .admin-content-title {
  color: var(--text-dark);
}

.page-create_news .admin-content-title i {
  color: var(--primary-color);
  margin-right: 8px;
}

/* Enhanced Form Styling for Create News */
.page-create_news .form-control {
  border-width: 2px;
}

.page-create_news .form-control:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 4px rgba(var(--primary-rgb), 0.15);
}

/* Enhanced Card Styling */
.page-create_news .admin-card {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

.page-create_news .admin-card-header {
  background-color: var(--bg-light);
  border-bottom: 1px solid var(--border-light);
}

/* Enhanced <PERSON><PERSON> Styling */
.page-create_news .admin-btn.primary {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
  font-weight: var(--font-weight-semibold);
  padding: var(--spacing-3) var(--spacing-5);
  transition: all var(--transition-fast) ease;
}

.page-create_news .admin-btn.primary:hover {
  background-color: var(--primary-dark);
  border-color: var(--primary-dark);
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(var(--primary-rgb), 0.3);
}

.page-create_news .admin-btn.primary:active {
  transform: translateY(0);
  box-shadow: 0 2px 4px rgba(var(--primary-rgb), 0.3);
}

/* Enhanced Image Upload Area */
.page-create_news .image-preview {
  border: 2px dashed var(--primary-color);
  background-color: rgba(var(--primary-rgb), 0.05);
}

.page-create_news .image-preview-empty i {
  color: var(--primary-color);
}

.page-create_news .custom-file-label {
  background-color: rgba(var(--primary-rgb), 0.1);
  border-color: var(--primary-color);
  color: var(--primary-dark);
  font-weight: var(--font-weight-medium);
}

.page-create_news .custom-file-label:hover {
  background-color: rgba(var(--primary-rgb), 0.15);
}

/* Enhanced WYSIWYG Editor */
.page-create_news .wysiwyg-editor-container {
  border-color: var(--border-color);
  border-radius: var(--radius-md);
  overflow: hidden;
}

.page-create_news .wysiwyg-toolbar {
  background-color: rgba(var(--primary-rgb), 0.05);
}

.page-create_news .wysiwyg-toolbar button.active {
  background-color: var(--primary-color);
}

/* Fix for sidebar toggle visibility */
#sidebarToggleContainer,
.sidebar-toggle,
body.sidebar-collapsed #sidebarToggleContainer,
body.sidebar-collapsed .sidebar-toggle {
  display: flex;
  visibility: visible;
  opacity: 1;
  z-index: 9999;
}

/* Responsive Adjustments */
@media (max-width: 992px) {
  .page-create_news .admin-content-grid {
    gap: var(--spacing-4);
  }

  .page-create_news .admin-card {
    margin-bottom: var(--spacing-4);
  }
}

@media (max-width: 768px) {
  .page-create_news .admin-content-title {
    font-size: var(--font-size-xl);
  }

  .page-create_news .admin-content-subtitle {
    font-size: var(--font-size-sm);
  }

  .page-create_news .admin-btn.primary {
    width: 100%;
    justify-content: center;
  }
}
