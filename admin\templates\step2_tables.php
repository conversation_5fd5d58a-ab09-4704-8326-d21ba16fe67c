<h2>Database Tables Setup</h2>
<p>Now we'll set up the necessary database tables for your website.</p>

<div style="position: relative; margin-bottom: 20px; text-align: right;">
    <span class="help-icon" style="display: inline-block; position: relative; cursor: help;">
        <i class="fas fa-info-circle" style="font-size: 24px; color: #2196F3;"></i>
        <span style="margin-left: 5px; font-size: 14px; color: #666;">Hover for installation details</span>
        <div class="tooltip" style="visibility: hidden; width: 400px; background-color: #fff; color: #333; text-align: left; border-radius: 6px; padding: 15px; position: absolute; z-index: 1; bottom: 125%; right: 0; opacity: 0; transition: opacity 0.3s; box-shadow: 0 2px 8px rgba(0,0,0,0.15); border: 1px solid #ddd; max-height: 80vh; overflow-y: auto;">
            <!-- Arrow pointing to the help icon -->
            <div style="position: absolute; bottom: -10px; right: 10px; width: 0; height: 0; border-left: 10px solid transparent; border-right: 10px solid transparent; border-top: 10px solid #fff; filter: drop-shadow(0 2px 2px rgba(0,0,0,0.1));"></div>
            <h4 style="margin-top: 0; color: #333;"><i class="fas fa-info-circle" style="color: #2196F3;"></i> Installation Process</h4>
            <p>The installation will:</p>
            <ul style="margin-top: 10px; margin-bottom: 10px; padding-left: 20px;">
                <li>Check if tables already exist in the database</li>
                <li>Add any missing tables that are required using <code>IF NOT EXISTS</code></li>
                <li>Verify all required fields are present in existing tables</li>
                <li>Add any missing fields to existing tables</li>
                <li>Check for and add any missing indexes</li>
                <li>Insert default data only if it doesn't already exist</li>
            </ul>
            <p>This ensures your database structure is complete without losing any existing data.</p>
            <p><strong>Note:</strong> The installation will not delete or modify any existing data in your database. All operations use safe SQL practices to prevent errors.</p>
            <p><strong>Error Handling:</strong> If any operation fails, the installer will log the error and continue with the next operation rather than stopping the entire process.</p>
        </div>
    </span>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Function to handle tooltip functionality
    function setupTooltip(iconSelector, tooltipSelector) {
        const icon = document.querySelector(iconSelector);
        const tooltip = document.querySelector(tooltipSelector);

        if (icon && tooltip) {
            icon.addEventListener('mouseenter', function() {
                tooltip.style.visibility = 'visible';
                tooltip.style.opacity = '1';
            });

            icon.addEventListener('mouseleave', function() {
                tooltip.style.visibility = 'hidden';
                tooltip.style.opacity = '0';
            });

            // Also handle click for mobile devices
            icon.addEventListener('click', function(e) {
                e.preventDefault();
                if (tooltip.style.visibility === 'visible') {
                    tooltip.style.visibility = 'hidden';
                    tooltip.style.opacity = '0';
                } else {
                    tooltip.style.visibility = 'visible';
                    tooltip.style.opacity = '1';
                }
            });
        }
    }

    // Setup tooltip
    setupTooltip('.help-icon', '.tooltip');

    // Close tooltip when clicking outside
    document.addEventListener('click', function(e) {
        if (!e.target.closest('.help-icon')) {
            document.querySelector('.tooltip').style.visibility = 'hidden';
            document.querySelector('.tooltip').style.opacity = '0';
        }
    });
});
</script>

<form method="post" action="">
    <input type="hidden" name="csrf_token" value="<?php echo $_SESSION['csrf_token']; ?>">
    <div class="form-actions">
        <a href="install.php?step=1" class="btn-secondary" style="margin-right: 10px;">Previous</a>
        <button type="submit" name="setup_tables">Start Tables Setup</button>
    </div>
</form>
