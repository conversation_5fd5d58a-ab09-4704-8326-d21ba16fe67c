<?php
/**
 * Email Templates Class
 *
 * Handles email templates management
 */
class EmailTemplates {
    private $conn;
    private $templates = [];

    /**
     * Constructor
     *
     * @param object $conn Database connection
     */
    public function __construct($conn) {
        $this->conn = $conn;
        $this->initializeTemplates();
    }

    /**
     * Initialize templates
     */
    private function initializeTemplates() {
        // Check if email_templates table exists
        $check_table = "SHOW TABLES LIKE 'email_templates'";
        $table_result = $this->conn->query($check_table);

        if ($table_result->num_rows == 0) {
            // Table doesn't exist, create it
            $this->createTemplatesTable();
        }

        // Load templates
        $this->loadTemplates();

        // Insert default templates if none exist
        if (empty($this->templates)) {
            $this->insertDefaultTemplates();
            $this->loadTemplates(); // Reload after inserting defaults
        }
    }

    /**
     * Create email templates table
     */
    private function createTemplatesTable() {
        $sql = "CREATE TABLE `email_templates` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `template_key` varchar(100) NOT NULL UNIQUE,
            `template_name` varchar(255) NOT NULL,
            `subject` varchar(500) NOT NULL,
            `content` text NOT NULL,
            `variables` text DEFAULT NULL,
            `header_image` varchar(255) DEFAULT NULL,
            `is_active` tinyint(1) NOT NULL DEFAULT 1,
            `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
            `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            UNIQUE KEY `template_key` (`template_key`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;";

        if (!$this->conn->query($sql)) {
            error_log("Failed to create email_templates table: " . $this->conn->error);
        }

        // Also ensure the header_image column exists in case table already existed
        $this->ensureHeaderImageColumn();
    }

    /**
     * Ensure header_image column exists
     */
    private function ensureHeaderImageColumn() {
        // Check if header_image column exists
        $check_column = "SHOW COLUMNS FROM email_templates LIKE 'header_image'";
        $result = $this->conn->query($check_column);

        if ($result->num_rows == 0) {
            // Column doesn't exist, add it
            $add_column = "ALTER TABLE email_templates ADD COLUMN header_image varchar(255) DEFAULT NULL AFTER variables";
            if (!$this->conn->query($add_column)) {
                error_log("Failed to add header_image column: " . $this->conn->error);
            }
        }
    }

    /**
     * Insert default email templates
     */
    private function insertDefaultTemplates() {
        $default_templates = [
            [
                'template_key' => 'contact_auto_reply',
                'template_name' => 'Contact Form Auto-Reply',
                'subject' => 'Thank You for Contacting Us',
                'content' => "Dear {contact_name},\n\nThank you for contacting {company_name}. This is an automatic confirmation that we have received your message.\n\nOur team will review your inquiry and get back to you as soon as possible. Please allow 1-2 business days for a response.\n\nFor your reference, here is a copy of your message:\n\nMessage:\n{contact_message}\n\nIf you have any urgent matters, please call us directly at our support number.\n\nBest regards,\nThe {company_name} Team",
                'variables' => '{contact_name}, {company_name}, {contact_message}, {site_url}'
            ],
            [
                'template_key' => 'contact_notification',
                'template_name' => 'Contact Form Notification (Admin)',
                'subject' => 'New Contact Form Submission from {contact_name}',
                'content' => "You have received a new message from your website contact form.\n\nName: {contact_name}\nEmail: {contact_email}\nPhone: {contact_phone}\nMessage:\n{contact_message}\n\nSource: {contact_source}\nDate: {submission_date}\n\nPlease respond to this inquiry as soon as possible.",
                'variables' => '{contact_name}, {contact_email}, {contact_phone}, {contact_message}, {contact_source}, {submission_date}'
            ],
            [
                'template_key' => 'password_reset',
                'template_name' => 'Password Reset',
                'subject' => 'Password Reset Request - {site_name}',
                'content' => "Hello {username},\n\nWe received a request to reset your password. Please click the link below to reset your password:\n\n{reset_url}\n\nThis link will expire in 1 hour.\n\nIf you did not request a password reset, please ignore this email.\n\nBest regards,\nThe {site_name} Team",
                'variables' => '{username}, {reset_url}, {site_name}, {site_url}'
            ],
            [
                'template_key' => 'email_verification',
                'template_name' => 'Email Verification',
                'subject' => 'Verify Your Account - {site_name}',
                'content' => "Hello {username},\n\nThank you for creating an account with us. Please click the link below to verify your email address:\n\n{verification_url}\n\nThis link will expire in 24 hours.\n\nIf you did not create an account, please ignore this email.\n\nBest regards,\nThe {site_name} Team",
                'variables' => '{username}, {verification_url}, {site_name}, {site_url}'
            ]
        ];

        foreach ($default_templates as $template) {
            $this->insertTemplate(
                $template['template_key'],
                $template['template_name'],
                $template['subject'],
                $template['content'],
                $template['variables']
            );
        }
    }

    /**
     * Load templates from database
     */
    private function loadTemplates() {
        $sql = "SELECT * FROM email_templates ORDER BY template_name";
        $result = $this->conn->query($sql);

        $this->templates = [];
        if ($result && $result->num_rows > 0) {
            while ($row = $result->fetch_assoc()) {
                $this->templates[] = $row;
            }
        }
    }

    /**
     * Get all templates
     *
     * @return array Array of templates
     */
    public function getAllTemplates() {
        return $this->templates;
    }

    /**
     * Get template by key
     *
     * @param string $key Template key
     * @return array|null Template data or null if not found
     */
    public function getTemplate($key) {
        foreach ($this->templates as $template) {
            if ($template['template_key'] === $key) {
                return $template;
            }
        }
        return null;
    }

    /**
     * Get template by ID
     *
     * @param int $id Template ID
     * @return array|null Template data or null if not found
     */
    public function getTemplateById($id) {
        $sql = "SELECT * FROM email_templates WHERE id = ?";
        $stmt = $this->conn->prepare($sql);
        if (!$stmt) {
            error_log("Failed to prepare template select statement: " . $this->conn->error);
            return null;
        }

        $stmt->bind_param("i", $id);
        $stmt->execute();
        $result = $stmt->get_result();
        $template = $result->fetch_assoc();
        $stmt->close();

        return $template;
    }

    /**
     * Insert a new template
     *
     * @param string $key Template key
     * @param string $name Template name
     * @param string $subject Template subject
     * @param string $content Template content
     * @param string $variables Available variables
     * @return bool Success status
     */
    public function insertTemplate($key, $name, $subject, $content, $variables = '') {
        $sql = "INSERT INTO email_templates (template_key, template_name, subject, content, variables)
                VALUES (?, ?, ?, ?, ?)
                ON DUPLICATE KEY UPDATE
                template_name = VALUES(template_name),
                subject = VALUES(subject),
                content = VALUES(content),
                variables = VALUES(variables),
                updated_at = CURRENT_TIMESTAMP";

        $stmt = $this->conn->prepare($sql);
        if (!$stmt) {
            error_log("Failed to prepare template insert statement: " . $this->conn->error);
            return false;
        }

        $stmt->bind_param("sssss", $key, $name, $subject, $content, $variables);
        $result = $stmt->execute();
        $stmt->close();

        if ($result) {
            $this->loadTemplates(); // Reload templates
        }

        return $result;
    }

    /**
     * Update template
     *
     * @param int $id Template ID
     * @param string $subject Template subject
     * @param string $content Template content
     * @return bool Success status
     */
    public function updateTemplate($id, $subject, $content) {
        $sql = "UPDATE email_templates SET subject = ?, content = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?";

        $stmt = $this->conn->prepare($sql);
        if (!$stmt) {
            error_log("Failed to prepare template update statement: " . $this->conn->error);
            return false;
        }

        $stmt->bind_param("ssi", $subject, $content, $id);
        $result = $stmt->execute();
        $stmt->close();

        if ($result) {
            $this->loadTemplates(); // Reload templates
        }

        return $result;
    }

    /**
     * Update template with header image
     *
     * @param int $id Template ID
     * @param string $subject Template subject
     * @param string $content Template content
     * @param string|null $header_image Header image path
     * @return bool Success status
     */
    public function updateTemplateWithImage($id, $subject, $content, $header_image = null) {
        // Ensure the header_image column exists
        $this->ensureHeaderImageColumn();

        $sql = "UPDATE email_templates SET subject = ?, content = ?, header_image = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?";

        $stmt = $this->conn->prepare($sql);
        if (!$stmt) {
            error_log("Failed to prepare template update with image statement: " . $this->conn->error);
            return false;
        }

        $stmt->bind_param("sssi", $subject, $content, $header_image, $id);
        $result = $stmt->execute();
        $stmt->close();

        if ($result) {
            $this->loadTemplates(); // Reload templates
        }

        return $result;
    }

    /**
     * Process template variables
     *
     * @param string $content Template content
     * @param array $variables Variables to replace
     * @return string Processed content
     */
    public function processTemplate($content, $variables = []) {
        // Default variables
        $default_vars = [
            '{site_name}' => 'Manage Inc.',
            '{site_url}' => 'https://manageinc.com',
            '{company_name}' => 'Manage Inc.'
        ];

        // Merge with provided variables
        $all_variables = array_merge($default_vars, $variables);

        // Replace variables in content
        return str_replace(array_keys($all_variables), array_values($all_variables), $content);
    }
}
?>
