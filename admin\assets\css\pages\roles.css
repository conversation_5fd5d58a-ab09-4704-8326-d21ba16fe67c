/**
 * Roles Management CSS
 *
 * This file contains styles for the roles management pages.
 */

/* Role Content Layout */
.admin-content-layout {
  display: grid;
  grid-template-columns: 3fr 1fr;
  gap: var(--spacing-4);
}

/* Main Content Area */
.admin-content-main {
  width: 100%;
}

/* Sidebar Area */
.admin-content-sidebar {
  width: 100%;
}

/* Permission Categories */
.permission-category {
  margin-bottom: var(--spacing-4);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  overflow: hidden;
}

.permission-category h4 {
  margin: 0;
  padding: var(--spacing-3);
  background-color: var(--background-light);
  border-bottom: 1px solid var(--border-color);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  color: var(--text-dark);
}

.permission-category-content {
  padding: var(--spacing-3);
  display: flex;
  flex-direction: column;
  gap: var(--spacing-3);
}

/* Permission Items */
.permission-item {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-1);
  padding-bottom: var(--spacing-2);
  border-bottom: 1px solid var(--border-light);
}

.permission-item:last-child {
  border-bottom: none;
  padding-bottom: 0;
}

.permission-description {
  font-size: var(--font-size-xs);
  color: var(--text-light);
  margin-left: var(--spacing-6);
}

/* Custom Checkbox */
.custom-checkbox {
  display: flex;
  align-items: center;
  position: relative;
  padding-left: var(--spacing-6);
  cursor: pointer;
  font-size: var(--font-size-sm);
  user-select: none;
  color: var(--text-color);
  font-weight: var(--font-weight-medium);
}

.custom-checkbox input {
  position: absolute;
  opacity: 0;
  cursor: pointer;
  height: 0;
  width: 0;
}

.checkmark {
  position: absolute;
  top: 0;
  left: 0;
  height: 18px;
  width: 18px;
  background-color: var(--white);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-sm);
  transition: all var(--transition-fast) ease;
}

.custom-checkbox:hover input ~ .checkmark {
  background-color: var(--background-light);
  border-color: var(--primary-light);
}

.custom-checkbox input:checked ~ .checkmark {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
}

.checkmark:after {
  content: "";
  position: absolute;
  display: none;
}

.custom-checkbox input:checked ~ .checkmark:after {
  display: block;
}

.custom-checkbox .checkmark:after {
  left: 6px;
  top: 2px;
  width: 5px;
  height: 10px;
  border: solid var(--white);
  border-width: 0 2px 2px 0;
  transform: rotate(45deg);
}

/* Form Actions */
.form-actions {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-2);
}

.form-actions .admin-btn {
  width: 100%;
  justify-content: center;
}

/* Role List */
.role-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: var(--spacing-4);
}

.role-card {
  background-color: var(--white);
  border-radius: var(--radius-md);
  border: 1px solid var(--border-color);
  box-shadow: var(--shadow-sm);
  overflow: hidden;
  transition: all var(--transition-fast) ease;
}

.role-card:hover {
  box-shadow: var(--shadow-md);
  transform: translateY(-2px);
  border-color: var(--primary-light);
}

.role-card-header {
  padding: var(--spacing-3);
  border-bottom: 1px solid var(--border-color);
  background-color: var(--background-light);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.role-card-title {
  margin: 0;
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-semibold);
  color: var(--text-dark);
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
}

.role-card-title i {
  color: var(--primary-color);
}

.role-card-body {
  padding: var(--spacing-3);
}

.role-description {
  margin-bottom: var(--spacing-3);
  font-size: var(--font-size-sm);
  color: var(--text-color);
}

.role-permissions {
  margin-top: var(--spacing-3);
}

.role-permissions-title {
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
  color: var(--text-light);
  margin-bottom: var(--spacing-2);
  display: flex;
  align-items: center;
  gap: var(--spacing-1);
}

.role-permissions-list {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-1);
}

.role-permission-tag {
  font-size: var(--font-size-xs);
  background-color: var(--primary-very-light);
  color: var(--primary-dark);
  padding: 2px 8px;
  border-radius: var(--radius-full);
  white-space: nowrap;
}

.role-card-actions {
  padding: var(--spacing-3);
  border-top: 1px solid var(--border-light);
  display: flex;
  gap: var(--spacing-2);
}

.role-card-actions .admin-btn {
  flex: 1;
  justify-content: center;
}

/* Responsive Styles */
@media (max-width: 992px) {
  .admin-content-layout {
    grid-template-columns: 2fr 1fr;
  }
}

@media (max-width: 768px) {
  .admin-content-layout {
    grid-template-columns: 1fr;
  }
  
  .role-list {
    grid-template-columns: 1fr;
  }
  
  .permission-category h4 {
    font-size: var(--font-size-sm);
    padding: var(--spacing-2);
  }
  
  .permission-category-content {
    padding: var(--spacing-2);
    gap: var(--spacing-2);
  }
  
  .permission-item {
    gap: var(--spacing-1);
  }
  
  .permission-description {
    margin-left: var(--spacing-5);
  }
  
  .custom-checkbox {
    padding-left: var(--spacing-5);
    font-size: var(--font-size-xs);
  }
  
  .checkmark {
    height: 16px;
    width: 16px;
  }
  
  .custom-checkbox .checkmark:after {
    left: 5px;
    top: 2px;
    width: 4px;
    height: 8px;
  }
}
