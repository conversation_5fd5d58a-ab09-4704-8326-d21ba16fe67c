/**
 * Simple Partner Logo Carousel
 * A lightweight, dependency-free carousel implementation
 */
document.addEventListener('DOMContentLoaded', function() {
    // Find the partner logos container
    const carouselContainer = document.querySelector('.partner-logos-carousel');
    if (!carouselContainer) {
        console.warn('Partner logos carousel container not found');
        return;
    }

    // Get all slides
    const slides = carouselContainer.querySelectorAll('.partner-logo');
    if (slides.length === 0) {
        console.warn('No partner logo slides found');
        return;
    }

    // Configuration
    const config = {
        slidesToShow: {
            desktop: 6,
            tablet: 3,
            mobile: 2
        },
        autoplay: true,
        autoplaySpeed: 3000,
        infinite: true
    };

    // State
    let currentPosition = 0;
    let autoplayInterval = null;
    
    // Create navigation buttons if they don't exist
    if (!carouselContainer.querySelector('.carousel-prev')) {
        const prevButton = document.createElement('button');
        prevButton.className = 'carousel-prev';
        prevButton.innerHTML = '&#10094;';
        carouselContainer.appendChild(prevButton);
        
        prevButton.addEventListener('click', function() {
            moveSlide(-1);
        });
    }
    
    if (!carouselContainer.querySelector('.carousel-next')) {
        const nextButton = document.createElement('button');
        nextButton.className = 'carousel-next';
        nextButton.innerHTML = '&#10095;';
        carouselContainer.appendChild(nextButton);
        
        nextButton.addEventListener('click', function() {
            moveSlide(1);
        });
    }

    // Get the number of slides to show based on screen width
    function getSlidesToShow() {
        const width = window.innerWidth;
        if (width < 768) {
            return config.slidesToShow.mobile;
        } else if (width < 981) {
            return config.slidesToShow.tablet;
        } else {
            return config.slidesToShow.desktop;
        }
    }

    // Move the carousel by a certain number of slides
    function moveSlide(direction) {
        const slidesToShow = getSlidesToShow();
        const maxPosition = slides.length - slidesToShow;
        
        // Update position
        currentPosition += direction;
        
        // Handle infinite scrolling
        if (currentPosition < 0) {
            currentPosition = config.infinite ? maxPosition : 0;
        } else if (currentPosition > maxPosition) {
            currentPosition = config.infinite ? 0 : maxPosition;
        }
        
        // Update display
        updateCarousel();
    }

    // Update the carousel display
    function updateCarousel() {
        const slidesToShow = getSlidesToShow();
        const slideWidth = 100 / slidesToShow;
        
        // Hide all slides first
        slides.forEach(slide => {
            slide.style.display = 'none';
        });
        
        // Show only the visible slides
        for (let i = 0; i < slidesToShow; i++) {
            const index = (currentPosition + i) % slides.length;
            if (slides[index]) {
                slides[index].style.display = 'block';
                slides[index].style.width = `${slideWidth}%`;
            }
        }
    }

    // Start autoplay
    function startAutoplay() {
        if (config.autoplay && !autoplayInterval) {
            autoplayInterval = setInterval(function() {
                moveSlide(1);
            }, config.autoplaySpeed);
        }
    }

    // Stop autoplay
    function stopAutoplay() {
        if (autoplayInterval) {
            clearInterval(autoplayInterval);
            autoplayInterval = null;
        }
    }

    // Handle window resize
    window.addEventListener('resize', function() {
        updateCarousel();
    });

    // Pause on hover
    carouselContainer.addEventListener('mouseenter', stopAutoplay);
    carouselContainer.addEventListener('mouseleave', startAutoplay);

    // Initialize
    updateCarousel();
    startAutoplay();
});
