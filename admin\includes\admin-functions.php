<?php
/**
 * Admin Functions
 * Core functions for the admin panel
 * Consolidated from multiple PHP files
 */

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

/**
 * Check if user is logged in
 *
 * @return bool True if user is logged in, false otherwise
 */
function is_logged_in() {
    return isset($_SESSION['user_id']) && !empty($_SESSION['user_id']);
}

/**
 * Redirect to login page if user is not logged in
 *
 * @return void
 */
function require_login() {
    if (!is_logged_in()) {
        header('Location: login.php');
        exit;
    }
}

/**
 * Check if user has admin role
 *
 * @return bool True if user has admin role, false otherwise
 */
function is_admin() {
    return isset($_SESSION['user_role']) && $_SESSION['user_role'] === 'admin';
}

/**
 * Redirect to dashboard if user does not have admin role
 *
 * @return void
 */
function require_admin() {
    require_login();

    if (!is_admin()) {
        header('Location: dashboard.php');
        exit;
    }
}

/**
 * Get current user ID
 *
 * @return int|null User ID or null if not logged in
 */
function get_current_user_id() {
    return isset($_SESSION['user_id']) ? $_SESSION['user_id'] : null;
}

/**
 * Get current user data
 *
 * @return array|null User data or null if not logged in
 */
function get_current_user_data() {
    global $conn;

    $user_id = get_current_user_id();

    if (!$user_id) {
        return null;
    }

    $stmt = $conn->prepare("SELECT * FROM users WHERE id = ?");
    $stmt->bind_param("i", $user_id);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result->num_rows === 0) {
        return null;
    }

    return $result->fetch_assoc();
}

/**
 * Get user by ID
 *
 * @param int $user_id User ID
 * @return array|null User data or null if not found
 */
function get_user($user_id) {
    global $conn;

    $stmt = $conn->prepare("SELECT * FROM users WHERE id = ?");
    $stmt->bind_param("i", $user_id);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result->num_rows === 0) {
        return null;
    }

    return $result->fetch_assoc();
}

/**
 * Get all users
 *
 * @return array Array of users
 */
function get_users() {
    global $conn;

    $result = $conn->query("SELECT * FROM users ORDER BY username");

    $users = [];

    while ($row = $result->fetch_assoc()) {
        $users[] = $row;
    }

    return $users;
}

/**
 * Get setting value
 *
 * @param string $key Setting key
 * @param mixed $default Default value if setting not found
 * @return mixed Setting value or default
 */
function get_setting($key, $default = '') {
    global $conn;

    $stmt = $conn->prepare("SELECT value FROM settings WHERE `key` = ?");
    $stmt->bind_param("s", $key);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result->num_rows === 0) {
        return $default;
    }

    $row = $result->fetch_assoc();
    return $row['value'];
}

/**
 * Update setting value
 *
 * @param string $key Setting key
 * @param mixed $value Setting value
 * @return bool True on success, false on failure
 */
function update_setting($key, $value) {
    global $conn;

    // Check if setting exists
    $stmt = $conn->prepare("SELECT id FROM settings WHERE `key` = ?");
    $stmt->bind_param("s", $key);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result->num_rows === 0) {
        // Insert new setting
        $stmt = $conn->prepare("INSERT INTO settings (`key`, value) VALUES (?, ?)");
        $stmt->bind_param("ss", $key, $value);
    } else {
        // Update existing setting
        $stmt = $conn->prepare("UPDATE settings SET value = ? WHERE `key` = ?");
        $stmt->bind_param("ss", $value, $key);
    }

    return $stmt->execute();
}

/**
 * Delete setting
 *
 * @param string $key Setting key
 * @return bool True on success, false on failure
 */
function delete_setting($key) {
    global $conn;

    $stmt = $conn->prepare("DELETE FROM settings WHERE `key` = ?");
    $stmt->bind_param("s", $key);

    return $stmt->execute();
}

/**
 * Get all settings
 *
 * @return array Array of settings
 */
function get_all_settings() {
    global $conn;

    $result = $conn->query("SELECT * FROM settings");

    $settings = [];

    while ($row = $result->fetch_assoc()) {
        $settings[$row['key']] = $row['value'];
    }

    return $settings;
}

/**
 * Sanitize input
 *
 * @param string $input Input to sanitize
 * @return string Sanitized input
 */
function sanitize_input($input) {
    return htmlspecialchars(trim($input), ENT_QUOTES, 'UTF-8');
}

/**
 * Validate email
 *
 * @param string $email Email to validate
 * @return bool True if valid, false otherwise
 */
function validate_email($email) {
    return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
}

/**
 * Validate URL
 *
 * @param string $url URL to validate
 * @return bool True if valid, false otherwise
 */
function validate_url($url) {
    return filter_var($url, FILTER_VALIDATE_URL) !== false;
}

/**
 * Generate random string
 *
 * @param int $length Length of string
 * @return string Random string
 */
function generate_random_string($length = 10) {
    $characters = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
    $charactersLength = strlen($characters);
    $randomString = '';

    for ($i = 0; $i < $length; $i++) {
        $randomString .= $characters[rand(0, $charactersLength - 1)];
    }

    return $randomString;
}

/**
 * Generate slug from string
 *
 * @param string $string String to convert to slug
 * @return string Slug
 */
function generate_slug($string) {
    // Replace non letter or digits by -
    $string = preg_replace('~[^\pL\d]+~u', '-', $string);

    // Transliterate
    $string = iconv('utf-8', 'us-ascii//TRANSLIT', $string);

    // Remove unwanted characters
    $string = preg_replace('~[^-\w]+~', '', $string);

    // Trim
    $string = trim($string, '-');

    // Remove duplicate -
    $string = preg_replace('~-+~', '-', $string);

    // Lowercase
    $string = strtolower($string);

    if (empty($string)) {
        return 'n-a';
    }

    return $string;
}

/**
 * Format date
 *
 * @param string $date Date to format
 * @param string $format Format string
 * @return string Formatted date
 */
function format_date($date, $format = 'Y-m-d H:i:s') {
    return date($format, strtotime($date));
}

/**
 * Get admin URL
 *
 * @param string $path Path to append to admin URL
 * @return string Admin URL
 */
function get_admin_url($path = '') {
    $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
    $host = $_SERVER['HTTP_HOST'];
    $script_name = $_SERVER['SCRIPT_NAME'];
    $admin_dir = dirname($script_name);

    return $protocol . '://' . $host . $admin_dir . '/' . ltrim($path, '/');
}

/**
 * Get site URL
 *
 * @param string $path Path to append to site URL
 * @return string Site URL
 */
function get_site_url($path = '') {
    $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
    $host = $_SERVER['HTTP_HOST'];
    $script_name = $_SERVER['SCRIPT_NAME'];
    $site_dir = dirname(dirname($script_name));

    if ($site_dir === '/' || $site_dir === '\\') {
        $site_dir = '';
    }

    return $protocol . '://' . $host . $site_dir . '/' . ltrim($path, '/');
}

/**
 * Add admin notice
 *
 * @param string $message Notice message
 * @param string $type Notice type (success, error, warning, info)
 * @return void
 */
function add_admin_notice($message, $type = 'info') {
    if (!isset($_SESSION['admin_notices'])) {
        $_SESSION['admin_notices'] = [];
    }

    $_SESSION['admin_notices'][] = [
        'message' => $message,
        'type' => $type
    ];
}

/**
 * Display admin notices
 *
 * @return void
 */
function display_admin_notices() {
    if (!isset($_SESSION['admin_notices']) || empty($_SESSION['admin_notices'])) {
        return;
    }

    foreach ($_SESSION['admin_notices'] as $notice) {
        echo '<div class="admin-alert ' . $notice['type'] . '">';

        switch ($notice['type']) {
            case 'success':
                echo '<i class="fas fa-check-circle"></i>';
                break;
            case 'error':
                echo '<i class="fas fa-times-circle"></i>';
                break;
            case 'warning':
                echo '<i class="fas fa-exclamation-triangle"></i>';
                break;
            default:
                echo '<i class="fas fa-info-circle"></i>';
                break;
        }

        echo $notice['message'];
        echo '<button class="dismiss"><i class="fas fa-times"></i></button>';
        echo '</div>';
    }

    // Clear notices
    $_SESSION['admin_notices'] = [];
}

/**
 * Log activity
 *
 * @param string $action Action performed
 * @param string $description Description of action
 * @param int $user_id User ID (defaults to current user)
 * @return bool True on success, false on failure
 */
function log_activity($action, $description, $user_id = null) {
    global $conn;

    if ($user_id === null) {
        $user_id = get_current_user_id();
    }

    $ip_address = $_SERVER['REMOTE_ADDR'];
    $user_agent = $_SERVER['HTTP_USER_AGENT'];

    $stmt = $conn->prepare("INSERT INTO activity_log (user_id, action, description, ip_address, user_agent) VALUES (?, ?, ?, ?, ?)");
    $stmt->bind_param("issss", $user_id, $action, $description, $ip_address, $user_agent);

    return $stmt->execute();
}

/**
 * Get activity log
 *
 * @param int $limit Number of entries to return
 * @param int $offset Offset for pagination
 * @return array Activity log entries
 */
function get_activity_log($limit = 50, $offset = 0) {
    global $conn;

    $stmt = $conn->prepare("SELECT a.*, u.username FROM activity_log a LEFT JOIN users u ON a.user_id = u.id ORDER BY a.created_at DESC LIMIT ? OFFSET ?");
    $stmt->bind_param("ii", $limit, $offset);
    $stmt->execute();
    $result = $stmt->get_result();

    $log = [];

    while ($row = $result->fetch_assoc()) {
        $log[] = $row;
    }

    return $log;
}

/**
 * Create backup of settings
 *
 * @return string|bool Backup filename on success, false on failure
 */
function create_settings_backup() {
    global $conn;

    $settings = get_all_settings();
    $backup_data = json_encode($settings);

    $backup_dir = __DIR__ . '/../backups';

    if (!file_exists($backup_dir)) {
        mkdir($backup_dir, 0755, true);
    }

    $backup_file = $backup_dir . '/backup_' . date('Y-m-d_H-i-s') . '.json';

    if (file_put_contents($backup_file, $backup_data)) {
        return basename($backup_file);
    }

    return false;
}

/**
 * Restore settings from backup
 *
 * @param string $backup_file Backup filename
 * @return bool True on success, false on failure
 */
function restore_settings_backup($backup_file) {
    global $conn;

    $backup_path = __DIR__ . '/../backups/' . $backup_file;

    if (!file_exists($backup_path)) {
        return false;
    }

    $backup_data = file_get_contents($backup_path);
    $settings = json_decode($backup_data, true);

    if (!$settings) {
        return false;
    }

    // Begin transaction
    $conn->begin_transaction();

    try {
        // Clear existing settings
        $conn->query("DELETE FROM settings");

        // Insert new settings
        foreach ($settings as $key => $value) {
            update_setting($key, $value);
        }

        // Commit transaction
        $conn->commit();

        return true;
    } catch (Exception $e) {
        // Rollback transaction
        $conn->rollback();

        return false;
    }
}

/**
 * Get available backups
 *
 * @return array Array of backup files
 */
function get_available_backups() {
    $backup_dir = __DIR__ . '/../backups';

    if (!file_exists($backup_dir)) {
        return [];
    }

    $backups = glob($backup_dir . '/backup_*.json');
    $backups = array_map('basename', $backups);
    rsort($backups); // Sort by newest first

    return $backups;
}

/**
 * Get system information
 *
 * @return array System information
 */
function get_system_info() {
    $info = [
        'php_version' => PHP_VERSION,
        'mysql_version' => mysqli_get_client_info(),
        'server_software' => $_SERVER['SERVER_SOFTWARE'],
        'os' => PHP_OS,
        'max_upload_size' => ini_get('upload_max_filesize'),
        'max_post_size' => ini_get('post_max_size'),
        'memory_limit' => ini_get('memory_limit'),
        'max_execution_time' => ini_get('max_execution_time') . ' seconds',
        'extensions' => get_loaded_extensions(),
        'disabled_functions' => ini_get('disable_functions'),
        'server_time' => date('Y-m-d H:i:s'),
        'server_timezone' => date_default_timezone_get()
    ];

    return $info;
}

/**
 * Check if system meets requirements
 *
 * @return array Requirements check results
 */
function check_system_requirements() {
    $requirements = [
        'php_version' => [
            'name' => 'PHP Version',
            'required' => '7.0.0',
            'current' => PHP_VERSION,
            'status' => version_compare(PHP_VERSION, '7.0.0', '>=')
        ],
        'mysql_version' => [
            'name' => 'MySQL Version',
            'required' => '5.6.0',
            'current' => mysqli_get_client_info(),
            'status' => version_compare(mysqli_get_client_info(), '5.6.0', '>=')
        ],
        'extensions' => [
            'mysqli' => [
                'name' => 'MySQLi Extension',
                'status' => extension_loaded('mysqli')
            ],
            'gd' => [
                'name' => 'GD Extension',
                'status' => extension_loaded('gd')
            ],
            'curl' => [
                'name' => 'cURL Extension',
                'status' => extension_loaded('curl')
            ],
            'json' => [
                'name' => 'JSON Extension',
                'status' => extension_loaded('json')
            ],
            'mbstring' => [
                'name' => 'Multibyte String Extension',
                'status' => extension_loaded('mbstring')
            ]
        ],
        'writable_directories' => [
            'uploads' => [
                'name' => 'Uploads Directory',
                'path' => __DIR__ . '/../../uploads',
                'status' => is_writable(__DIR__ . '/../../uploads')
            ],
            'backups' => [
                'name' => 'Backups Directory',
                'path' => __DIR__ . '/../backups',
                'status' => is_writable(__DIR__ . '/../backups') || mkdir(__DIR__ . '/../backups', 0755, true)
            ]
        ]
    ];

    return $requirements;
}
