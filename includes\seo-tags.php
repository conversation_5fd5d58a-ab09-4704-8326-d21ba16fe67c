<?php
/**
 * SEO Tags
 * This file contains functions to generate SEO tags for HTML pages
 */

/**
 * Generate canonical tag
 * 
 * @param string $path The current page path
 * @return string The canonical tag HTML
 */
function getCanonicalTag($path = '') {
    // Get the base URL
    $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
    $host = $_SERVER['HTTP_HOST'];
    $baseUrl = $protocol . '://' . $host;
    
    // If no path is provided, use the current request URI
    if (empty($path)) {
        $path = $_SERVER['REQUEST_URI'];
        
        // Remove query parameters
        $path = strtok($path, '?');
    }
    
    // Build the canonical URL
    $canonicalUrl = $baseUrl . $path;
    
    // Return the canonical tag
    return '<link rel="canonical" href="' . htmlspecialchars($canonicalUrl) . '" />';
}

/**
 * Generate meta tags for a page
 * 
 * @param array $data The page data
 * @return string The meta tags HTML
 */
function getMetaTags($data = []) {
    $title = isset($data['title']) ? $data['title'] : 'Manage Inc';
    $description = isset($data['description']) ? $data['description'] : 'Manage Inc - Professional Management Services';
    $keywords = isset($data['keywords']) ? $data['keywords'] : 'manage, management, services, professional';
    $ogImage = isset($data['og_image']) ? $data['og_image'] : '/images/logo.png';
    
    // Get the base URL
    $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
    $host = $_SERVER['HTTP_HOST'];
    $baseUrl = $protocol . '://' . $host;
    
    // Build the meta tags
    $metaTags = '';
    
    // Basic meta tags
    $metaTags .= '<meta name="description" content="' . htmlspecialchars($description) . '" />' . PHP_EOL;
    $metaTags .= '<meta name="keywords" content="' . htmlspecialchars($keywords) . '" />' . PHP_EOL;
    
    // Open Graph meta tags
    $metaTags .= '<meta property="og:title" content="' . htmlspecialchars($title) . '" />' . PHP_EOL;
    $metaTags .= '<meta property="og:description" content="' . htmlspecialchars($description) . '" />' . PHP_EOL;
    $metaTags .= '<meta property="og:image" content="' . $baseUrl . $ogImage . '" />' . PHP_EOL;
    $metaTags .= '<meta property="og:url" content="' . $baseUrl . $_SERVER['REQUEST_URI'] . '" />' . PHP_EOL;
    $metaTags .= '<meta property="og:type" content="website" />' . PHP_EOL;
    
    // Twitter Card meta tags
    $metaTags .= '<meta name="twitter:card" content="summary_large_image" />' . PHP_EOL;
    $metaTags .= '<meta name="twitter:title" content="' . htmlspecialchars($title) . '" />' . PHP_EOL;
    $metaTags .= '<meta name="twitter:description" content="' . htmlspecialchars($description) . '" />' . PHP_EOL;
    $metaTags .= '<meta name="twitter:image" content="' . $baseUrl . $ogImage . '" />' . PHP_EOL;
    
    return $metaTags;
}

/**
 * Generate structured data for a page
 * 
 * @param string $type The type of structured data (article, website, etc.)
 * @param array $data The page data
 * @return string The structured data HTML
 */
function getStructuredData($type = 'website', $data = []) {
    // Get the base URL
    $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
    $host = $_SERVER['HTTP_HOST'];
    $baseUrl = $protocol . '://' . $host;
    
    // Default data
    $structuredData = [];
    
    // Build structured data based on type
    switch ($type) {
        case 'article':
            $structuredData = [
                '@context' => 'https://schema.org',
                '@type' => 'Article',
                'headline' => isset($data['title']) ? $data['title'] : 'Article',
                'description' => isset($data['description']) ? $data['description'] : '',
                'image' => isset($data['image']) ? $baseUrl . $data['image'] : '',
                'datePublished' => isset($data['date_published']) ? $data['date_published'] : date('Y-m-d'),
                'dateModified' => isset($data['date_modified']) ? $data['date_modified'] : date('Y-m-d'),
                'author' => [
                    '@type' => 'Organization',
                    'name' => 'Manage Inc'
                ],
                'publisher' => [
                    '@type' => 'Organization',
                    'name' => 'Manage Inc',
                    'logo' => [
                        '@type' => 'ImageObject',
                        'url' => $baseUrl . '/images/logo.png'
                    ]
                ],
                'mainEntityOfPage' => [
                    '@type' => 'WebPage',
                    '@id' => $baseUrl . $_SERVER['REQUEST_URI']
                ]
            ];
            break;
            
        case 'website':
        default:
            $structuredData = [
                '@context' => 'https://schema.org',
                '@type' => 'WebSite',
                'name' => 'Manage Inc',
                'url' => $baseUrl,
                'potentialAction' => [
                    '@type' => 'SearchAction',
                    'target' => $baseUrl . '/search?q={search_term_string}',
                    'query-input' => 'required name=search_term_string'
                ]
            ];
            break;
    }
    
    // Return the structured data as JSON-LD
    return '<script type="application/ld+json">' . json_encode($structuredData, JSON_UNESCAPED_SLASHES | JSON_PRETTY_PRINT) . '</script>';
}
?>
