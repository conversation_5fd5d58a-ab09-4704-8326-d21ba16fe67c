document.addEventListener('DOMContentLoaded', function() {
    // Get the submenu toggle element
    const submenuToggle = document.querySelector('.submenu-toggle');
    const submenuContainer = document.querySelector('.submenu-container');
    const pageSubmenu = document.querySelector('.page-submenu');
    let isFixed = false;

    // Find the active menu item and update the toggle text
    if (submenuToggle) {
        const activeMenuItem = document.querySelector('.submenu a.active');
        if (activeMenuItem) {
            // Always set the toggle text to the active menu item text
            submenuToggle.textContent = activeMenuItem.textContent;
        } else {
            // If no active item is found, find the first submenu item
            const firstMenuItem = document.querySelector('.submenu a');
            if (firstMenuItem) {
                submenuToggle.textContent = firstMenuItem.textContent;
            }
        }

        // Make sure the toggle is only visible on mobile
        if (window.innerWidth <= 768) {
            submenuToggle.style.display = 'block';
        } else {
            submenuToggle.style.display = 'none';
        }
    }

    // Function to toggle submenu
    function toggleSubmenu(e) {
        if (e) {
            e.preventDefault();
            e.stopPropagation();
        }

        if (submenuToggle && submenuContainer) {
            submenuToggle.classList.toggle('active');
            submenuContainer.classList.toggle('active');
            submenuContainer.style.display = submenuContainer.classList.contains('active') ? 'block' : 'none';
            console.log('Submenu toggled');
        }
    }

    // Add click event listener to toggle submenu
    if (submenuToggle && submenuContainer) {
        submenuToggle.addEventListener('click', toggleSubmenu);

        // Add click event listeners to submenu items
        const submenuItems = document.querySelectorAll('.submenu a');
        submenuItems.forEach(item => {
            item.addEventListener('click', function() {
                // Update toggle text with selected item text
                if (window.innerWidth <= 768) {
                    submenuToggle.textContent = this.textContent;
                    // Close the submenu
                    submenuToggle.classList.remove('active');
                    submenuContainer.classList.remove('active');
                }
            });
        });
    }

    // Add window resize listener to handle responsive behavior
    window.addEventListener('resize', function() {
        if (submenuToggle) {
            if (window.innerWidth <= 768) {
                submenuToggle.style.display = 'block';
                submenuContainer.style.display = submenuContainer.classList.contains('active') ? 'block' : 'none';
            } else {
                submenuToggle.style.display = 'none';
                submenuContainer.style.display = 'block';
            }
        }
    });

    // Handle submenu when scrolling
    if (pageSubmenu) {
        const header = document.querySelector('.main-header');
        const headerHeight = header ? header.offsetHeight : 60;
        const submenuOriginalTop = pageSubmenu.offsetTop;

        window.addEventListener('scroll', function() {
            const scrollPosition = window.scrollY;

            // Check if we should fix the submenu under the header
            if (scrollPosition > submenuOriginalTop - headerHeight) {
                if (!isFixed) {
                    pageSubmenu.style.position = 'fixed';
                    pageSubmenu.style.top = headerHeight + 'px';
                    pageSubmenu.style.left = '0';
                    pageSubmenu.style.right = '0';
                    pageSubmenu.style.zIndex = '999';

                    // Add padding to the next element to prevent content jump
                    if (pageSubmenu.nextElementSibling) {
                        pageSubmenu.nextElementSibling.style.paddingTop = pageSubmenu.offsetHeight + 'px';
                    }

                    isFixed = true;

                    // Ensure submenu toggle works when fixed
                    if (submenuToggle && window.innerWidth <= 768) {
                        submenuToggle.addEventListener('click', toggleSubmenu);
                    }
                }
            } else {
                if (isFixed) {
                    pageSubmenu.style.position = '';
                    pageSubmenu.style.top = '';
                    pageSubmenu.style.left = '';
                    pageSubmenu.style.right = '';
                    pageSubmenu.style.zIndex = '';

                    // Remove padding from the next element
                    if (pageSubmenu.nextElementSibling) {
                        pageSubmenu.nextElementSibling.style.paddingTop = '';
                    }

                    isFixed = false;
                }
            }
        });
    }
});
