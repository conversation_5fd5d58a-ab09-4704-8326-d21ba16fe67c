<?php
/**
 * User Functions
 * Functions for user management
 * Consolidated from multiple PHP files
 */

/**
 * Get all users
 *
 * @return array Array of users
 */
function get_users() {
    global $conn;

    $result = $conn->query("SELECT * FROM users ORDER BY username");

    $users = [];

    while ($row = $result->fetch_assoc()) {
        $users[] = $row;
    }

    return $users;
}

/**
 * Get user by ID
 *
 * @param int $id User ID
 * @return array|null User data or null if not found
 */
function get_user($id) {
    global $conn;

    $stmt = $conn->prepare("SELECT * FROM users WHERE id = ?");
    $stmt->bind_param("i", $id);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result->num_rows === 0) {
        return null;
    }

    return $result->fetch_assoc();
}

/**
 * Get user by email
 *
 * @param string $email User email
 * @return array|null User data or null if not found
 */
function get_user_by_email($email) {
    global $conn;

    $stmt = $conn->prepare("SELECT * FROM users WHERE email = ?");
    $stmt->bind_param("s", $email);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result->num_rows === 0) {
        return null;
    }

    return $result->fetch_assoc();
}

/**
 * Get user by username
 *
 * @param string $username Username
 * @return array|null User data or null if not found
 */
function get_user_by_username($username) {
    global $conn;

    $stmt = $conn->prepare("SELECT * FROM users WHERE username = ?");
    $stmt->bind_param("s", $username);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result->num_rows === 0) {
        return null;
    }

    return $result->fetch_assoc();
}

/**
 * Get current user ID
 *
 * @return int|null User ID or null if not logged in
 */
function get_current_user_id() {
    return isset($_SESSION['user_id']) ? $_SESSION['user_id'] : null;
}

/**
 * Get current user data
 *
 * @return array|null User data or null if not logged in
 */
function get_current_user_data() {
    $user_id = get_current_user_id();

    if (!$user_id) {
        return null;
    }

    return get_user($user_id);
}

/**
 * Check if user is logged in
 *
 * @return bool True if user is logged in, false otherwise
 */
function is_logged_in() {
    return isset($_SESSION['user_id']) && !empty($_SESSION['user_id']);
}

/**
 * Check if user has admin role
 *
 * @return bool True if user has admin role, false otherwise
 */
function is_admin() {
    return isset($_SESSION['user_role']) && $_SESSION['user_role'] === 'admin';
}

/**
 * Add user
 *
 * @param array $data User data
 * @return int|bool New user ID on success, false on failure
 */
function add_user($data) {
    global $conn;

    // Check if email already exists
    $stmt = $conn->prepare("SELECT id FROM users WHERE email = ?");
    $stmt->bind_param("s", $data['email']);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result->num_rows > 0) {
        return false; // Email already exists
    }

    // Check if username already exists
    $stmt = $conn->prepare("SELECT id FROM users WHERE username = ?");
    $stmt->bind_param("s", $data['username']);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result->num_rows > 0) {
        return false; // Username already exists
    }

    // Hash password
    $password_hash = password_hash($data['password'], PASSWORD_DEFAULT);

    // Generate verification token if needed
    $verification_token = null;
    if (isset($data['verified']) && $data['verified'] === 0) {
        $verification_token = generate_token();
    }

    // Set default values
    $role = isset($data['role']) ? $data['role'] : 'user';
    $verified = isset($data['verified']) ? $data['verified'] : 0;
    $created_at = date('Y-m-d H:i:s');

    // Insert user
    $stmt = $conn->prepare("INSERT INTO users (username, email, password, role, verified, verification_token, created_at)
                           VALUES (?, ?, ?, ?, ?, ?, ?)");

    $stmt->bind_param("sssssss",
        $data['username'],
        $data['email'],
        $password_hash,
        $role,
        $verified,
        $verification_token,
        $created_at
    );

    if ($stmt->execute()) {
        $user_id = $conn->insert_id;

        // Send verification email if needed
        if ($verification_token) {
            send_verification_email($data['email'], $data['username'], $verification_token);
        }

        return $user_id;
    }

    return false;
}

/**
 * Update user
 *
 * @param int $id User ID
 * @param array $data User data
 * @return bool True on success, false on failure
 */
function update_user($id, $data) {
    global $conn;

    // Check if email already exists for other users
    if (isset($data['email'])) {
        $stmt = $conn->prepare("SELECT id FROM users WHERE email = ? AND id != ?");
        $stmt->bind_param("si", $data['email'], $id);
        $stmt->execute();
        $result = $stmt->get_result();

        if ($result->num_rows > 0) {
            return false; // Email already exists
        }
    }

    // Check if username already exists for other users
    if (isset($data['username'])) {
        $stmt = $conn->prepare("SELECT id FROM users WHERE username = ? AND id != ?");
        $stmt->bind_param("si", $data['username'], $id);
        $stmt->execute();
        $result = $stmt->get_result();

        if ($result->num_rows > 0) {
            return false; // Username already exists
        }
    }

    // Build update query
    $query = "UPDATE users SET ";
    $params = [];
    $types = "";

    if (isset($data['username'])) {
        $query .= "username = ?, ";
        $params[] = $data['username'];
        $types .= "s";
    }

    if (isset($data['email'])) {
        $query .= "email = ?, ";
        $params[] = $data['email'];
        $types .= "s";
    }

    if (isset($data['password'])) {
        $password_hash = password_hash($data['password'], PASSWORD_DEFAULT);
        $query .= "password = ?, ";
        $params[] = $password_hash;
        $types .= "s";
    }

    if (isset($data['role'])) {
        $query .= "role = ?, ";
        $params[] = $data['role'];
        $types .= "s";
    }

    if (isset($data['verified'])) {
        $query .= "verified = ?, ";
        $params[] = $data['verified'];
        $types .= "s";
    }

    if (isset($data['verification_token'])) {
        $query .= "verification_token = ?, ";
        $params[] = $data['verification_token'];
        $types .= "s";
    }

    if (isset($data['reset_token'])) {
        $query .= "reset_token = ?, ";
        $params[] = $data['reset_token'];
        $types .= "s";
    }

    if (isset($data['reset_token_expires'])) {
        $query .= "reset_token_expires = ?, ";
        $params[] = $data['reset_token_expires'];
        $types .= "s";
    }

    // Remove trailing comma and space
    $query = rtrim($query, ", ");

    // Add WHERE clause
    $query .= " WHERE id = ?";
    $params[] = $id;
    $types .= "i";

    // Execute query
    $stmt = $conn->prepare($query);
    $stmt->bind_param($types, ...$params);

    return $stmt->execute();
}

/**
 * Delete user
 *
 * @param int $id User ID
 * @return bool True on success, false on failure
 */
function delete_user($id) {
    global $conn;

    $stmt = $conn->prepare("DELETE FROM users WHERE id = ?");
    $stmt->bind_param("i", $id);

    return $stmt->execute();
}

/**
 * Authenticate user
 *
 * @param string $username Username or email
 * @param string $password Password
 * @return array|bool User data on success, false on failure
 */
function authenticate_user($username, $password) {
    global $conn;

    // Check if username is an email
    if (filter_var($username, FILTER_VALIDATE_EMAIL)) {
        $stmt = $conn->prepare("SELECT * FROM users WHERE email = ?");
    } else {
        $stmt = $conn->prepare("SELECT * FROM users WHERE username = ?");
    }

    $stmt->bind_param("s", $username);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result->num_rows === 0) {
        return false; // User not found
    }

    $user = $result->fetch_assoc();

    // Verify password
    if (!password_verify($password, $user['password'])) {
        return false; // Incorrect password
    }

    // Check if user is verified
    if ($user['verified'] === '0') {
        return false; // User not verified
    }

    return $user;
}

/**
 * Login user
 *
 * @param array $user User data
 * @return void
 */
function login_user($user) {
    // Start session if not already started
    if (session_status() === PHP_SESSION_NONE) {
        session_start();
    }

    // Set session variables
    $_SESSION['user_id'] = $user['id'];
    $_SESSION['username'] = $user['username'];
    $_SESSION['user_role'] = $user['role'];

    // Update last login time
    global $conn;
    $stmt = $conn->prepare("UPDATE users SET last_login = NOW() WHERE id = ?");
    $stmt->bind_param("i", $user['id']);
    $stmt->execute();

    // Log activity
    log_activity('login', 'User logged in');
}

/**
 * Logout user
 *
 * @return void
 */
function logout_user() {
    // Log activity
    log_activity('logout', 'User logged out');

    // Start session if not already started
    if (session_status() === PHP_SESSION_NONE) {
        session_start();
    }

    // Unset all session variables
    $_SESSION = [];

    // Destroy the session
    session_destroy();
}

/**
 * Generate token
 *
 * @param int $length Token length
 * @return string Generated token
 */
function generate_token($length = 32) {
    return bin2hex(random_bytes($length / 2));
}

/**
 * Verify user email
 *
 * @param string $token Verification token
 * @return bool True on success, false on failure
 */
function verify_user_email($token) {
    global $conn;

    $stmt = $conn->prepare("SELECT id FROM users WHERE verification_token = ?");
    $stmt->bind_param("s", $token);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result->num_rows === 0) {
        return false; // Invalid token
    }

    $user = $result->fetch_assoc();

    // Update user
    $stmt = $conn->prepare("UPDATE users SET verified = 1, verification_token = NULL WHERE id = ?");
    $stmt->bind_param("i", $user['id']);

    return $stmt->execute();
}

/**
 * Request password reset
 *
 * @param string $email User email
 * @return bool True on success, false on failure
 */
function request_password_reset($email) {
    global $conn;

    $stmt = $conn->prepare("SELECT id, username FROM users WHERE email = ?");
    $stmt->bind_param("s", $email);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result->num_rows === 0) {
        return false; // User not found
    }

    $user = $result->fetch_assoc();

    // Generate reset token
    $reset_token = generate_token();
    $reset_token_expires = date('Y-m-d H:i:s', strtotime('+1 hour'));

    // Update user
    $stmt = $conn->prepare("UPDATE users SET reset_token = ?, reset_token_expires = ? WHERE id = ?");
    $stmt->bind_param("ssi", $reset_token, $reset_token_expires, $user['id']);

    if ($stmt->execute()) {
        // Send password reset email
        return send_password_reset_email($email, $user['username'], $reset_token);
    }

    return false;
}

/**
 * Reset password
 *
 * @param string $token Reset token
 * @param string $password New password
 * @return bool True on success, false on failure
 */
function reset_password($token, $password) {
    global $conn;

    $stmt = $conn->prepare("SELECT id FROM users WHERE reset_token = ? AND reset_token_expires > NOW()");
    $stmt->bind_param("s", $token);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result->num_rows === 0) {
        return false; // Invalid or expired token
    }

    $user = $result->fetch_assoc();

    // Hash password
    $password_hash = password_hash($password, PASSWORD_DEFAULT);

    // Update user
    $stmt = $conn->prepare("UPDATE users SET password = ?, reset_token = NULL, reset_token_expires = NULL WHERE id = ?");
    $stmt->bind_param("si", $password_hash, $user['id']);

    return $stmt->execute();
}

/**
 * Change password
 *
 * @param int $user_id User ID
 * @param string $current_password Current password
 * @param string $new_password New password
 * @return bool True on success, false on failure
 */
function change_password($user_id, $current_password, $new_password) {
    global $conn;

    $stmt = $conn->prepare("SELECT password FROM users WHERE id = ?");
    $stmt->bind_param("i", $user_id);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result->num_rows === 0) {
        return false; // User not found
    }

    $user = $result->fetch_assoc();

    // Verify current password
    if (!password_verify($current_password, $user['password'])) {
        return false; // Incorrect password
    }

    // Hash new password
    $password_hash = password_hash($new_password, PASSWORD_DEFAULT);

    // Update user
    $stmt = $conn->prepare("UPDATE users SET password = ? WHERE id = ?");
    $stmt->bind_param("si", $password_hash, $user_id);

    return $stmt->execute();
}
