<?php
// Include necessary files
require_once 'config.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header('Location: login.php');
    exit;
}

// Include header
include 'includes/header.php';
?>

<div class="admin-content">
    <div class="admin-content-inner">
        <div class="admin-card">
            <div class="admin-card-header">
                <h2><i class="fas fa-envelope"></i> Gmail SMTP Troubleshooting Guide</h2>
                <div class="admin-card-actions">
                    <a href="simple_email_test.php" class="admin-btn secondary"><i class="fas fa-arrow-left"></i> Back to Email Test</a>
                </div>
            </div>
            <div class="admin-card-body">
                <div class="alert alert-info">
                    <p><strong>Note:</strong> This guide helps you configure Gmail SMTP for reliable email delivery.</p>
                </div>

                <h3>Common Gmail SMTP Issues</h3>
                
                <div class="troubleshooting-section">
                    <h4>1. Authentication Issues</h4>
                    <p>If you're seeing authentication errors when trying to use Gmail SMTP, it's likely due to one of these reasons:</p>
                    
                    <div class="solution-box">
                        <h5>Two-Factor Authentication (2FA) is Enabled</h5>
                        <p>When 2FA is enabled on your Google account, you <strong>cannot</strong> use your regular password for SMTP. Instead, you must create an App Password:</p>
                        <ol>
                            <li>Go to your <a href="https://myaccount.google.com/security" target="_blank">Google Account Security settings</a></li>
                            <li>Under "Signing in to Google," select "App passwords" (you may need to sign in again)</li>
                            <li>At the bottom, select "Select app" and choose "Mail"</li>
                            <li>Select "Select device" and choose "Other"</li>
                            <li>Enter a name (e.g., "ManageInc SMTP")</li>
                            <li>Click "Generate"</li>
                            <li>Use the 16-character password that appears as your SMTP password</li>
                        </ol>
                    </div>
                    
                    <div class="solution-box">
                        <h5>Less Secure App Access</h5>
                        <p>If you don't have 2FA enabled, you need to allow "Less secure app access":</p>
                        <ol>
                            <li>Go to your <a href="https://myaccount.google.com/security" target="_blank">Google Account Security settings</a></li>
                            <li>Scroll down to "Less secure app access"</li>
                            <li>Turn on "Allow less secure apps"</li>
                            <li><strong>Note:</strong> Google is phasing this option out, so using an App Password with 2FA is recommended</li>
                        </ol>
                    </div>
                </div>
                
                <div class="troubleshooting-section">
                    <h4>2. Connection Issues</h4>
                    
                    <div class="solution-box">
                        <h5>Correct SMTP Settings for Gmail</h5>
                        <p>Make sure you're using these exact settings:</p>
                        <ul>
                            <li><strong>SMTP Host:</strong> smtp.gmail.com</li>
                            <li><strong>SMTP Port:</strong> 587 (TLS) or 465 (SSL)</li>
                            <li><strong>SMTP Security:</strong> TLS for port 587, SSL for port 465</li>
                            <li><strong>SMTP Username:</strong> Your full Gmail address</li>
                            <li><strong>SMTP Password:</strong> Your password or App Password</li>
                        </ul>
                    </div>
                    
                    <div class="solution-box">
                        <h5>Firewall or Network Restrictions</h5>
                        <p>Some networks block outgoing SMTP connections:</p>
                        <ul>
                            <li>Check if your hosting provider or network blocks outgoing SMTP connections</li>
                            <li>Try using port 587 with TLS instead of port 465 (or vice versa)</li>
                            <li>Contact your network administrator if you suspect firewall issues</li>
                        </ul>
                    </div>
                </div>
                
                <div class="troubleshooting-section">
                    <h4>3. Gmail Account Security Restrictions</h4>
                    
                    <div class="solution-box">
                        <h5>New Location/Device Alerts</h5>
                        <p>Gmail may block connections from new locations or devices:</p>
                        <ul>
                            <li>Check your Gmail inbox for security alerts</li>
                            <li>Look for emails about "Critical security alert" or "New sign-in"</li>
                            <li>Confirm that the new sign-in was you</li>
                        </ul>
                    </div>
                    
                    <div class="solution-box">
                        <h5>Account Activity</h5>
                        <p>Check your recent account activity:</p>
                        <ol>
                            <li>Go to your <a href="https://myaccount.google.com/security" target="_blank">Google Account Security settings</a></li>
                            <li>Scroll down to "Your devices" and click "Manage devices"</li>
                            <li>Look for any blocked sign-in attempts</li>
                        </ol>
                    </div>
                </div>
                
                <div class="troubleshooting-section">
                    <h4>4. Email Delivery Issues</h4>
                    
                    <div class="solution-box">
                        <h5>Spam Filtering</h5>
                        <p>Even if the email is sent successfully, it might be filtered as spam:</p>
                        <ul>
                            <li>Check the spam/junk folder in the recipient's email</li>
                            <li>Add your sending email address to the recipient's contacts</li>
                            <li>Use a consistent "From" name and email address</li>
                        </ul>
                    </div>
                    
                    <div class="solution-box">
                        <h5>Gmail Sending Limits</h5>
                        <p>Gmail has sending limits that may affect bulk emails:</p>
                        <ul>
                            <li>Regular Gmail accounts: 500 emails per day</li>
                            <li>Google Workspace accounts: 2,000 emails per day</li>
                            <li>Consider using a dedicated email service for bulk emails</li>
                        </ul>
                    </div>
                </div>
                
                <div class="alert alert-warning">
                    <p><strong>Important:</strong> For production use, consider using a dedicated email service like SendGrid, Mailgun, or Amazon SES instead of Gmail SMTP. These services offer better deliverability, higher sending limits, and detailed analytics.</p>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.troubleshooting-section {
    margin-bottom: 30px;
}
.solution-box {
    background-color: #f9f9f9;
    border: 1px solid #e0e0e0;
    border-radius: 5px;
    padding: 15px;
    margin-bottom: 15px;
}
.solution-box h5 {
    margin-top: 0;
    color: #333;
}
.alert {
    padding: 15px;
    border-radius: 5px;
    margin-bottom: 20px;
}
.alert-info {
    background-color: #d1ecf1;
    color: #0c5460;
    border: 1px solid #bee5eb;
}
.alert-warning {
    background-color: #fff3cd;
    color: #856404;
    border: 1px solid #ffeeba;
}
</style>

<?php include 'includes/footer.php'; ?>
