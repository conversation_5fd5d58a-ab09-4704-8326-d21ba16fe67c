/**
 * Dropdown Override CSS
 * This file contains !important declarations to ensure dropdown styles are never overridden
 * Load this LAST to guarantee dropdown functionality
 */

/* Force dropdown container to be positioned correctly */
.topbar-user-menu {
  position: relative !important;
  height: 100% !important;
  display: flex !important;
  align-items: center !important;
  margin-left: 8px !important;
  z-index: 1000 !important;
}

/* Force toggle button styling */
.user-menu-toggle {
  display: flex !important;
  align-items: center !important;
  gap: 0.5rem !important;
  padding: 0.25rem 0.5rem !important;
  background-color: transparent !important;
  border: none !important;
  border-radius: 0.375rem !important;
  color: #333333 !important;
  font-size: 0.875rem !important;
  cursor: pointer !important;
  transition: all 0.15s ease !important;
  height: 40px !important;
}

.user-menu-toggle:hover {
  background-color: rgba(0, 0, 0, 0.05) !important;
}

/* Force avatar styling */
.user-avatar {
  position: relative !important;
  width: 36px !important;
  height: 36px !important;
  border-radius: 50rem !important;
  overflow: hidden !important;
  flex-shrink: 0 !important;
  background-color: #f8f9fa !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  border: 2px solid #ffffff !important;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
}

.avatar-placeholder {
  width: 100% !important;
  height: 100% !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  background-color: #f1ca2f !important;
  color: #ffffff !important;
  font-weight: 700 !important;
  font-size: 1rem !important;
  text-transform: uppercase !important;
}

/* Force dropdown arrow */
.dropdown-icon {
  font-size: 0.75rem !important;
  transition: transform 0.15s ease !important;
  margin-left: auto !important;
  color: #6c757d !important;
}

.user-menu-toggle.active .dropdown-icon {
  transform: rotate(180deg) !important;
  color: #f1ca2f !important;
}

/* Force user name visibility */
.user-name {
  font-weight: 500 !important;
  display: none !important;
  white-space: nowrap !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
  max-width: 120px !important;
  margin-right: 0.5rem !important;
}

@media (min-width: 768px) {
  .user-name {
    display: inline-block !important;
  }
}

/* FORCE DROPDOWN VISIBILITY AND POSITIONING */
.user-dropdown {
  position: absolute !important;
  top: calc(100% + 5px) !important;
  right: 10px !important;
  z-index: 9999 !important;
  width: 320px !important;
  background-color: #ffffff !important;
  border-radius: 0.5rem !important;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04) !important;
  border: 1px solid #dee2e6 !important;
  display: none !important;
  transform: translateY(10px) !important;
  opacity: 0 !important;
  transition: transform 0.15s ease, opacity 0.15s ease !important;
  overflow: visible !important;
  margin-top: 5px !important;
}

.user-dropdown.show {
  display: block !important;
  transform: translateY(0) !important;
  opacity: 1 !important;
  z-index: 9999 !important;
}

/* Force dropdown header */
.user-dropdown-header {
  padding: 1.5rem !important;
  display: flex !important;
  align-items: center !important;
  gap: 1rem !important;
  border-bottom: 1px solid #e9ecef !important;
  background-color: #f8f9fa !important;
  margin-bottom: 0 !important;
  visibility: visible !important;
  opacity: 1 !important;
  position: relative !important;
  z-index: 1 !important;
}

.user-dropdown-avatar {
  position: relative !important;
  width: 60px !important;
  height: 60px !important;
  border-radius: 50rem !important;
  overflow: hidden !important;
  flex-shrink: 0 !important;
  border: 3px solid #ffffff !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
  display: flex !important;
  visibility: visible !important;
  opacity: 1 !important;
}

.avatar-placeholder-large {
  width: 100% !important;
  height: 100% !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  background-color: #f1ca2f !important;
  color: #ffffff !important;
  font-weight: 700 !important;
  font-size: 1.25rem !important;
  text-transform: uppercase !important;
}

.user-dropdown-info {
  flex: 1 !important;
  min-width: 0 !important;
  display: block !important;
  visibility: visible !important;
  opacity: 1 !important;
}

.user-dropdown-name {
  margin: 0 0 4px 0 !important;
  font-size: 1rem !important;
  font-weight: 700 !important;
  color: #333333 !important;
  white-space: nowrap !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
  display: block !important;
  visibility: visible !important;
  opacity: 1 !important;
}

.user-dropdown-email {
  margin: 0 0 8px 0 !important;
  font-size: 0.75rem !important;
  color: #6c757d !important;
  white-space: nowrap !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
  display: block !important;
  visibility: visible !important;
  opacity: 1 !important;
}

.user-dropdown-badge {
  display: inline-block !important;
  padding: 2px 8px !important;
  background-color: #f1ca2f !important;
  color: #212529 !important;
  font-size: 10px !important;
  font-weight: 500 !important;
  border-radius: 12px !important;
  text-transform: uppercase !important;
  letter-spacing: 0.5px !important;
  visibility: visible !important;
  opacity: 1 !important;
}

/* Force dropdown menu visibility */
.dropdown-menu {
  padding: 0 !important;
  margin: 0 !important;
  display: block !important;
  visibility: visible !important;
  opacity: 1 !important;
  position: relative !important;
  z-index: 1 !important;
}

.dropdown-section {
  margin-bottom: 1rem !important;
  border-bottom: 1px solid #dee2e6 !important;
  padding-bottom: 1rem !important;
}

.dropdown-section:last-child {
  border-bottom: none !important;
  margin-bottom: 0 !important;
  padding-bottom: 0 !important;
}

.dropdown-item {
  margin: 0 !important;
  padding: 0 !important;
  display: block !important;
  visibility: visible !important;
  opacity: 1 !important;
  position: relative !important;
}

.dropdown-link {
  padding: 1rem 1.5rem !important;
  display: flex !important;
  align-items: center !important;
  gap: 1rem !important;
  color: #212529 !important;
  text-decoration: none !important;
  transition: all 0.15s ease !important;
  font-size: 0.875rem !important;
  border-left: 3px solid transparent !important;
  visibility: visible !important;
}

.dropdown-link:hover {
  background-color: #f8f9fa !important;
  color: #f1ca2f !important;
  border-left-color: #f1ca2f !important;
}

/* Force dropdown icons */
.dropdown-icon {
  width: 24px !important;
  height: 24px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  color: #6c757d !important;
  font-size: 1rem !important;
  background-color: #f8f9fa !important;
  border-radius: 50rem !important;
  flex-shrink: 0 !important;
}

.dropdown-link:hover .dropdown-icon {
  color: #f1ca2f !important;
  background-color: rgba(241, 202, 47, 0.1) !important;
}

.dropdown-link-content {
  flex: 1 !important;
  min-width: 0 !important;
}

.dropdown-link-title {
  display: block !important;
  font-weight: 500 !important;
  margin-bottom: 2px !important;
  color: inherit !important;
}

.dropdown-link-description {
  display: block !important;
  font-size: 0.75rem !important;
  color: #868e96 !important;
}

/* Force logout link styling */
.dropdown-link.logout-link {
  color: #dc3545 !important;
  border-left-color: transparent !important;
}

.dropdown-link.logout-link:hover {
  background-color: rgba(220, 53, 69, 0.1) !important;
  color: #dc3545 !important;
  border-left-color: #dc3545 !important;
}

/* Force button dropdown links */
button.dropdown-link {
  width: 100% !important;
  text-align: left !important;
  background: none !important;
  border: none !important;
  cursor: pointer !important;
  font-family: inherit !important;
}
