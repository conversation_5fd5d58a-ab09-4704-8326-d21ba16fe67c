<?php
/**
 * Force Refresh Tool
 * This script forces a cache refresh by updating version numbers
 */

// Start session
session_start();

// Include config
require_once 'config.php';

// Check if user is admin
if (!isset($_SESSION['user_id']) || !isset($_SESSION['is_admin']) || $_SESSION['is_admin'] != 1) {
    header('Location: login.php');
    exit();
}

$message = '';
$success = false;

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'update_versions':
                // Read current header file
                $header_file = 'includes/header.php';
                $content = file_get_contents($header_file);
                
                if ($content) {
                    // Update CSS version
                    $content = preg_replace(
                        '/\$css_version = \'([^\']+)\';/',
                        '$css_version = \'' . date('Y.m.d.H.i') . '\';',
                        $content
                    );
                    
                    // Update JS version
                    $content = preg_replace(
                        '/\$js_version = \'([^\']+)\';/',
                        '$js_version = \'' . date('Y.m.d.H.i') . '\';',
                        $content
                    );
                    
                    if (file_put_contents($header_file, $content)) {
                        $message = 'Version numbers updated successfully! All CSS and JS files will now reload.';
                        $success = true;
                    } else {
                        $message = 'Failed to update version numbers.';
                    }
                } else {
                    $message = 'Failed to read header file.';
                }
                break;
                
            case 'clear_browser_cache':
                // Generate a script to clear browser cache
                $message = 'Browser cache clearing script generated. Please run the JavaScript in your browser console.';
                $success = true;
                break;
        }
    }
}

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Force Refresh Tool</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .btn {
            background: #007cba;
            color: white;
            padding: 12px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 10px 5px;
            font-size: 14px;
        }
        .btn:hover {
            background: #005a87;
        }
        .btn-danger {
            background: #dc3545;
        }
        .btn-danger:hover {
            background: #c82333;
        }
        .alert {
            padding: 15px;
            margin: 20px 0;
            border-radius: 4px;
        }
        .alert-success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .alert-error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .section {
            margin: 30px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .code {
            background: #f8f8f8;
            padding: 15px;
            border-radius: 4px;
            font-family: monospace;
            margin: 10px 0;
            white-space: pre-wrap;
        }
        .warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
            padding: 15px;
            border-radius: 4px;
            margin: 15px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔄 Force Refresh Tool</h1>
        <p>This tool helps resolve CSS/JS caching issues by forcing browsers to reload all assets.</p>
        
        <?php if ($message): ?>
        <div class="alert <?php echo $success ? 'alert-success' : 'alert-error'; ?>">
            <?php echo htmlspecialchars($message); ?>
        </div>
        <?php endif; ?>
        
        <div class="section">
            <h2>1. Update Version Numbers</h2>
            <p>This will update the CSS and JS version numbers in the header file, forcing all browsers to reload the files.</p>
            <form method="POST">
                <input type="hidden" name="action" value="update_versions">
                <button type="submit" class="btn">Update Version Numbers</button>
            </form>
        </div>
        
        <div class="section">
            <h2>2. Browser Cache Clearing</h2>
            <p>Use these methods to clear browser cache:</p>
            
            <h3>Manual Methods:</h3>
            <ul>
                <li><strong>Hard Refresh:</strong> Ctrl+F5 (Windows) or Cmd+Shift+R (Mac)</li>
                <li><strong>Incognito/Private Mode:</strong> Test in a private browsing window</li>
                <li><strong>Clear Browser Data:</strong> Go to browser settings and clear cache</li>
            </ul>
            
            <h3>JavaScript Method:</h3>
            <p>Run this in your browser console (F12 → Console):</p>
            <div class="code">// Clear cache and reload
if ('caches' in window) {
    caches.keys().then(function(names) {
        names.forEach(function(name) {
            caches.delete(name);
        });
    });
}
location.reload(true);</div>
            
            <button class="btn" onclick="clearCacheAndReload()">Run Cache Clear Script</button>
        </div>
        
        <div class="section">
            <h2>3. CSS Loading Test</h2>
            <p>Test if CSS files are loading correctly:</p>
            <button class="btn" onclick="testCSSLoading()">Test CSS Loading</button>
            <div id="css-test-results"></div>
        </div>
        
        <div class="section">
            <h2>4. Emergency Actions</h2>
            <div class="warning">
                <strong>⚠️ Warning:</strong> These actions will affect all users on the site.
            </div>
            
            <h3>Force Reload All Assets</h3>
            <p>This will append a timestamp to all CSS/JS files, forcing immediate reload:</p>
            <button class="btn btn-danger" onclick="forceReloadAllAssets()">Force Reload All Assets</button>
        </div>
        
        <div class="section">
            <h2>5. Quick Links</h2>
            <a href="test-dropdown.php" class="btn">Test Dropdown Page</a>
            <a href="dashboard.php" class="btn">Back to Dashboard</a>
        </div>
    </div>
    
    <script>
        function clearCacheAndReload() {
            // Clear cache and reload
            if ('caches' in window) {
                caches.keys().then(function(names) {
                    names.forEach(function(name) {
                        caches.delete(name);
                    });
                });
            }
            
            // Clear localStorage and sessionStorage
            if (typeof(Storage) !== "undefined") {
                localStorage.clear();
                sessionStorage.clear();
            }
            
            // Force reload
            location.reload(true);
        }
        
        function testCSSLoading() {
            const results = document.getElementById('css-test-results');
            results.innerHTML = '<p>Testing CSS loading...</p>';
            
            const cssFiles = [
                'assets/css/emergency-dropdown.css',
                'assets/css/consolidated.css',
                'assets/css/main.css',
                'assets/css/components/admin_user-dropdown.css'
            ];
            
            let testResults = [];
            let completed = 0;
            
            cssFiles.forEach((file, index) => {
                fetch(file + '?test=' + Date.now())
                    .then(response => {
                        if (response.ok) {
                            return response.text();
                        }
                        throw new Error(`HTTP ${response.status}`);
                    })
                    .then(content => {
                        testResults[index] = `<div style="color:green;">✅ ${file} loaded (${content.length} bytes)</div>`;
                        completed++;
                        if (completed === cssFiles.length) showResults();
                    })
                    .catch(error => {
                        testResults[index] = `<div style="color:red;">❌ ${file} failed: ${error.message}</div>`;
                        completed++;
                        if (completed === cssFiles.length) showResults();
                    });
            });
            
            function showResults() {
                results.innerHTML = '<h4>CSS Loading Results:</h4>' + testResults.join('');
            }
        }
        
        function forceReloadAllAssets() {
            if (confirm('This will force reload all CSS and JS files for all users. Continue?')) {
                // Add timestamp to all CSS and JS links
                const links = document.querySelectorAll('link[rel="stylesheet"]');
                const scripts = document.querySelectorAll('script[src]');
                const timestamp = Date.now();
                
                links.forEach(link => {
                    const href = link.href.split('?')[0];
                    link.href = href + '?v=' + timestamp;
                });
                
                scripts.forEach(script => {
                    const src = script.src.split('?')[0];
                    script.src = src + '?v=' + timestamp;
                });
                
                alert('All assets have been force reloaded!');
                location.reload();
            }
        }
    </script>
</body>
</html>
