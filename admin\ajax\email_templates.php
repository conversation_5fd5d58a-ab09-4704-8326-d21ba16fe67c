<?php
session_start();
require_once '../config.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Unauthorized']);
    exit;
}

// Include permissions class
require_once '../lib/Permissions.php';

// Initialize permission system
$permissions = new Permissions($conn, $_SESSION['user_id']);

// Check if user has permission to manage email templates
if (!$permissions->hasPermission('manage_email_templates') && !$permissions->hasPermission('view_email_templates')) {
    http_response_code(403);
    echo json_encode(['success' => false, 'message' => 'Access denied']);
    exit;
}

// Include EmailTemplates class
require_once '../lib/EmailTemplates.php';
$emailTemplates = new EmailTemplates($conn);

// Set content type
header('Content-Type: application/json');

// Handle different actions
$action = $_POST['action'] ?? $_GET['action'] ?? '';

try {
    switch ($action) {
        case 'get_template':
            $template_id = (int)($_POST['id'] ?? $_GET['id'] ?? 0);
            
            if ($template_id <= 0) {
                throw new Exception('Invalid template ID');
            }
            
            $template = $emailTemplates->getTemplate($template_id);
            
            if (!$template) {
                throw new Exception('Template not found');
            }
            
            echo json_encode([
                'success' => true,
                'template' => $template
            ]);
            break;
            
        case 'update_template':
            // Check if user has permission to edit
            if (!$permissions->hasPermission('manage_email_templates') && !$permissions->hasPermission('edit_email_templates')) {
                throw new Exception('You do not have permission to edit email templates');
            }
            
            $template_id = (int)($_POST['template_id'] ?? 0);
            $template_name = trim($_POST['template_name'] ?? '');
            $subject = trim($_POST['subject'] ?? '');
            $content = $_POST['content'] ?? '';
            
            if ($template_id <= 0) {
                throw new Exception('Invalid template ID');
            }
            
            if (empty($template_name)) {
                throw new Exception('Template name is required');
            }
            
            if (empty($subject)) {
                throw new Exception('Subject is required');
            }
            
            if (empty($content)) {
                throw new Exception('Content is required');
            }
            
            $result = $emailTemplates->updateTemplate($template_id, $template_name, $subject, $content);
            
            if ($result) {
                // Log the activity
                require_once '../includes/admin-functions.php';
                log_activity('update', 'Updated email template: ' . $template_name, $_SESSION['user_id']);
                
                echo json_encode([
                    'success' => true,
                    'message' => 'Template updated successfully'
                ]);
            } else {
                throw new Exception('Failed to update template');
            }
            break;
            
        case 'create_template':
            // Check if user has permission to create
            if (!$permissions->hasPermission('manage_email_templates')) {
                throw new Exception('You do not have permission to create email templates');
            }
            
            $template_name = trim($_POST['template_name'] ?? '');
            $subject = trim($_POST['subject'] ?? '');
            $content = $_POST['content'] ?? '';
            $variables = trim($_POST['variables'] ?? '');
            
            if (empty($template_name)) {
                throw new Exception('Template name is required');
            }
            
            if (empty($subject)) {
                throw new Exception('Subject is required');
            }
            
            if (empty($content)) {
                throw new Exception('Content is required');
            }
            
            $result = $emailTemplates->createTemplate($template_name, $subject, $content, $variables);
            
            if ($result) {
                // Log the activity
                require_once '../includes/admin-functions.php';
                log_activity('create', 'Created email template: ' . $template_name, $_SESSION['user_id']);
                
                echo json_encode([
                    'success' => true,
                    'message' => 'Template created successfully',
                    'template_id' => $result
                ]);
            } else {
                throw new Exception('Failed to create template');
            }
            break;
            
        case 'delete_template':
            // Check if user has permission to delete
            if (!$permissions->hasPermission('manage_email_templates')) {
                throw new Exception('You do not have permission to delete email templates');
            }
            
            $template_id = (int)($_POST['id'] ?? $_GET['id'] ?? 0);
            
            if ($template_id <= 0) {
                throw new Exception('Invalid template ID');
            }
            
            // Get template name for logging
            $template = $emailTemplates->getTemplate($template_id);
            $template_name = $template ? $template['template_name'] : 'Unknown';
            
            $result = $emailTemplates->deleteTemplate($template_id);
            
            if ($result) {
                // Log the activity
                require_once '../includes/admin-functions.php';
                log_activity('delete', 'Deleted email template: ' . $template_name, $_SESSION['user_id']);
                
                echo json_encode([
                    'success' => true,
                    'message' => 'Template deleted successfully'
                ]);
            } else {
                throw new Exception('Failed to delete template');
            }
            break;
            
        case 'get_all_templates':
            $templates = $emailTemplates->getAllTemplates();
            
            echo json_encode([
                'success' => true,
                'templates' => $templates
            ]);
            break;
            
        default:
            throw new Exception('Invalid action');
    }
    
} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}
?>
