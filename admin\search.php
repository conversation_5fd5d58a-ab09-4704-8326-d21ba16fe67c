<?php
session_start();
require_once 'config.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    redirect('index.php');
}

// Get search query
$search_query = isset($_GET['q']) ? trim($_GET['q']) : '';

// Set page title
$page_title = "Search Results";

// Initialize results arrays
$news_results = [];
$category_results = [];
$user_results = [];

// Perform search if query is not empty
if (!empty($search_query)) {
    // Sanitize search query for SQL
    $search_term = '%' . $conn->real_escape_string($search_query) . '%';

    // Search in news
    $news_sql = "SELECT n.*, c.name as category_name
                FROM news n
                LEFT JOIN categories c ON n.category_id = c.id
                WHERE n.title LIKE ? OR n.content LIKE ?
                ORDER BY n.created_at DESC";

    $stmt = $conn->prepare($news_sql);
    $stmt->bind_param('ss', $search_term, $search_term);
    $stmt->execute();
    $news_result = $stmt->get_result();

    while ($row = $news_result->fetch_assoc()) {
        $news_results[] = $row;
    }

    // Search in categories
    $category_sql = "SELECT * FROM categories WHERE name LIKE ? ORDER BY name ASC";
    $stmt = $conn->prepare($category_sql);
    $stmt->bind_param('s', $search_term);
    $stmt->execute();
    $category_result = $stmt->get_result();

    while ($row = $category_result->fetch_assoc()) {
        $category_results[] = $row;
    }

    // Search in users (only for admins)
    if (isset($_SESSION['is_admin']) && $_SESSION['is_admin'] == 1) {
        $user_sql = "SELECT id, username, email, is_admin, created_at FROM users
                    WHERE username LIKE ? OR email LIKE ?
                    ORDER BY username ASC";
        $stmt = $conn->prepare($user_sql);
        $stmt->bind_param('ss', $search_term, $search_term);
        $stmt->execute();
        $user_result = $stmt->get_result();

        while ($row = $user_result->fetch_assoc()) {
            $user_results[] = $row;
        }
    }
}
?>

<?php include 'includes/header.php'; ?>

<div class="admin-container">
    <div class="admin-header">
        <h2><i class="fas fa-search"></i> Search Results</h2>
        <div class="admin-actions">
            <form action="search.php" method="get" class="search-form">
                <div class="form-group">
                    <input type="text" name="q" value="<?php echo htmlspecialchars($search_query); ?>" placeholder="Search..." class="form-control">
                    <button type="submit" class="admin-btn"><i class="fas fa-search"></i> Search</button>
                </div>
            </form>
        </div>
    </div>

    <?php if (empty($search_query)): ?>
        <div class="admin-alert info">
            <i class="fas fa-info-circle"></i> Please enter a search term to find content.
        </div>
    <?php else: ?>
        <div class="search-summary">
            <p>Found <?php echo count($news_results) + count($category_results) + count($user_results); ?> results for "<strong><?php echo htmlspecialchars($search_query); ?></strong>"</p>
        </div>

        <?php if (empty($news_results) && empty($category_results) && empty($user_results)): ?>
            <div class="admin-alert info">
                <i class="fas fa-info-circle"></i> No results found for "<?php echo htmlspecialchars($search_query); ?>". Try a different search term.
            </div>
        <?php endif; ?>

        <!-- News Results -->
        <?php if (!empty($news_results)): ?>
            <div class="admin-card">
                <div class="admin-card-header">
                    <h3><i class="fas fa-newspaper"></i> News (<?php echo count($news_results); ?>)</h3>
                </div>
                <div class="admin-table-responsive">
                    <table class="admin-table">
                        <thead>
                            <tr>
                                <th>Title</th>
                                <th>Category</th>
                                <th>Date</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($news_results as $row): ?>
                                <tr>
                                    <td>
                                        <div class="search-result-title">
                                            <?php echo htmlspecialchars($row['title']); ?>
                                        </div>
                                        <div class="search-result-excerpt">
                                            <?php
                                            $excerpt = strip_tags($row['content']);
                                            $excerpt = substr($excerpt, 0, 100) . (strlen($excerpt) > 100 ? '...' : '');
                                            echo $excerpt;
                                            ?>
                                        </div>
                                    </td>
                                    <td><?php echo !empty($row['category_name']) ? htmlspecialchars($row['category_name']) : '<span class="no-category">Uncategorized</span>'; ?></td>
                                    <td><?php echo date('Y/m/d', strtotime($row['created_at'])); ?></td>
                                    <td>
                                        <div class="news-actions">
                                            <a href="edit_news.php?id=<?php echo $row['id']; ?>" class="admin-btn small" title="Edit"><i class="fas fa-edit"></i></a>
                                            <a href="../<?php echo !empty($row['slug']) ? $row['slug'] . '.html' : 'news-detail.php?id=' . $row['id']; ?>" class="admin-btn small secondary" title="View" target="_blank"><i class="fas fa-eye"></i></a>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        <?php endif; ?>

        <!-- Category Results -->
        <?php if (!empty($category_results)): ?>
            <div class="admin-card">
                <div class="admin-card-header">
                    <h3><i class="fas fa-folder"></i> Categories (<?php echo count($category_results); ?>)</h3>
                </div>
                <div class="admin-table-responsive">
                    <table class="admin-table">
                        <thead>
                            <tr>
                                <th>Name</th>
                                <th>Slug</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($category_results as $row): ?>
                                <tr>
                                    <td><?php echo htmlspecialchars($row['name']); ?></td>
                                    <td><?php echo htmlspecialchars($row['slug']); ?></td>
                                    <td>
                                        <div class="news-actions">
                                            <a href="categories.php?edit=<?php echo $row['id']; ?>" class="admin-btn small" title="Edit"><i class="fas fa-edit"></i></a>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        <?php endif; ?>

        <!-- User Results (Admin Only) -->
        <?php if (!empty($user_results)): ?>
            <div class="admin-card">
                <div class="admin-card-header">
                    <h3><i class="fas fa-users"></i> Users (<?php echo count($user_results); ?>)</h3>
                </div>
                <div class="admin-table-responsive">
                    <table class="admin-table">
                        <thead>
                            <tr>
                                <th>Username</th>
                                <th>Email</th>
                                <th>Role</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($user_results as $row): ?>
                                <tr>
                                    <td><?php echo htmlspecialchars($row['username']); ?></td>
                                    <td><?php echo htmlspecialchars($row['email']); ?></td>
                                    <td><?php echo $row['is_admin'] ? 'Admin' : 'User'; ?></td>
                                    <td>
                                        <div class="news-actions">
                                            <a href="users.php?edit=<?php echo $row['id']; ?>" class="admin-btn small" title="Edit"><i class="fas fa-edit"></i></a>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</div>

<style>
.search-form {
    display: flex;
    align-items: center;
}

.search-form .form-group {
    display: flex;
    margin: 0;
}

.search-form .form-control {
    width: 300px;
    margin-right: 10px;
}

.search-summary {
    margin-bottom: 20px;
    color: var(--text-light);
    font-size: 14px;
}

.search-result-title {
    font-weight: 600;
    margin-bottom: 5px;
}

.search-result-excerpt {
    font-size: 12px;
    color: var(--text-light);
}

.no-category {
    color: var(--text-light);
    font-style: italic;
}
</style>

<?php include 'includes/footer.php'; ?>
