/**
 * Admin Pagination CSS
 * 
 * This file contains styles for pagination controls in the admin panel.
 */

/* Pagination Container */
.pagination {
  display: flex;
  align-items: center;
  list-style: none;
  margin: 0;
  padding: 0;
  gap: var(--spacing-1);
}

/* Pagination Item */
.page-item {
  margin: 0;
}

/* Pagination Link */
.page-link {
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 36px;
  height: 36px;
  padding: 0 var(--spacing-2);
  font-size: var(--font-size-sm);
  color: var(--text-color);
  background-color: var(--white);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  text-decoration: none;
  transition: all var(--transition-fast) ease;
  cursor: pointer;
}

.page-link:hover {
  background-color: var(--gray-100);
  color: var(--text-dark);
  border-color: var(--border-dark);
  z-index: 1;
}

/* Active Page */
.page-item.active .page-link {
  background-color: var(--primary-color);
  color: var(--secondary-dark);
  border-color: var(--primary-color);
  font-weight: var(--font-weight-medium);
  z-index: 2;
}

/* Disabled Page */
.page-item.disabled .page-link {
  color: var(--text-muted);
  background-color: var(--gray-50);
  border-color: var(--border-color);
  cursor: not-allowed;
  pointer-events: none;
}

/* Pagination with Icons */
.page-link-icon {
  font-size: var(--font-size-base);
}

/* Pagination Sizes */
.pagination-sm .page-link {
  min-width: 30px;
  height: 30px;
  font-size: var(--font-size-xs);
}

.pagination-lg .page-link {
  min-width: 44px;
  height: 44px;
  font-size: var(--font-size-base);
}

/* Pagination Variants */
/* Rounded Pagination */
.pagination-rounded .page-link {
  border-radius: var(--radius-full);
}

/* Squared Pagination */
.pagination-squared .page-link {
  border-radius: 0;
}

/* Borderless Pagination */
.pagination-borderless .page-link {
  border: none;
  background-color: transparent;
}

.pagination-borderless .page-link:hover {
  background-color: var(--gray-100);
}

.pagination-borderless .page-item.active .page-link {
  background-color: var(--primary-very-light);
  color: var(--primary-color);
}

/* Pagination with Background */
.pagination-bg {
  padding: var(--spacing-2) var(--spacing-3);
  background-color: var(--white);
  border-radius: var(--radius-md);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--border-color);
}

/* Pagination with Dots */
.pagination-dots .page-dots {
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 36px;
  height: 36px;
  font-size: var(--font-size-sm);
  color: var(--text-muted);
}

/* Pagination with Page Info */
.pagination-info {
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-wrap: wrap;
  gap: var(--spacing-3);
}

.pagination-info-text {
  font-size: var(--font-size-sm);
  color: var(--text-light);
}

/* Pagination with Page Size Selector */
.pagination-size {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  font-size: var(--font-size-sm);
  color: var(--text-light);
}

.pagination-size-select {
  height: 36px;
  padding: 0 var(--spacing-3);
  font-size: var(--font-size-sm);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  background-color: var(--white);
  color: var(--text-color);
}

/* Pagination with Jump to Page */
.pagination-jump {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  font-size: var(--font-size-sm);
  color: var(--text-light);
}

.pagination-jump-input {
  width: 60px;
  height: 36px;
  padding: 0 var(--spacing-2);
  font-size: var(--font-size-sm);
  text-align: center;
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  background-color: var(--white);
  color: var(--text-color);
}

.pagination-jump-btn {
  height: 36px;
  padding: 0 var(--spacing-3);
  font-size: var(--font-size-sm);
}

/* Pagination with First/Last Buttons */
.pagination-first-last .page-first,
.pagination-first-last .page-last {
  font-weight: var(--font-weight-medium);
}

/* Pagination Alignment */
.pagination-center {
  justify-content: center;
}

.pagination-right {
  justify-content: flex-end;
}

/* Pagination with Compact Layout */
.pagination-compact .page-link {
  min-width: auto;
  padding: 0 var(--spacing-2);
}

.pagination-compact .page-item:not(:first-child) .page-link {
  margin-left: -1px;
  border-radius: 0;
}

.pagination-compact .page-item:first-child .page-link {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}

.pagination-compact .page-item:last-child .page-link {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}

/* Pagination with Ellipsis */
.pagination-ellipsis .page-ellipsis {
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 36px;
  height: 36px;
  font-size: var(--font-size-sm);
  color: var(--text-muted);
}

/* Responsive Pagination */
@media (max-width: 576px) {
  .pagination {
    flex-wrap: wrap;
  }
  
  .pagination-info {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-2);
  }
  
  .pagination-responsive .page-item:not(.active):not(:first-child):not(:last-child):not(.page-prev):not(.page-next) {
    display: none;
  }
  
  .pagination-responsive .page-item.active + .page-item,
  .pagination-responsive .page-item.active + .page-item + .page-item {
    display: block;
  }
  
  .pagination-responsive .page-item:nth-last-child(2):not(.active),
  .pagination-responsive .page-item:nth-last-child(3):not(.active) {
    display: block;
  }
  
  .pagination-jump {
    margin-top: var(--spacing-2);
  }
}
