/**
 * Edit Role Page CSS
 *
 * This file contains styles for the edit role page.
 */

/* Main Layout */
.role-edit-container {
  display: grid;
  grid-template-columns: 3fr 1fr;
  gap: var(--spacing-4);
  margin-bottom: var(--spacing-6);
}

/* Main Content Area */
.role-edit-main {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-4);
}

/* Sidebar Area */
.role-edit-sidebar {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-4);
}

/* Role Details Card */
.role-details-card {
  background-color: var(--white);
  border-radius: var(--radius-md);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--border-color);
  overflow: hidden;
}

.role-details-header {
  padding: var(--spacing-3) var(--spacing-4);
  background-color: var(--background-light);
  border-bottom: 1px solid var(--border-color);
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
}

.role-details-header h3 {
  margin: 0;
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--text-dark);
}

.role-details-header i {
  color: var(--primary-color);
  font-size: var(--font-size-lg);
}

.role-details-body {
  padding: var(--spacing-4);
}

/* Permission Categories */
.permission-categories {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-4);
  margin-top: var(--spacing-4);
}

.permission-category {
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  overflow: hidden;
  background-color: var(--white);
  transition: all var(--transition-fast) ease;
}

.permission-category:hover {
  box-shadow: var(--shadow-md);
  border-color: var(--primary-light);
}

.permission-category-header {
  padding: var(--spacing-3);
  background-color: var(--background-light);
  border-bottom: 1px solid var(--border-color);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.permission-category-title {
  margin: 0;
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  color: var(--text-dark);
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
}

.permission-category-title i {
  color: var(--primary-color);
  font-size: var(--font-size-base);
}

.permission-category-toggle {
  color: var(--text-light);
  transition: transform var(--transition-fast) ease;
}

.permission-category.expanded .permission-category-toggle {
  transform: rotate(180deg);
}

.permission-category-content {
  padding: var(--spacing-3);
  display: none;
}

.permission-category.expanded .permission-category-content {
  display: block;
  animation: fadeIn 0.3s ease;
}

/* Permission Items */
.permission-items {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: var(--spacing-3);
}

.permission-item {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-1);
  padding: var(--spacing-2);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-sm);
  background-color: var(--background-light);
  transition: all var(--transition-fast) ease;
}

.permission-item:hover {
  background-color: var(--background-hover);
  border-color: var(--border-hover);
}

.permission-item-header {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
}

.permission-checkbox {
  position: relative;
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  cursor: pointer;
  user-select: none;
  font-weight: var(--font-weight-medium);
  color: var(--text-dark);
}

.permission-checkbox input {
  position: absolute;
  opacity: 0;
  cursor: pointer;
  height: 0;
  width: 0;
}

.permission-checkbox .checkmark {
  position: relative;
  height: 18px;
  width: 18px;
  background-color: var(--white);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-sm);
  transition: all var(--transition-fast) ease;
  flex-shrink: 0;
}

.permission-checkbox:hover input ~ .checkmark {
  background-color: var(--background-light);
  border-color: var(--primary-light);
}

.permission-checkbox input:checked ~ .checkmark {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
}

.permission-checkbox .checkmark:after {
  content: "";
  position: absolute;
  display: none;
  left: 6px;
  top: 2px;
  width: 5px;
  height: 10px;
  border: solid var(--white);
  border-width: 0 2px 2px 0;
  transform: rotate(45deg);
}

.permission-checkbox input:checked ~ .checkmark:after {
  display: block;
}

.permission-description {
  font-size: var(--font-size-xs);
  color: var(--text-light);
  margin-left: 26px;
}

/* Action Buttons */
.role-actions-card {
  background-color: var(--white);
  border-radius: var(--radius-md);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--border-color);
  overflow: hidden;
}

.role-actions-header {
  padding: var(--spacing-3);
  background-color: var(--background-light);
  border-bottom: 1px solid var(--border-color);
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
}

.role-actions-header h3 {
  margin: 0;
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-semibold);
  color: var(--text-dark);
}

.role-actions-header i {
  color: var(--primary-color);
}

.role-actions-body {
  padding: var(--spacing-3);
}

.role-action-buttons {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-2);
}

.role-action-buttons .btn {
  width: 100%;
  justify-content: center;
}

/* Animation */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

/* Responsive Styles */
@media (max-width: 992px) {
  .role-edit-container {
    grid-template-columns: 2fr 1fr;
  }
  
  .permission-items {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .role-edit-container {
    grid-template-columns: 1fr;
  }
  
  .role-edit-sidebar {
    order: -1;
  }
}
