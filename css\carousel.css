/* Partners Carousel */
.partners-carousel {
    position: relative;
    width: 100%;
    max-width: 1400px;
    margin: 0 auto;
    padding: 20px 40px;
    overflow: hidden;
}

.partners-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    transition: transform 0.5s ease;
}

.partner {
    flex: 0 0 auto;
    padding: 0 20px;
    text-align: center;
}

.partner img {
    max-height: 60px;
    filter: grayscale(100%);
    opacity: 0.7;
    transition: all 0.3s;
}

.partner img:hover {
    filter: grayscale(0);
    opacity: 1;
    transform: scale(1.05);
}

/* Navigation buttons */
.carousel-prev,
.carousel-next {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    width: 40px;
    height: 40px;
    background-color: rgba(0, 0, 0, 0.1);
    border: none;
    border-radius: 50%;
    font-size: 24px;
    color: #3c3c45;
    cursor: pointer;
    z-index: 10;
    display: flex;
    justify-content: center;
    align-items: center;
    transition: all 0.3s ease;
}

.carousel-prev:hover,
.carousel-next:hover {
    background-color: rgba(0, 0, 0, 0.2);
    color: #f1ca2f;
}

.carousel-prev {
    left: 10px;
}

.carousel-next {
    right: 10px;
}

/* Responsive styles */
@media (max-width: 992px) {
    .partners-carousel {
        padding: 15px 50px;
    }

    .partners-container {
        flex-wrap: wrap;
        justify-content: center;
    }

    .partner {
        flex: 0 0 33.33%;
        margin-bottom: 20px;
    }
}

@media (max-width: 768px) {
    .partners-carousel {
        padding: 10px 40px;
    }

    .partner {
        flex: 0 0 50%;
    }

    .carousel-prev,
    .carousel-next {
        width: 30px;
        height: 30px;
        font-size: 18px;
    }
}

@media (max-width: 576px) {
    .partner {
        flex: 0 0 100%;
    }
}
