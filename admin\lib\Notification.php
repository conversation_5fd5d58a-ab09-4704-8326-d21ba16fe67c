<?php
/**
 * Notification Class
 * Handles all notification operations
 */
class Notification {
    private $conn;
    
    /**
     * Constructor
     * 
     * @param mysqli $conn Database connection
     */
    public function __construct($conn) {
        $this->conn = $conn;
        
        // Create notifications table if it doesn't exist
        $this->createNotificationsTable();
    }
    
    /**
     * Create notifications table if it doesn't exist
     */
    private function createNotificationsTable() {
        $table_check = $this->conn->query("SHOW TABLES LIKE 'notifications'");
        
        if ($table_check->num_rows == 0) {
            $sql = "CREATE TABLE IF NOT EXISTS `notifications` (
                `id` int(11) NOT NULL AUTO_INCREMENT,
                `user_id` int(11) DEFAULT NULL,
                `title` varchar(255) NOT NULL,
                `message` text NOT NULL,
                `type` varchar(50) NOT NULL DEFAULT 'info',
                `icon` varchar(50) DEFAULT 'fas fa-bell',
                `link` varchar(255) DEFAULT NULL,
                `is_read` tinyint(1) NOT NULL DEFAULT 0,
                `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
                PRIMARY KEY (`id`),
                KEY `user_id` (`user_id`),
                KEY `is_read` (`is_read`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;";
            
            $this->conn->query($sql);
        }
    }
    
    /**
     * Add a new notification
     * 
     * @param int|null $user_id User ID or null for all users
     * @param string $title Notification title
     * @param string $message Notification message
     * @param string $type Notification type (info, success, warning, danger)
     * @param string $icon Notification icon (FontAwesome class)
     * @param string|null $link Link to redirect when clicked
     * @return int|bool Notification ID or false on failure
     */
    public function add($user_id, $title, $message, $type = 'info', $icon = 'fas fa-bell', $link = null) {
        $user_id = $user_id === null ? 'NULL' : (int)$user_id;
        $title = $this->conn->real_escape_string($title);
        $message = $this->conn->real_escape_string($message);
        $type = $this->conn->real_escape_string($type);
        $icon = $this->conn->real_escape_string($icon);
        $link = $link === null ? 'NULL' : "'" . $this->conn->real_escape_string($link) . "'";
        
        $sql = "INSERT INTO notifications (user_id, title, message, type, icon, link) 
                VALUES ($user_id, '$title', '$message', '$type', '$icon', $link)";
        
        if ($this->conn->query($sql)) {
            return $this->conn->insert_id;
        }
        
        return false;
    }
    
    /**
     * Get notifications for a user
     * 
     * @param int $user_id User ID
     * @param int $limit Maximum number of notifications to return
     * @param bool $unread_only Get only unread notifications
     * @return array Notifications
     */
    public function getForUser($user_id, $limit = 10, $unread_only = false) {
        $user_id = (int)$user_id;
        $limit = (int)$limit;
        $unread_condition = $unread_only ? "AND is_read = 0" : "";
        
        $sql = "SELECT * FROM notifications 
                WHERE (user_id = $user_id OR user_id IS NULL) $unread_condition
                ORDER BY created_at DESC 
                LIMIT $limit";
        
        $result = $this->conn->query($sql);
        $notifications = [];
        
        if ($result && $result->num_rows > 0) {
            while ($row = $result->fetch_assoc()) {
                $notifications[] = $row;
            }
        }
        
        return $notifications;
    }
    
    /**
     * Get unread notification count for a user
     * 
     * @param int $user_id User ID
     * @return int Unread notification count
     */
    public function getUnreadCount($user_id) {
        $user_id = (int)$user_id;
        
        $sql = "SELECT COUNT(*) as count FROM notifications 
                WHERE (user_id = $user_id OR user_id IS NULL) AND is_read = 0";
        
        $result = $this->conn->query($sql);
        
        if ($result && $result->num_rows > 0) {
            $row = $result->fetch_assoc();
            return (int)$row['count'];
        }
        
        return 0;
    }
    
    /**
     * Mark a notification as read
     * 
     * @param int $notification_id Notification ID
     * @param int $user_id User ID (for security check)
     * @return bool Success
     */
    public function markAsRead($notification_id, $user_id) {
        $notification_id = (int)$notification_id;
        $user_id = (int)$user_id;
        
        $sql = "UPDATE notifications SET is_read = 1 
                WHERE id = $notification_id AND (user_id = $user_id OR user_id IS NULL)";
        
        return $this->conn->query($sql);
    }
    
    /**
     * Mark all notifications as read for a user
     * 
     * @param int $user_id User ID
     * @return bool Success
     */
    public function markAllAsRead($user_id) {
        $user_id = (int)$user_id;
        
        $sql = "UPDATE notifications SET is_read = 1 
                WHERE (user_id = $user_id OR user_id IS NULL) AND is_read = 0";
        
        return $this->conn->query($sql);
    }
    
    /**
     * Delete a notification
     * 
     * @param int $notification_id Notification ID
     * @param int $user_id User ID (for security check)
     * @return bool Success
     */
    public function delete($notification_id, $user_id) {
        $notification_id = (int)$notification_id;
        $user_id = (int)$user_id;
        
        $sql = "DELETE FROM notifications 
                WHERE id = $notification_id AND (user_id = $user_id OR user_id IS NULL)";
        
        return $this->conn->query($sql);
    }
    
    /**
     * Delete all notifications for a user
     * 
     * @param int $user_id User ID
     * @return bool Success
     */
    public function deleteAll($user_id) {
        $user_id = (int)$user_id;
        
        $sql = "DELETE FROM notifications 
                WHERE user_id = $user_id OR user_id IS NULL";
        
        return $this->conn->query($sql);
    }
}
?>
