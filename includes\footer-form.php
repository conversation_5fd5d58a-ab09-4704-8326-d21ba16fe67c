<?php
// This file now only provides the footer form HTML
// All processing is handled by process-contact.php

// Check for form status in session
$footer_success = '';
$footer_error = '';

if (isset($_SESSION['contact_form_status'])) {
    if ($_SESSION['contact_form_status']['success']) {
        $footer_success = $_SESSION['contact_form_status']['message'];
    } else {
        $footer_error = $_SESSION['contact_form_status']['message'];
    }
    // Clear the status after displaying
    unset($_SESSION['contact_form_status']);
}

// Footer form HTML
function getFooterForm() {
    global $footer_success, $footer_error;

    $html = '';

    if (!empty($footer_success)) {
        $html .= '<div class="footer-success-message">' . $footer_success . '</div>';
    }

    if (!empty($footer_error)) {
        $html .= '<div class="footer-error-message">' . $footer_error . '</div>';
    }

    $html .= '
    <form method="post" action="process-contact-clean.php" id="footer-contact-form">
        <input type="hidden" name="source" value="Footer Form">
        <div class="form-row">
            <input type="text" name="name" placeholder="Name" required>
            <input type="email" name="email" placeholder="Email Address" required>
        </div>
        <textarea name="message" placeholder="Message" required></textarea>
        <button type="submit" class="submit-btn">Submit</button>
    </form>';

    return $html;
}
?>
