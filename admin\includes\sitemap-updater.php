<?php
/**
 * Sitemap Updater
 * This file contains functions to update the XML sitemap
 */

/**
 * Update the XML sitemap
 * This function should be called whenever news is created, edited, or updated
 */
function updateSitemap() {
    // Get the root directory
    $rootDir = dirname(dirname(__DIR__));
    
    // Check if the generate-sitemap.php file exists
    if (file_exists($rootDir . '/generate-sitemap.php')) {
        // Execute the generate-sitemap.php script
        include_once $rootDir . '/generate-sitemap.php';
        
        // Log the sitemap update
        error_log('Sitemap updated at ' . date('Y-m-d H:i:s'));
        
        return true;
    }
    
    return false;
}

/**
 * Schedule a sitemap update
 * This function creates a flag file that will trigger a sitemap update on the next page load
 */
function scheduleSitemapUpdate() {
    // Get the root directory
    $rootDir = dirname(dirname(__DIR__));
    
    // Create a flag file
    file_put_contents($rootDir . '/sitemap-update-needed.flag', date('Y-m-d H:i:s'));
    
    return true;
}

/**
 * Check if a sitemap update is needed and perform it if necessary
 * This function should be called on every page load
 */
function checkAndUpdateSitemap() {
    // Get the root directory
    $rootDir = dirname(dirname(__DIR__));
    
    // Check if the flag file exists
    if (file_exists($rootDir . '/sitemap-update-needed.flag')) {
        // Delete the flag file
        unlink($rootDir . '/sitemap-update-needed.flag');
        
        // Update the sitemap
        return updateSitemap();
    }
    
    return false;
}
?>
