<?php
/**
 * Delete News Post
 *
 * This script deletes a news post and its associated image.
 */

session_start();
require_once 'config.php';
require_once 'lib/Permissions.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    redirect('index.php');
}

// Initialize permission system
$permissions = new Permissions($conn, $_SESSION['user_id']);

// Check if user has permission to manage news
if (!$permissions->hasPermission('manage_news')) {
    $_SESSION['error_message'] = "You do not have permission to delete news posts.";
    redirect('all_news.php');
}

// Check if ID is provided
if (!isset($_GET['id']) || empty($_GET['id'])) {
    $_SESSION['error_message'] = "No post ID specified for deletion.";
    redirect('all_news.php');
}

$id = intval($_GET['id']);

try {
    // Get news post data to get image filename using prepared statement
    $stmt = $conn->prepare("SELECT * FROM news WHERE id = ?");
    if (!$stmt) {
        throw new Exception("Failed to prepare statement: " . $conn->error);
    }

    $stmt->bind_param("i", $id);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result->num_rows > 0) {
        $news = $result->fetch_assoc();

        // Delete image file if it exists
        if (!empty($news['image'])) {
            $image_path = "../images/news/" . $news['image'];
            if (file_exists($image_path)) {
                if (!unlink($image_path)) {
                    error_log("Failed to delete image file: $image_path");
                }
            }
        }

        // Delete from database using prepared statement
        $delete_stmt = $conn->prepare("DELETE FROM news WHERE id = ?");
        if (!$delete_stmt) {
            throw new Exception("Failed to prepare delete statement: " . $conn->error);
        }

        $delete_stmt->bind_param("i", $id);
        if ($delete_stmt->execute()) {
            // Log the activity
            require_once 'includes/admin-functions.php';
            log_activity('delete', 'Deleted news post: ' . $news['title'], $_SESSION['user_id']);

            $_SESSION['success_message'] = "News post deleted successfully.";

            // Update sitemap
            require_once 'includes/hooks.php';
            if (function_exists('afterNewsDelete')) {
                afterNewsDelete();
            }
        } else {
            throw new Exception("Failed to delete news post: " . $delete_stmt->error);
        }
    } else {
        $_SESSION['error_message'] = "News post not found.";
    }

    // Redirect back to all news page
    redirect('all_news.php');

} catch (Exception $e) {
    error_log("Error in delete_news.php: " . $e->getMessage());
    $_SESSION['error_message'] = "An error occurred while deleting the news post: " . $e->getMessage();
    redirect('all_news.php');
}
?>
