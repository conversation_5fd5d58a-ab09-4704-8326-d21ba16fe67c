<?php
/**
 * Sync Contact Notifications
 * 
 * This script creates notifications for existing contact form submissions
 * that don't have notifications yet. Run this once to migrate existing data.
 */

// Start session and check authentication
session_start();

// Check if user is logged in and is admin
if (!isset($_SESSION['user_id']) || !isset($_SESSION['is_admin']) || !$_SESSION['is_admin']) {
    header('Location: login.php');
    exit;
}

// Include database connection
require_once 'config/database.php';
require_once 'lib/ContactNotifications.php';

$page_title = 'Sync Contact Notifications';
include 'includes/header.php';

$success_message = '';
$error_message = '';
$sync_results = [];

// Handle sync request
if ($_POST && isset($_POST['sync_notifications'])) {
    try {
        $contact_notifications = new ContactNotifications($conn);
        
        // Get statistics before sync
        $before_stats = getNotificationStats($conn);
        
        // Perform sync
        $sync_success = $contact_notifications->syncExistingSubmissions();
        
        // Get statistics after sync
        $after_stats = getNotificationStats($conn);
        
        if ($sync_success) {
            $synced_count = $after_stats['total_notifications'] - $before_stats['total_notifications'];
            $success_message = "Successfully synced {$synced_count} contact submissions with notifications.";
            
            $sync_results = [
                'before' => $before_stats,
                'after' => $after_stats,
                'synced' => $synced_count
            ];
        } else {
            $error_message = "Failed to sync contact notifications. Check error logs for details.";
        }
        
    } catch (Exception $e) {
        $error_message = "Error during sync: " . $e->getMessage();
        error_log("Contact notification sync error: " . $e->getMessage());
    }
}

// Get current statistics
$current_stats = getNotificationStats($conn);

/**
 * Get notification statistics
 */
function getNotificationStats($conn) {
    $stats = [
        'total_submissions' => 0,
        'total_notifications' => 0,
        'unsynced_submissions' => 0,
        'contact_notifications' => 0
    ];
    
    try {
        // Total contact submissions
        $result = $conn->query("SELECT COUNT(*) as count FROM contact_submissions");
        if ($result && $result->num_rows > 0) {
            $row = $result->fetch_assoc();
            $stats['total_submissions'] = (int)$row['count'];
        }
        
        // Total notifications
        $result = $conn->query("SELECT COUNT(*) as count FROM notifications");
        if ($result && $result->num_rows > 0) {
            $row = $result->fetch_assoc();
            $stats['total_notifications'] = (int)$row['count'];
        }
        
        // Contact-related notifications
        $result = $conn->query("SELECT COUNT(*) as count FROM notifications WHERE title LIKE '%Contact Form%'");
        if ($result && $result->num_rows > 0) {
            $row = $result->fetch_assoc();
            $stats['contact_notifications'] = (int)$row['count'];
        }
        
        // Unsynced submissions (submissions without notifications)
        $sql = "SELECT COUNT(*) as count 
                FROM contact_submissions cs 
                LEFT JOIN notifications n ON n.link = CONCAT('inbox.php?id=', cs.id) 
                WHERE n.id IS NULL";
        $result = $conn->query($sql);
        if ($result && $result->num_rows > 0) {
            $row = $result->fetch_assoc();
            $stats['unsynced_submissions'] = (int)$row['count'];
        }
        
    } catch (Exception $e) {
        error_log("Error getting notification stats: " . $e->getMessage());
    }
    
    return $stats;
}
?>

<div class="admin-container">
    <!-- Content Header -->
    <div class="content-header">
        <div class="content-header-left">
            <div class="content-header-icon">
                <i class="fas fa-sync-alt"></i>
            </div>
            <div class="content-header-text">
                <h1>Sync Contact Notifications</h1>
                <p>Create notifications for existing contact form submissions</p>
            </div>
        </div>
        <div class="content-header-actions">
            <a href="inbox.php" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Back to Inbox
            </a>
        </div>
    </div>

    <!-- Success/Error Messages -->
    <?php if ($success_message): ?>
        <div class="alert alert-success alert-dismissible">
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            <i class="fas fa-check-circle"></i>
            <?php echo htmlspecialchars($success_message); ?>
        </div>
    <?php endif; ?>

    <?php if ($error_message): ?>
        <div class="alert alert-danger alert-dismissible">
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            <i class="fas fa-exclamation-triangle"></i>
            <?php echo htmlspecialchars($error_message); ?>
        </div>
    <?php endif; ?>

    <div class="row">
        <!-- Current Statistics -->
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h3><i class="fas fa-chart-bar"></i> Current Statistics</h3>
                </div>
                <div class="card-body">
                    <div class="stats-grid">
                        <div class="stat-item">
                            <div class="stat-value"><?php echo number_format($current_stats['total_submissions']); ?></div>
                            <div class="stat-label">Total Contact Submissions</div>
                        </div>
                        
                        <div class="stat-item">
                            <div class="stat-value"><?php echo number_format($current_stats['contact_notifications']); ?></div>
                            <div class="stat-label">Contact Notifications</div>
                        </div>
                        
                        <div class="stat-item">
                            <div class="stat-value text-warning"><?php echo number_format($current_stats['unsynced_submissions']); ?></div>
                            <div class="stat-label">Unsynced Submissions</div>
                        </div>
                        
                        <div class="stat-item">
                            <div class="stat-value"><?php echo number_format($current_stats['total_notifications']); ?></div>
                            <div class="stat-label">Total Notifications</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Sync Action -->
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h3><i class="fas fa-sync-alt"></i> Sync Notifications</h3>
                </div>
                <div class="card-body">
                    <?php if ($current_stats['unsynced_submissions'] > 0): ?>
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle"></i>
                            <strong><?php echo number_format($current_stats['unsynced_submissions']); ?> contact submissions</strong> 
                            don't have notifications yet.
                        </div>
                        
                        <form method="post" action="">
                            <p>Click the button below to create notifications for all existing contact submissions that don't have notifications yet.</p>
                            
                            <div class="form-actions">
                                <button type="submit" name="sync_notifications" class="btn btn-primary" 
                                        onclick="return confirm('Are you sure you want to sync notifications? This will create notifications for <?php echo $current_stats['unsynced_submissions']; ?> submissions.');">
                                    <i class="fas fa-sync-alt"></i> Sync Notifications
                                </button>
                            </div>
                        </form>
                    <?php else: ?>
                        <div class="alert alert-success">
                            <i class="fas fa-check-circle"></i>
                            All contact submissions are already synced with notifications!
                        </div>
                        
                        <p>No action needed. All existing contact form submissions have corresponding notifications.</p>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Sync Results -->
    <?php if (!empty($sync_results)): ?>
        <div class="card mt-4">
            <div class="card-header">
                <h3><i class="fas fa-chart-line"></i> Sync Results</h3>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4">
                        <h5>Before Sync</h5>
                        <ul class="list-unstyled">
                            <li><strong>Contact Notifications:</strong> <?php echo number_format($sync_results['before']['contact_notifications']); ?></li>
                            <li><strong>Total Notifications:</strong> <?php echo number_format($sync_results['before']['total_notifications']); ?></li>
                            <li><strong>Unsynced Submissions:</strong> <?php echo number_format($sync_results['before']['unsynced_submissions']); ?></li>
                        </ul>
                    </div>
                    
                    <div class="col-md-4">
                        <h5>After Sync</h5>
                        <ul class="list-unstyled">
                            <li><strong>Contact Notifications:</strong> <?php echo number_format($sync_results['after']['contact_notifications']); ?></li>
                            <li><strong>Total Notifications:</strong> <?php echo number_format($sync_results['after']['total_notifications']); ?></li>
                            <li><strong>Unsynced Submissions:</strong> <?php echo number_format($sync_results['after']['unsynced_submissions']); ?></li>
                        </ul>
                    </div>
                    
                    <div class="col-md-4">
                        <h5>Changes</h5>
                        <ul class="list-unstyled">
                            <li><strong>Notifications Created:</strong> <span class="text-success">+<?php echo number_format($sync_results['synced']); ?></span></li>
                            <li><strong>Sync Status:</strong> <span class="text-success">✅ Complete</span></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    <?php endif; ?>

    <!-- Information -->
    <div class="card mt-4">
        <div class="card-header">
            <h3><i class="fas fa-info-circle"></i> How It Works</h3>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <h5>Automatic Notifications</h5>
                    <p>From now on, all new contact form submissions will automatically create notifications that appear in:</p>
                    <ul>
                        <li>The notification dropdown in the admin header</li>
                        <li>The notifications page</li>
                        <li>Email alerts (if configured)</li>
                    </ul>
                </div>
                
                <div class="col-md-6">
                    <h5>Notification Features</h5>
                    <ul>
                        <li><strong>Real-time alerts:</strong> Instant notification when forms are submitted</li>
                        <li><strong>Auto-read:</strong> Notifications marked as read when you view the message</li>
                        <li><strong>Auto-cleanup:</strong> Notifications deleted when messages are deleted</li>
                        <li><strong>Bulk actions:</strong> Mark multiple notifications as read at once</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 1rem;
}

.stat-item {
    text-align: center;
    padding: 1rem;
    background: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #e9ecef;
}

.stat-value {
    font-size: 2rem;
    font-weight: 600;
    color: #212529;
    margin-bottom: 0.5rem;
}

.stat-value.text-warning {
    color: #f1ca2f !important;
}

.stat-label {
    font-size: 0.875rem;
    color: #6c757d;
    font-weight: 500;
}

.form-actions {
    margin-top: 1.5rem;
}
</style>

<?php include 'includes/footer.php'; ?>
