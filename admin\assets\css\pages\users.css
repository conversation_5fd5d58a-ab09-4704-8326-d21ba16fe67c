/**
 * Users Page CSS
 *
 * This file contains styles specific to the users management page.
 */

/* Users Page Specific Styles */
.page-users .admin-content-header {
  margin-bottom: var(--spacing-4);
}

.page-users .admin-content-title-group {
  display: flex;
  flex-direction: column;
}

.page-users .admin-content-title {
  color: var(--text-dark);
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  margin: 0;
}

.page-users .admin-content-title i {
  color: var(--primary-color);
  margin-right: 8px;
}

.page-users .admin-content-subtitle {
  font-weight: var(--font-weight-medium);
  color: var(--text-muted);
  margin: var(--spacing-1) 0 0;
  font-size: var(--font-size-sm);
}

.page-users .admin-content-actions {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
}

/* Enhanced Card Styling */
.page-users .admin-card {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

.page-users .admin-card-header {
  background-color: var(--bg-light);
  border-bottom: 1px solid var(--border-light);
}

/* Users Table */
.admin-table-responsive {
  width: 100%;
  overflow-x: auto;
  margin-bottom: var(--spacing-4);
}

.admin-table {
  width: 100%;
  border-collapse: collapse;
  border-spacing: 0;
  background-color: var(--white);
  border-radius: var(--radius-md);
  box-shadow: var(--shadow-sm);
  overflow: hidden;
}

.admin-table.wp-style {
  border: 1px solid var(--border-color);
}

.admin-table th {
  background-color: var(--bg-light);
  padding: 15px;
  text-align: left;
  font-weight: var(--font-weight-semibold);
  color: var(--text-dark);
  border-bottom: 1px solid var(--border-color);
  position: sticky;
  top: 0;
  z-index: 10;
}

.admin-table td {
  padding: 15px;
  border-bottom: 1px solid var(--border-light);
  vertical-align: middle;
}

.admin-table tr:last-child td {
  border-bottom: none;
}

.admin-table tr:hover {
  background-color: var(--bg-hover);
}

/* Column Styles */
.username-column {
  min-width: 250px;
}

.email-column {
  min-width: 200px;
}

.role-column {
  min-width: 120px;
}

.date-column {
  min-width: 120px;
  color: var(--text-muted);
  font-size: 13px;
}

.actions-column {
  min-width: 120px;
  text-align: right;
}

/* User Title Area */
.user-title-area {
  display: flex;
  align-items: center;
  gap: 15px;
}

.user-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: var(--bg-light);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-muted);
  font-weight: var(--font-weight-semibold);
  font-size: 16px;
  overflow: hidden;
  flex-shrink: 0;
}

.user-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.user-info {
  display: flex;
  flex-direction: column;
  flex: 1;
  min-width: 0;
}

.user-info strong {
  display: block;
  font-weight: var(--font-weight-semibold);
  color: var(--text-dark);
  margin-bottom: 3px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* Row Actions */
.row-actions {
  display: flex;
  flex-wrap: wrap;
  gap: 5px;
  font-size: var(--font-size-xs);
  color: var(--text-muted);
  margin-top: 3px;
}

.row-actions span {
  white-space: nowrap;
}

.row-actions a {
  color: var(--text-muted);
  text-decoration: none;
  transition: color 0.2s;
}

.row-actions a:hover {
  color: var(--primary-color);
  text-decoration: underline;
}

.row-actions .edit a {
  color: var(--primary-color);
}

.row-actions .trash a {
  color: var(--danger-color);
}

.row-actions .verify a {
  color: var(--success-color);
}

/* User Badge */
.user-badge {
  display: inline-block;
  padding: 3px 8px;
  border-radius: 3px;
  font-size: 12px;
  font-weight: 600;
  background-color: var(--bg-light);
  color: var(--text-dark);
  margin-right: 5px;
  margin-bottom: 3px;
}

.user-badge.admin {
  background-color: var(--primary-color);
  color: var(--white);
}

.user-badge.current {
  background-color: var(--success-color);
  color: var(--white);
}

/* User Date */
.user-date {
  display: flex;
  flex-direction: column;
}

.date-display {
  font-weight: var(--font-weight-medium);
  color: var(--text-dark);
}

.time-display {
  font-size: 11px;
  color: var(--text-muted);
  margin-top: 2px;
}

/* User Actions */
.user-actions {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
}

.user-actions .admin-btn {
  width: 32px;
  height: 32px;
  padding: 0;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

/* Password Container */
.password-container {
  position: relative;
}

.password-toggle, 
.password-generate {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  cursor: pointer;
  padding: 5px;
  color: var(--text-muted);
}

.password-toggle {
  right: 40px;
}

.password-generate {
  right: 10px;
}

.password-toggle:hover,
.password-generate:hover {
  color: var(--primary-color);
}

/* Utility Classes */
.hidden {
  display: none !important;
}

/* Responsive Adjustments */
@media (max-width: 992px) {
  .admin-table th:not(.username-column):not(.actions-column) {
    display: none;
  }
  
  .admin-table td:not(.username-column):not(.actions-column) {
    display: none;
  }
  
  .username-column {
    width: 70%;
  }
  
  .actions-column {
    width: 30%;
  }
}

@media (max-width: 768px) {
  .admin-table {
    border: none;
    box-shadow: none;
    background-color: transparent;
  }
  
  .admin-table thead {
    display: none;
  }
  
  .admin-table tr {
    display: block;
    margin-bottom: 15px;
    border-radius: var(--radius-md);
    box-shadow: var(--shadow-sm);
    background-color: var(--white);
    border: 1px solid var(--border-color);
    position: relative;
  }
  
  .admin-table td {
    display: block;
    text-align: left;
    padding: 12px 15px;
  }
  
  .admin-table td:before {
    content: attr(data-label);
    font-weight: var(--font-weight-semibold);
    color: var(--text-muted);
    display: block;
    margin-bottom: 5px;
    font-size: 12px;
  }
  
  .admin-table td.username-column {
    width: 100%;
    border-bottom: 1px solid var(--border-light);
  }
  
  .admin-table td.actions-column {
    width: 100%;
    text-align: left;
    border-top: 1px solid var(--border-light);
    background-color: var(--bg-light);
  }
  
  .user-actions {
    justify-content: flex-start;
  }
  
  .user-title-area {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }
  
  .user-avatar {
    width: 50px;
    height: 50px;
  }
}
