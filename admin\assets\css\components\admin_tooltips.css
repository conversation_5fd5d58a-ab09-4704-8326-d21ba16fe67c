/**
 * Admin Tooltips CSS
 * 
 * This file contains styles for tooltips in the admin panel.
 */

/* Base Tooltip */
.tooltip {
  position: absolute;
  z-index: var(--z-index-tooltip);
  display: block;
  margin: 0;
  font-family: var(--font-family-sans);
  font-style: normal;
  font-weight: var(--font-weight-normal);
  line-height: 1.5;
  text-align: left;
  text-decoration: none;
  text-shadow: none;
  text-transform: none;
  letter-spacing: normal;
  word-break: normal;
  word-spacing: normal;
  white-space: normal;
  line-break: auto;
  font-size: var(--font-size-xs);
  word-wrap: break-word;
  opacity: 0;
  pointer-events: none;
}

.tooltip.show {
  opacity: 1;
}

/* Tooltip Arrow */
.tooltip-arrow {
  position: absolute;
  display: block;
  width: 8px;
  height: 8px;
}

.tooltip-arrow::before {
  position: absolute;
  content: "";
  border-color: transparent;
  border-style: solid;
}

/* Tooltip Inner */
.tooltip-inner {
  max-width: 200px;
  padding: var(--spacing-2) var(--spacing-3);
  color: var(--white);
  text-align: center;
  background-color: var(--gray-800);
  border-radius: var(--radius-md);
}

/* Tooltip Placements */
.tooltip-top {
  padding: 5px 0;
  margin-top: -5px;
}

.tooltip-top .tooltip-arrow {
  bottom: 0;
  left: 50%;
  margin-left: -4px;
}

.tooltip-top .tooltip-arrow::before {
  top: 0;
  border-width: 5px 4px 0;
  border-top-color: var(--gray-800);
}

.tooltip-right {
  padding: 0 5px;
  margin-left: 5px;
}

.tooltip-right .tooltip-arrow {
  top: 50%;
  left: 0;
  margin-top: -4px;
}

.tooltip-right .tooltip-arrow::before {
  right: 0;
  border-width: 4px 5px 4px 0;
  border-right-color: var(--gray-800);
}

.tooltip-bottom {
  padding: 5px 0;
  margin-top: 5px;
}

.tooltip-bottom .tooltip-arrow {
  top: 0;
  left: 50%;
  margin-left: -4px;
}

.tooltip-bottom .tooltip-arrow::before {
  bottom: 0;
  border-width: 0 4px 5px;
  border-bottom-color: var(--gray-800);
}

.tooltip-left {
  padding: 0 5px;
  margin-left: -5px;
}

.tooltip-left .tooltip-arrow {
  top: 50%;
  right: 0;
  margin-top: -4px;
}

.tooltip-left .tooltip-arrow::before {
  left: 0;
  border-width: 4px 0 4px 5px;
  border-left-color: var(--gray-800);
}

/* Tooltip Variants */
.tooltip-primary .tooltip-inner {
  background-color: var(--primary-color);
  color: var(--secondary-dark);
}

.tooltip-primary.tooltip-top .tooltip-arrow::before {
  border-top-color: var(--primary-color);
}

.tooltip-primary.tooltip-right .tooltip-arrow::before {
  border-right-color: var(--primary-color);
}

.tooltip-primary.tooltip-bottom .tooltip-arrow::before {
  border-bottom-color: var(--primary-color);
}

.tooltip-primary.tooltip-left .tooltip-arrow::before {
  border-left-color: var(--primary-color);
}

.tooltip-secondary .tooltip-inner {
  background-color: var(--secondary-color);
}

.tooltip-secondary.tooltip-top .tooltip-arrow::before {
  border-top-color: var(--secondary-color);
}

.tooltip-secondary.tooltip-right .tooltip-arrow::before {
  border-right-color: var(--secondary-color);
}

.tooltip-secondary.tooltip-bottom .tooltip-arrow::before {
  border-bottom-color: var(--secondary-color);
}

.tooltip-secondary.tooltip-left .tooltip-arrow::before {
  border-left-color: var(--secondary-color);
}

.tooltip-success .tooltip-inner {
  background-color: var(--success-color);
}

.tooltip-success.tooltip-top .tooltip-arrow::before {
  border-top-color: var(--success-color);
}

.tooltip-success.tooltip-right .tooltip-arrow::before {
  border-right-color: var(--success-color);
}

.tooltip-success.tooltip-bottom .tooltip-arrow::before {
  border-bottom-color: var(--success-color);
}

.tooltip-success.tooltip-left .tooltip-arrow::before {
  border-left-color: var(--success-color);
}

.tooltip-danger .tooltip-inner {
  background-color: var(--danger-color);
}

.tooltip-danger.tooltip-top .tooltip-arrow::before {
  border-top-color: var(--danger-color);
}

.tooltip-danger.tooltip-right .tooltip-arrow::before {
  border-right-color: var(--danger-color);
}

.tooltip-danger.tooltip-bottom .tooltip-arrow::before {
  border-bottom-color: var(--danger-color);
}

.tooltip-danger.tooltip-left .tooltip-arrow::before {
  border-left-color: var(--danger-color);
}

.tooltip-warning .tooltip-inner {
  background-color: var(--warning-color);
  color: var(--secondary-dark);
}

.tooltip-warning.tooltip-top .tooltip-arrow::before {
  border-top-color: var(--warning-color);
}

.tooltip-warning.tooltip-right .tooltip-arrow::before {
  border-right-color: var(--warning-color);
}

.tooltip-warning.tooltip-bottom .tooltip-arrow::before {
  border-bottom-color: var(--warning-color);
}

.tooltip-warning.tooltip-left .tooltip-arrow::before {
  border-left-color: var(--warning-color);
}

.tooltip-info .tooltip-inner {
  background-color: var(--info-color);
}

.tooltip-info.tooltip-top .tooltip-arrow::before {
  border-top-color: var(--info-color);
}

.tooltip-info.tooltip-right .tooltip-arrow::before {
  border-right-color: var(--info-color);
}

.tooltip-info.tooltip-bottom .tooltip-arrow::before {
  border-bottom-color: var(--info-color);
}

.tooltip-info.tooltip-left .tooltip-arrow::before {
  border-left-color: var(--info-color);
}

.tooltip-light .tooltip-inner {
  background-color: var(--white);
  color: var(--text-dark);
  border: 1px solid var(--border-color);
}

.tooltip-light.tooltip-top .tooltip-arrow::before {
  border-top-color: var(--white);
}

.tooltip-light.tooltip-right .tooltip-arrow::before {
  border-right-color: var(--white);
}

.tooltip-light.tooltip-bottom .tooltip-arrow::before {
  border-bottom-color: var(--white);
}

.tooltip-light.tooltip-left .tooltip-arrow::before {
  border-left-color: var(--white);
}

/* Tooltip Sizes */
.tooltip-sm .tooltip-inner {
  padding: var(--spacing-1) var(--spacing-2);
  font-size: 10px;
}

.tooltip-lg .tooltip-inner {
  padding: var(--spacing-2) var(--spacing-4);
  font-size: var(--font-size-sm);
}

/* Tooltip with Icon */
.tooltip-icon {
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-1);
}

.tooltip-icon i {
  font-size: var(--font-size-sm);
}

/* Tooltip with HTML Content */
.tooltip-html .tooltip-inner {
  text-align: left;
  white-space: normal;
}

/* Tooltip with Title */
.tooltip-title {
  display: block;
  font-weight: var(--font-weight-semibold);
  margin-bottom: var(--spacing-1);
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  padding-bottom: var(--spacing-1);
}

/* Tooltip with List */
.tooltip-list {
  text-align: left;
  padding-left: var(--spacing-4);
  margin: var(--spacing-1) 0;
}

.tooltip-list li {
  margin-bottom: var(--spacing-1);
}

/* Tooltip with Custom Width */
.tooltip-wide .tooltip-inner {
  max-width: 300px;
}

.tooltip-narrow .tooltip-inner {
  max-width: 150px;
}

/* Tooltip Animations */
.tooltip-fade {
  transition: opacity var(--transition-fast) ease;
}

.tooltip-zoom {
  transition: opacity var(--transition-fast) ease, transform var(--transition-fast) ease;
  transform: scale(0.9);
}

.tooltip-zoom.show {
  transform: scale(1);
}

/* Tooltip Trigger */
.tooltip-trigger {
  position: relative;
  display: inline-block;
}

.tooltip-trigger .tooltip {
  visibility: hidden;
  opacity: 0;
  transition: opacity var(--transition-fast) ease, visibility var(--transition-fast) ease;
}

.tooltip-trigger:hover .tooltip {
  visibility: visible;
  opacity: 1;
}

/* Help Icon with Tooltip */
.help-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background-color: var(--gray-200);
  color: var(--text-muted);
  font-size: 10px;
  cursor: help;
  margin-left: var(--spacing-1);
}

.help-icon:hover {
  background-color: var(--primary-color);
  color: var(--secondary-dark);
}

/* Responsive Tooltips */
@media (max-width: 576px) {
  .tooltip {
    display: none;
  }
}
