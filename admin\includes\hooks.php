<?php
/**
 * Admin Hooks
 * This file contains hooks for various admin actions
 */

// Include the sitemap updater
require_once __DIR__ . '/sitemap-updater.php';

/**
 * Hook for after news is created, edited, or updated
 * This function should be called after any news operation
 */
function afterNewsOperation() {
    // Schedule a sitemap update
    scheduleSitemapUpdate();
}

/**
 * Hook for after news is deleted
 * This function should be called after a news item is deleted
 */
function afterNewsDelete() {
    // Schedule a sitemap update
    scheduleSitemapUpdate();
}
?>
