<?php
/**
 * Security wrapper for the application
 * This file provides security functions and headers
 */

// Define a constant to indicate that the security wrapper has been included
define('SECURITY_WRAPPER_INCLUDED', true);

// Set up error logging
ini_set('log_errors', 1);
ini_set('error_log', __DIR__ . '/../error.log');

// Apply session security settings before starting the session
if (session_status() === PHP_SESSION_NONE) {
    // Session cookie settings - must be set before session_start()
    ini_set('session.cookie_httponly', 1);
    ini_set('session.use_only_cookies', 1);
    ini_set('session.cookie_secure', isset($_SERVER['HTTPS']) ? 1 : 0);
    ini_set('session.cookie_samesite', 'Strict');

    // Start the session
    session_start();
}

/**
 * Apply security headers
 * These headers should be applied before any output is sent to the browser
 */
function apply_security_headers() {
    // Check if headers have already been sent
    if (headers_sent($file, $line)) {
        // Log warning about headers already sent
        error_log("Warning: Cannot send security headers - output started at $file:$line");
        return false;
    }

    // Content Security Policy
    header("Content-Security-Policy: default-src 'self'; script-src 'self' 'unsafe-inline' https://cdnjs.cloudflare.com; style-src 'self' 'unsafe-inline' https://cdnjs.cloudflare.com https://fonts.googleapis.com; img-src 'self' data:; font-src 'self' https://cdnjs.cloudflare.com https://fonts.gstatic.com; connect-src 'self'");

    // XSS Protection
    header('X-XSS-Protection: 1; mode=block');

    // Content Type Options
    header('X-Content-Type-Options: nosniff');

    // Frame Options
    header('X-Frame-Options: SAMEORIGIN');

    // Referrer Policy
    header('Referrer-Policy: strict-origin-when-cross-origin');

    // Content Type
    header('Content-Type: text/html; charset=UTF-8');

    return true;
}

/**
 * Generate CSRF token
 * @return string CSRF token
 */
if (!function_exists('generate_csrf_token')) {
    function generate_csrf_token() {
        if (!isset($_SESSION['csrf_token'])) {
            $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
        }
        return $_SESSION['csrf_token'];
    }
}

/**
 * Validate CSRF token
 * @param string $token Token to validate
 * @return bool True if token is valid
 */
if (!function_exists('validate_csrf_token')) {
    function validate_csrf_token($token) {
        if (!isset($_SESSION['csrf_token'])) {
            return false;
        }
        return hash_equals($_SESSION['csrf_token'], $token);
    }
}

/**
 * Sanitize user input
 * @param string $input Input to sanitize
 * @return string Sanitized input
 */
if (!function_exists('sanitize_input')) {
    function sanitize_input($input) {
        return htmlspecialchars(trim($input), ENT_QUOTES, 'UTF-8');
    }
}

/**
 * Initialize secure session
 */
if (!function_exists('init_secure_session')) {
    function init_secure_session() {
        // Session cookie settings
        ini_set('session.cookie_httponly', 1);
        ini_set('session.use_only_cookies', 1);
        ini_set('session.cookie_secure', isset($_SERVER['HTTPS']) ? 1 : 0);
        ini_set('session.cookie_samesite', 'Strict');
        
        // Start session if not already started
        if (session_status() === PHP_SESSION_NONE) {
            session_start();
        }
        
        // Regenerate session ID periodically
        if (!isset($_SESSION['created'])) {
            $_SESSION['created'] = time();
        } else if (time() - $_SESSION['created'] > 1800) {
            // Regenerate session ID every 30 minutes
            session_regenerate_id(true);
            $_SESSION['created'] = time();
        }
    }
}

// Apply security headers
apply_security_headers();
?>
