/**
 * Admin Sidebar CSS
 *
 * This file contains styles for the admin sidebar navigation.
 * The sidebar uses the black and yellow brand colors.
 */

/* Sidebar Container */
.admin-sidebar {
  position: fixed;
  top: 0;
  left: 0;
  bottom: 0;
  width: var(--sidebar-width);
  background-color: var(--secondary-color);
  color: var(--white);
  z-index: var(--z-index-fixed);
  display: flex;
  flex-direction: column;
  transition: width var(--transition-normal) ease, transform var(--transition-normal) ease;
  overflow-x: hidden;
  overflow-y: auto;
  box-shadow: var(--shadow-lg);
}

/* Hide scrollbar but allow scrolling */
.admin-sidebar::-webkit-scrollbar {
  width: 0;
  background: transparent;
}

body.sidebar-collapsed .admin-sidebar {
  width: var(--sidebar-collapsed-width);
}

/* Collapsed sidebar styles */
.admin-sidebar.collapsed {
  width: var(--sidebar-collapsed-width);
}

/* Sidebar Header */
.admin-sidebar-header {
  padding: var(--spacing-4);
  display: flex;
  align-items: center;
  justify-content: center;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  min-height: var(--topbar-height);
}

.sidebar-logo {
  display: flex;
  align-items: center;
  justify-content: center; /* Center align the logo */
  width: 100%;
  transition: all var(--transition-normal) ease;
}

.sidebar-logo img {
  max-height: 40px;
  max-width: 100%;
  transition: all var(--transition-normal) ease;
}

body.sidebar-collapsed .sidebar-logo img {
  max-width: 40px;
}

/* Sidebar Menu */
.admin-sidebar-menu {
  flex: 1;
  padding: var(--spacing-2) 0;
  overflow-y: auto;
}

.admin-sidebar-menu ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.admin-sidebar-menu li {
  margin: 0;
  padding: 0;
  position: relative;
}

.admin-sidebar-menu a {
  display: flex;
  align-items: center;
  padding: var(--spacing-3) var(--spacing-4);
  color: rgba(255, 255, 255, 0.8);
  transition: all var(--transition-fast) ease;
  position: relative;
  text-decoration: none;
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.admin-sidebar-menu a:hover {
  color: var(--white);
  background-color: rgba(255, 255, 255, 0.05);
}

.admin-sidebar-menu a.active {
  color: var(--white);
  background-color: rgba(255, 255, 255, 0.1);
  border-left: 3px solid var(--primary-color);
}

.admin-sidebar-menu a i {
  font-size: var(--font-size-lg);
  width: 24px;
  text-align: center;
  margin-right: var(--spacing-3);
  color: rgba(255, 255, 255, 0.6);
  transition: all var(--transition-fast) ease;
}

.admin-sidebar-menu a:hover i,
.admin-sidebar-menu a.active i {
  color: var(--primary-color);
}

.admin-sidebar-menu a span {
  transition: opacity var(--transition-normal) ease;
}

body.sidebar-collapsed .admin-sidebar-menu a span {
  opacity: 0;
  visibility: hidden;
  width: 0;
}

/* Show submenu on hover in collapsed state */
body.sidebar-collapsed .admin-sidebar-menu .sidebar-item:hover .sidebar-submenu {
  display: block;
  position: absolute;
  left: var(--sidebar-collapsed-width);
  top: 0;
  width: 200px;
  background-color: var(--secondary-color);
  border-radius: 0 var(--radius-md) var(--radius-md) 0;
  box-shadow: var(--shadow-lg);
  opacity: 1;
  visibility: visible;
  max-height: 500px;
  padding: var(--spacing-1) 0;
  z-index: 1000;
}

body.sidebar-collapsed .admin-sidebar-menu .sidebar-item:hover .sidebar-submenu a span {
  opacity: 1;
  visibility: visible;
  width: auto;
}

/* Submenu */
.admin-sidebar-menu .sidebar-submenu {
  max-height: 0;
  overflow: hidden;
  transition: max-height var(--transition-normal) ease, padding var(--transition-normal) ease;
  background-color: rgba(0, 0, 0, 0.15);
  opacity: 0;
  visibility: hidden;
  display: none;
}

.admin-sidebar-menu .has-submenu.open .sidebar-submenu {
  max-height: 500px;
  opacity: 1;
  visibility: visible;
  padding: var(--spacing-1) 0;
  display: block;
}

.admin-sidebar-menu .submenu a {
  padding-left: var(--spacing-8);
  font-size: var(--font-size-xs);
  min-height: 36px;
}

.admin-sidebar-menu .submenu-icon {
  margin-left: auto;
  font-size: var(--font-size-xs);
  transition: transform var(--transition-normal) ease;
}

.admin-sidebar-menu .has-submenu.open .submenu-icon {
  transform: rotate(180deg);
}

/* Make submenu icon visible in collapsed state */
body.sidebar-collapsed .admin-sidebar-menu .submenu-icon {
  position: absolute;
  right: 5px;
  top: 50%;
  transform: translateY(-50%);
  opacity: 1;
  visibility: visible;
  width: auto;
}

body.sidebar-collapsed .admin-sidebar-menu .has-submenu.open .submenu-icon {
  transform: translateY(-50%) rotate(180deg);
}

/* Menu Toggle */
.admin-sidebar-menu .menu-toggle {
  cursor: pointer;
}

/* Notification Badge */
.menu-notification {
  position: absolute;
  right: var(--spacing-4);
  top: 50%;
  transform: translateY(-50%);
  background-color: var(--danger-color);
  color: var(--white);
  border-radius: var(--radius-full);
  min-width: 18px;
  height: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 10px;
  font-weight: var(--font-weight-bold);
  padding: 0 var(--spacing-1);
}

body.sidebar-collapsed .menu-notification {
  right: var(--spacing-1);
}

/* Enhanced Sidebar Badge for Inbox */
.sidebar-badge {
  position: absolute !important;
  top: 50% !important;
  right: 12px !important;
  transform: translateY(-50%) !important;
  background-color: #dc3545 !important;
  color: #ffffff !important;
  border-radius: 12px !important;
  min-width: 24px !important;
  height: 24px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  font-size: 11px !important;
  font-weight: 600 !important;
  line-height: 1 !important;
  box-shadow: 0 2px 4px rgba(220, 53, 69, 0.3) !important;
  transition: all 0.2s ease !important;
  z-index: 10 !important;
}

.sidebar-badge.urgent {
  background-color: #f1ca2f !important;
  color: #212529 !important;
  box-shadow: 0 2px 8px rgba(241, 202, 47, 0.4) !important;
  animation: urgentPulse 2s infinite !important;
}

.sidebar-badge.high-count {
  background: linear-gradient(135deg, #dc3545, #c82333) !important;
  min-width: 28px !important;
  height: 28px !important;
  font-size: 10px !important;
  font-weight: 700 !important;
}

.badge-text {
  padding: 0 4px !important;
  white-space: nowrap !important;
}

.badge-pulse {
  position: absolute !important;
  top: -2px !important;
  right: -2px !important;
  width: 8px !important;
  height: 8px !important;
  background-color: #28a745 !important;
  border-radius: 50% !important;
  animation: pulse 1.5s infinite !important;
}

/* Badge animations */
@keyframes urgentPulse {
  0%, 100% {
    transform: translateY(-50%) scale(1);
    box-shadow: 0 2px 8px rgba(241, 202, 47, 0.4);
  }
  50% {
    transform: translateY(-50%) scale(1.1);
    box-shadow: 0 4px 12px rgba(241, 202, 47, 0.6);
  }
}

@keyframes pulse {
  0% {
    transform: scale(0.8);
    opacity: 1;
  }
  50% {
    transform: scale(1.2);
    opacity: 0.7;
  }
  100% {
    transform: scale(0.8);
    opacity: 1;
  }
}

/* Hover effects */
.sidebar-link:hover .sidebar-badge {
  transform: translateY(-50%) scale(1.05) !important;
}

.sidebar-link:hover .sidebar-badge.urgent {
  background-color: #e6b82a !important;
  box-shadow: 0 4px 12px rgba(241, 202, 47, 0.6) !important;
}

/* Collapsed sidebar adjustments */
.admin-sidebar.collapsed .sidebar-badge {
  right: 8px !important;
  min-width: 20px !important;
  height: 20px !important;
  font-size: 10px !important;
}

.admin-sidebar.collapsed .sidebar-badge.high-count {
  min-width: 24px !important;
  height: 24px !important;
}

/* Sidebar Footer */
.admin-sidebar-footer {
  padding: var(--spacing-3) var(--spacing-4);
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: auto;
  position: sticky;
  bottom: 0;
  background-color: var(--secondary-color);
  z-index: 1;
}

.admin-sidebar-footer a {
  color: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  font-size: var(--font-size-sm);
  text-decoration: none;
  transition: color var(--transition-fast) ease;
}

.admin-sidebar-footer a:hover {
  color: var(--white);
}

.admin-sidebar-footer a i {
  margin-right: var(--spacing-2);
  font-size: var(--font-size-base);
}

body.sidebar-collapsed .admin-sidebar-footer a:not(.sidebar-toggle):not(#sidebarToggleContainer) span {
  display: none;
}

/* Sidebar toggle container */
#sidebarToggleContainer {
  margin-left: auto;
  display: flex;
  align-items: center;
}

/* Hide sidebar toggle in mobile view */
@media (max-width: 768px) {
  #sidebarToggleContainer,
  .sidebar-toggle {
    display: none !important;
  }
}

/* Sidebar Toggle Button */
.sidebar-toggle {
  background-color: var(--primary-color);
  border: none;
  color: var(--secondary-color);
  cursor: pointer;
  padding: var(--spacing-1);
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: var(--radius-full);
  transition: all var(--transition-fast) ease;
  width: 32px;
  height: 32px;
  margin-left: 10px; /* Add space between logout button and toggle */
}

.sidebar-toggle:hover {
  color: var(--secondary-color);
  background-color: var(--primary-light);
  transform: scale(1.1);
}

.sidebar-toggle:active {
  transform: scale(0.95);
}

.sidebar-toggle i {
  font-size: var(--font-size-base);
  transition: transform var(--transition-normal) ease;
}

body.sidebar-collapsed .sidebar-toggle i {
  transform: rotate(180deg);
}

/* Desktop Sidebar Toggle Button (DISABLED - using footer toggle only) */
.desktop-sidebar-toggle {
  display: none !important; /* Completely hide to prevent duplicates */
}

/* Hide all toggle buttons on mobile */
@media (max-width: 768px) {
  .sidebar-toggle,
  .sidebar-collapse-toggle,
  .desktop-sidebar-toggle,
  .floating-sidebar-toggle,
  #sidebarToggleContainer {
    display: none !important;
    visibility: hidden !important;
    opacity: 0 !important;
  }
}

/* Mobile Styles */
@media (max-width: 768px) {
  .admin-sidebar {
    position: fixed;
    top: 0;
    left: -280px;
    width: 280px !important;
    height: 100vh;
    box-shadow: none;
    z-index: 99999; /* Maximum z-index to ensure it's above everything */
    transition: left 0.3s ease;
    overflow-y: auto;
    transform: none !important;
    will-change: left; /* Optimize for animations */
  }

  .admin-sidebar.show {
    left: 0;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.2);
  }

  body.sidebar-collapsed .admin-sidebar {
    width: 280px !important;
    left: -280px;
  }

  body.sidebar-collapsed .admin-sidebar.show {
    left: 0;
  }

  /* Ensure menu items are visible */
  .admin-sidebar-menu a span,
  body.sidebar-collapsed .admin-sidebar-menu a span {
    opacity: 1;
    visibility: visible;
    width: auto;
  }

  .admin-sidebar-footer a span,
  body.sidebar-collapsed .admin-sidebar-footer a span {
    display: inline;
  }

  /* Hide user profile menu in mobile sidebar */
  .admin-sidebar .user-profile {
    display: none;
  }

  /* Sidebar Overlay */
  .sidebar-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 9999; /* Just below sidebar but above other elements */
    display: none;
    backdrop-filter: blur(2px);
    -webkit-backdrop-filter: blur(2px);
  }

  .sidebar-overlay.show {
    display: block;
    animation: fadeIn 0.3s ease-out;
  }

  @keyframes fadeIn {
    from {
      opacity: 0;
    }
    to {
      opacity: 1;
    }
  }

  /* Prevent scrolling when sidebar is open */
  body.sidebar-open {
    overflow: hidden;
  }

  /* Adjust sidebar toggle in mobile view */
  .sidebar-toggle {
    width: 36px;
    height: 36px;
  }

  /* Fix submenu visibility in mobile view */
  .sidebar-item.has-submenu.open .sidebar-submenu {
    display: block !important;
    max-height: 500px !important;
    opacity: 1 !important;
    visibility: visible !important;
    position: static; /* Ensure submenu is not positioned absolutely in mobile */
    width: 100%; /* Full width in mobile */
    box-shadow: none; /* No shadow in mobile */
    border-radius: 0; /* No border radius in mobile */
  }

  /* Fix submenu toggle icon in mobile view */
  .sidebar-item.has-submenu .menu-toggle .submenu-icon {
    display: inline-block !important;
    position: static; /* Reset position in mobile */
    transform: none; /* Reset transform in mobile */
    margin-left: auto; /* Align to right */
  }

  /* Ensure submenu items are properly styled in mobile */
  .sidebar-submenu a {
    padding-left: var(--spacing-8) !important; /* Consistent padding */
  }

  /* Ensure submenu items are visible */
  .sidebar-subitem a span {
    opacity: 1 !important;
    visibility: visible !important;
    width: auto !important;
  }

  /* Mobile menu toggle button */
  .topbar-mobile-toggle {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    background-color: #000; /* Black background */
    border: none;
    color: var(--primary-color); /* Yellow color */
    font-size: 20px;
    cursor: pointer;
    z-index: 1001;
    border-radius: 4px;
  }

  .topbar-mobile-toggle i {
    color: var(--primary-color); /* Ensure icon is yellow */
  }

  .topbar-mobile-toggle.active {
    color: var(--primary-color);
    background-color: #222; /* Slightly lighter black when active */
  }
}
