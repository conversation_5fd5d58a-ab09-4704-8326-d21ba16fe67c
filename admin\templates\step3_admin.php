<h2>Create Admin Account</h2>

<?php
// Check if an admin user already exists
$db_host = $_SESSION['db_host'] ?? '';
$db_user = $_SESSION['db_user'] ?? '';
$db_pass = $_SESSION['db_pass'] ?? '';
$db_name = $_SESSION['db_name'] ?? '';

$admin_exists = false;

if (!empty($db_host) && !empty($db_user) && !empty($db_name)) {
    $mysqli = @new mysqli($db_host, $db_user, $db_pass, $db_name);

    if (!$mysqli->connect_error) {
        $admin_check = $mysqli->query("SELECT COUNT(*) as count FROM users WHERE is_admin = 1");

        if ($admin_check && $admin_row = $admin_check->fetch_assoc()) {
            $admin_exists = ($admin_row['count'] > 0);
        }

        $mysqli->close();
    }
}

if ($admin_exists) {
    echo '<div class="alert alert-success">An administrator user already exists. You can proceed to the next step.</div>';
    echo '<div class="form-actions">';
    echo '<a href="install.php?step=2" class="btn-secondary" style="margin-right: 10px;">Previous</a>';
    echo '<a href="install.php?step=4" class="btn-secondary" style="background-color: #f1ca2f; color: #333; border: none;">Next</a>';
    echo '</div>';
} else {
    echo '<p>Create your administrator account to manage the website:</p>';
    echo '<form method="post" action="">';
?>
    <input type="hidden" name="csrf_token" value="<?php echo $_SESSION['csrf_token']; ?>">

    <div class="form-group">
        <label for="username">Username</label>
        <input type="text" id="username" name="username" required value="<?php echo isset($_POST['username']) ? htmlspecialchars($_POST['username']) : ''; ?>" placeholder="admin">
    </div>

    <div class="form-group">
        <label for="email">Email Address</label>
        <input type="email" id="email" name="email" required value="<?php echo isset($_POST['email']) ? htmlspecialchars($_POST['email']) : ''; ?>" placeholder="<EMAIL>">
    </div>

    <div class="form-group">
        <label for="password">Password</label>
        <div class="password-container" style="position: relative;">
            <input type="password" id="password" name="password" required value="<?php echo isset($_POST['password']) ? htmlspecialchars($_POST['password']) : ''; ?>" placeholder="Enter a strong password" oninput="checkPasswordStrength(this.value)">
            <button type="button" class="password-toggle" onclick="togglePasswordVisibility('password')" style="position: absolute; right: 40px; top: 50%; transform: translateY(-50%); background: none; border: none; cursor: pointer; padding: 5px;">
                <i class="fas fa-eye"></i>
            </button>
            <button type="button" class="password-generate" onclick="generatePassword('password'); copyToConfirm();" style="position: absolute; right: 10px; top: 50%; transform: translateY(-50%); background: none; border: none; cursor: pointer; padding: 5px;">
                <i class="fas fa-key"></i>
            </button>
        </div>
        <div class="password-strength-meter" style="margin-top: 5px; height: 5px; background-color: #e0e0e0; border-radius: 3px; overflow: hidden;">
            <div id="password-strength-bar" style="width: 0%; height: 100%; transition: width 0.3s, background-color 0.3s;"></div>
        </div>
        <div id="password-strength-text" style="font-size: 12px; margin-top: 5px;"></div>
    </div>

    <div class="form-group">
        <label for="confirm_password">Confirm Password</label>
        <div class="password-container" style="position: relative;">
            <input type="password" id="confirm_password" name="confirm_password" required value="<?php echo isset($_POST['confirm_password']) ? htmlspecialchars($_POST['confirm_password']) : ''; ?>" placeholder="Confirm your password" oninput="checkPasswordMatch()">
            <button type="button" class="password-toggle" onclick="togglePasswordVisibility('confirm_password')" style="position: absolute; right: 10px; top: 50%; transform: translateY(-50%); background: none; border: none; cursor: pointer; padding: 5px;">
                <i class="fas fa-eye"></i>
            </button>
        </div>
        <div id="password-match-text" style="font-size: 12px; margin-top: 5px;"></div>
    </div>

    <script>
    function checkPasswordStrength(password) {
        // Password strength criteria
        const hasLowercase = /[a-z]/.test(password);
        const hasUppercase = /[A-Z]/.test(password);
        const hasNumber = /[0-9]/.test(password);
        const hasSpecial = /[^A-Za-z0-9]/.test(password);
        const isLongEnough = password.length >= 8;

        // Calculate strength score (0-4)
        let score = 0;
        if (hasLowercase) score++;
        if (hasUppercase) score++;
        if (hasNumber) score++;
        if (hasSpecial) score++;
        if (isLongEnough) score++;

        // Adjust score based on length
        if (password.length > 12) score++;

        // Calculate percentage for the strength bar
        const percentage = Math.min(100, Math.round((score / 6) * 100));

        // Update the strength bar
        const strengthBar = document.getElementById('password-strength-bar');
        strengthBar.style.width = percentage + '%';

        // Set color and text based on strength
        let strengthText = '';
        let color = '';

        if (password.length === 0) {
            strengthText = '';
            color = '#e0e0e0';
        } else if (percentage < 30) {
            strengthText = 'Weak password';
            color = '#f44336'; // Red
        } else if (percentage < 60) {
            strengthText = 'Moderate password';
            color = '#ff9800'; // Orange
        } else if (percentage < 80) {
            strengthText = 'Strong password';
            color = '#4caf50'; // Green
        } else {
            strengthText = 'Very strong password';
            color = '#2e7d32'; // Dark green
        }

        strengthBar.style.backgroundColor = color;
        document.getElementById('password-strength-text').textContent = strengthText;
        document.getElementById('password-strength-text').style.color = color;

        // Check if passwords match
        checkPasswordMatch();
    }

    function checkPasswordMatch() {
        const password = document.getElementById('password').value;
        const confirmPassword = document.getElementById('confirm_password').value;
        const matchText = document.getElementById('password-match-text');

        if (confirmPassword.length === 0) {
            matchText.textContent = '';
            return;
        }

        if (password === confirmPassword) {
            matchText.textContent = 'Passwords match';
            matchText.style.color = '#4caf50';
        } else {
            matchText.textContent = 'Passwords do not match';
            matchText.style.color = '#f44336';
        }
    }
    </script>

    <div class="form-actions">
        <a href="install.php?step=2" class="btn-secondary" style="margin-right: 10px;">Previous</a>
        <button type="submit" name="setup_admin">Next</button>
    </div>
</form>
<?php } ?>
