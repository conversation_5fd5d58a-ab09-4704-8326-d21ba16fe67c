<?php
/**
 * Sync Notification Count AJAX Handler
 *
 * This script synchronizes the notification count with the actual unread inbox items.
 */

// Start output buffering to prevent any output before JSON
ob_start();

// Enable error logging but disable display
error_reporting(E_ALL);
ini_set('display_errors', 0);
ini_set('log_errors', 1);

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Set proper JSON headers
header('Content-Type: application/json');
header('Cache-Control: no-store, no-cache, must-revalidate, max-age=0');
header('Cache-Control: post-check=0, pre-check=0', false);
header('Pragma: no-cache');

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    echo json_encode([
        'success' => false,
        'message' => 'You must be logged in to perform this action'
    ]);
    exit;
}

// Wrap everything in a try-catch to prevent any errors from breaking JSON output
try {
    // Include database connection
    require_once '../config.php';

    // Include permissions class
    require_once '../lib/Permissions.php';

    // Initialize permission system
    $permissions = new Permissions($conn, $_SESSION['user_id']);

    // Check if user has permission to manage inbox
    if (!$permissions->hasPermission('manage_inbox')) {
        echo json_encode([
            'success' => false,
            'message' => 'You do not have permission to access the Inbox'
        ]);
        exit;
    }

    // Function to get unread inbox count
    function getUnreadInboxCount($conn) {
        // Log the function call for debugging
        error_log("getUnreadInboxCount called");

        // Check if contact_submissions table exists
        $table_check = $conn->query("SHOW TABLES LIKE 'contact_submissions'");
        if (!$table_check || $table_check->num_rows === 0) {
            error_log("contact_submissions table does not exist");
            return 0;
        }

        $sql = "SELECT COUNT(*) as unread FROM contact_submissions WHERE is_read = 0";

        // Log the SQL query for debugging
        error_log("Unread inbox count SQL query: $sql");

        $result = $conn->query($sql);

        if (!$result) {
            error_log("Failed to get unread inbox count: " . $conn->error);
            return 0;
        }

        if ($result->num_rows > 0) {
            $row = $result->fetch_assoc();
            $count = (int)$row['unread'];

            // Log the unread count for debugging
            error_log("Unread inbox count: $count");

            return $count;
        }

        return 0;
    }

    // Get unread inbox count
    $unread_count = getUnreadInboxCount($conn);

    // Return the result
    echo json_encode([
        'success' => true,
        'unread_count' => $unread_count
    ], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
} catch (Exception $e) {
    // Log the error
    error_log('Error in sync_notification_count.php: ' . $e->getMessage());

    // Return a generic error message
    echo json_encode([
        'success' => false,
        'message' => 'An unexpected error occurred',
        'unread_count' => 0
    ], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
}

// Check for JSON encoding errors
if (json_last_error() !== JSON_ERROR_NONE) {
    // Clear any previous output
    ob_clean();

    // Return a simplified error response
    echo json_encode([
        'success' => false,
        'message' => 'JSON encoding error: ' . json_last_error_msg(),
        'unread_count' => 0
    ], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
}

// End output buffering and flush
ob_end_flush();
exit;
