<?php
// Start output buffering to prevent any output before JSO<PERSON>
ob_start();

// Start session
session_start();

// Include required files
require_once '../config.php';
require_once '../lib/CollaborativeEditing.php';
require_once '../lib/Permissions.php';

// Set proper JSON headers
header('Content-Type: application/json');
header('Cache-Control: no-store, no-cache, must-revalidate, max-age=0');
header('Cache-Control: post-check=0, pre-check=0', false);
header('Pragma: no-cache');

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    echo json_encode([
        'success' => false,
        'message' => 'User not logged in'
    ]);
    exit;
}

// Initialize classes
$user_id = $_SESSION['user_id'];
$collaborative = new CollaborativeEditing($conn, $user_id);
$permissions = new Permissions($conn, $user_id);

// Check if user has collaborative editing permission
if (!$permissions->hasPermission('collaborative_editing')) {
    echo json_encode([
        'success' => false,
        'message' => 'You do not have permission for collaborative editing'
    ]);
    exit;
}

// Get the action
$action = isset($_POST['action']) ? $_POST['action'] : '';
$file_path = isset($_POST['file_path']) ? $_POST['file_path'] : '';

// Validate file path
if (empty($file_path)) {
    echo json_encode([
        'success' => false,
        'message' => 'File path is required'
    ]);
    exit;
}

// Define allowed directories
$allowed_dirs = [
    '../',
    '../images/',
    '../css/',
    '../js/'
];

// Validate file path is within allowed directories
$valid_path = false;
foreach ($allowed_dirs as $dir) {
    if (strpos($file_path, $dir) === 0) {
        $valid_path = true;
        break;
    }
}

if (!$valid_path) {
    echo json_encode([
        'success' => false,
        'message' => 'Invalid file path'
    ]);
    exit;
}

// Check file permissions
if (!$permissions->canReadFile($file_path)) {
    echo json_encode([
        'success' => false,
        'message' => 'You do not have permission to access this file'
    ]);
    exit;
}

// Process the action
switch ($action) {
    case 'acquire_lock':
        $result = $collaborative->acquireLock($file_path);
        echo json_encode($result);
        break;

    case 'extend_lock':
        $result = $collaborative->extendLock($file_path);
        echo json_encode($result);
        break;

    case 'release_lock':
        $result = $collaborative->releaseLock($file_path);
        echo json_encode($result);
        break;

    case 'get_lock':
        $lock = $collaborative->getLock($file_path);

        if ($lock) {
            echo json_encode([
                'success' => true,
                'locked' => true,
                'lock_info' => $lock
            ]);
        } else {
            echo json_encode([
                'success' => true,
                'locked' => false
            ]);
        }
        break;

    case 'force_release_lock':
        $result = $collaborative->forceReleaseLock($file_path);
        echo json_encode($result);
        break;

    case 'get_all_locks':
        // Check if user is admin
        if (!isset($_SESSION['is_admin']) || $_SESSION['is_admin'] != 1) {
            echo json_encode([
                'success' => false,
                'message' => 'Only administrators can view all locks'
            ]);
            exit;
        }

        $locks = $collaborative->getAllActiveLocks();
        echo json_encode([
            'success' => true,
            'locks' => $locks
        ]);
        break;

    default:
        echo json_encode([
            'success' => false,
            'message' => 'Invalid action'
        ]);
        break;
}

// Check for JSON encoding errors
if (json_last_error() !== JSON_ERROR_NONE) {
    // Clear any previous output
    ob_clean();

    // Return a simplified error response
    echo json_encode([
        'success' => false,
        'message' => 'JSON encoding error: ' . json_last_error_msg()
    ], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
}

// End output buffering and flush
ob_end_flush();
exit;
?>
