/**
 * Admin Progress Bars CSS
 * 
 * This file contains styles for progress bars in the admin panel.
 */

/* Base Progress */
.progress {
  display: flex;
  height: 8px;
  overflow: hidden;
  font-size: var(--font-size-xs);
  background-color: var(--gray-100);
  border-radius: var(--radius-full);
}

/* Progress Bar */
.progress-bar {
  display: flex;
  flex-direction: column;
  justify-content: center;
  overflow: hidden;
  color: var(--white);
  text-align: center;
  white-space: nowrap;
  background-color: var(--primary-color);
  transition: width var(--transition-normal) ease;
}

/* Progress Bar Variants */
.progress-bar-primary {
  background-color: var(--primary-color);
}

.progress-bar-secondary {
  background-color: var(--secondary-color);
}

.progress-bar-success {
  background-color: var(--success-color);
}

.progress-bar-danger {
  background-color: var(--danger-color);
}

.progress-bar-warning {
  background-color: var(--warning-color);
}

.progress-bar-info {
  background-color: var(--info-color);
}

/* Progress Bar Sizes */
.progress-sm {
  height: 4px;
}

.progress-md {
  height: 8px;
}

.progress-lg {
  height: 12px;
}

.progress-xl {
  height: 16px;
}

/* Progress with Label */
.progress-with-label {
  display: flex;
  flex-direction: column;
}

.progress-label {
  display: flex;
  justify-content: space-between;
  margin-bottom: var(--spacing-1);
}

.progress-title {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--text-dark);
}

.progress-value {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--text-dark);
}

/* Progress with Text */
.progress-text {
  height: 24px;
  font-size: var(--font-size-xs);
  line-height: 24px;
}

.progress-text .progress-bar {
  text-align: right;
  padding-right: var(--spacing-2);
}

/* Progress Striped */
.progress-striped .progress-bar {
  background-image: linear-gradient(
    45deg,
    rgba(255, 255, 255, 0.15) 25%,
    transparent 25%,
    transparent 50%,
    rgba(255, 255, 255, 0.15) 50%,
    rgba(255, 255, 255, 0.15) 75%,
    transparent 75%,
    transparent
  );
  background-size: 1rem 1rem;
}

/* Progress Animated */
.progress-animated .progress-bar {
  animation: progress-bar-stripes 1s linear infinite;
}

@keyframes progress-bar-stripes {
  from {
    background-position: 1rem 0;
  }
  to {
    background-position: 0 0;
  }
}

/* Progress Stacked */
.progress-stacked {
  display: flex;
  height: 8px;
  overflow: hidden;
  font-size: var(--font-size-xs);
  background-color: var(--gray-100);
  border-radius: var(--radius-full);
}

.progress-stacked .progress-bar {
  border-radius: 0;
}

.progress-stacked .progress-bar:first-child {
  border-top-left-radius: var(--radius-full);
  border-bottom-left-radius: var(--radius-full);
}

.progress-stacked .progress-bar:last-child {
  border-top-right-radius: var(--radius-full);
  border-bottom-right-radius: var(--radius-full);
}

/* Progress with Steps */
.progress-steps {
  display: flex;
  justify-content: space-between;
  margin-bottom: var(--spacing-3);
}

.progress-step {
  position: relative;
  flex: 1;
  text-align: center;
}

.progress-step:not(:last-child)::after {
  content: "";
  position: absolute;
  top: 12px;
  left: calc(50% + 12px);
  width: calc(100% - 24px);
  height: 2px;
  background-color: var(--gray-100);
  z-index: 1;
}

.progress-step.active:not(:last-child)::after {
  background-color: var(--primary-color);
}

.progress-step.completed:not(:last-child)::after {
  background-color: var(--success-color);
}

.progress-step-icon {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background-color: var(--gray-100);
  color: var(--text-muted);
  font-size: var(--font-size-xs);
  margin: 0 auto var(--spacing-2);
  z-index: 2;
}

.progress-step.active .progress-step-icon {
  background-color: var(--primary-color);
  color: var(--white);
}

.progress-step.completed .progress-step-icon {
  background-color: var(--success-color);
  color: var(--white);
}

.progress-step-label {
  font-size: var(--font-size-xs);
  color: var(--text-muted);
}

.progress-step.active .progress-step-label {
  color: var(--primary-color);
  font-weight: var(--font-weight-medium);
}

.progress-step.completed .progress-step-label {
  color: var(--success-color);
}

/* Progress Circular */
.progress-circular {
  position: relative;
  width: 60px;
  height: 60px;
}

.progress-circular-track {
  fill: transparent;
  stroke: var(--gray-100);
  stroke-width: 4;
}

.progress-circular-bar {
  fill: transparent;
  stroke: var(--primary-color);
  stroke-width: 4;
  stroke-linecap: round;
  transform-origin: center;
  transform: rotate(-90deg);
  transition: stroke-dashoffset var(--transition-normal) ease;
}

.progress-circular-text {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--text-dark);
}

.progress-circular-sm {
  width: 40px;
  height: 40px;
}

.progress-circular-sm .progress-circular-track,
.progress-circular-sm .progress-circular-bar {
  stroke-width: 3;
}

.progress-circular-sm .progress-circular-text {
  font-size: var(--font-size-xs);
}

.progress-circular-lg {
  width: 80px;
  height: 80px;
}

.progress-circular-lg .progress-circular-track,
.progress-circular-lg .progress-circular-bar {
  stroke-width: 5;
}

.progress-circular-lg .progress-circular-text {
  font-size: var(--font-size-base);
}

/* Progress Circular Variants */
.progress-circular-primary .progress-circular-bar {
  stroke: var(--primary-color);
}

.progress-circular-secondary .progress-circular-bar {
  stroke: var(--secondary-color);
}

.progress-circular-success .progress-circular-bar {
  stroke: var(--success-color);
}

.progress-circular-danger .progress-circular-bar {
  stroke: var(--danger-color);
}

.progress-circular-warning .progress-circular-bar {
  stroke: var(--warning-color);
}

.progress-circular-info .progress-circular-bar {
  stroke: var(--info-color);
}

/* Progress with Icon */
.progress-with-icon {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
}

.progress-icon {
  width: 40px;
  height: 40px;
  border-radius: var(--radius-md);
  background-color: var(--primary-very-light);
  color: var(--primary-color);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--font-size-lg);
  flex-shrink: 0;
}

.progress-content {
  flex: 1;
}

/* Progress Group */
.progress-group {
  margin-bottom: var(--spacing-4);
}

.progress-group:last-child {
  margin-bottom: 0;
}

/* Progress with Animation */
.progress-indeterminate .progress-bar {
  width: 100%;
  background-image: linear-gradient(
    45deg,
    rgba(255, 255, 255, 0.15) 25%,
    transparent 25%,
    transparent 50%,
    rgba(255, 255, 255, 0.15) 50%,
    rgba(255, 255, 255, 0.15) 75%,
    transparent 75%,
    transparent
  );
  background-size: 1rem 1rem;
  animation: progress-bar-indeterminate 1s linear infinite;
}

@keyframes progress-bar-indeterminate {
  0% {
    background-position: 0 0;
  }
  100% {
    background-position: 1rem 0;
  }
}

/* Progress with Gradient */
.progress-gradient .progress-bar {
  background-image: linear-gradient(to right, var(--primary-color), var(--info-color));
}

/* Progress with Shadow */
.progress-shadow {
  box-shadow: var(--shadow-sm);
}

/* Progress with Border */
.progress-bordered {
  border: 1px solid var(--border-color);
}

/* Progress with Tooltip */
.progress-tooltip {
  position: relative;
}

.progress-tooltip-text {
  position: absolute;
  bottom: 100%;
  left: var(--progress-value, 0%);
  transform: translateX(-50%);
  padding: var(--spacing-1) var(--spacing-2);
  background-color: var(--gray-800);
  color: var(--white);
  font-size: var(--font-size-xs);
  border-radius: var(--radius-sm);
  margin-bottom: var(--spacing-1);
  white-space: nowrap;
  opacity: 0;
  visibility: hidden;
  transition: opacity var(--transition-fast) ease, visibility var(--transition-fast) ease;
}

.progress-tooltip-text::after {
  content: "";
  position: absolute;
  top: 100%;
  left: 50%;
  margin-left: -5px;
  border-width: 5px;
  border-style: solid;
  border-color: var(--gray-800) transparent transparent transparent;
}

.progress-tooltip:hover .progress-tooltip-text {
  opacity: 1;
  visibility: visible;
}
