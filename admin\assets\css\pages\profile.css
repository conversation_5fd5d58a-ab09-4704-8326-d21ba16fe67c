/**
 * Profile Page CSS
 *
 * Professional profile page styling that matches the admin panel design
 */

/* Profile Container - Full Width */
.profile-container {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    display: flex;
    flex-direction: column;
    gap: 30px;
}

/* Profile Overview Card */
.profile-overview-card {
    margin-bottom: 0;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border: 1px solid var(--border-color);
}

/* Profile Tabs Card */
.profile-tabs-card {
    margin-bottom: 0;
}

/* Profile Overview in Card Header */
.profile-overview {
    display: flex;
    align-items: center;
    gap: 30px;
    padding: 20px 0;
}

.profile-avatar-section {
    flex-shrink: 0;
}

.profile-avatar-img {
    width: 100px;
    height: 100px;
    border-radius: 50%;
    object-fit: cover;
    border: 4px solid var(--primary-color);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.profile-avatar-placeholder {
    width: 100px;
    height: 100px;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 40px;
    color: white;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.profile-info-section {
    flex: 1;
}

.profile-display-name {
    font-size: 2rem;
    font-weight: 700;
    color: var(--text-dark);
    margin-bottom: 8px;
}

.profile-display-email {
    font-size: 1.1rem;
    color: var(--text-muted);
    margin-bottom: 15px;
}

.profile-badges {
    display: flex;
    gap: 12px;
    flex-wrap: wrap;
}

/* Enhanced Tab Navigation */
.admin-tabs {
    background: var(--card-bg);
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.admin-tab-nav {
    display: flex;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-bottom: 1px solid var(--border-color);
    position: relative;
}

.admin-tab-nav::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 50%;
    height: 3px;
    background: linear-gradient(90deg, var(--primary-color), var(--primary-dark));
    transition: transform 0.3s ease;
    transform: translateX(0);
}

.admin-tab-nav:has(.admin-tab-btn:nth-child(2).active)::after {
    transform: translateX(100%);
}

.admin-tab-btn {
    flex: 1;
    padding: 20px 30px;
    background: none;
    border: none;
    font-size: 1rem;
    font-weight: 600;
    color: var(--text-muted);
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12px;
    position: relative;
    z-index: 1;
}

.admin-tab-btn:hover {
    background: rgba(241, 202, 47, 0.1);
    color: var(--primary-dark);
    transform: translateY(-1px);
}

.admin-tab-btn.active {
    background: var(--card-bg);
    color: var(--primary-color);
    box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.05);
}

.admin-tab-btn i {
    font-size: 1.1rem;
    transition: transform 0.3s ease;
}

.admin-tab-btn:hover i,
.admin-tab-btn.active i {
    transform: scale(1.1);
}

/* Tab Content Styling */
.admin-tab-content {
    padding: 0;
    background: var(--card-bg);
}

.admin-tab-pane {
    display: none;
    padding: 40px;
    animation: fadeIn 0.3s ease;
}

.admin-tab-pane.active {
    display: block;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Form Section Styling */
.admin-form-section {
    margin-bottom: 40px;
    background: #fafbfc;
    border-radius: var(--border-radius);
    padding: 30px;
    border: 1px solid #f0f1f2;
}

.admin-section-title {
    font-size: 1.3rem;
    font-weight: 700;
    color: var(--text-dark);
    margin-bottom: 25px;
    padding-bottom: 15px;
    border-bottom: 3px solid var(--primary-color);
    display: flex;
    align-items: center;
    gap: 12px;
    position: relative;
}

.admin-section-title::after {
    content: '';
    position: absolute;
    bottom: -3px;
    left: 0;
    width: 60px;
    height: 3px;
    background: linear-gradient(90deg, var(--primary-color), var(--primary-dark));
    border-radius: 2px;
}

.admin-section-title i {
    color: var(--primary-color);
    font-size: 1.2rem;
    padding: 8px;
    background: rgba(241, 202, 47, 0.1);
    border-radius: 50%;
}

/* Form Grid Layout */
.admin-form-grid {
    display: grid;
    gap: 20px;
}

.admin-form-grid-2 {
    grid-template-columns: 1fr 1fr;
}

/* Form Group Styling */
.admin-form-group {
    margin-bottom: 25px;
}

.admin-form-label {
    display: block;
    font-weight: 600;
    color: var(--text-dark);
    margin-bottom: 10px;
    font-size: 1rem;
    position: relative;
}

.admin-form-label::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 0;
    height: 2px;
    background: var(--primary-color);
    transition: width 0.3s ease;
}

.admin-form-group:focus-within .admin-form-label::after {
    width: 30px;
}

.admin-form-input {
    width: 100%;
    padding: 15px 18px;
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius);
    font-size: 1rem;
    transition: all 0.3s ease;
    background-color: var(--card-bg);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.02);
}

.admin-form-input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 4px rgba(241, 202, 47, 0.15), 0 4px 12px rgba(0, 0, 0, 0.1);
    transform: translateY(-1px);
}

.admin-form-input:hover {
    border-color: var(--primary-light);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);
}

.admin-form-hint {
    font-size: 0.9rem;
    color: var(--text-muted);
    margin-top: 8px;
    display: flex;
    align-items: center;
    gap: 6px;
}

.admin-form-hint::before {
    content: 'ℹ️';
    font-size: 0.8rem;
}

/* Form Actions */
.admin-form-actions {
    padding-top: 30px;
    border-top: 2px solid var(--border-color);
    text-align: right;
    margin-top: 40px;
    background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
    margin: 40px -30px -30px -30px;
    padding: 30px;
    border-radius: 0 0 var(--border-radius) var(--border-radius);
}

.admin-btn {
    display: inline-flex;
    align-items: center;
    gap: 10px;
    padding: 15px 30px;
    border: none;
    border-radius: var(--border-radius);
    font-size: 1rem;
    font-weight: 600;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.admin-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.admin-btn:hover::before {
    left: 100%;
}

.admin-btn-primary {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    color: var(--text-dark);
    box-shadow: 0 4px 12px rgba(241, 202, 47, 0.3);
}

.admin-btn-primary:hover {
    background: linear-gradient(135deg, var(--primary-dark), #d4a017);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(241, 202, 47, 0.4);
}

.admin-btn-primary:active {
    transform: translateY(0);
    box-shadow: 0 2px 8px rgba(241, 202, 47, 0.3);
}

.admin-btn i {
    font-size: 1.1rem;
    transition: transform 0.3s ease;
}

.admin-btn:hover i {
    transform: scale(1.1);
}

/* Image Preview Styling */
#image-preview {
    margin-top: 15px;
    padding: 20px;
    border: 2px dashed var(--primary-color);
    border-radius: var(--border-radius);
    background: linear-gradient(135deg, rgba(241, 202, 47, 0.05), rgba(241, 202, 47, 0.1));
    text-align: center;
    transition: all 0.3s ease;
}

#image-preview:hover {
    border-color: var(--primary-dark);
    background: linear-gradient(135deg, rgba(241, 202, 47, 0.1), rgba(241, 202, 47, 0.15));
}

#preview-img {
    max-width: 200px;
    max-height: 200px;
    border-radius: 12px;
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
    border: 3px solid var(--primary-color);
    transition: transform 0.3s ease;
}

#preview-img:hover {
    transform: scale(1.05);
}

/* Responsive Design */
@media (max-width: 768px) {
    .profile-container {
        max-width: 100%;
        margin: 0;
        padding: 0 15px;
        gap: 20px;
    }

    .profile-overview {
        flex-direction: column;
        text-align: center;
        gap: 20px;
        padding: 15px 0;
    }

    .profile-avatar-img,
    .profile-avatar-placeholder {
        width: 80px;
        height: 80px;
    }

    .profile-avatar-placeholder {
        font-size: 32px;
    }

    .profile-display-name {
        font-size: 1.6rem;
    }

    .profile-badges {
        justify-content: center;
    }

    .admin-form-grid-2 {
        grid-template-columns: 1fr;
    }

    .admin-tab-pane {
        padding: 25px;
    }

    .admin-form-section {
        padding: 20px;
        margin-bottom: 25px;
    }

    .admin-form-actions {
        text-align: center;
        margin: 25px -20px -20px -20px;
        padding: 20px;
    }

    .admin-btn {
        width: 100%;
        justify-content: center;
        padding: 12px 20px;
    }

    .admin-tab-btn {
        padding: 15px 20px;
        font-size: 0.9rem;
    }

    .admin-section-title {
        font-size: 1.2rem;
    }
}

@media (max-width: 480px) {
    .profile-container {
        padding: 0 10px;
        gap: 15px;
    }

    .profile-overview {
        gap: 15px;
        padding: 10px 0;
    }

    .profile-avatar-img,
    .profile-avatar-placeholder {
        width: 70px;
        height: 70px;
    }

    .profile-avatar-placeholder {
        font-size: 28px;
    }

    .profile-display-name {
        font-size: 1.4rem;
    }

    .profile-display-email {
        font-size: 1rem;
    }

    .admin-tab-pane {
        padding: 20px;
    }

    .admin-form-section {
        padding: 15px;
        margin-bottom: 20px;
    }

    .admin-section-title {
        font-size: 1.1rem;
        margin-bottom: 20px;
    }

    .admin-tab-btn {
        padding: 12px 15px;
        font-size: 0.85rem;
        gap: 8px;
    }

    .admin-form-actions {
        margin: 20px -15px -15px -15px;
        padding: 15px;
    }

    .admin-btn {
        padding: 12px 16px;
        font-size: 0.9rem;
    }

    #preview-img {
        max-width: 150px;
        max-height: 150px;
    }
}

/* Large Desktop - Full width utilization */
@media (min-width: 1200px) {
    .profile-container {
        max-width: 1000px;
    }

    .admin-tab-pane {
        padding: 50px;
    }

    .admin-form-section {
        padding: 40px;
    }
}
