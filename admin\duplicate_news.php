<?php
session_start();
require_once 'config.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    redirect('index.php');
}

// Check if ID is provided
if (!isset($_GET['id']) || empty($_GET['id'])) {
    redirect('dashboard.php');
}

$id = intval($_GET['id']);

// Get news post data to duplicate
$sql = "SELECT * FROM news WHERE id = $id";
$result = $conn->query($sql);

if ($result->num_rows > 0) {
    $news = $result->fetch_assoc();

    // Prepare data for duplication
    $title = $conn->real_escape_string($news['title'] . ' (Copy)');
    $content = $conn->real_escape_string($news['content']);
    $category_id = $news['category_id'] ? intval($news['category_id']) : 'NULL';

    // Generate a new slug based on the title
    $base_slug = strtolower(trim(preg_replace('/[^a-zA-Z0-9-]+/', '-', $news['title'])));
    $slug = $base_slug . '-copy';

    // Check if slug already exists and append a number if needed
    $slug_exists = true;
    $counter = 1;

    while ($slug_exists) {
        $check_sql = "SELECT id FROM news WHERE slug = '$slug'";
        $check_result = $conn->query($check_sql);

        if ($check_result->num_rows == 0) {
            $slug_exists = false;
        } else {
            $slug = $base_slug . '-copy-' . $counter;
            $counter++;
        }
    }

    // Handle image duplication
    $original_image = $news['featured_image'];
    $image_path = "../images/news/" . $original_image;
    $new_image = '';

    if (file_exists($image_path)) {
        // Generate a new filename
        $image_info = pathinfo($original_image);
        $new_image_name = $image_info['filename'] . '-copy-' . time();

        if (isset($image_info['extension'])) {
            $new_image_name .= '.' . $image_info['extension'];
        }

        $new_image_path = "../images/news/" . $new_image_name;

        // Copy the image file
        if (copy($image_path, $new_image_path)) {
            $new_image = $new_image_name;
        } else {
            // If copy fails, use the original image
            $new_image = $original_image;
        }
    } else {
        // If original image doesn't exist, use the original image name
        $new_image = $original_image;
    }

    // Insert duplicated news post with current user as author using prepared statement
    $author_id = $_SESSION['user_id']; // Get the current logged-in user ID
    $sql = "INSERT INTO news (title, content, featured_image, slug, category_id, author_id) VALUES (?, ?, ?, ?, ?, ?)";

    $stmt = $conn->prepare($sql);
    if (!$stmt) {
        $_SESSION['error_message'] = "Failed to prepare statement: " . $conn->error;
    } else {
        $stmt->bind_param("ssssii", $title, $content, $new_image, $slug, $category_id, $author_id);

        if ($stmt->execute()) {
            // Set success message
            $_SESSION['success_message'] = "News post duplicated successfully!";
        } else {
            // Set error message
            $_SESSION['error_message'] = "Error duplicating news post: " . $stmt->error;
        }

        $stmt->close();
    }
}

// Redirect back to all news page
redirect('all_news.php');
?>
