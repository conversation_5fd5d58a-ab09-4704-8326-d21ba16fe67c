<?php
/**
 * User Settings Page
 *
 * This page allows users to manage their account settings and preferences.
 */

// Include necessary files
session_start();
require_once 'config.php';
require_once 'includes/admin-functions.php';

// Check if user is logged in
require_login();

// Initialize variables
$page_title = "Account Settings";
$page_icon = "fas fa-cog";
$page_subtitle = "Manage your account settings and preferences";
$user_id = $_SESSION['user_id'];
$success_message = '';
$error_message = '';

// Get user data
$user_sql = "SELECT * FROM users WHERE id = $user_id";
$user_result = $conn->query($user_sql);
$user = $user_result->fetch_assoc();

// Check if user_settings table exists and what structure it has
$check_table = $conn->query("SHOW TABLES LIKE 'user_settings'");
$user_settings_table_exists = ($check_table && $check_table->num_rows > 0);

// Default settings (only include columns that exist in the database)
$user_settings = [
    'timezone' => 'UTC',
    'language' => 'en',
    'notifications_enabled' => 1,
    'email_notifications' => 1,
    'browser_notifications' => 1,
    'notification_sound' => 1,
    'theme' => 'light',
    'items_per_page' => 10,
    'date_format' => 'Y-m-d',
    'time_format' => 'H:i'
];

if ($user_settings_table_exists) {
    // Check if table has setting_key and setting_value columns (key-value structure)
    $check_key_column = $conn->query("SHOW COLUMNS FROM user_settings LIKE 'setting_key'");
    $check_value_column = $conn->query("SHOW COLUMNS FROM user_settings LIKE 'setting_value'");
    $has_key_value_structure = ($check_key_column && $check_key_column->num_rows > 0 &&
                               $check_value_column && $check_value_column->num_rows > 0);

    if ($has_key_value_structure) {
        // Use key-value structure
        $settings_query = "SELECT setting_key, setting_value FROM user_settings WHERE user_id = $user_id";
        $settings_result = $conn->query($settings_query);

        if ($settings_result && $settings_result->num_rows > 0) {
            while ($row = $settings_result->fetch_assoc()) {
                $user_settings[$row['setting_key']] = $row['setting_value'];
            }
        }
    } else {
        // Use direct column structure
        $settings_query = "SELECT * FROM user_settings WHERE user_id = $user_id";
        $settings_result = $conn->query($settings_query);

        if ($settings_result && $settings_result->num_rows > 0) {
            $row = $settings_result->fetch_assoc();
            // Map database columns to settings array
            foreach ($user_settings as $key => $default_value) {
                if (isset($row[$key])) {
                    $user_settings[$key] = $row[$key];
                }
            }
        }
    }
}

// Process form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['update_settings'])) {
    // Get form data
    $timezone = $_POST['timezone'] ?? 'UTC';
    $language = $_POST['language'] ?? 'en';
    $notifications_enabled = isset($_POST['notifications_enabled']) ? 1 : 0;
    $email_notifications = isset($_POST['email_notifications']) ? 1 : 0;
    $browser_notifications = isset($_POST['browser_notifications']) ? 1 : 0;
    $notification_sound = isset($_POST['notification_sound']) ? 1 : 0;
    $theme = $_POST['theme'] ?? 'light';
    $items_per_page = (int)($_POST['items_per_page'] ?? 10);
    $date_format = $_POST['date_format'] ?? 'Y-m-d';
    $time_format = $_POST['time_format'] ?? 'H:i';

    // Validate input
    if ($items_per_page < 5 || $items_per_page > 100) {
        $items_per_page = 10;
    }

    // Update settings in database
    $settings_to_update = [
        'timezone' => $timezone,
        'language' => $language,
        'notifications_enabled' => $notifications_enabled,
        'email_notifications' => $email_notifications,
        'browser_notifications' => $browser_notifications,
        'notification_sound' => $notification_sound,
        'theme' => $theme,
        'items_per_page' => $items_per_page,
        'date_format' => $date_format,
        'time_format' => $time_format
    ];

    // Start transaction
    $conn->begin_transaction();

    try {
        if ($user_settings_table_exists) {
            // Check table structure again for updates
            $check_key_column = $conn->query("SHOW COLUMNS FROM user_settings LIKE 'setting_key'");
            $check_value_column = $conn->query("SHOW COLUMNS FROM user_settings LIKE 'setting_value'");
            $has_key_value_structure = ($check_key_column && $check_key_column->num_rows > 0 &&
                                       $check_value_column && $check_value_column->num_rows > 0);

            if ($has_key_value_structure) {
                // Use key-value structure
                foreach ($settings_to_update as $key => $value) {
                    // Check if setting exists
                    $check_sql = "SELECT id FROM user_settings WHERE user_id = $user_id AND setting_key = '$key'";
                    $check_result = $conn->query($check_sql);

                    if ($check_result->num_rows > 0) {
                        // Update existing setting
                        $update_sql = "UPDATE user_settings SET setting_value = '$value' WHERE user_id = $user_id AND setting_key = '$key'";
                        $conn->query($update_sql);
                    } else {
                        // Insert new setting
                        $insert_sql = "INSERT INTO user_settings (user_id, setting_key, setting_value) VALUES ($user_id, '$key', '$value')";
                        $conn->query($insert_sql);
                    }
                }
            } else {
                // Use direct column structure
                // Check if user has a row in the table
                $check_user_sql = "SELECT id FROM user_settings WHERE user_id = $user_id";
                $check_user_result = $conn->query($check_user_sql);

                if ($check_user_result->num_rows > 0) {
                    // Update existing row
                    $update_fields = [];
                    foreach ($settings_to_update as $key => $value) {
                        $update_fields[] = "$key = '$value'";
                    }
                    $update_sql = "UPDATE user_settings SET " . implode(', ', $update_fields) . " WHERE user_id = $user_id";
                    $conn->query($update_sql);
                } else {
                    // Insert new row
                    $columns = array_keys($settings_to_update);
                    $values = array_values($settings_to_update);
                    $columns_str = implode(', ', $columns);
                    $values_str = "'" . implode("', '", $values) . "'";
                    $insert_sql = "INSERT INTO user_settings (user_id, $columns_str) VALUES ($user_id, $values_str)";
                    $conn->query($insert_sql);
                }
            }
        } else {
            // Table doesn't exist, create it with direct column structure
            $create_table_sql = "CREATE TABLE user_settings (
                id INT(11) NOT NULL AUTO_INCREMENT,
                user_id INT(11) NOT NULL,
                timezone VARCHAR(100) DEFAULT 'UTC',
                language VARCHAR(10) DEFAULT 'en',
                notifications_enabled TINYINT(1) DEFAULT 1,
                email_notifications TINYINT(1) DEFAULT 1,
                browser_notifications TINYINT(1) DEFAULT 1,
                notification_sound TINYINT(1) DEFAULT 1,
                theme VARCHAR(20) DEFAULT 'light',
                items_per_page INT(11) DEFAULT 10,
                date_format VARCHAR(20) DEFAULT 'Y-m-d',
                time_format VARCHAR(20) DEFAULT 'H:i',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                PRIMARY KEY (id),
                UNIQUE KEY user_id (user_id)
            )";
            $conn->query($create_table_sql);

            // Insert new row
            $columns = array_keys($settings_to_update);
            $values = array_values($settings_to_update);
            $columns_str = implode(', ', $columns);
            $values_str = "'" . implode("', '", $values) . "'";
            $insert_sql = "INSERT INTO user_settings (user_id, $columns_str) VALUES ($user_id, $values_str)";
            $conn->query($insert_sql);
        }

        // Update theme in users table if column exists
        $check_theme_column = $conn->query("SHOW COLUMNS FROM users LIKE 'theme'");
        if ($check_theme_column->num_rows > 0) {
            $update_theme_sql = "UPDATE users SET theme = '$theme' WHERE id = $user_id";
            $conn->query($update_theme_sql);
        }



        // Commit transaction
        $conn->commit();

        // Log the activity
        require_once 'includes/admin-functions.php';
        log_activity('update', 'Updated user settings', $_SESSION['user_id']);

        $success_message = "Settings updated successfully";

        // Update user settings in memory
        $user_settings = $settings_to_update;
    } catch (Exception $e) {
        // Rollback transaction on error
        $conn->rollback();
        $error_message = "Error updating settings: " . $e->getMessage();
    }
}

// Get available timezones
$timezones = DateTimeZone::listIdentifiers();

// Get available languages
$languages = [
    'en' => 'English',
    'es' => 'Spanish',
    'fr' => 'French',
    'de' => 'German',
    'it' => 'Italian',
    'pt' => 'Portuguese',
    'ru' => 'Russian',
    'zh' => 'Chinese',
    'ja' => 'Japanese',
    'ar' => 'Arabic'
];

// Get available date formats
$date_formats = [
    'Y-m-d' => date('Y-m-d') . ' (YYYY-MM-DD)',
    'm/d/Y' => date('m/d/Y') . ' (MM/DD/YYYY)',
    'd/m/Y' => date('d/m/Y') . ' (DD/MM/YYYY)',
    'M j, Y' => date('M j, Y') . ' (Month Day, Year)',
    'j F Y' => date('j F Y') . ' (Day Month Year)'
];

// Get available time formats
$time_formats = [
    'H:i' => date('H:i') . ' (24-hour)',
    'h:i A' => date('h:i A') . ' (12-hour with AM/PM)'
];

// Add page-specific CSS
$extra_css = '<link rel="stylesheet" href="assets/css/pages/user_settings.css?v=' . time() . '">';

// Include header
include 'includes/header.php';
?>

<div class="admin-container">
    <div class="admin-content-header">
        <div class="admin-content-title-group">
            <h2 class="admin-content-title"><i class="<?php echo $page_icon; ?>"></i> <?php echo $page_title; ?></h2>
            <p class="admin-content-subtitle"><?php echo $page_subtitle; ?></p>
        </div>
        <div class="admin-content-actions">
            <a href="profile.php" class="admin-btn">
                <i class="fas fa-user"></i> View Profile
            </a>
        </div>
    </div>

    <?php if (!empty($success_message)): ?>
    <div class="alert alert-success">
        <i class="fas fa-check-circle"></i> <?php echo $success_message; ?>
    </div>
    <?php endif; ?>

    <?php if (!empty($error_message)): ?>
    <div class="alert alert-danger">
        <i class="fas fa-exclamation-circle"></i> <?php echo $error_message; ?>
    </div>
    <?php endif; ?>

    <div class="admin-content-body">
        <div class="settings-container">
            <div class="settings-sidebar">
                <ul class="settings-nav">
                    <li class="settings-nav-item active" data-target="general-settings">
                        <i class="fas fa-sliders-h"></i> General Settings
                    </li>
                    <li class="settings-nav-item" data-target="appearance-settings">
                        <i class="fas fa-palette"></i> Appearance
                    </li>
                    <li class="settings-nav-item" data-target="notification-settings">
                        <i class="fas fa-bell"></i> Notifications
                    </li>

                </ul>
            </div>

            <div class="settings-content">
                <form method="post" action="" class="settings-form">
                    <!-- General Settings -->
                    <div class="settings-section active" id="general-settings">
                        <h3 class="settings-section-title">General Settings</h3>

                        <div class="form-group">
                            <label for="timezone">Timezone</label>
                            <select id="timezone" name="timezone" class="form-control">
                                <?php foreach ($timezones as $tz): ?>
                                <option value="<?php echo $tz; ?>" <?php echo ($user_settings['timezone'] === $tz) ? 'selected' : ''; ?>><?php echo $tz; ?></option>
                                <?php endforeach; ?>
                            </select>
                            <p class="form-hint">Select your local timezone for accurate date and time display.</p>
                        </div>

                        <div class="form-group">
                            <label for="language">Language</label>
                            <select id="language" name="language" class="form-control">
                                <?php foreach ($languages as $code => $name): ?>
                                <option value="<?php echo $code; ?>" <?php echo ($user_settings['language'] === $code) ? 'selected' : ''; ?>><?php echo $name; ?></option>
                                <?php endforeach; ?>
                            </select>
                            <p class="form-hint">Select your preferred language for the admin interface.</p>
                        </div>

                        <div class="form-group">
                            <label for="date_format">Date Format</label>
                            <select id="date_format" name="date_format" class="form-control">
                                <?php foreach ($date_formats as $format => $example): ?>
                                <option value="<?php echo $format; ?>" <?php echo ($user_settings['date_format'] === $format) ? 'selected' : ''; ?>><?php echo $example; ?></option>
                                <?php endforeach; ?>
                            </select>
                            <p class="form-hint">Choose how dates should be displayed throughout the admin panel.</p>
                        </div>

                        <div class="form-group">
                            <label for="time_format">Time Format</label>
                            <select id="time_format" name="time_format" class="form-control">
                                <?php foreach ($time_formats as $format => $example): ?>
                                <option value="<?php echo $format; ?>" <?php echo ($user_settings['time_format'] === $format) ? 'selected' : ''; ?>><?php echo $example; ?></option>
                                <?php endforeach; ?>
                            </select>
                            <p class="form-hint">Choose how times should be displayed throughout the admin panel.</p>
                        </div>
                    </div>

                    <!-- Appearance Settings -->
                    <div class="settings-section" id="appearance-settings">
                        <h3 class="settings-section-title">Appearance Settings</h3>

                        <div class="form-group">
                            <label>Theme</label>
                            <div class="theme-options">
                                <label class="theme-option">
                                    <input type="radio" name="theme" value="light" <?php echo ($user_settings['theme'] === 'light') ? 'checked' : ''; ?>>
                                    <div class="theme-preview light-theme">
                                        <div class="theme-preview-header"></div>
                                        <div class="theme-preview-sidebar"></div>
                                        <div class="theme-preview-content"></div>
                                    </div>
                                    <span class="theme-name">Light</span>
                                </label>

                                <label class="theme-option">
                                    <input type="radio" name="theme" value="dark" <?php echo ($user_settings['theme'] === 'dark') ? 'checked' : ''; ?>>
                                    <div class="theme-preview dark-theme">
                                        <div class="theme-preview-header"></div>
                                        <div class="theme-preview-sidebar"></div>
                                        <div class="theme-preview-content"></div>
                                    </div>
                                    <span class="theme-name">Dark</span>
                                </label>
                            </div>
                            <p class="form-hint">Choose between light and dark theme for the admin interface.</p>
                        </div>



                        <div class="form-group">
                            <label for="items_per_page">Items Per Page</label>
                            <input type="number" id="items_per_page" name="items_per_page" value="<?php echo $user_settings['items_per_page']; ?>" min="5" max="100" class="form-control">
                            <p class="form-hint">Number of items to display in tables and lists (5-100).</p>
                        </div>
                    </div>

                    <!-- Notification Settings -->
                    <div class="settings-section" id="notification-settings">
                        <h3 class="settings-section-title">Notification Settings</h3>

                        <div class="form-group">
                            <label class="checkbox-label">
                                <input type="checkbox" name="notifications_enabled" value="1" <?php echo ($user_settings['notifications_enabled'] == 1) ? 'checked' : ''; ?>>
                                <span>Enable Notifications</span>
                            </label>
                            <p class="form-hint">Receive notifications about system events and updates.</p>
                        </div>

                        <div class="form-group">
                            <label class="checkbox-label">
                                <input type="checkbox" name="email_notifications" value="1" <?php echo (isset($user_settings['email_notifications']) && $user_settings['email_notifications'] == 1) ? 'checked' : ''; ?>>
                                <span>Email Notifications</span>
                            </label>
                            <p class="form-hint">Receive notifications via email.</p>
                        </div>

                        <div class="form-group">
                            <label class="checkbox-label">
                                <input type="checkbox" name="browser_notifications" value="1" <?php echo (isset($user_settings['browser_notifications']) && $user_settings['browser_notifications'] == 1) ? 'checked' : ''; ?>>
                                <span>Browser Notifications</span>
                            </label>
                            <p class="form-hint">Show notifications in your browser.</p>
                        </div>

                        <div class="form-group">
                            <label class="checkbox-label">
                                <input type="checkbox" name="notification_sound" value="1" <?php echo (isset($user_settings['notification_sound']) && $user_settings['notification_sound'] == 1) ? 'checked' : ''; ?>>
                                <span>Notification Sound</span>
                            </label>
                            <p class="form-hint">Play sound when notifications are received.</p>
                        </div>
                    </div>



                    <div class="form-actions">
                        <button type="submit" name="update_settings" class="admin-btn">
                            <i class="fas fa-save"></i> Save Settings
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Settings navigation
        const navItems = document.querySelectorAll('.settings-nav-item');
        const sections = document.querySelectorAll('.settings-section');

        navItems.forEach(item => {
            item.addEventListener('click', function() {
                const target = this.getAttribute('data-target');

                // Update active nav item
                navItems.forEach(navItem => navItem.classList.remove('active'));
                this.classList.add('active');

                // Show target section, hide others
                sections.forEach(section => {
                    if (section.id === target) {
                        section.classList.add('active');
                    } else {
                        section.classList.remove('active');
                    }
                });
            });
        });



        // Alert auto-dismissal is now handled by the global alert-auto-dismiss.js script
    });
</script>

<?php include 'includes/footer.php'; ?>
