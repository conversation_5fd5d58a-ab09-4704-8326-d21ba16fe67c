<?php
/**
 * AJAX Handler for Getting User Data
 *
 * This file handles AJAX requests to get user data for editing
 */

// Start output buffering to prevent any output before JSON
ob_start();

// Start session and include required files
session_start();
require_once '../config.php';

// Set proper JSON headers
header('Content-Type: application/json');
header('Cache-Control: no-store, no-cache, must-revalidate, max-age=0');
header('Cache-Control: post-check=0, pre-check=0', false);
header('Pragma: no-cache');

// Check if user is logged in and is admin
if (!isset($_SESSION['user_id']) || !isset($_SESSION['is_admin']) || $_SESSION['is_admin'] != 1) {
    echo json_encode([
        'success' => false,
        'message' => 'Unauthorized access'
    ]);
    exit;
}

// Check if user ID is provided
if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
    echo json_encode([
        'success' => false,
        'message' => 'Invalid user ID'
    ]);
    exit;
}

$user_id = (int)$_GET['id'];

// Get user data
$user_sql = "SELECT id, username, email, is_admin FROM users WHERE id = ?";
$stmt = $conn->prepare($user_sql);
$stmt->bind_param("i", $user_id);
$stmt->execute();
$result = $stmt->get_result();

if ($result->num_rows === 0) {
    echo json_encode([
        'success' => false,
        'message' => 'User not found'
    ]);
    exit;
}

$user = $result->fetch_assoc();

// Get user's role
$role_sql = "SELECT role_id FROM user_roles WHERE user_id = ? LIMIT 1";
$stmt = $conn->prepare($role_sql);
$stmt->bind_param("i", $user_id);
$stmt->execute();
$role_result = $stmt->get_result();

if ($role_result->num_rows > 0) {
    $role = $role_result->fetch_assoc();
    $user['role_id'] = $role['role_id'];
} else {
    $user['role_id'] = 0; // Default if no role is assigned
}

// Return user data as JSON
echo json_encode([
    'success' => true,
    'user' => $user
], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);

// Check for JSON encoding errors
if (json_last_error() !== JSON_ERROR_NONE) {
    // Clear any previous output
    ob_clean();

    // Return a simplified error response
    echo json_encode([
        'success' => false,
        'message' => 'JSON encoding error: ' . json_last_error_msg()
    ], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
}

// End output buffering and flush
ob_end_flush();
exit;
