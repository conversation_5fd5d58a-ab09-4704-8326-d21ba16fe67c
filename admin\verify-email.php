<?php
session_start();
require_once 'config.php';

$error = '';
$success = '';

// Check if token is provided
if (isset($_GET['token']) && !empty($_GET['token'])) {
    $token = sanitize($_GET['token']);
    
    // Check if token exists
    $sql = "SELECT id, username FROM users WHERE verification_token = '$token' AND is_verified = 0";
    $result = $conn->query($sql);
    
    if ($result->num_rows == 1) {
        $user = $result->fetch_assoc();
        
        // Update user as verified
        $update_sql = "UPDATE users SET is_verified = 1, verification_token = NULL WHERE id = " . $user['id'];
        
        if ($conn->query($update_sql) === TRUE) {
            $success = "Your email has been verified successfully. You can now login to your account.";
        } else {
            $error = "An error occurred. Please try again later.";
        }
    } else {
        $error = "Invalid verification token or account already verified.";
    }
} else {
    $error = "No verification token provided.";
}

// Set page title
$page_title = "Email Verification";
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> | Manage Inc.</title>
    <link rel="stylesheet" href="../css/style.css?v=<?php echo time(); ?>">
    <link rel="stylesheet" href="css/admin-style.css?v=<?php echo time(); ?>">
    <link href="https://fonts.googleapis.com/css2?family=Open+Sans:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <style>
        body {
            background-color: #f5f5f5;
            background-image: linear-gradient(135deg, #f5f5f5 0%, #e0e0e0 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            min-height: 100vh;
            padding: 20px;
            margin: 0;
            font-family: 'Open Sans', sans-serif;
        }

        .verification-container {
            max-width: 500px;
            width: 100%;
            padding: 40px;
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            position: relative;
            overflow: hidden;
            text-align: center;
        }

        .verification-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 5px;
            background: #f1ca2f;
        }

        .verification-header {
            margin-bottom: 35px;
        }

        .verification-header img {
            max-width: 200px;
            margin-bottom: 25px;
        }

        .verification-header h2 {
            color: #3c3c45;
            font-weight: 600;
            margin-bottom: 10px;
            font-size: 24px;
        }

        .verification-icon {
            font-size: 60px;
            margin-bottom: 20px;
        }

        .verification-icon.success {
            color: #2e7d32;
        }

        .verification-icon.error {
            color: #c62828;
        }

        .verification-message {
            margin-bottom: 30px;
            font-size: 16px;
            line-height: 1.6;
            color: #555;
        }

        .verification-button {
            display: inline-block;
            padding: 12px 30px;
            background-color: #3c3c45;
            color: #fff;
            text-decoration: none;
            border-radius: 4px;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .verification-button:hover {
            background-color: #4a4a52;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }

        .verification-footer {
            margin-top: 35px;
            color: #777;
            font-size: 13px;
            border-top: 1px solid #eee;
            padding-top: 20px;
        }
    </style>
</head>
<body>
    <div class="verification-container">
        <div class="verification-header">
            <img src="../images/logo.png" alt="Manage Incorporated">
            <h2>Email Verification</h2>
        </div>

        <?php if (!empty($success)): ?>
            <div class="verification-icon success">
                <i class="fas fa-check-circle"></i>
            </div>
            <div class="verification-message">
                <?php echo $success; ?>
            </div>
            <a href="index.php" class="verification-button">
                Login Now
            </a>
        <?php elseif (!empty($error)): ?>
            <div class="verification-icon error">
                <i class="fas fa-times-circle"></i>
            </div>
            <div class="verification-message">
                <?php echo $error; ?>
            </div>
            <a href="index.php" class="verification-button">
                Back to Login
            </a>
        <?php endif; ?>

        <div class="verification-footer">
            <p>&copy; <?php echo date('Y'); ?> Manage Inc. All Rights Reserved.</p>
        </div>
    </div>
</body>
</html>
