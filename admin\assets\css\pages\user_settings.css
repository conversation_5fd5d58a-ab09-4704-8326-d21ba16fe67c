/**
 * User Settings Page CSS
 *
 * This file contains styles for the user settings page.
 */

/* Settings Container */
.settings-container {
  display: flex;
  gap: var(--spacing-4);
  margin-top: var(--spacing-4);
}

/* Settings Sidebar */
.settings-sidebar {
  width: 250px;
  flex-shrink: 0;
}

.settings-nav {
  list-style: none;
  padding: 0;
  margin: 0;
  background-color: var(--white);
  border-radius: var(--radius-md);
  box-shadow: var(--shadow-sm);
  overflow: hidden;
  border: 1px solid var(--border-color);
}

.settings-nav-item {
  padding: var(--spacing-3) var(--spacing-4);
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  cursor: pointer;
  transition: all var(--transition-fast) ease;
  border-left: 3px solid transparent;
  color: var(--text-color);
  font-weight: var(--font-weight-medium);
}

.settings-nav-item:not(:last-child) {
  border-bottom: 1px solid var(--border-light);
}

.settings-nav-item:hover {
  background-color: var(--background-light);
  color: var(--primary-color);
}

.settings-nav-item.active {
  background-color: var(--background-light);
  color: var(--primary-color);
  border-left-color: var(--primary-color);
}

.settings-nav-item i {
  width: 20px;
  text-align: center;
  font-size: var(--font-size-base);
  color: inherit;
}

/* Settings Content */
.settings-content {
  flex: 1;
  min-width: 0;
}

.settings-form {
  background-color: var(--white);
  border-radius: var(--radius-md);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--border-color);
  padding: var(--spacing-4);
}

.settings-section {
  display: none;
}

.settings-section.active {
  display: block;
  animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.settings-section-title {
  margin-top: 0;
  margin-bottom: var(--spacing-4);
  font-size: var(--font-size-lg);
  color: var(--text-color);
  padding-bottom: var(--spacing-2);
  border-bottom: 1px solid var(--border-light);
}

/* Form Styling */
.form-group {
  margin-bottom: var(--spacing-4);
}

.form-group label {
  display: block;
  margin-bottom: var(--spacing-2);
  font-weight: var(--font-weight-medium);
  color: var(--text-color);
}

.form-control {
  width: 100%;
  padding: var(--spacing-2) var(--spacing-3);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  font-size: var(--font-size-sm);
  color: var(--text-color);
  background-color: var(--white);
  transition: border-color var(--transition-fast) ease, box-shadow var(--transition-fast) ease;
}

.form-control:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(241, 202, 47, 0.25);
}

.form-hint {
  margin-top: var(--spacing-1);
  font-size: var(--font-size-xs);
  color: var(--text-light);
}

/* Modern Checkbox Styling */
.checkbox-label {
  display: flex !important;
  align-items: center !important;
  gap: 12px !important;
  cursor: pointer !important;
  padding: 8px 0 !important;
  margin-bottom: 0 !important;
  font-weight: normal !important;
  user-select: none !important;
}

.checkbox-label input[type="checkbox"] {
  position: relative !important;
  width: 20px !important;
  height: 20px !important;
  cursor: pointer !important;
  appearance: none !important;
  -webkit-appearance: none !important;
  -moz-appearance: none !important;
  background-color: #ffffff !important;
  border: 2px solid #dee2e6 !important;
  border-radius: 4px !important;
  transition: all 0.2s ease !important;
  flex-shrink: 0 !important;
}

.checkbox-label input[type="checkbox"]:hover {
  border-color: #f1ca2f !important;
  box-shadow: 0 0 0 3px rgba(241, 202, 47, 0.1) !important;
}

.checkbox-label input[type="checkbox"]:focus {
  outline: none !important;
  border-color: #f1ca2f !important;
  box-shadow: 0 0 0 3px rgba(241, 202, 47, 0.25) !important;
}

.checkbox-label input[type="checkbox"]:checked {
  background-color: #f1ca2f !important;
  border-color: #f1ca2f !important;
}

.checkbox-label input[type="checkbox"]:checked::after {
  content: '✓' !important;
  position: absolute !important;
  top: 50% !important;
  left: 50% !important;
  transform: translate(-50%, -50%) !important;
  color: #212529 !important;
  font-size: 14px !important;
  font-weight: bold !important;
  line-height: 1 !important;
}

.checkbox-label span {
  font-weight: 500 !important;
  color: #212529 !important;
  font-size: 14px !important;
  line-height: 1.4 !important;
}

.checkbox-label:hover span {
  color: #f1ca2f !important;
}

/* Dark mode checkbox styles */
.dark-mode .checkbox-label input[type="checkbox"] {
  background-color: rgba(255, 255, 255, 0.1) !important;
  border-color: rgba(255, 255, 255, 0.3) !important;
}

.dark-mode .checkbox-label input[type="checkbox"]:checked {
  background-color: #f1ca2f !important;
  border-color: #f1ca2f !important;
}

.dark-mode .checkbox-label span {
  color: rgba(255, 255, 255, 0.9) !important;
}

.dark-mode .checkbox-label:hover span {
  color: #f1ca2f !important;
}

/* Theme Options */
.theme-options {
  display: flex;
  gap: var(--spacing-4);
  margin-top: var(--spacing-2);
}

.theme-option {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-2);
  cursor: pointer;
}

.theme-option input[type="radio"] {
  position: absolute;
  opacity: 0;
  width: 0;
  height: 0;
}

.theme-preview {
  width: 160px;
  height: 100px;
  border-radius: var(--radius-md);
  overflow: hidden;
  border: 2px solid var(--border-color);
  transition: border-color var(--transition-fast) ease, transform var(--transition-fast) ease;
  position: relative;
}

.theme-option input[type="radio"]:checked + .theme-preview {
  border-color: var(--primary-color);
  transform: scale(1.05);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.theme-preview-header {
  height: 20%;
  width: 100%;
}

.theme-preview-sidebar {
  position: absolute;
  left: 0;
  top: 20%;
  bottom: 0;
  width: 25%;
}

.theme-preview-content {
  position: absolute;
  right: 0;
  top: 20%;
  bottom: 0;
  width: 75%;
}

.light-theme .theme-preview-header {
  background-color: #ffffff;
  border-bottom: 1px solid #e0e0e0;
}

.light-theme .theme-preview-sidebar {
  background-color: #f8f9fa;
  border-right: 1px solid #e0e0e0;
}

.light-theme .theme-preview-content {
  background-color: #ffffff;
}

.dark-theme .theme-preview-header {
  background-color: #2c3e50;
  border-bottom: 1px solid #34495e;
}

.dark-theme .theme-preview-sidebar {
  background-color: #1a2533;
  border-right: 1px solid #34495e;
}

.dark-theme .theme-preview-content {
  background-color: #2c3e50;
}

.theme-name {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
}

/* Form Actions */
.form-actions {
  margin-top: var(--spacing-6);
  padding-top: var(--spacing-4);
  border-top: 1px solid var(--border-light);
  display: flex;
  justify-content: flex-end;
}

/* Dark Mode Styles */
.dark-mode .settings-nav {
  background-color: var(--secondary-dark);
  border-color: var(--secondary-light);
}

.dark-mode .settings-nav-item:not(:last-child) {
  border-bottom-color: rgba(255, 255, 255, 0.1);
}

.dark-mode .settings-nav-item:hover {
  background-color: rgba(255, 255, 255, 0.05);
}

.dark-mode .settings-nav-item.active {
  background-color: rgba(255, 255, 255, 0.05);
}

.dark-mode .settings-form {
  background-color: var(--secondary-dark);
  border-color: var(--secondary-light);
}

.dark-mode .settings-section-title {
  color: rgba(255, 255, 255, 0.9);
  border-bottom-color: rgba(255, 255, 255, 0.1);
}

.dark-mode .form-control {
  background-color: rgba(255, 255, 255, 0.05);
  border-color: rgba(255, 255, 255, 0.1);
  color: rgba(255, 255, 255, 0.9);
}

.dark-mode .form-control:focus {
  border-color: var(--primary-color);
}

.dark-mode .form-hint {
  color: rgba(255, 255, 255, 0.5);
}

.dark-mode .form-actions {
  border-top-color: rgba(255, 255, 255, 0.1);
}

/* Responsive Styles */
@media (max-width: 992px) {
  .settings-container {
    flex-direction: column;
  }

  .settings-sidebar {
    width: 100%;
  }

  .settings-nav {
    display: flex;
    flex-wrap: wrap;
  }

  .settings-nav-item {
    flex: 1;
    min-width: 150px;
    text-align: center;
    justify-content: center;
    border-left: none;
    border-bottom: 3px solid transparent;
  }

  .settings-nav-item.active {
    border-left-color: transparent;
    border-bottom-color: var(--primary-color);
  }

  .settings-nav-item i {
    margin-right: var(--spacing-1);
  }
}

@media (max-width: 768px) {
  .theme-options {
    flex-direction: column;
    align-items: flex-start;
  }

  .theme-option {
    flex-direction: row;
    width: 100%;
  }

  .theme-preview {
    width: 120px;
    height: 75px;
  }
}
