/**
 * Roles List CSS
 *
 * This file contains styles for the roles list page.
 */

/* Role table styling */
.role-title-area {
    display: flex;
    flex-direction: column;
}

.role-title-area strong {
    font-size: var(--font-size-base);
    margin-bottom: var(--spacing-1);
    color: var(--text-dark);
}

.role-user-count {
    display: inline-block;
    padding: 2px 8px;
    background-color: var(--background-light);
    border-radius: var(--radius-sm);
    font-size: var(--font-size-xs);
    color: var(--text-light);
}

.role-actions {
    display: flex;
    gap: var(--spacing-1);
}

/* Row actions styling */
.row-actions {
    font-size: var(--font-size-xs);
    color: var(--text-light);
    visibility: hidden;
    transition: visibility var(--transition-fast) ease;
    margin-top: var(--spacing-1);
}

tr:hover .row-actions {
    visibility: visible;
}

.row-actions a {
    color: var(--primary-color);
    text-decoration: none;
    transition: color var(--transition-fast) ease;
}

.row-actions a:hover {
    color: var(--primary-dark);
    text-decoration: underline;
}

.row-actions .trash a {
    color: var(--danger-color);
}

.row-actions .trash a:hover {
    color: var(--danger-dark);
}

/* Mobile styling */
@media (max-width: 768px) {
    .row-actions {
        visibility: visible;
    }
    
    .role-actions {
        flex-direction: column;
        gap: var(--spacing-2);
    }
    
    .role-actions .admin-btn {
        width: 100%;
        justify-content: center;
    }
}
