<?php
// Start output buffering to prevent any output before JSO<PERSON>
ob_start();

// Enable error logging but disable display
error_reporting(E_ALL);
ini_set('display_errors', 0);
ini_set('log_errors', 1);

// Set proper JSON headers
header('Content-Type: application/json');
header('Cache-Control: no-store, no-cache, must-revalidate, max-age=0');
header('Cache-Control: post-check=0, pre-check=0', false);
header('Pragma: no-cache');

// Start session
session_start();

// Wrap everything in a try-catch to prevent any errors from breaking JSON output
try {
    require_once '../config.php';
    require_once '../lib/Notifications.php';

    // Check if user is logged in
    if (!isset($_SESSION['user_id'])) {
        echo json_encode(['error' => 'Unauthorized']);
        exit;
    }

$user_id = $_SESSION['user_id'];
$notifications = new Notifications($conn);

// Handle different request methods
$method = $_SERVER['REQUEST_METHOD'];

switch ($method) {
    case 'GET':
        // Get notifications
        $limit = isset($_GET['limit']) ? (int)$_GET['limit'] : 10;
        $unread_only = isset($_GET['unread_only']) && $_GET['unread_only'] === 'true';

        $user_notifications = $notifications->getNotifications($user_id, $limit, $unread_only);
        $unread_count = $notifications->getUnreadCount($user_id);

        // Add time_ago field for each notification
        foreach ($user_notifications as &$notification) {
            // Calculate time ago
            $created_at = strtotime($notification['created_at']);
            $now = time();
            $diff = $now - $created_at;

            if ($diff < 60) {
                $notification['time_ago'] = 'Just now';
            } elseif ($diff < 3600) {
                $minutes = floor($diff / 60);
                $notification['time_ago'] = $minutes . ' minute' . ($minutes > 1 ? 's' : '') . ' ago';
            } elseif ($diff < 86400) {
                $hours = floor($diff / 3600);
                $notification['time_ago'] = $hours . ' hour' . ($hours > 1 ? 's' : '') . ' ago';
            } else {
                $days = floor($diff / 86400);
                $notification['time_ago'] = $days . ' day' . ($days > 1 ? 's' : '') . ' ago';
            }

            // Fix link paths if needed
            if (!empty($notification['link'])) {
                // Log original link
                error_log("Original notification link: " . $notification['link']);

                // Remove any 'admin/' prefix
                if (strpos($notification['link'], 'admin/') === 0) {
                    $notification['link'] = substr($notification['link'], 6);
                    error_log("Fixed notification link: " . $notification['link']);
                }
            }
        }

        // Log notifications for debugging
        error_log("Returning notifications: " . json_encode($user_notifications));

        echo json_encode([
            'success' => true,
            'data' => $user_notifications,
            'unread_count' => $unread_count
        ], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
        break;

    case 'POST':
        // Get action from query parameter or POST data
        $action = isset($_GET['action']) ? $_GET['action'] : '';

        // If no action in query, try to get from POST data
        if (empty($action)) {
            // Try to parse JSON input
            $json_data = file_get_contents('php://input');
            if (!empty($json_data)) {
                $data = json_decode($json_data, true);
                if ($data && isset($data['action'])) {
                    $action = $data['action'];
                }
            } else {
                // Try to get from regular POST data
                $action = isset($_POST['action']) ? $_POST['action'] : '';
            }
        }

        // Initialize data array
        $data = [];

        switch ($action) {
            case 'mark_read':
                // Mark notification as read
                $notification_id = isset($_POST['notification_id']) ? (int)$_POST['notification_id'] : 0;

                if ($notification_id > 0) {
                    $success = $notifications->markAsRead($notification_id, $user_id);
                    echo json_encode([
                        'success' => $success,
                        'unread_count' => $notifications->getUnreadCount($user_id)
                    ]);
                } else {
                    echo json_encode(['error' => 'Invalid notification ID']);
                }
                break;

            case 'mark_all_read':
                // Mark all notifications as read
                $success = $notifications->markAllAsRead($user_id);

                // Log the action for debugging
                error_log("Mark all as read for user $user_id: " . ($success ? 'success' : 'failed'));

                echo json_encode([
                    'success' => $success,
                    'unread_count' => 0,
                    'message' => 'All notifications marked as read'
                ]);
                break;

            case 'delete':
                // Delete notification
                $notification_id = isset($data['notification_id']) ? (int)$data['notification_id'] : 0;

                if ($notification_id > 0) {
                    // Our Notifications class doesn't have a delete method yet
                    $success = false;
                    echo json_encode(['error' => 'Delete method not implemented']);
                    exit;
                } else {
                    echo json_encode(['error' => 'Invalid notification ID']);
                }
                break;

            case 'add':
                // Only allow admins to add notifications
                if (!isset($_SESSION['is_admin']) || $_SESSION['is_admin'] != 1) {
                    echo json_encode(['error' => 'Unauthorized']);
                    exit;
                }

                // Add notification
                $target_user_id = isset($data['user_id']) ? (int)$data['user_id'] : null;
                $title = isset($data['title']) ? $data['title'] : '';
                $message = isset($data['message']) ? $data['message'] : '';
                $type = isset($data['type']) ? $data['type'] : 'info';
                $icon = isset($data['icon']) ? $data['icon'] : 'fas fa-bell';
                $link = isset($data['link']) ? $data['link'] : null;

                if (empty($title) || empty($message)) {
                    echo json_encode(['error' => 'Title and message are required']);
                    exit;
                }

                $success = $notifications->addNotification($target_user_id, $title, $message, $type, $link);

                if ($success) {
                    echo json_encode(['success' => true]);
                } else {
                    echo json_encode(['error' => 'Failed to add notification']);
                }
                break;

            default:
                echo json_encode(['error' => 'Invalid action']);
                break;
        }
        break;

    default:
        echo json_encode(['error' => 'Method not allowed']);
        break;
}

} catch (Exception $e) {
    // Log the error
    error_log('Error in notifications.php: ' . $e->getMessage());

    // Return a generic error message
    echo json_encode([
        'error' => 'An unexpected error occurred',
        'success' => false
    ], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
}

// Check for JSON encoding errors
if (json_last_error() !== JSON_ERROR_NONE) {
    // Clear any previous output
    ob_clean();

    // Return a simplified error response
    echo json_encode([
        'success' => false,
        'error' => 'JSON encoding error: ' . json_last_error_msg()
    ], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
}

// End output buffering and flush
ob_end_flush();
exit;
?>
