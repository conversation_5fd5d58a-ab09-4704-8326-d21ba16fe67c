document.addEventListener('DOMContentLoaded', function() {
    const header = document.querySelector('.main-header');
    const submenu = document.querySelector('.page-submenu');
    const banner = document.querySelector('.cloud-page-banner, .managed-services-page-banner, .infrastructure-page-banner, .services-page-banner');

    if (!submenu || !banner) return;

    function updateSubmenuPosition() {
        const headerHeight = header.offsetHeight;
        const bannerHeight = banner.offsetHeight;
        const bannerOffset = banner.offsetTop;
        const scrollPosition = window.scrollY;

        // When scrolled past the banner
        if (scrollPosition > bannerOffset + bannerHeight) {
            submenu.style.position = 'fixed';
            submenu.style.top = headerHeight + 'px';
            submenu.style.left = '0';
            submenu.style.width = '100%';
            submenu.style.zIndex = '999';
        } else {
            // Reset to original position
            submenu.style.position = 'relative';
            submenu.style.top = '0';
        }
    }

    // Set initial position
    submenu.style.position = 'relative';
    submenu.style.top = '0';

    // Update on scroll
    window.addEventListener('scroll', updateSubmenuPosition);

    // Update on resize (for responsive layouts)
    window.addEventListener('resize', updateSubmenuPosition);

    // Initial call
    updateSubmenuPosition();
});
