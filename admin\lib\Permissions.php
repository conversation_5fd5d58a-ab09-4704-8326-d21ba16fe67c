<?php
/**
 * Permissions Class
 *
 * Handles role-based access control for the admin panel
 */
class Permissions {
    private $conn;
    private $user_id;
    private $user_permissions = [];
    private $user_roles = [];
    private $file_permissions = [];

    /**
     * Constructor
     *
     * @param mysqli $conn Database connection
     * @param int $user_id User ID
     */
    public function __construct($conn, $user_id) {
        $this->conn = $conn;
        $this->user_id = $user_id;
        $this->loadUserPermissions();
        $this->loadUserRoles();
    }

    /**
     * Load user permissions from the database
     */
    private function loadUserPermissions() {
        // Get permissions through roles
        $sql = "SELECT DISTINCT p.name
                FROM permissions p
                JOIN role_permissions rp ON p.id = rp.permission_id
                JOIN user_roles ur ON rp.role_id = ur.role_id
                WHERE ur.user_id = ?";

        $stmt = $this->conn->prepare($sql);
        $stmt->bind_param("i", $this->user_id);
        $stmt->execute();
        $result = $stmt->get_result();

        while ($row = $result->fetch_assoc()) {
            $this->user_permissions[] = $row['name'];
        }
    }

    /**
     * Load user roles from the database
     */
    private function loadUserRoles() {
        $sql = "SELECT r.id, r.name
                FROM roles r
                JOIN user_roles ur ON r.id = ur.role_id
                WHERE ur.user_id = ?";

        $stmt = $this->conn->prepare($sql);
        $stmt->bind_param("i", $this->user_id);
        $stmt->execute();
        $result = $stmt->get_result();

        while ($row = $result->fetch_assoc()) {
            $this->user_roles[$row['id']] = $row['name'];
        }
    }

    /**
     * Check if user has a specific permission
     *
     * @param string $permission Permission name
     * @return bool True if user has permission, false otherwise
     */
    public function hasPermission($permission) {
        // Check if user exists
        if (empty($this->user_id)) {
            error_log("Permission check failed: No user ID provided");
            return false;
        }

        // Administrator role has all permissions
        if (in_array('Administrator', $this->user_roles)) {
            return true;
        }

        // Check if user has the specific permission
        if (in_array($permission, $this->user_permissions)) {
            return true;
        }

        // If tables don't exist yet or we're in setup mode, check for is_admin flag
        $sql = "SELECT is_admin FROM users WHERE id = ?";
        $stmt = $this->conn->prepare($sql);
        if ($stmt) {
            $stmt->bind_param("i", $this->user_id);
            $stmt->execute();
            $result = $stmt->get_result();

            if ($result && $result->num_rows > 0) {
                $user = $result->fetch_assoc();
                if (isset($user['is_admin']) && $user['is_admin'] == 1) {
                    return true;
                }
            }
            $stmt->close();
        }

        return false;
    }

    /**
     * Check if user has a specific role
     *
     * @param string $role_name Role name
     * @return bool True if user has role, false otherwise
     */
    public function hasRole($role_name) {
        return in_array($role_name, $this->user_roles);
    }

    /**
     * Load file permissions for a specific file
     *
     * @param string $file_path File path
     */
    public function loadFilePermissions($file_path) {
        // Check user-specific permissions first
        $sql = "SELECT can_read, can_write, can_delete
                FROM file_permissions
                WHERE file_path = ? AND user_id = ?";

        $stmt = $this->conn->prepare($sql);
        $stmt->bind_param("si", $file_path, $this->user_id);
        $stmt->execute();
        $result = $stmt->get_result();

        if ($result->num_rows > 0) {
            $this->file_permissions[$file_path] = $result->fetch_assoc();
            return;
        }

        // Check role-based permissions
        $sql = "SELECT fp.can_read, fp.can_write, fp.can_delete
                FROM file_permissions fp
                JOIN user_roles ur ON fp.role_id = ur.role_id
                WHERE fp.file_path = ? AND ur.user_id = ?
                LIMIT 1";

        $stmt = $this->conn->prepare($sql);
        $stmt->bind_param("si", $file_path, $this->user_id);
        $stmt->execute();
        $result = $stmt->get_result();

        if ($result->num_rows > 0) {
            $this->file_permissions[$file_path] = $result->fetch_assoc();
        } else {
            // Default permissions based on user's general permissions
            $this->file_permissions[$file_path] = [
                'can_read' => $this->hasPermission('view_files') ? 1 : 0,
                'can_write' => $this->hasPermission('edit_files') ? 1 : 0,
                'can_delete' => 0 // Default to no delete permission
            ];
        }
    }

    /**
     * Check if user can read a file
     *
     * @param string $file_path File path
     * @return bool True if user can read the file, false otherwise
     */
    public function canReadFile($file_path) {
        // Administrator can read all files
        if (in_array('Administrator', $this->user_roles)) {
            return true;
        }

        if (!isset($this->file_permissions[$file_path])) {
            $this->loadFilePermissions($file_path);
        }

        return $this->file_permissions[$file_path]['can_read'] == 1;
    }

    /**
     * Check if user can write to a file
     *
     * @param string $file_path File path
     * @return bool True if user can write to the file, false otherwise
     */
    public function canWriteFile($file_path) {
        // Administrator can write to all files
        if (in_array('Administrator', $this->user_roles)) {
            return true;
        }

        if (!isset($this->file_permissions[$file_path])) {
            $this->loadFilePermissions($file_path);
        }

        return $this->file_permissions[$file_path]['can_write'] == 1;
    }

    /**
     * Check if user can delete a file
     *
     * @param string $file_path File path
     * @return bool True if user can delete the file, false otherwise
     */
    public function canDeleteFile($file_path) {
        // Administrator can delete files
        if (in_array('Administrator', $this->user_roles)) {
            return true;
        }

        if (!isset($this->file_permissions[$file_path])) {
            $this->loadFilePermissions($file_path);
        }

        return $this->file_permissions[$file_path]['can_delete'] == 1;
    }

    /**
     * Get all user roles
     *
     * @return array Array of user roles
     */
    public function getUserRoles() {
        return $this->user_roles;
    }

    /**
     * Get all user permissions
     *
     * @return array Array of user permissions
     */
    public function getUserPermissions() {
        return $this->user_permissions;
    }
}
?>
