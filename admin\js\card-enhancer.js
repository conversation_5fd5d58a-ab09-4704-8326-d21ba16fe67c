/**
 * Card View Enhancer
 *
 * Advanced script to enhance tables with modern card view
 */

document.addEventListener('DOMContentLoaded', function() {
    // Add page class to body
    addPageClass();

    // Enhance tables with card view
    enhanceTablesWithCards();

    // Re-enhance on resize
    window.addEventListener('resize', enhanceTablesWithCards);

    // Enhance form elements
    enhanceFormElements();
});

/**
 * Add page class to body
 */
function addPageClass() {
    const path = window.location.pathname;
    const page = path.split('/').pop().replace('.php', '');

    if (page) {
        document.body.classList.add('page-' + page);
    }
}

/**
 * Enhance form elements
 */
function enhanceFormElements() {
    // Enhance textareas
    const textareas = document.querySelectorAll('textarea');
    textareas.forEach(function(textarea) {
        // Add form-control class if not present
        if (!textarea.classList.contains('form-control')) {
            textarea.classList.add('form-control');
        }

        // Set minimum height
        if (!textarea.style.minHeight) {
            textarea.style.minHeight = '120px';
        }
    });

    // Enhance select elements
    const selects = document.querySelectorAll('select');
    selects.forEach(function(select) {
        // Add form-control class if not present
        if (!select.classList.contains('form-control')) {
            select.classList.add('form-control');
        }
    });
}

/**
 * Enhance tables with modern card view
 */
function enhanceTablesWithCards() {
    // Only run on mobile
    if (window.innerWidth > 767) {
        return;
    }

    // Get all tables
    const tables = document.querySelectorAll('table');

    tables.forEach(function(table, tableIndex) {
        // Add admin-table class if not present
        if (!table.classList.contains('admin-table')) {
            table.classList.add('admin-table');
        }

        // Get headers and rows
        const headers = table.querySelectorAll('th');
        const rows = table.querySelectorAll('tbody tr');

        // Get current page for custom titles
        const currentPage = window.location.pathname.split('/').pop();

        // Add data attributes to rows and cells
        rows.forEach(function(row, rowIndex) {
            // Add title attribute to row based on page
            let title = '';

            if (currentPage === 'all_news.php') {
                title = 'News Item';
                // Try to get the actual title from the row
                const titleCell = row.querySelector('td[data-label="Title"]');
                if (titleCell) {
                    const titleText = titleCell.textContent.trim();
                    if (titleText) {
                        title = 'News: ' + titleText;
                    }
                }
            } else if (currentPage === 'dashboard.php') {
                title = 'Dashboard Item';
            } else if (currentPage === 'users.php') {
                title = 'User Account';
                // Try to get the actual name from the row
                const nameCell = row.querySelector('td[data-label="Name"]');
                if (nameCell) {
                    const nameText = nameCell.textContent.trim();
                    if (nameText) {
                        title = 'User: ' + nameText;
                    }
                }
            } else if (currentPage === 'inbox.php') {
                title = 'Message';
                // Try to get the subject from the row
                const subjectCell = row.querySelector('td[data-label="Subject"]');
                if (subjectCell) {
                    const subjectText = subjectCell.textContent.trim();
                    if (subjectText) {
                        title = 'Message: ' + subjectText;
                    }
                }
            } else {
                title = 'Item #' + (rowIndex + 1);
            }

            row.setAttribute('data-title', title);

            // Add label attributes to cells
            const cells = row.querySelectorAll('td');
            cells.forEach(function(cell, cellIndex) {
                if (cellIndex < headers.length) {
                    const label = headers[cellIndex].textContent.trim();
                    cell.setAttribute('data-label', label);
                }
            });

            // Add special styling to status cells
            const statusCells = row.querySelectorAll('td[data-label="Status"]');
            statusCells.forEach(function(cell) {
                const statusText = cell.textContent.trim().toLowerCase();

                // Check if there's already a status badge
                if (!cell.querySelector('.status-badge')) {
                    // Create status badge
                    const statusContent = cell.innerHTML;
                    let statusClass = 'read';

                    if (statusText.includes('unread') || statusText.includes('pending')) {
                        statusClass = 'unread';
                    } else if (statusText.includes('draft') || statusText.includes('inactive')) {
                        statusClass = 'draft';
                    }

                    // Replace content with badge
                    cell.innerHTML = `<span class="status-badge ${statusClass}">${statusContent}</span>`;
                }
            });

            // Add special styling to action columns
            const actionCells = row.querySelectorAll('td.actions-column');
            actionCells.forEach(function(cell) {
                // Add class to button container if not already present
                const buttonContainers = cell.querySelectorAll('div');
                buttonContainers.forEach(function(container) {
                    if (!container.classList.contains('actions')) {
                        container.classList.add('actions');
                    }

                    // Count buttons to add appropriate class
                    const buttons = container.querySelectorAll('.admin-btn, a, button');
                    if (buttons.length > 2) {
                        container.classList.add('has-multiple');
                    }

                    // Add specific classes to buttons
                    buttons.forEach(function(button) {
                        const buttonText = button.textContent.trim().toLowerCase();

                        if (buttonText.includes('view') || buttonText.includes('show')) {
                            button.classList.add('btn-view');
                        } else if (buttonText.includes('edit') || buttonText.includes('update')) {
                            button.classList.add('btn-edit');
                        } else if (buttonText.includes('delete') || buttonText.includes('remove')) {
                            button.classList.add('btn-delete');
                        }
                    });
                });
            });
        });
    });
}
