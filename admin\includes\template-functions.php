<?php
/**
 * Template Functions
 * Functions for the templates module
 * Consolidated from multiple PHP files
 */

/**
 * Get all templates
 *
 * @return array Array of templates
 */
function get_templates() {
    global $conn;

    $result = $conn->query("SELECT * FROM templates ORDER BY name");

    $templates = [];

    while ($row = $result->fetch_assoc()) {
        $templates[] = $row;
    }

    return $templates;
}

/**
 * Get templates by type
 *
 * @param string $type Template type
 * @return array Array of templates
 */
function get_templates_by_type($type) {
    global $conn;

    $stmt = $conn->prepare("SELECT * FROM templates WHERE type = ? ORDER BY name");
    $stmt->bind_param("s", $type);
    $stmt->execute();
    $result = $stmt->get_result();

    $templates = [];

    while ($row = $result->fetch_assoc()) {
        $templates[] = $row;
    }

    return $templates;
}

/**
 * Get template by ID
 *
 * @param int $id Template ID
 * @return array|null Template data or null if not found
 */
function get_template($id) {
    global $conn;

    $stmt = $conn->prepare("SELECT * FROM templates WHERE id = ?");
    $stmt->bind_param("i", $id);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result->num_rows === 0) {
        return null;
    }

    return $result->fetch_assoc();
}

/**
 * Get template by slug
 *
 * @param string $slug Template slug
 * @return array|null Template data or null if not found
 */
function get_template_by_slug($slug) {
    global $conn;

    $stmt = $conn->prepare("SELECT * FROM templates WHERE slug = ?");
    $stmt->bind_param("s", $slug);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result->num_rows === 0) {
        return null;
    }

    return $result->fetch_assoc();
}

/**
 * Add template
 *
 * @param array $data Template data
 * @return int|bool New template ID on success, false on failure
 */
function add_template($data) {
    global $conn;

    // Generate slug if not provided
    if (empty($data['slug'])) {
        $data['slug'] = generate_slug($data['name']);
    }

    // Check if slug already exists
    $stmt = $conn->prepare("SELECT id FROM templates WHERE slug = ?");
    $stmt->bind_param("s", $data['slug']);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result->num_rows > 0) {
        // Append random string to slug
        $data['slug'] = $data['slug'] . '-' . substr(md5(uniqid()), 0, 5);
    }

    // Insert template
    $stmt = $conn->prepare("INSERT INTO templates (name, slug, description, content, type, created_at, updated_at)
                           VALUES (?, ?, ?, ?, ?, NOW(), NOW())");

    $stmt->bind_param("sssss",
        $data['name'],
        $data['slug'],
        $data['description'],
        $data['content'],
        $data['type']
    );

    if ($stmt->execute()) {
        return $conn->insert_id;
    }

    return false;
}

/**
 * Update template
 *
 * @param int $id Template ID
 * @param array $data Template data
 * @return bool True on success, false on failure
 */
function update_template($id, $data) {
    global $conn;

    // Generate slug if not provided
    if (empty($data['slug'])) {
        $data['slug'] = generate_slug($data['name']);
    }

    // Check if slug already exists for other templates
    $stmt = $conn->prepare("SELECT id FROM templates WHERE slug = ? AND id != ?");
    $stmt->bind_param("si", $data['slug'], $id);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result->num_rows > 0) {
        // Append random string to slug
        $data['slug'] = $data['slug'] . '-' . substr(md5(uniqid()), 0, 5);
    }

    // Update template
    $stmt = $conn->prepare("UPDATE templates SET
                           name = ?,
                           slug = ?,
                           description = ?,
                           content = ?,
                           type = ?,
                           updated_at = NOW()
                           WHERE id = ?");

    $stmt->bind_param("sssssi",
        $data['name'],
        $data['slug'],
        $data['description'],
        $data['content'],
        $data['type'],
        $id
    );

    return $stmt->execute();
}

/**
 * Delete template
 *
 * @param int $id Template ID
 * @return bool True on success, false on failure
 */
function delete_template($id) {
    global $conn;

    $stmt = $conn->prepare("DELETE FROM templates WHERE id = ?");
    $stmt->bind_param("i", $id);

    return $stmt->execute();
}

/**
 * Export template
 *
 * @param int $id Template ID
 * @return string|bool JSON data on success, false on failure
 */
function export_template($id) {
    $template = get_template($id);

    if (!$template) {
        return false;
    }

    // Remove internal fields
    unset($template['id']);

    return json_encode($template);
}

/**
 * Import template
 *
 * @param string $json_data JSON data
 * @return int|bool New template ID on success, false on failure
 */
function import_template($json_data) {
    $data = json_decode($json_data, true);

    if (!$data) {
        return false;
    }

    // Validate required fields
    if (empty($data['name']) || empty($data['content']) || empty($data['type'])) {
        return false;
    }

    return add_template($data);
}

/**
 * Get email templates
 *
 * @return array Array of email templates
 */
function get_email_templates() {
    global $conn;

    $result = $conn->query("SELECT * FROM email_templates ORDER BY name");

    $templates = [];

    while ($row = $result->fetch_assoc()) {
        $templates[] = $row;
    }

    return $templates;
}

/**
 * Get email template by ID
 *
 * @param int $id Template ID
 * @return array|null Template data or null if not found
 */
function get_email_template($id) {
    global $conn;

    $stmt = $conn->prepare("SELECT * FROM email_templates WHERE id = ?");
    $stmt->bind_param("i", $id);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result->num_rows === 0) {
        return null;
    }

    return $result->fetch_assoc();
}

/**
 * Get email template by slug
 *
 * @param string $slug Template slug
 * @return array|null Template data or null if not found
 */
function get_email_template_by_slug($slug) {
    global $conn;

    $stmt = $conn->prepare("SELECT * FROM email_templates WHERE slug = ?");
    $stmt->bind_param("s", $slug);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result->num_rows === 0) {
        return null;
    }

    return $result->fetch_assoc();
}

/**
 * Update email template
 *
 * @param int $id Template ID
 * @param array $data Template data
 * @return bool True on success, false on failure
 */
function update_email_template($id, $data) {
    global $conn;

    $stmt = $conn->prepare("UPDATE email_templates SET subject = ?, content = ?, updated_at = NOW() WHERE id = ?");
    $stmt->bind_param("ssi", $data['subject'], $data['content'], $id);

    return $stmt->execute();
}

/**
 * Reset email template to default
 *
 * @param int $id Template ID
 * @return bool True on success, false on failure
 */
function reset_email_template($id) {
    global $conn;

    $stmt = $conn->prepare("UPDATE email_templates SET subject = default_subject, content = default_content, updated_at = NOW() WHERE id = ?");
    $stmt->bind_param("i", $id);

    return $stmt->execute();
}

/**
 * Get template variables
 *
 * @param string $type Template type
 * @return array Array of template variables
 */
function get_template_variables($type = '') {
    $variables = [
        'general' => [
            '{{site_name}}' => 'Name of the website',
            '{{site_url}}' => 'URL of the website',
            '{{admin_email}}' => 'Admin email address',
            '{{current_year}}' => 'Current year',
            '{{logo}}' => 'Website logo URL',
            '{{admin_logo}}' => 'Admin panel logo URL',
            '{{primary_color}}' => 'Primary theme color',
            '{{secondary_color}}' => 'Secondary theme color'
        ],
        'email' => [
            '{{user_name}}' => 'Name of the user',
            '{{user_email}}' => 'Email address of the user',
            '{{verification_link}}' => 'Email verification link',
            '{{reset_link}}' => 'Password reset link',
            '{{login_link}}' => 'Login page link',
            '{{contact_name}}' => 'Name of the contact form submitter',
            '{{contact_email}}' => 'Email of the contact form submitter',
            '{{contact_subject}}' => 'Subject of the contact form',
            '{{contact_message}}' => 'Message from the contact form'
        ],
        'news' => [
            '{{news_title}}' => 'Title of the news article',
            '{{news_excerpt}}' => 'Excerpt of the news article',
            '{{news_content}}' => 'Content of the news article',
            '{{news_image}}' => 'Image of the news article',
            '{{news_date}}' => 'Publication date of the news article',
            '{{news_author}}' => 'Author of the news article',
            '{{news_category}}' => 'Category of the news article',
            '{{news_url}}' => 'URL of the news article'
        ]
    ];

    if ($type && isset($variables[$type])) {
        return $variables[$type];
    }

    return $variables;
}

/**
 * Parse template with variables
 *
 * @param string $template Template content
 * @param array $variables Variables to replace
 * @return string Parsed template
 */
function parse_template($template, $variables) {
    foreach ($variables as $key => $value) {
        $template = str_replace($key, $value, $template);
    }

    return $template;
}

/**
 * Send email using template
 *
 * @param string $template_slug Template slug
 * @param string $to Recipient email
 * @param array $variables Variables to replace
 * @return bool True on success, false on failure
 */
function send_email_template($template_slug, $to, $variables = []) {
    global $conn;

    // Get template
    $template = get_email_template_by_slug($template_slug);

    if (!$template) {
        return false;
    }

    // Add general variables with proper email URLs
    $site_url = get_site_url();
    $logo_path = get_setting('logo', 'images/logo.png');
    $admin_logo_path = get_admin_logo_path();

    // Clean admin logo path for email delivery
    if (strpos($admin_logo_path, 'admin/') === 0) {
        $admin_logo_path = substr($admin_logo_path, 6); // Remove 'admin/' prefix
    }

    $variables = array_merge([
        '{{site_name}}' => get_setting('site_name', 'Your Website'),
        '{{site_url}}' => $site_url,
        '{{admin_email}}' => get_setting('admin_email', ''),
        '{{current_year}}' => date('Y'),
        '{{logo}}' => $site_url . '/' . ltrim($logo_path, '/'),
        '{{admin_logo}}' => $site_url . '/' . ltrim($admin_logo_path, '/'),
        '{{primary_color}}' => get_setting('primary_color', '#3c3c45'),
        '{{secondary_color}}' => get_setting('secondary_color', '#f1ca2f')
    ], $variables);

    // Parse template
    $subject = parse_template($template['subject'], $variables);
    $content = parse_template($template['content'], $variables);

    // Use EmailTemplateProcessor to wrap content with proper header image
    require_once __DIR__ . '/../lib/EmailTemplateProcessor.php';
    $processor = new EmailTemplateProcessor($conn);
    $processed_content = $processor->processTemplate($content, $variables, $template);

    // Send email with processed content
    return send_email($to, $subject, $processed_content);
}

/**
 * Send email
 *
 * @param string $to Recipient email
 * @param string $subject Email subject
 * @param string $content Email content
 * @param array $attachments Email attachments
 * @return bool True on success, false on failure
 */
function send_email($to, $subject, $content, $attachments = []) {
    // Include PHPMailer
    require_once __DIR__ . '/../lib/phpmailer/PHPMailer.php';
    require_once __DIR__ . '/../lib/phpmailer/SMTP.php';
    require_once __DIR__ . '/../lib/phpmailer/Exception.php';

    $mail = new PHPMailer\PHPMailer\PHPMailer(true);

    try {
        // Get email settings
        $smtp_enabled = get_setting('smtp_enabled', '0');

        if ($smtp_enabled === '1') {
            // Server settings
            $mail->isSMTP();
            $mail->Host = get_setting('smtp_host', '');
            $mail->SMTPAuth = true;
            $mail->Username = get_setting('smtp_username', '');
            $mail->Password = get_setting('smtp_password', '');
            $mail->SMTPSecure = get_setting('smtp_encryption', 'tls');
            $mail->Port = get_setting('smtp_port', '587');
        }

        // Recipients
        $mail->setFrom(get_setting('from_email', ''), get_setting('from_name', ''));
        $mail->addAddress($to);

        // Attachments
        foreach ($attachments as $attachment) {
            $mail->addAttachment($attachment);
        }

        // Content
        $mail->isHTML(true);
        $mail->Subject = $subject;
        $mail->Body = $content;
        $mail->AltBody = strip_tags($content);

        $mail->send();
        return true;
    } catch (Exception $e) {
        error_log('Error sending email: ' . $mail->ErrorInfo);
        return false;
    }
}
