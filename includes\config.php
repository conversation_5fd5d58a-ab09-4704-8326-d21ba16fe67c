<?php
// Database configuration
define('DB_HOST', 'localhost');
define('DB_USER', 'u171951215_news');
define('DB_PASS', '#jQdYdr6');
define('DB_NAME', 'u171951215_news');

// Dynamic path detection for flexible deployment
$protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
$host = $_SERVER['HTTP_HOST'];

// Get the current script path and determine the site root
$current_path = dirname($_SERVER['SCRIPT_NAME']);

// If we're in an admin folder, get the parent directory
if (basename($current_path) === 'admin') {
    $site_root_path = dirname($current_path);
} else {
    $site_root_path = $current_path;
}

// Clean up the path - if it's just '/' or empty, we're in document root
if ($site_root_path === '/' || $site_root_path === '.' || empty($site_root_path)) {
    $site_root_path = '';
}

// Application settings with dynamic URLs
define('APP_NAME', 'Manage Inc');
define('APP_URL', $protocol . '://' . $host . $site_root_path);
define('ADMIN_URL', APP_URL . '/admin');
define('UPLOADS_DIR', __DIR__ . '/../uploads');
define('UPLOADS_URL', APP_URL . '/uploads');

// Security settings
define('CSRF_PROTECTION', true);
define('SESSION_TIMEOUT', 3600); // 1 hour
?>