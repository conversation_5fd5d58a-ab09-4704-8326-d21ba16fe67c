/**
 * Emergency User Dropdown CSS
 * Critical styles for user dropdown functionality - no dependencies
 */

/* Essential Variables */
:root {
  --primary-color: #f1ca2f;
  --text-color: #333333;
  --text-dark: #212529;
  --text-light: #6c757d;
  --text-muted: #868e96;
  --white: #ffffff;
  --background-light: #f8f9fa;
  --border-color: #dee2e6;
  --border-light: #e9ecef;
  --spacing-1: 0.25rem;
  --spacing-2: 0.5rem;
  --spacing-3: 1rem;
  --spacing-4: 1.5rem;
  --radius-md: 0.375rem;
  --radius-lg: 0.5rem;
  --radius-full: 50rem;
  --font-size-xs: 0.75rem;
  --font-size-sm: 0.875rem;
  --font-size-base: 1rem;
  --font-size-xl: 1.25rem;
  --font-weight-medium: 500;
  --font-weight-bold: 700;
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  --transition-fast: 0.15s;
}

/* User Menu Container */
.topbar-user-menu {
  position: relative;
  height: 100%;
  display: flex;
  align-items: center;
  margin-left: 8px;
}

/* User Menu Toggle Button */
.user-menu-toggle {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  padding: var(--spacing-1) var(--spacing-2);
  background-color: transparent;
  border: none;
  border-radius: var(--radius-md);
  color: var(--text-color);
  font-size: var(--font-size-sm);
  cursor: pointer;
  transition: all var(--transition-fast) ease;
  height: 40px;
}

.user-menu-toggle:hover {
  background-color: rgba(0, 0, 0, 0.05);
}

.user-menu-toggle:focus {
  outline: none;
  box-shadow: 0 0 0 2px rgba(241, 202, 47, 0.25);
}

/* User Avatar */
.user-avatar {
  position: relative;
  width: 36px;
  height: 36px;
  border-radius: var(--radius-full);
  overflow: hidden;
  flex-shrink: 0;
  background-color: var(--background-light);
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2px solid var(--white);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.avatar-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.avatar-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--primary-color);
  color: var(--white);
  font-weight: var(--font-weight-bold);
  font-size: var(--font-size-base);
  text-transform: uppercase;
}

.user-menu-toggle .dropdown-icon {
  font-size: var(--font-size-xs);
  transition: transform var(--transition-fast) ease;
  margin-left: auto;
  color: var(--text-light);
}

.user-menu-toggle.active .dropdown-icon {
  transform: rotate(180deg);
  color: var(--primary-color);
}

/* User Name in Toggle */
.user-name {
  font-weight: var(--font-weight-medium);
  display: none;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 120px;
  margin-right: var(--spacing-2);
}

@media (min-width: 768px) {
  .user-name {
    display: inline-block;
  }
}

/* User Dropdown */
.user-dropdown {
  position: absolute;
  top: calc(100% + 5px);
  right: 10px;
  z-index: 9999 !important;
  width: 320px;
  background-color: var(--white);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-xl);
  border: 1px solid var(--border-color);
  display: none;
  transform: translateY(10px);
  opacity: 0;
  transition: transform var(--transition-fast) ease, opacity var(--transition-fast) ease;
  overflow: visible;
  margin-top: 5px;
}

.user-dropdown.show {
  display: block !important;
  transform: translateY(0) !important;
  opacity: 1;
  z-index: 9999 !important;
}

/* User Dropdown Header */
.user-dropdown-header {
  padding: var(--spacing-4);
  display: flex !important;
  align-items: center;
  gap: var(--spacing-3);
  border-bottom: 1px solid var(--border-light);
  background-color: var(--background-light);
  margin-bottom: 0;
  visibility: visible !important;
  opacity: 1 !important;
  position: relative;
  z-index: 1;
}

.user-dropdown-avatar {
  position: relative;
  width: 60px;
  height: 60px;
  border-radius: var(--radius-full);
  overflow: hidden;
  flex-shrink: 0;
  border: 3px solid var(--white);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  display: flex !important;
  visibility: visible !important;
  opacity: 1 !important;
}

.avatar-placeholder-large {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--primary-color);
  color: var(--white);
  font-weight: var(--font-weight-bold);
  font-size: var(--font-size-xl);
  text-transform: uppercase;
}

.user-dropdown-info {
  flex: 1;
  min-width: 0;
  display: block !important;
  visibility: visible !important;
  opacity: 1 !important;
}

.user-dropdown-name {
  margin: 0 0 4px 0;
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-bold);
  color: var(--text-color);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  display: block !important;
  visibility: visible !important;
  opacity: 1 !important;
}

.user-dropdown-email {
  margin: 0 0 8px 0;
  font-size: var(--font-size-xs);
  color: var(--text-light);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  display: block !important;
  visibility: visible !important;
  opacity: 1 !important;
}

.user-dropdown-badge {
  display: inline-block !important;
  padding: 2px 8px;
  background-color: var(--primary-color);
  color: var(--text-dark);
  font-size: 10px;
  font-weight: var(--font-weight-medium);
  border-radius: 12px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  visibility: visible !important;
  opacity: 1 !important;
}

/* Dropdown Menu */
.dropdown-menu {
  padding: 0;
  margin: 0;
  display: block !important;
  visibility: visible !important;
  opacity: 1 !important;
  position: relative;
  z-index: 1;
}

/* Dropdown Section */
.dropdown-section {
  margin-bottom: var(--spacing-3);
  border-bottom: 1px solid var(--border-color);
  padding-bottom: var(--spacing-3);
}

.dropdown-section:last-child {
  border-bottom: none;
  margin-bottom: 0;
  padding-bottom: 0;
}

/* Dropdown Item */
.dropdown-item {
  margin: 0;
  padding: 0;
  display: block !important;
  visibility: visible !important;
  opacity: 1 !important;
  position: relative;
}

.dropdown-link {
  padding: var(--spacing-3) var(--spacing-4);
  display: flex !important;
  align-items: center;
  gap: var(--spacing-3);
  color: var(--text-dark) !important;
  text-decoration: none;
  transition: all var(--transition-fast) ease;
  font-size: var(--font-size-sm);
  border-left: 3px solid transparent;
  visibility: visible !important;
}

.dropdown-link:hover {
  background-color: var(--background-light);
  color: var(--primary-color) !important;
  border-left-color: var(--primary-color);
}

.dropdown-icon {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-light);
  font-size: var(--font-size-base);
  background-color: var(--background-light);
  border-radius: var(--radius-full);
  flex-shrink: 0;
}

.dropdown-link:hover .dropdown-icon {
  color: var(--primary-color);
  background-color: rgba(241, 202, 47, 0.1);
}

.dropdown-link-content {
  flex: 1;
  min-width: 0;
}

.dropdown-link-title {
  display: block;
  font-weight: var(--font-weight-medium);
  margin-bottom: 2px;
  color: inherit;
}

.dropdown-link-description {
  display: block;
  font-size: var(--font-size-xs);
  color: var(--text-muted) !important;
}

/* Logout Link */
.dropdown-link.logout-link {
  color: #dc3545 !important;
  border-left-color: transparent;
}

.dropdown-link.logout-link:hover {
  background-color: rgba(220, 53, 69, 0.1);
  color: #dc3545 !important;
  border-left-color: #dc3545;
}

/* Button Dropdown Link */
button.dropdown-link {
  width: 100%;
  text-align: left;
  background: none;
  border: none;
  cursor: pointer;
  font-family: inherit;
}
