<?php
/**
 * Notifications class for handling admin notifications
 */
class Notifications {
    private $conn;

    /**
     * Constructor
     *
     * @param object $conn Database connection
     */
    public function __construct($conn) {
        $this->conn = $conn;

        // Check if the notifications table exists and create it if it doesn't
        $this->createNotificationsTable();
    }

    /**
     * Create notifications table if it doesn't exist
     */
    private function createNotificationsTable() {
        try {
            $table_check = $this->conn->query("SHOW TABLES LIKE 'notifications'");

            if (!$table_check || $table_check->num_rows === 0) {
                error_log("Creating notifications table");

                $sql = "CREATE TABLE IF NOT EXISTS `notifications` (
                    `id` int(11) NOT NULL AUTO_INCREMENT,
                    `user_id` int(11) NOT NULL,
                    `title` varchar(255) NOT NULL,
                    `message` text NOT NULL,
                    `type` varchar(50) DEFAULT 'info',
                    `link` varchar(255) DEFAULT NULL,
                    `is_read` tinyint(1) DEFAULT 0,
                    `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
                    PRIMARY KEY (`id`),
                    <PERSON>EY `user_id` (`user_id`),
                    KEY `is_read` (`is_read`)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;";

                $result = $this->conn->query($sql);

                if (!$result) {
                    error_log("Failed to create notifications table: " . $this->conn->error);
                } else {
                    error_log("Notifications table created successfully");
                }
            }
        } catch (Exception $e) {
            error_log("Error creating notifications table: " . $e->getMessage());
        }
    }

    /**
     * Add a notification
     *
     * @param int $user_id User ID to send notification to (0 for all admins)
     * @param string $title Notification title
     * @param string $message Notification message
     * @param string $type Notification type (info, success, warning, error)
     * @param string $link Optional link to include with notification
     * @return bool True if successful, false otherwise
     */
    public function addNotification($user_id, $title, $message, $type = 'info', $link = null) {
        // Validate parameters
        if (empty($title) || empty($message)) {
            error_log("Notification error: Title and message are required");
            return false;
        }

        // Sanitize inputs
        $title = $this->conn->real_escape_string($title);
        $message = $this->conn->real_escape_string($message);
        $type = $this->conn->real_escape_string($type);

        // Fix link path if it's a relative path
        if ($link) {
            // Remove any leading 'admin/' from the link if it's already in the admin directory
            if (strpos($link, 'admin/') === 0) {
                $link = substr($link, 6); // Remove 'admin/'
            }

            $link = $this->conn->real_escape_string($link);
        }

        // If user_id is 0, send to all admins
        if ($user_id === 0) {
            return $this->addNotificationToAllAdmins($title, $message, $type, $link);
        }

        // Insert notification
        $sql = "INSERT INTO `notifications`
                (`user_id`, `title`, `message`, `type`, `link`)
                VALUES
                ($user_id, '$title', '$message', '$type', " . ($link ? "'$link'" : "NULL") . ")";

        $result = $this->conn->query($sql);

        if (!$result) {
            error_log("Failed to add notification: " . $this->conn->error);
            return false;
        }

        return true;
    }

    /**
     * Add notification to all admin users
     *
     * @param string $title Notification title
     * @param string $message Notification message
     * @param string $type Notification type
     * @param string $link Optional link
     * @return bool True if successful, false otherwise
     */
    private function addNotificationToAllAdmins($title, $message, $type, $link) {
        // Get all admin users with notifications enabled
        $sql = "SELECT u.id
                FROM `users` u
                JOIN `user_settings` s ON u.id = s.user_id
                WHERE u.is_admin = 1
                AND s.notifications_enabled = 1";

        $result = $this->conn->query($sql);

        if (!$result) {
            error_log("Failed to get admin users: " . $this->conn->error);
            return false;
        }

        $success = true;

        // Add notification for each admin
        while ($row = $result->fetch_assoc()) {
            $user_id = $row['id'];

            $insert_sql = "INSERT INTO `notifications`
                          (`user_id`, `title`, `message`, `type`, `link`)
                          VALUES
                          ($user_id, '$title', '$message', '$type', " . ($link ? "'$link'" : "NULL") . ")";

            if (!$this->conn->query($insert_sql)) {
                error_log("Failed to add notification for user $user_id: " . $this->conn->error);
                $success = false;
            }
        }

        return $success;
    }

    /**
     * Get notifications for a user
     *
     * @param int $user_id User ID
     * @param int $limit Maximum number of notifications to return
     * @param bool $unread_only Whether to return only unread notifications
     * @return array Array of notifications
     */
    public function getNotifications($user_id, $limit = 10, $unread_only = false) {
        // Log the function call for debugging
        error_log("getNotifications called for user_id: $user_id, limit: $limit, unread_only: " . ($unread_only ? 'true' : 'false'));

        // Check if notifications table exists
        $table_check = $this->conn->query("SHOW TABLES LIKE 'notifications'");
        if (!$table_check || $table_check->num_rows === 0) {
            error_log("Notifications table does not exist, creating it");
            $this->createNotificationsTable();
        }

        $sql = "SELECT * FROM `notifications`
                WHERE `user_id` = $user_id";

        if ($unread_only) {
            $sql .= " AND `is_read` = 0";
        }

        $sql .= " ORDER BY `created_at` DESC LIMIT $limit";

        // Log the SQL query for debugging
        error_log("Notifications SQL query: $sql");

        $result = $this->conn->query($sql);

        if (!$result) {
            error_log("Failed to get notifications: " . $this->conn->error);
            return [];
        }

        $notifications = [];
        while ($row = $result->fetch_assoc()) {
            $notifications[] = $row;
        }

        // Log the number of notifications found
        error_log("Found " . count($notifications) . " notifications for user $user_id");

        return $notifications;
    }

    /**
     * Get unread notification count for a user
     *
     * @param int $user_id User ID
     * @return int Number of unread notifications
     */
    public function getUnreadCount($user_id) {
        // Log the function call for debugging
        error_log("getUnreadCount called for user_id: $user_id");

        // Check if notifications table exists
        $table_check = $this->conn->query("SHOW TABLES LIKE 'notifications'");
        if (!$table_check || $table_check->num_rows === 0) {
            error_log("Notifications table does not exist, creating it");
            $this->createNotificationsTable();
        }

        $sql = "SELECT COUNT(*) as count FROM `notifications`
                WHERE `user_id` = $user_id AND `is_read` = 0";

        // Log the SQL query for debugging
        error_log("Unread count SQL query: $sql");

        $result = $this->conn->query($sql);

        if (!$result) {
            error_log("Failed to get unread count: " . $this->conn->error);
            return 0;
        }

        $row = $result->fetch_assoc();
        $count = (int)$row['count'];

        // Log the unread count for debugging
        error_log("Unread count for user $user_id: $count");

        return $count;
    }

    /**
     * Mark notification as read
     *
     * @param int $notification_id Notification ID
     * @param int $user_id User ID (for security)
     * @return bool True if successful, false otherwise
     */
    public function markAsRead($notification_id, $user_id) {
        $sql = "UPDATE `notifications`
                SET `is_read` = 1
                WHERE `id` = $notification_id AND `user_id` = $user_id";

        $result = $this->conn->query($sql);

        if (!$result) {
            error_log("Failed to mark notification as read: " . $this->conn->error);
            return false;
        }

        return true;
    }

    /**
     * Mark all notifications as read for a user
     *
     * @param int $user_id User ID
     * @return bool True if successful, false otherwise
     */
    public function markAllAsRead($user_id) {
        $sql = "UPDATE `notifications`
                SET `is_read` = 1
                WHERE `user_id` = $user_id";

        $result = $this->conn->query($sql);

        if (!$result) {
            error_log("Failed to mark all notifications as read: " . $this->conn->error);
            return false;
        }

        return true;
    }

    /**
     * Delete a notification
     *
     * @param int $notification_id Notification ID
     * @param int $user_id User ID (for security)
     * @return bool True if successful, false otherwise
     */
    public function delete($notification_id, $user_id) {
        $sql = "DELETE FROM `notifications`
                WHERE `id` = $notification_id AND `user_id` = $user_id";

        $result = $this->conn->query($sql);

        if (!$result) {
            error_log("Failed to delete notification: " . $this->conn->error);
            return false;
        }

        return true;
    }
}
