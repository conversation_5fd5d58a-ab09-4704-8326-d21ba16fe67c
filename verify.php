<?php
/**
 * Account Verification Page
 * 
 * This page handles account verification using tokens.
 */

// Start session
session_start();

// Include database configuration
require_once 'admin/config.php';

// Initialize variables
$message = '';
$status = '';
$redirect_url = 'admin/index.php';

// Check if token is provided
if (isset($_GET['token']) && !empty($_GET['token'])) {
    $token = $_GET['token'];
    
    // Sanitize token
    $token = $conn->real_escape_string($token);
    
    // Check if verification_tokens table exists
    $check_table = $conn->query("SHOW TABLES LIKE 'verification_tokens'");
    
    if ($check_table->num_rows > 0) {
        // Check if token exists and is valid
        $token_sql = "SELECT vt.*, u.username, u.email 
                     FROM verification_tokens vt 
                     JOIN users u ON vt.user_id = u.id 
                     WHERE vt.token = '$token' AND vt.expiry > NOW()";
        $token_result = $conn->query($token_sql);
        
        if ($token_result && $token_result->num_rows > 0) {
            $token_data = $token_result->fetch_assoc();
            $user_id = $token_data['user_id'];
            
            // Update user as verified
            $update_sql = "UPDATE users SET is_verified = 1, verification_token = NULL, token_expiry = NULL WHERE id = $user_id";
            
            if ($conn->query($update_sql)) {
                // Delete the used token
                $delete_sql = "DELETE FROM verification_tokens WHERE token = '$token'";
                $conn->query($delete_sql);
                
                $message = "Your account has been successfully verified. You can now log in.";
                $status = "success";
                
                // Set session variable for redirect message
                $_SESSION['verification_success'] = true;
            } else {
                $message = "Failed to verify your account. Please try again or contact support.";
                $status = "error";
            }
        } else {
            // Check if token exists but is expired
            $expired_sql = "SELECT vt.*, u.username, u.email 
                           FROM verification_tokens vt 
                           JOIN users u ON vt.user_id = u.id 
                           WHERE vt.token = '$token' AND vt.expiry <= NOW()";
            $expired_result = $conn->query($expired_sql);
            
            if ($expired_result && $expired_result->num_rows > 0) {
                $expired_data = $expired_result->fetch_assoc();
                
                // Store email and user_id in session for resend functionality
                $_SESSION['expired_token_email'] = $expired_data['email'];
                $_SESSION['expired_token_user_id'] = $expired_data['user_id'];
                
                $message = "This verification link has expired. Please request a new verification email.";
                $status = "warning";
            } else {
                $message = "Invalid verification token. Please check your email or request a new verification link.";
                $status = "error";
            }
        }
    } else {
        // Check if token is in users table (old method)
        $user_sql = "SELECT id, username, email FROM users WHERE verification_token = '$token' AND token_expiry > NOW()";
        $user_result = $conn->query($user_sql);
        
        if ($user_result && $user_result->num_rows > 0) {
            $user_data = $user_result->fetch_assoc();
            $user_id = $user_data['id'];
            
            // Update user as verified
            $update_sql = "UPDATE users SET is_verified = 1, verification_token = NULL, token_expiry = NULL WHERE id = $user_id";
            
            if ($conn->query($update_sql)) {
                $message = "Your account has been successfully verified. You can now log in.";
                $status = "success";
                
                // Set session variable for redirect message
                $_SESSION['verification_success'] = true;
            } else {
                $message = "Failed to verify your account. Please try again or contact support.";
                $status = "error";
            }
        } else {
            // Check if token exists but is expired
            $expired_sql = "SELECT id, username, email FROM users WHERE verification_token = '$token' AND token_expiry <= NOW()";
            $expired_result = $conn->query($expired_sql);
            
            if ($expired_result && $expired_result->num_rows > 0) {
                $expired_data = $expired_result->fetch_assoc();
                
                // Store email and user_id in session for resend functionality
                $_SESSION['expired_token_email'] = $expired_data['email'];
                $_SESSION['expired_token_user_id'] = $expired_data['id'];
                
                $message = "This verification link has expired. Please request a new verification email.";
                $status = "warning";
            } else {
                $message = "Invalid verification token. Please check your email or request a new verification link.";
                $status = "error";
            }
        }
    }
} else {
    $message = "No verification token provided.";
    $status = "error";
}

// HTML output
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Account Verification - Manage Inc.</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="admin/css/admin-style.css">
    <link href="https://fonts.googleapis.com/css2?family=Open+Sans:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <style>
        body {
            background-color: #f5f5f5;
            font-family: 'Open Sans', sans-serif;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            margin: 0;
            padding: 20px;
        }
        .verification-container {
            max-width: 600px;
            width: 100%;
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            padding: 30px;
            text-align: center;
        }
        .logo {
            margin-bottom: 20px;
        }
        .logo img {
            max-width: 200px;
        }
        h1 {
            color: #333;
            margin-bottom: 20px;
        }
        .message {
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 20px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .warning {
            background-color: #fff3cd;
            color: #856404;
            border: 1px solid #ffeeba;
        }
        .btn {
            display: inline-block;
            background-color: #f1ca2f;
            color: #333;
            padding: 10px 20px;
            border-radius: 4px;
            text-decoration: none;
            font-weight: bold;
            margin-top: 20px;
            transition: background-color 0.3s;
        }
        .btn:hover {
            background-color: #e0bb20;
        }
        .redirect-message {
            margin-top: 20px;
            font-size: 14px;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="verification-container">
        <div class="logo">
            <img src="images/logo.png" alt="Manage Inc.">
        </div>
        <h1>Account Verification</h1>
        
        <div class="message <?php echo $status; ?>">
            <?php echo $message; ?>
        </div>
        
        <?php if ($status === 'success'): ?>
            <p>You will be redirected to the login page in <span id="countdown">5</span> seconds.</p>
            <a href="<?php echo $redirect_url; ?>" class="btn">Login Now</a>
            
            <script>
                // Countdown and redirect
                let seconds = 5;
                const countdownElement = document.getElementById('countdown');
                
                const interval = setInterval(function() {
                    seconds--;
                    countdownElement.textContent = seconds;
                    
                    if (seconds <= 0) {
                        clearInterval(interval);
                        window.location.href = '<?php echo $redirect_url; ?>';
                    }
                }, 1000);
            </script>
        <?php elseif ($status === 'warning' && isset($_SESSION['expired_token_user_id'])): ?>
            <p>You can request a new verification email by clicking the button below.</p>
            <a href="admin/resend-verification.php?user_id=<?php echo $_SESSION['expired_token_user_id']; ?>" class="btn">Resend Verification Email</a>
        <?php else: ?>
            <a href="admin/index.php" class="btn">Go to Login</a>
        <?php endif; ?>
    </div>
</body>
</html>
