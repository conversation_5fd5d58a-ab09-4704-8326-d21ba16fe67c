<?php
// Start output buffering to prevent any output before JSO<PERSON>
ob_start();

// Start session
session_start();

// Include required files
require_once '../config.php';
require_once '../lib/FileVersions.php';
require_once '../lib/Permissions.php';

// Set proper JSON headers
header('Content-Type: application/json');
header('Cache-Control: no-store, no-cache, must-revalidate, max-age=0');
header('Cache-Control: post-check=0, pre-check=0', false);
header('Pragma: no-cache');

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    echo json_encode([
        'success' => false,
        'message' => 'User not logged in'
    ]);
    exit;
}

// Initialize classes
$user_id = $_SESSION['user_id'];
$file_versions = new FileVersions($conn, $user_id);
$permissions = new Permissions($conn, $user_id);

// Get the action
$action = isset($_POST['action']) ? $_POST['action'] : '';
$file_path = isset($_POST['file_path']) ? $_POST['file_path'] : '';

// Validate file path
if (empty($file_path)) {
    echo json_encode([
        'success' => false,
        'message' => 'File path is required'
    ]);
    exit;
}

// Define allowed directories
$allowed_dirs = [
    '../',
    '../images/',
    '../css/',
    '../js/'
];

// Validate file path is within allowed directories
$valid_path = false;
foreach ($allowed_dirs as $dir) {
    if (strpos($file_path, $dir) === 0) {
        $valid_path = true;
        break;
    }
}

if (!$valid_path) {
    echo json_encode([
        'success' => false,
        'message' => 'Invalid file path'
    ]);
    exit;
}

// Check file permissions
if (!$permissions->canReadFile($file_path)) {
    echo json_encode([
        'success' => false,
        'message' => 'You do not have permission to access this file'
    ]);
    exit;
}

// Process the action
switch ($action) {
    case 'get_all_versions':
        // Check if user has permission to view file versions
        if (!$permissions->hasPermission('manage_file_versions')) {
            echo json_encode([
                'success' => false,
                'message' => 'You do not have permission to view file versions'
            ]);
            exit;
        }

        $versions = $file_versions->getAllVersions($file_path);
        echo json_encode([
            'success' => true,
            'versions' => $versions
        ]);
        break;

    case 'get_version':
        // Check if user has permission to view file versions
        if (!$permissions->hasPermission('manage_file_versions')) {
            echo json_encode([
                'success' => false,
                'message' => 'You do not have permission to view file versions'
            ]);
            exit;
        }

        $version = isset($_POST['version']) ? (int)$_POST['version'] : 0;
        $version_data = $file_versions->getVersion($file_path, $version);

        if ($version_data) {
            echo json_encode([
                'success' => true,
                'version' => $version_data
            ]);
        } else {
            echo json_encode([
                'success' => false,
                'message' => 'Version not found'
            ]);
        }
        break;

    case 'restore_version':
        // Check if user has permission to manage file versions
        if (!$permissions->hasPermission('manage_file_versions')) {
            echo json_encode([
                'success' => false,
                'message' => 'You do not have permission to restore file versions'
            ]);
            exit;
        }

        // Check if user has write permission for the file
        if (!$permissions->canWriteFile($file_path)) {
            echo json_encode([
                'success' => false,
                'message' => 'You do not have permission to write to this file'
            ]);
            exit;
        }

        $version = isset($_POST['version']) ? (int)$_POST['version'] : 0;

        if ($version <= 0) {
            echo json_encode([
                'success' => false,
                'message' => 'Invalid version number'
            ]);
            exit;
        }

        $result = $file_versions->restoreVersion($file_path, $version);

        if ($result) {
            echo json_encode([
                'success' => true,
                'message' => 'Version restored successfully'
            ]);
        } else {
            echo json_encode([
                'success' => false,
                'message' => 'Failed to restore version'
            ]);
        }
        break;

    case 'compare_versions':
        // Check if user has permission to view file versions
        if (!$permissions->hasPermission('manage_file_versions')) {
            echo json_encode([
                'success' => false,
                'message' => 'You do not have permission to compare file versions'
            ]);
            exit;
        }

        $version1 = isset($_POST['version1']) ? (int)$_POST['version1'] : 0;
        $version2 = isset($_POST['version2']) ? (int)$_POST['version2'] : 0;

        if ($version1 <= 0) {
            echo json_encode([
                'success' => false,
                'message' => 'Invalid version number for version1'
            ]);
            exit;
        }

        $result = $file_versions->compareVersions($file_path, $version1, $version2);
        echo json_encode($result);
        break;

    default:
        echo json_encode([
            'success' => false,
            'message' => 'Invalid action'
        ]);
        break;
}

// Check for JSON encoding errors
if (json_last_error() !== JSON_ERROR_NONE) {
    // Clear any previous output
    ob_clean();

    // Return a simplified error response
    echo json_encode([
        'success' => false,
        'message' => 'JSON encoding error: ' . json_last_error_msg()
    ], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
}

// End output buffering and flush
ob_end_flush();
exit;
?>
