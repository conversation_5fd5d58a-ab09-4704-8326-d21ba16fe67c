<?php
/**
 * HTML Editor AJAX Handler
 *
 * Handles AJAX requests for the HTML editor
 */

// Start session
session_start();

// Set content type to JSON and prevent any output before our JSON response
header('Content-Type: application/json');

// Buffer all output to ensure we only send JSO<PERSON>
ob_start();

// Include required files
require_once '../config.php';
require_once '../lib/Permissions.php';
require_once '../lib/FileVersions.php';
require_once '../lib/CollaborativeEditing.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    echo json_encode([
        'success' => false,
        'message' => 'You must be logged in to use this feature.'
    ]);
    exit;
}

// Initialize permission system
$permissions = new Permissions($conn, $_SESSION['user_id']);

// Check if user has permission to view files
if (!$permissions->hasPermission('view_files')) {
    echo json_encode([
        'success' => false,
        'message' => 'You do not have permission to access the HTML Editor.'
    ]);
    exit;
}

// Initialize file versions and collaborative editing
$file_versions = new FileVersions($conn, $_SESSION['user_id']);
$collaborative = new CollaborativeEditing($conn, $_SESSION['user_id']);

// Get action
$action = isset($_POST['action']) ? $_POST['action'] : '';

// Handle different actions
switch ($action) {
    case 'save_file':
        handleSaveFile();
        break;
    case 'acquire_lock':
        handleAcquireLock();
        break;
    case 'extend_lock':
        handleExtendLock();
        break;
    case 'release_lock':
        handleReleaseLock();
        break;
    case 'force_release_lock':
        handleForceReleaseLock();
        break;
    case 'check_lock':
        handleCheckLock();
        break;
    case 'get_version':
        handleGetVersion();
        break;
    case 'get_all_versions':
        handleGetAllVersions();
        break;
    case 'restore_version':
        handleRestoreVersion();
        break;
    case 'delete_version':
        handleDeleteVersion();
        break;
    case 'get_html_files':
        handleGetHtmlFiles();
        break;
    case 'get_site_root':
        handleGetSiteRoot();
        break;
    default:
        echo json_encode([
            'success' => false,
            'message' => 'Invalid action.'
        ]);
        break;
}

/**
 * Handle save file action
 */
function handleSaveFile() {
    global $conn, $permissions, $file_versions, $collaborative;

    // Get file path
    $file_path = isset($_POST['file_path']) ? $_POST['file_path'] : '';

    // Validate file path
    if (empty($file_path)) {
        echo json_encode([
            'success' => false,
            'message' => 'File path is required.'
        ]);
        return;
    }

    // Check if user has write permission for this file
    if (!$permissions->canWriteFile($file_path)) {
        echo json_encode([
            'success' => false,
            'message' => 'You do not have permission to edit this file.'
        ]);
        return;
    }

    // Get file content
    $file_content = isset($_POST['file_content']) ? $_POST['file_content'] : '';
    $comment = isset($_POST['version_comment']) ? $_POST['version_comment'] : '';

    // Check if file is locked by another user
    $lock = $collaborative->getLock($file_path);
    if ($lock && $lock['user_id'] != $_SESSION['user_id']) {
        echo json_encode([
            'success' => false,
            'message' => "This file is currently being edited by {$lock['username']}. Please try again later."
        ]);
        return;
    }

    // Write content to file
    if (file_put_contents($file_path, $file_content) !== false) {
        // Save version history
        $file_versions->saveVersion($file_path, $file_content, $comment);

        // Extend lock
        $collaborative->extendLock($file_path);

        echo json_encode([
            'success' => true,
            'message' => 'File saved successfully.'
        ]);
    } else {
        echo json_encode([
            'success' => false,
            'message' => 'Failed to save file. Check file permissions.'
        ]);
    }
}

/**
 * Handle acquire lock action
 */
function handleAcquireLock() {
    global $collaborative;

    // Get file path
    $file_path = isset($_POST['file_path']) ? $_POST['file_path'] : '';

    // Validate file path
    if (empty($file_path)) {
        echo json_encode([
            'success' => false,
            'message' => 'File path is required.'
        ]);
        return;
    }

    // Acquire lock
    $result = $collaborative->acquireLock($file_path);

    echo json_encode($result);
}

/**
 * Handle extend lock action
 */
function handleExtendLock() {
    global $collaborative;

    // Get file path
    $file_path = isset($_POST['file_path']) ? $_POST['file_path'] : '';

    // Validate file path
    if (empty($file_path)) {
        echo json_encode([
            'success' => false,
            'message' => 'File path is required.'
        ]);
        return;
    }

    // Extend lock
    $result = $collaborative->extendLock($file_path);

    echo json_encode($result);
}

/**
 * Handle release lock action
 */
function handleReleaseLock() {
    global $collaborative;

    // Get file path
    $file_path = isset($_POST['file_path']) ? $_POST['file_path'] : '';

    // Validate file path
    if (empty($file_path)) {
        echo json_encode([
            'success' => false,
            'message' => 'File path is required.'
        ]);
        return;
    }

    // Release lock
    $result = $collaborative->releaseLock($file_path);

    echo json_encode($result);
}

/**
 * Handle force release lock action
 */
function handleForceReleaseLock() {
    global $collaborative;

    // Get file path
    $file_path = isset($_POST['file_path']) ? $_POST['file_path'] : '';

    // Validate file path
    if (empty($file_path)) {
        echo json_encode([
            'success' => false,
            'message' => 'File path is required.'
        ]);
        return;
    }

    // Force release lock
    $result = $collaborative->forceReleaseLock($file_path);

    echo json_encode($result);
}

/**
 * Handle check lock action
 */
function handleCheckLock() {
    global $collaborative;

    // Get file path
    $file_path = isset($_POST['file_path']) ? $_POST['file_path'] : '';

    // Validate file path
    if (empty($file_path)) {
        echo json_encode([
            'success' => false,
            'message' => 'File path is required.'
        ]);
        return;
    }

    // Check lock
    $lock = $collaborative->getLock($file_path);

    if ($lock) {
        echo json_encode([
            'success' => true,
            'locked' => true,
            'locked_by' => $lock['username'],
            'locked_by_current_user' => $lock['user_id'] == $_SESSION['user_id'],
            'expires_at' => $lock['expires_at']
        ]);
    } else {
        echo json_encode([
            'success' => true,
            'locked' => false
        ]);
    }
}

/**
 * Handle get version action
 */
function handleGetVersion() {
    global $file_versions;

    // Get file path and version
    $file_path = isset($_POST['file_path']) ? $_POST['file_path'] : '';
    $version = isset($_POST['version']) ? intval($_POST['version']) : 0;

    // Validate file path
    if (empty($file_path)) {
        echo json_encode([
            'success' => false,
            'message' => 'File path is required.'
        ]);
        return;
    }

    // Get version
    $version_data = $file_versions->getVersion($file_path, $version);

    if ($version_data) {
        echo json_encode([
            'success' => true,
            'version' => $version_data
        ]);
    } else {
        echo json_encode([
            'success' => false,
            'message' => 'Version not found.'
        ]);
    }
}

/**
 * Handle get all versions action
 */
function handleGetAllVersions() {
    global $file_versions;

    // Get file path
    $file_path = isset($_POST['file_path']) ? $_POST['file_path'] : '';

    // Validate file path
    if (empty($file_path)) {
        echo json_encode([
            'success' => false,
            'message' => 'File path is required.'
        ]);
        return;
    }

    // Get all versions
    $versions = $file_versions->getAllVersions($file_path);

    echo json_encode([
        'success' => true,
        'versions' => $versions
    ]);
}

/**
 * Handle restore version action
 */
function handleRestoreVersion() {
    global $permissions, $file_versions, $collaborative;

    // Get file path and version
    $file_path = isset($_POST['file_path']) ? $_POST['file_path'] : '';
    $version = isset($_POST['version']) ? intval($_POST['version']) : 0;

    // Validate file path
    if (empty($file_path)) {
        echo json_encode([
            'success' => false,
            'message' => 'File path is required.'
        ]);
        return;
    }

    // Check if user has write permission for this file
    if (!$permissions->canWriteFile($file_path)) {
        echo json_encode([
            'success' => false,
            'message' => 'You do not have permission to edit this file.'
        ]);
        return;
    }

    // Check if file is locked by another user
    $lock = $collaborative->getLock($file_path);
    if ($lock && $lock['user_id'] != $_SESSION['user_id']) {
        echo json_encode([
            'success' => false,
            'message' => "This file is currently being edited by {$lock['username']}. Please try again later."
        ]);
        return;
    }

    // Get version
    $version_data = $file_versions->getVersion($file_path, $version);

    if (!$version_data) {
        echo json_encode([
            'success' => false,
            'message' => 'Version not found.'
        ]);
        return;
    }

    // Write content to file
    if (file_put_contents($file_path, $version_data['content']) !== false) {
        // Save new version
        $comment = "Restored from version {$version_data['version']}";
        $file_versions->saveVersion($file_path, $version_data['content'], $comment);

        echo json_encode([
            'success' => true,
            'message' => 'Version restored successfully.'
        ]);
    } else {
        echo json_encode([
            'success' => false,
            'message' => 'Failed to restore version. Check file permissions.'
        ]);
    }
}

/**
 * Handle delete version action
 */
function handleDeleteVersion() {
    global $permissions, $file_versions;

    // Get file path and version
    $file_path = isset($_POST['file_path']) ? $_POST['file_path'] : '';
    $version = isset($_POST['version']) ? intval($_POST['version']) : 0;

    // Validate file path
    if (empty($file_path)) {
        echo json_encode([
            'success' => false,
            'message' => 'File path is required.'
        ]);
        return;
    }

    // Validate version
    if ($version <= 0) {
        echo json_encode([
            'success' => false,
            'message' => 'Valid version number is required.'
        ]);
        return;
    }

    // Check if user has permission to manage file versions
    if (!$permissions->hasPermission('manage_file_versions')) {
        echo json_encode([
            'success' => false,
            'message' => 'You do not have permission to delete file versions.'
        ]);
        return;
    }

    // Check if version exists
    $version_data = $file_versions->getVersion($file_path, $version);
    if (!$version_data) {
        echo json_encode([
            'success' => false,
            'message' => 'Version not found.'
        ]);
        return;
    }

    // Get all versions to check if this is the only version
    $all_versions = $file_versions->getAllVersions($file_path);
    if (count($all_versions) <= 1) {
        echo json_encode([
            'success' => false,
            'message' => 'Cannot delete the only version of a file.'
        ]);
        return;
    }

    // Delete the version
    if ($file_versions->deleteVersion($file_path, $version)) {
        echo json_encode([
            'success' => true,
            'message' => 'Version deleted successfully.'
        ]);
    } else {
        echo json_encode([
            'success' => false,
            'message' => 'Failed to delete version.'
        ]);
    }
}

/**
 * Handle get HTML files action
 */
function handleGetHtmlFiles() {
    // Define allowed directories
    $allowed_dirs = [
        '../' => 'Root'
    ];

    // Get HTML files
    $files = [];
    foreach ($allowed_dirs as $dir => $name) {
        $files = array_merge($files, getHtmlFiles($dir));
    }

    // Sort files by name
    usort($files, function($a, $b) {
        return strcasecmp($a['name'], $b['name']);
    });

    echo json_encode([
        'success' => true,
        'files' => $files
    ]);
}

/**
 * Get HTML files from a directory
 *
 * @param string $dir Directory path
 * @return array Array of HTML files
 */
function getHtmlFiles($dir) {
    $files = [];

    if (is_dir($dir)) {
        $items = scandir($dir);

        foreach ($items as $item) {
            if ($item != '.' && $item != '..') {
                $path = $dir . $item;

                if (is_dir($path)) {
                    // Skip admin directory
                    if ($item !== 'admin') {
                        $files = array_merge($files, getHtmlFiles($path . '/'));
                    }
                } elseif (strtolower(pathinfo($path, PATHINFO_EXTENSION)) === 'html') {
                    // Skip header and footer files
                    if (strtolower(basename($path)) !== 'header.html' && strtolower(basename($path)) !== 'footer.html') {
                        // Check if file contains dynamic content
                        $is_dynamic = containsDynamicContent($path);

                        $files[] = [
                            'name' => $item,
                            'path' => $path,
                            'is_dynamic' => $is_dynamic
                        ];
                    }
                }
            }
        }
    }

    return $files;
}

/**
 * Check if a file contains dynamic content
 *
 * @param string $filepath File path
 * @return bool True if file contains dynamic content, false otherwise
 */
function containsDynamicContent($filepath) {
    if (!file_exists($filepath)) {
        return false;
    }

    $content = file_get_contents($filepath);

    // Check for PHP tags
    if (strpos($content, '<?php') !== false ||
        strpos($content, '<?=') !== false ||
        strpos($content, '<%') !== false) {
        return true;
    }

    // Check for server-side includes
    if (strpos($content, '<!--#include') !== false) {
        return true;
    }

    // Check for template syntax
    if (strpos($content, '{{') !== false ||
        strpos($content, '}}') !== false ||
        strpos($content, '{%') !== false ||
        strpos($content, '%}') !== false ||
        strpos($content, '@if') !== false ||
        strpos($content, '@foreach') !== false) {
        return true;
    }

    return false;
}

/**
 * Handle get site root action
 */
function handleGetSiteRoot() {
    // Get the site root URL
    $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https://' : 'http://';
    $host = $_SERVER['HTTP_HOST'];

    // Get the path to the document root
    $documentRoot = $_SERVER['DOCUMENT_ROOT'];

    // Determine the site root
    $siteRoot = $protocol . $host;

    // Check if the site is in a subdirectory
    $scriptPath = dirname(dirname($_SERVER['SCRIPT_NAME'])); // Go up two levels from ajax/html_editor.php
    if ($scriptPath !== '/' && $scriptPath !== '\\') {
        // Remove '/admin' from the path to get the site root
        $siteRoot .= str_replace('/admin', '', $scriptPath);
    }

    echo json_encode([
        'success' => true,
        'site_root' => $siteRoot
    ]);
}

// Flush the output buffer and send only the JSON response
$output = ob_get_clean();

// Check if there was any unexpected output before our JSON
if (strpos($output, '<!DOCTYPE') !== false || strpos($output, '<html') !== false) {
    // Extract just the JSON part
    $jsonStart = strpos($output, '{');
    if ($jsonStart !== false) {
        $output = substr($output, $jsonStart);
        // Find the end of the JSON object
        $jsonEnd = strrpos($output, '}');
        if ($jsonEnd !== false) {
            $output = substr($output, 0, $jsonEnd + 1);
        }
    }
}

// Send the clean JSON response
echo $output;
