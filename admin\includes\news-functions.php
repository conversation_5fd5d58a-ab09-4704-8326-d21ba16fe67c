<?php
/**
 * News Functions
 * Functions for the news management module
 * Consolidated from multiple PHP files
 */

/**
 * Get all news articles
 * 
 * @param int $limit Number of articles to return
 * @param int $offset Offset for pagination
 * @param string $order_by Field to order by
 * @param string $order_dir Order direction (ASC or DESC)
 * @return array Array of news articles
 */
function get_news($limit = 10, $offset = 0, $order_by = 'created_at', $order_dir = 'DESC') {
    global $conn;
    
    $valid_order_fields = ['id', 'title', 'created_at', 'updated_at', 'status', 'category_id'];
    $valid_order_dirs = ['ASC', 'DESC'];
    
    // Validate order_by and order_dir
    if (!in_array($order_by, $valid_order_fields)) {
        $order_by = 'created_at';
    }
    
    if (!in_array($order_dir, $valid_order_dirs)) {
        $order_dir = 'DESC';
    }
    
    $sql = "SELECT n.*, c.name as category_name 
            FROM news n 
            LEFT JOIN categories c ON n.category_id = c.id 
            ORDER BY n.$order_by $order_dir 
            LIMIT ? OFFSET ?";
    
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("ii", $limit, $offset);
    $stmt->execute();
    $result = $stmt->get_result();
    
    $news = [];
    
    while ($row = $result->fetch_assoc()) {
        $news[] = $row;
    }
    
    return $news;
}

/**
 * Get news article by ID
 * 
 * @param int $id News article ID
 * @return array|null News article data or null if not found
 */
function get_news_by_id($id) {
    global $conn;
    
    $stmt = $conn->prepare("SELECT n.*, c.name as category_name 
                           FROM news n 
                           LEFT JOIN categories c ON n.category_id = c.id 
                           WHERE n.id = ?");
    $stmt->bind_param("i", $id);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows === 0) {
        return null;
    }
    
    return $result->fetch_assoc();
}

/**
 * Get news article by slug
 * 
 * @param string $slug News article slug
 * @return array|null News article data or null if not found
 */
function get_news_by_slug($slug) {
    global $conn;
    
    $stmt = $conn->prepare("SELECT n.*, c.name as category_name 
                           FROM news n 
                           LEFT JOIN categories c ON n.category_id = c.id 
                           WHERE n.slug = ?");
    $stmt->bind_param("s", $slug);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows === 0) {
        return null;
    }
    
    return $result->fetch_assoc();
}

/**
 * Get news articles by category
 * 
 * @param int $category_id Category ID
 * @param int $limit Number of articles to return
 * @param int $offset Offset for pagination
 * @return array Array of news articles
 */
function get_news_by_category($category_id, $limit = 10, $offset = 0) {
    global $conn;
    
    $stmt = $conn->prepare("SELECT n.*, c.name as category_name 
                           FROM news n 
                           LEFT JOIN categories c ON n.category_id = c.id 
                           WHERE n.category_id = ? 
                           ORDER BY n.created_at DESC 
                           LIMIT ? OFFSET ?");
    $stmt->bind_param("iii", $category_id, $limit, $offset);
    $stmt->execute();
    $result = $stmt->get_result();
    
    $news = [];
    
    while ($row = $result->fetch_assoc()) {
        $news[] = $row;
    }
    
    return $news;
}

/**
 * Get news articles by status
 * 
 * @param string $status Status (published, draft, scheduled)
 * @param int $limit Number of articles to return
 * @param int $offset Offset for pagination
 * @return array Array of news articles
 */
function get_news_by_status($status, $limit = 10, $offset = 0) {
    global $conn;
    
    $stmt = $conn->prepare("SELECT n.*, c.name as category_name 
                           FROM news n 
                           LEFT JOIN categories c ON n.category_id = c.id 
                           WHERE n.status = ? 
                           ORDER BY n.created_at DESC 
                           LIMIT ? OFFSET ?");
    $stmt->bind_param("sii", $status, $limit, $offset);
    $stmt->execute();
    $result = $stmt->get_result();
    
    $news = [];
    
    while ($row = $result->fetch_assoc()) {
        $news[] = $row;
    }
    
    return $news;
}

/**
 * Search news articles
 * 
 * @param string $query Search query
 * @param int $limit Number of articles to return
 * @param int $offset Offset for pagination
 * @return array Array of news articles
 */
function search_news($query, $limit = 10, $offset = 0) {
    global $conn;
    
    $search_query = "%$query%";
    
    $stmt = $conn->prepare("SELECT n.*, c.name as category_name 
                           FROM news n 
                           LEFT JOIN categories c ON n.category_id = c.id 
                           WHERE n.title LIKE ? OR n.content LIKE ? OR n.excerpt LIKE ? 
                           ORDER BY n.created_at DESC 
                           LIMIT ? OFFSET ?");
    $stmt->bind_param("sssii", $search_query, $search_query, $search_query, $limit, $offset);
    $stmt->execute();
    $result = $stmt->get_result();
    
    $news = [];
    
    while ($row = $result->fetch_assoc()) {
        $news[] = $row;
    }
    
    return $news;
}

/**
 * Count total news articles
 * 
 * @return int Total number of news articles
 */
function count_news() {
    global $conn;
    
    $result = $conn->query("SELECT COUNT(*) as count FROM news");
    $row = $result->fetch_assoc();
    
    return $row['count'];
}

/**
 * Count news articles by category
 * 
 * @param int $category_id Category ID
 * @return int Number of news articles in category
 */
function count_news_by_category($category_id) {
    global $conn;
    
    $stmt = $conn->prepare("SELECT COUNT(*) as count FROM news WHERE category_id = ?");
    $stmt->bind_param("i", $category_id);
    $stmt->execute();
    $result = $stmt->get_result();
    $row = $result->fetch_assoc();
    
    return $row['count'];
}

/**
 * Count news articles by status
 * 
 * @param string $status Status (published, draft, scheduled)
 * @return int Number of news articles with status
 */
function count_news_by_status($status) {
    global $conn;
    
    $stmt = $conn->prepare("SELECT COUNT(*) as count FROM news WHERE status = ?");
    $stmt->bind_param("s", $status);
    $stmt->execute();
    $result = $stmt->get_result();
    $row = $result->fetch_assoc();
    
    return $row['count'];
}

/**
 * Add news article
 * 
 * @param array $data News article data
 * @return int|bool New article ID on success, false on failure
 */
function add_news($data) {
    global $conn;
    
    // Generate slug if not provided
    if (empty($data['slug'])) {
        $data['slug'] = generate_slug($data['title']);
    }
    
    // Check if slug already exists
    $stmt = $conn->prepare("SELECT id FROM news WHERE slug = ?");
    $stmt->bind_param("s", $data['slug']);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows > 0) {
        // Append random string to slug
        $data['slug'] = $data['slug'] . '-' . substr(md5(uniqid()), 0, 5);
    }
    
    // Set default values
    if (empty($data['status'])) {
        $data['status'] = 'draft';
    }
    
    if (empty($data['created_at'])) {
        $data['created_at'] = date('Y-m-d H:i:s');
    }
    
    $data['updated_at'] = date('Y-m-d H:i:s');
    
    // Insert news article
    $stmt = $conn->prepare("INSERT INTO news (title, slug, content, excerpt, image, category_id, status, created_at, updated_at, author_id, scheduled_at) 
                           VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");
    
    $stmt->bind_param("sssssssssss", 
        $data['title'], 
        $data['slug'], 
        $data['content'], 
        $data['excerpt'], 
        $data['image'], 
        $data['category_id'], 
        $data['status'], 
        $data['created_at'], 
        $data['updated_at'], 
        $data['author_id'], 
        $data['scheduled_at']
    );
    
    if ($stmt->execute()) {
        return $conn->insert_id;
    }
    
    return false;
}

/**
 * Update news article
 * 
 * @param int $id News article ID
 * @param array $data News article data
 * @return bool True on success, false on failure
 */
function update_news($id, $data) {
    global $conn;
    
    // Generate slug if not provided
    if (empty($data['slug'])) {
        $data['slug'] = generate_slug($data['title']);
    }
    
    // Check if slug already exists for other articles
    $stmt = $conn->prepare("SELECT id FROM news WHERE slug = ? AND id != ?");
    $stmt->bind_param("si", $data['slug'], $id);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows > 0) {
        // Append random string to slug
        $data['slug'] = $data['slug'] . '-' . substr(md5(uniqid()), 0, 5);
    }
    
    $data['updated_at'] = date('Y-m-d H:i:s');
    
    // Update news article
    $stmt = $conn->prepare("UPDATE news SET 
                           title = ?, 
                           slug = ?, 
                           content = ?, 
                           excerpt = ?, 
                           image = ?, 
                           category_id = ?, 
                           status = ?, 
                           updated_at = ?, 
                           scheduled_at = ? 
                           WHERE id = ?");
    
    $stmt->bind_param("sssssssssi", 
        $data['title'], 
        $data['slug'], 
        $data['content'], 
        $data['excerpt'], 
        $data['image'], 
        $data['category_id'], 
        $data['status'], 
        $data['updated_at'], 
        $data['scheduled_at'], 
        $id
    );
    
    return $stmt->execute();
}

/**
 * Delete news article
 * 
 * @param int $id News article ID
 * @return bool True on success, false on failure
 */
function delete_news($id) {
    global $conn;
    
    $stmt = $conn->prepare("DELETE FROM news WHERE id = ?");
    $stmt->bind_param("i", $id);
    
    return $stmt->execute();
}

/**
 * Get all categories
 * 
 * @return array Array of categories
 */
function get_categories() {
    global $conn;
    
    $result = $conn->query("SELECT * FROM categories ORDER BY name");
    
    $categories = [];
    
    while ($row = $result->fetch_assoc()) {
        $categories[] = $row;
    }
    
    return $categories;
}

/**
 * Get category by ID
 * 
 * @param int $id Category ID
 * @return array|null Category data or null if not found
 */
function get_category($id) {
    global $conn;
    
    $stmt = $conn->prepare("SELECT * FROM categories WHERE id = ?");
    $stmt->bind_param("i", $id);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows === 0) {
        return null;
    }
    
    return $result->fetch_assoc();
}

/**
 * Get category by slug
 * 
 * @param string $slug Category slug
 * @return array|null Category data or null if not found
 */
function get_category_by_slug($slug) {
    global $conn;
    
    $stmt = $conn->prepare("SELECT * FROM categories WHERE slug = ?");
    $stmt->bind_param("s", $slug);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows === 0) {
        return null;
    }
    
    return $result->fetch_assoc();
}

/**
 * Add category
 * 
 * @param array $data Category data
 * @return int|bool New category ID on success, false on failure
 */
function add_category($data) {
    global $conn;
    
    // Generate slug if not provided
    if (empty($data['slug'])) {
        $data['slug'] = generate_slug($data['name']);
    }
    
    // Check if slug already exists
    $stmt = $conn->prepare("SELECT id FROM categories WHERE slug = ?");
    $stmt->bind_param("s", $data['slug']);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows > 0) {
        // Append random string to slug
        $data['slug'] = $data['slug'] . '-' . substr(md5(uniqid()), 0, 5);
    }
    
    // Insert category
    $stmt = $conn->prepare("INSERT INTO categories (name, slug, description) VALUES (?, ?, ?)");
    $stmt->bind_param("sss", $data['name'], $data['slug'], $data['description']);
    
    if ($stmt->execute()) {
        return $conn->insert_id;
    }
    
    return false;
}

/**
 * Update category
 * 
 * @param int $id Category ID
 * @param array $data Category data
 * @return bool True on success, false on failure
 */
function update_category($id, $data) {
    global $conn;
    
    // Generate slug if not provided
    if (empty($data['slug'])) {
        $data['slug'] = generate_slug($data['name']);
    }
    
    // Check if slug already exists for other categories
    $stmt = $conn->prepare("SELECT id FROM categories WHERE slug = ? AND id != ?");
    $stmt->bind_param("si", $data['slug'], $id);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows > 0) {
        // Append random string to slug
        $data['slug'] = $data['slug'] . '-' . substr(md5(uniqid()), 0, 5);
    }
    
    // Update category
    $stmt = $conn->prepare("UPDATE categories SET name = ?, slug = ?, description = ? WHERE id = ?");
    $stmt->bind_param("sssi", $data['name'], $data['slug'], $data['description'], $id);
    
    return $stmt->execute();
}

/**
 * Delete category
 * 
 * @param int $id Category ID
 * @return bool True on success, false on failure
 */
function delete_category($id) {
    global $conn;
    
    // Check if category has news articles
    $stmt = $conn->prepare("SELECT COUNT(*) as count FROM news WHERE category_id = ?");
    $stmt->bind_param("i", $id);
    $stmt->execute();
    $result = $stmt->get_result();
    $row = $result->fetch_assoc();
    
    if ($row['count'] > 0) {
        return false; // Cannot delete category with news articles
    }
    
    // Delete category
    $stmt = $conn->prepare("DELETE FROM categories WHERE id = ?");
    $stmt->bind_param("i", $id);
    
    return $stmt->execute();
}

/**
 * Upload news image
 * 
 * @param array $file File data from $_FILES
 * @return string|bool Image path on success, false on failure
 */
function upload_news_image($file) {
    $upload_dir = __DIR__ . '/../../uploads/news/';
    
    // Create directory if it doesn't exist
    if (!file_exists($upload_dir)) {
        mkdir($upload_dir, 0755, true);
    }
    
    // Generate unique filename
    $filename = uniqid() . '_' . sanitize_filename($file['name']);
    $upload_path = $upload_dir . $filename;
    
    // Move uploaded file
    if (move_uploaded_file($file['tmp_name'], $upload_path)) {
        return 'uploads/news/' . $filename;
    }
    
    return false;
}

/**
 * Sanitize filename
 * 
 * @param string $filename Filename to sanitize
 * @return string Sanitized filename
 */
function sanitize_filename($filename) {
    // Remove any path information
    $filename = basename($filename);
    
    // Replace spaces with underscores
    $filename = str_replace(' ', '_', $filename);
    
    // Remove any non-alphanumeric characters except for dots, underscores, and hyphens
    $filename = preg_replace('/[^a-zA-Z0-9\.\-\_]/', '', $filename);
    
    return $filename;
}

/**
 * Get recent news articles
 * 
 * @param int $limit Number of articles to return
 * @return array Array of recent news articles
 */
function get_recent_news($limit = 5) {
    global $conn;
    
    $stmt = $conn->prepare("SELECT n.*, c.name as category_name 
                           FROM news n 
                           LEFT JOIN categories c ON n.category_id = c.id 
                           WHERE n.status = 'published' 
                           ORDER BY n.created_at DESC 
                           LIMIT ?");
    $stmt->bind_param("i", $limit);
    $stmt->execute();
    $result = $stmt->get_result();
    
    $news = [];
    
    while ($row = $result->fetch_assoc()) {
        $news[] = $row;
    }
    
    return $news;
}

/**
 * Get popular news articles
 * 
 * @param int $limit Number of articles to return
 * @return array Array of popular news articles
 */
function get_popular_news($limit = 5) {
    global $conn;
    
    $stmt = $conn->prepare("SELECT n.*, c.name as category_name 
                           FROM news n 
                           LEFT JOIN categories c ON n.category_id = c.id 
                           WHERE n.status = 'published' 
                           ORDER BY n.views DESC 
                           LIMIT ?");
    $stmt->bind_param("i", $limit);
    $stmt->execute();
    $result = $stmt->get_result();
    
    $news = [];
    
    while ($row = $result->fetch_assoc()) {
        $news[] = $row;
    }
    
    return $news;
}

/**
 * Increment news article views
 * 
 * @param int $id News article ID
 * @return bool True on success, false on failure
 */
function increment_news_views($id) {
    global $conn;
    
    $stmt = $conn->prepare("UPDATE news SET views = views + 1 WHERE id = ?");
    $stmt->bind_param("i", $id);
    
    return $stmt->execute();
}
