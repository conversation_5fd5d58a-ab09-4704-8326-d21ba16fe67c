/**
 * Image Upload
 * 
 * This script enhances the image upload experience with:
 * - Drag and drop functionality
 * - Image preview
 * - Upload progress indicator
 * - Better error messages
 */

document.addEventListener('DOMContentLoaded', function() {
    // Get elements
    const imageInput = document.getElementById('image');
    if (!imageInput) return;
    
    const imagePreview = document.querySelector('.image-preview');
    if (!imagePreview) return;
    
    // Create drag-and-drop zone
    const dropZone = document.createElement('div');
    dropZone.className = 'image-drop-zone';
    
    // Create progress bar
    const progressContainer = document.createElement('div');
    progressContainer.className = 'upload-progress';
    progressContainer.style.display = 'none';
    
    const progressBar = document.createElement('div');
    progressBar.className = 'upload-progress-bar';
    progressContainer.appendChild(progressBar);
    
    // Create error message container
    const errorContainer = document.createElement('div');
    errorContainer.className = 'upload-error';
    errorContainer.style.display = 'none';
    
    // Insert elements into the DOM
    imagePreview.parentNode.insertBefore(dropZone, imagePreview);
    dropZone.appendChild(imagePreview);
    imagePreview.parentNode.insertBefore(progressContainer, imagePreview.nextSibling);
    imagePreview.parentNode.insertBefore(errorContainer, progressContainer.nextSibling);
    
    // Add text to drop zone
    const dropText = document.createElement('div');
    dropText.className = 'drop-text';
    dropText.innerHTML = '<i class="fas fa-cloud-upload-alt"></i><br>Drag & drop image here<br>or<br>click to select';
    
    // Only add drop text if there's no image already
    if (!imagePreview.querySelector('img')) {
        imagePreview.appendChild(dropText);
    }
    
    // Hide the original file input
    imageInput.style.display = 'none';
    
    // Add click event to the drop zone
    dropZone.addEventListener('click', function() {
        imageInput.click();
    });
    
    // Add drag and drop events
    ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
        dropZone.addEventListener(eventName, preventDefaults, false);
    });
    
    function preventDefaults(e) {
        e.preventDefault();
        e.stopPropagation();
    }
    
    ['dragenter', 'dragover'].forEach(eventName => {
        dropZone.addEventListener(eventName, highlight, false);
    });
    
    ['dragleave', 'drop'].forEach(eventName => {
        dropZone.addEventListener(eventName, unhighlight, false);
    });
    
    function highlight() {
        dropZone.classList.add('drag-over');
    }
    
    function unhighlight() {
        dropZone.classList.remove('drag-over');
    }
    
    // Handle dropped files
    dropZone.addEventListener('drop', handleDrop, false);
    
    function handleDrop(e) {
        const dt = e.dataTransfer;
        const files = dt.files;
        
        if (files.length) {
            imageInput.files = files;
            handleFiles(files);
        }
    }
    
    // Handle selected files
    imageInput.addEventListener('change', function() {
        if (this.files.length) {
            handleFiles(this.files);
        }
    });
    
    function handleFiles(files) {
        const file = files[0];
        
        // Validate file type
        const validTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/jpg'];
        if (!validTypes.includes(file.type)) {
            showError('Invalid file type. Only JPG, JPEG, PNG & GIF files are allowed.');
            return;
        }
        
        // Validate file size
        const maxSize = 5 * 1024 * 1024; // 5MB in bytes
        if (file.size > maxSize) {
            showError('File size must be less than 5MB.');
            return;
        }
        
        // Clear any previous errors
        hideError();
        
        // Show progress bar
        progressContainer.style.display = 'block';
        
        // Simulate upload progress
        let progress = 0;
        const interval = setInterval(function() {
            progress += 10;
            progressBar.style.width = progress + '%';
            
            if (progress >= 100) {
                clearInterval(interval);
                setTimeout(function() {
                    progressContainer.style.display = 'none';
                    progressBar.style.width = '0%';
                }, 500);
            }
        }, 100);
        
        // Preview the image
        previewFile(file);
    }
    
    function previewFile(file) {
        const reader = new FileReader();
        
        reader.onloadstart = function() {
            // Remove any existing preview
            while (imagePreview.firstChild) {
                imagePreview.removeChild(imagePreview.firstChild);
            }
            
            // Add loading indicator
            const loading = document.createElement('div');
            loading.className = 'loading-indicator';
            loading.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
            imagePreview.appendChild(loading);
        };
        
        reader.onload = function(e) {
            // Remove loading indicator
            while (imagePreview.firstChild) {
                imagePreview.removeChild(imagePreview.firstChild);
            }
            
            // Create image element
            const img = document.createElement('img');
            img.src = e.target.result;
            img.alt = 'Preview';
            
            // Add remove button
            const removeBtn = document.createElement('button');
            removeBtn.type = 'button';
            removeBtn.className = 'remove-image';
            removeBtn.innerHTML = '<i class="fas fa-times"></i>';
            
            // Add click event to remove button
            removeBtn.addEventListener('click', function(e) {
                e.stopPropagation();
                
                // Clear the file input
                imageInput.value = '';
                
                // Remove the image
                while (imagePreview.firstChild) {
                    imagePreview.removeChild(imagePreview.firstChild);
                }
                
                // Add drop text back
                imagePreview.appendChild(dropText);
            });
            
            // Add elements to preview
            imagePreview.appendChild(img);
            imagePreview.appendChild(removeBtn);
        };
        
        reader.onerror = function() {
            showError('Error reading file.');
        };
        
        reader.readAsDataURL(file);
    }
    
    function showError(message) {
        errorContainer.textContent = message;
        errorContainer.style.display = 'block';
    }
    
    function hideError() {
        errorContainer.textContent = '';
        errorContainer.style.display = 'none';
    }
});
