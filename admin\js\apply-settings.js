// Apply User Settings JavaScript
document.addEventListener('DOMContentLoaded', function() {
    // Get user settings from the data attribute
    const settingsContainer = document.getElementById('user-settings-data');
    if (!settingsContainer) {
        console.log('Settings container not found');
        return;
    }

    try {
        const settings = JSON.parse(settingsContainer.getAttribute('data-settings'));
        console.log('User settings loaded:', settings);

        // Apply theme
        applyTheme(settings.theme);

        // Apply sidebar state
        applySidebarState(settings.sidebar_collapsed);

        // Apply other settings
        applyDateTimeFormats(settings.date_format, settings.time_format);

        // Apply auto refresh if enabled
        if (settings.refresh_interval > 0) {
            setupAutoRefresh(settings.refresh_interval);
        } else if (settings.auto_refresh > 0) {
            // Fallback to auto_refresh for backward compatibility
            setupAutoRefresh(settings.auto_refresh);
        }

        // Apply items per page to tables
        applyItemsPerPage(settings.items_per_page);

    } catch (error) {
        console.error('Error parsing user settings:', error);
    }
});

// Apply theme setting
function applyTheme(theme) {
    if (!theme) return;

    const body = document.body;

    // Remove existing theme classes
    body.classList.remove('theme-light', 'theme-dark');

    if (theme === 'auto') {
        // Check system preference
        if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {
            body.classList.add('theme-dark');
            document.documentElement.setAttribute('data-theme', 'dark');
        } else {
            body.classList.add('theme-light');
            document.documentElement.setAttribute('data-theme', 'light');
        }

        // Listen for changes in system preference
        window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', e => {
            if (e.matches) {
                body.classList.remove('theme-light');
                body.classList.add('theme-dark');
                document.documentElement.setAttribute('data-theme', 'dark');
            } else {
                body.classList.remove('theme-dark');
                body.classList.add('theme-light');
                document.documentElement.setAttribute('data-theme', 'light');
            }
        });
    } else {
        // Apply specific theme
        body.classList.add('theme-' + theme);
        document.documentElement.setAttribute('data-theme', theme);
    }

    console.log('Theme applied:', theme);
}

// Apply sidebar collapsed state
function applySidebarState(collapsed) {
    if (collapsed === undefined) return;

    const sidebar = document.querySelector('.admin-sidebar');
    const mainContent = document.querySelector('.admin-main');
    const toggleButton = document.querySelector('.sidebar-collapse-toggle');

    if (!sidebar || !mainContent) return;

    // Convert to boolean
    collapsed = collapsed == 1;

    // Set localStorage value
    localStorage.setItem('sidebarCollapsed', collapsed);

    // Apply state
    if (collapsed) {
        sidebar.classList.add('collapsed');
        mainContent.classList.add('expanded');

        if (toggleButton) {
            const icon = toggleButton.querySelector('i');
            if (icon) {
                icon.className = 'fas fa-chevron-right';
            }
            toggleButton.title = 'Expand Sidebar';
            toggleButton.style.left = '55px';
        }
    } else {
        sidebar.classList.remove('collapsed');
        mainContent.classList.remove('expanded');

        if (toggleButton) {
            const icon = toggleButton.querySelector('i');
            if (icon) {
                icon.className = 'fas fa-chevron-left';
            }
            toggleButton.title = 'Collapse Sidebar';
            toggleButton.style.left = '225px';
        }
    }

    console.log('Sidebar state applied:', collapsed ? 'collapsed' : 'expanded');
}

// Apply date and time formats
function applyDateTimeFormats(dateFormat, timeFormat) {
    if (!dateFormat || !timeFormat) return;

    // Store formats in localStorage for use by other scripts
    localStorage.setItem('dateFormat', dateFormat);
    localStorage.setItem('timeFormat', timeFormat);

    console.log('Date/time formats applied:', dateFormat, timeFormat);
}

// Set up auto refresh
function setupAutoRefresh(seconds) {
    if (!seconds || seconds <= 0) {
        console.log('Auto refresh disabled or invalid value:', seconds);
        return;
    }

    console.log('Auto refresh enabled:', seconds + ' seconds');

    // Set up refresh timer
    const refreshInterval = setInterval(function() {
        console.log('Auto refresh check - interval:', seconds + 's');

        // Don't refresh if user is interacting with a form
        const activeElement = document.activeElement;
        if (activeElement && (activeElement.tagName === 'INPUT' || activeElement.tagName === 'TEXTAREA' || activeElement.tagName === 'SELECT')) {
            console.log('User is interacting with a form, skipping refresh');
            return;
        }

        // Don't refresh if there are unsaved changes
        if (window.hasUnsavedChanges) {
            console.log('Page has unsaved changes, skipping refresh');
            return;
        }

        console.log('Refreshing page now...');

        // Refresh the page
        window.location.reload();
    }, seconds * 1000);

    // Store the interval ID in case we need to clear it later
    window.dashboardRefreshInterval = refreshInterval;
}

// Apply items per page to tables
function applyItemsPerPage(itemsPerPage) {
    if (!itemsPerPage) return;

    // Store in localStorage for use by pagination scripts
    localStorage.setItem('itemsPerPage', itemsPerPage);

    // Update any existing pagination controls
    const paginationControls = document.querySelectorAll('.pagination-control');
    paginationControls.forEach(control => {
        const itemsPerPageSelect = control.querySelector('select[name="items_per_page"]');
        if (itemsPerPageSelect) {
            itemsPerPageSelect.value = itemsPerPage;
        }
    });

    console.log('Items per page applied:', itemsPerPage);
}
