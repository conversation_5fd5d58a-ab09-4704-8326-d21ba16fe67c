/**
 * Admin Core JavaScript
 * Core functionality for the admin panel
 * Consolidated from multiple JS files
 */

// Initialize admin panel when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    // Initialize all components
    AdminCore.init();
});

// Admin Core Namespace
const AdminCore = {
    // Initialize all components
    init: function() {
        this.initSidebar();
        this.initDropdowns();
        this.initTabs();
        this.initModals();
        this.initAlerts();
        this.initTooltips();
        this.initThemeToggle();
        this.initFormValidation();
        this.initCustomFileInputs();
        this.initConfirmActions();
        this.initAjaxForms();
        this.initNotifications();
    },

    // Initialize sidebar functionality
    initSidebar: function() {
        const toggleButton = document.querySelector('.mobile-menu-toggle');
        const sidebar = document.querySelector('.admin-sidebar');
        const backdrop = document.querySelector('.admin-sidebar-backdrop');
        
        if (toggleButton && sidebar) {
            toggleButton.addEventListener('click', function() {
                sidebar.classList.toggle('active');
                if (backdrop) {
                    backdrop.classList.toggle('active');
                }
            });
            
            if (backdrop) {
                backdrop.addEventListener('click', function() {
                    sidebar.classList.remove('active');
                    backdrop.classList.remove('active');
                });
            }
        }
        
        // Expand active submenu
        const activeLink = document.querySelector('.admin-sidebar-menu-item.active');
        if (activeLink) {
            const parentSubmenu = activeLink.closest('.admin-sidebar-submenu');
            if (parentSubmenu) {
                parentSubmenu.classList.add('active');
                const parentItem = parentSubmenu.closest('.admin-sidebar-menu-item');
                if (parentItem) {
                    parentItem.classList.add('expanded');
                }
            }
        }
        
        // Toggle submenus
        const submenuToggles = document.querySelectorAll('.admin-sidebar-submenu-toggle');
        submenuToggles.forEach(function(toggle) {
            toggle.addEventListener('click', function(e) {
                e.preventDefault();
                const menuItem = this.closest('.admin-sidebar-menu-item');
                menuItem.classList.toggle('expanded');
                const submenu = menuItem.querySelector('.admin-sidebar-submenu');
                if (submenu) {
                    submenu.classList.toggle('active');
                }
            });
        });
    },

    // Initialize dropdown functionality
    initDropdowns: function() {
        const dropdownToggles = document.querySelectorAll('[data-toggle="dropdown"]');
        
        dropdownToggles.forEach(function(toggle) {
            toggle.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                
                const dropdown = this.closest('.dropdown');
                const menu = dropdown.querySelector('.dropdown-menu');
                
                // Close all other dropdowns
                document.querySelectorAll('.dropdown.active').forEach(function(activeDropdown) {
                    if (activeDropdown !== dropdown) {
                        activeDropdown.classList.remove('active');
                        activeDropdown.querySelector('.dropdown-menu').classList.remove('active');
                    }
                });
                
                // Toggle current dropdown
                dropdown.classList.toggle('active');
                menu.classList.toggle('active');
            });
        });
        
        // Close dropdowns when clicking outside
        document.addEventListener('click', function(e) {
            if (!e.target.closest('.dropdown')) {
                document.querySelectorAll('.dropdown.active').forEach(function(dropdown) {
                    dropdown.classList.remove('active');
                    dropdown.querySelector('.dropdown-menu').classList.remove('active');
                });
            }
        });
    },

    // Initialize tabs functionality
    initTabs: function() {
        const tabContainers = document.querySelectorAll('.admin-tabs, .settings-tabs, .template-editor-tabs');
        
        tabContainers.forEach(function(container) {
            const tabButtons = container.querySelectorAll('[data-toggle="tab"]');
            
            tabButtons.forEach(function(button) {
                button.addEventListener('click', function(e) {
                    e.preventDefault();
                    
                    // Get target tab
                    const target = this.getAttribute('data-target');
                    const tabContent = document.querySelector(target);
                    
                    if (!tabContent) return;
                    
                    // Deactivate all tabs
                    const tabsContainer = this.closest('.admin-tabs, .settings-tabs, .template-editor-tabs');
                    tabsContainer.querySelectorAll('[data-toggle="tab"]').forEach(function(btn) {
                        btn.classList.remove('active');
                    });
                    
                    // Find all tab contents in the same group
                    const tabContents = document.querySelectorAll('.admin-tab-content, .settings-tab-content, .template-editor-content');
                    tabContents.forEach(function(content) {
                        content.classList.remove('active');
                    });
                    
                    // Activate current tab
                    this.classList.add('active');
                    tabContent.classList.add('active');
                    
                    // Save active tab to localStorage if ID is available
                    if (tabsContainer.id) {
                        localStorage.setItem('activeTab_' + tabsContainer.id, target);
                    }
                });
            });
            
            // Restore active tab from localStorage if available
            if (container.id) {
                const savedTab = localStorage.getItem('activeTab_' + container.id);
                if (savedTab) {
                    const savedTabButton = container.querySelector('[data-target="' + savedTab + '"]');
                    if (savedTabButton) {
                        savedTabButton.click();
                    }
                }
            }
        });
    },

    // Initialize modal functionality
    initModals: function() {
        const modalToggles = document.querySelectorAll('[data-toggle="modal"]');
        
        modalToggles.forEach(function(toggle) {
            toggle.addEventListener('click', function(e) {
                e.preventDefault();
                
                const target = this.getAttribute('data-target');
                const modal = document.querySelector(target);
                
                if (modal) {
                    modal.classList.add('active');
                    document.body.style.overflow = 'hidden';
                    
                    // Focus first input if exists
                    const firstInput = modal.querySelector('input, textarea, select');
                    if (firstInput) {
                        setTimeout(function() {
                            firstInput.focus();
                        }, 100);
                    }
                }
            });
        });
        
        // Close modal when clicking close button
        const closeButtons = document.querySelectorAll('[data-dismiss="modal"]');
        closeButtons.forEach(function(button) {
            button.addEventListener('click', function(e) {
                e.preventDefault();
                
                const modal = this.closest('.admin-modal-overlay');
                if (modal) {
                    modal.classList.remove('active');
                    document.body.style.overflow = '';
                }
            });
        });
        
        // Close modal when clicking outside
        const modals = document.querySelectorAll('.admin-modal-overlay');
        modals.forEach(function(modal) {
            modal.addEventListener('click', function(e) {
                if (e.target === this) {
                    this.classList.remove('active');
                    document.body.style.overflow = '';
                }
            });
        });
        
        // Close modal with Escape key
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                const activeModal = document.querySelector('.admin-modal-overlay.active');
                if (activeModal) {
                    activeModal.classList.remove('active');
                    document.body.style.overflow = '';
                }
            }
        });
    },

    // Initialize alert functionality
    initAlerts: function() {
        const dismissButtons = document.querySelectorAll('.admin-alert .dismiss');
        
        dismissButtons.forEach(function(button) {
            button.addEventListener('click', function() {
                const alert = this.closest('.admin-alert');
                alert.classList.add('fade-out');
                
                setTimeout(function() {
                    alert.remove();
                }, 300);
            });
        });
        
        // Auto-dismiss alerts with data-auto-dismiss attribute
        const autoDismissAlerts = document.querySelectorAll('.admin-alert[data-auto-dismiss]');
        autoDismissAlerts.forEach(function(alert) {
            const delay = parseInt(alert.getAttribute('data-auto-dismiss')) || 5000;
            
            setTimeout(function() {
                alert.classList.add('fade-out');
                
                setTimeout(function() {
                    alert.remove();
                }, 300);
            }, delay);
        });
    },

    // Initialize tooltip functionality
    initTooltips: function() {
        const tooltipElements = document.querySelectorAll('[data-tooltip]');
        
        tooltipElements.forEach(function(element) {
            const tooltip = element.getAttribute('data-tooltip');
            const position = element.getAttribute('data-tooltip-position') || 'top';
            
            // Create tooltip element
            const tooltipEl = document.createElement('div');
            tooltipEl.className = 'admin-tooltip ' + position;
            tooltipEl.textContent = tooltip;
            
            // Show tooltip on hover
            element.addEventListener('mouseenter', function() {
                document.body.appendChild(tooltipEl);
                
                const rect = element.getBoundingClientRect();
                const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
                const scrollLeft = window.pageXOffset || document.documentElement.scrollLeft;
                
                let top, left;
                
                switch (position) {
                    case 'top':
                        top = rect.top + scrollTop - tooltipEl.offsetHeight - 10;
                        left = rect.left + scrollLeft + (rect.width / 2) - (tooltipEl.offsetWidth / 2);
                        break;
                    case 'bottom':
                        top = rect.bottom + scrollTop + 10;
                        left = rect.left + scrollLeft + (rect.width / 2) - (tooltipEl.offsetWidth / 2);
                        break;
                    case 'left':
                        top = rect.top + scrollTop + (rect.height / 2) - (tooltipEl.offsetHeight / 2);
                        left = rect.left + scrollLeft - tooltipEl.offsetWidth - 10;
                        break;
                    case 'right':
                        top = rect.top + scrollTop + (rect.height / 2) - (tooltipEl.offsetHeight / 2);
                        left = rect.right + scrollLeft + 10;
                        break;
                }
                
                tooltipEl.style.top = top + 'px';
                tooltipEl.style.left = left + 'px';
                tooltipEl.classList.add('active');
            });
            
            // Hide tooltip on mouse leave
            element.addEventListener('mouseleave', function() {
                tooltipEl.classList.remove('active');
                if (tooltipEl.parentNode) {
                    tooltipEl.parentNode.removeChild(tooltipEl);
                }
            });
        });
    },

    // Initialize theme toggle functionality
    initThemeToggle: function() {
        const themeToggle = document.querySelector('.theme-toggle');
        
        if (themeToggle) {
            themeToggle.addEventListener('click', function(e) {
                e.preventDefault();
                
                const currentTheme = document.documentElement.getAttribute('data-theme') || 'light';
                const newTheme = currentTheme === 'light' ? 'dark' : 'light';
                
                document.documentElement.setAttribute('data-theme', newTheme);
                localStorage.setItem('admin_theme', newTheme);
                
                // Update toggle icon
                const icon = this.querySelector('i');
                if (icon) {
                    if (newTheme === 'dark') {
                        icon.classList.remove('fa-moon');
                        icon.classList.add('fa-sun');
                    } else {
                        icon.classList.remove('fa-sun');
                        icon.classList.add('fa-moon');
                    }
                }
            });
            
            // Set initial theme from localStorage
            const savedTheme = localStorage.getItem('admin_theme');
            if (savedTheme) {
                document.documentElement.setAttribute('data-theme', savedTheme);
                
                // Update toggle icon
                const icon = themeToggle.querySelector('i');
                if (icon) {
                    if (savedTheme === 'dark') {
                        icon.classList.remove('fa-moon');
                        icon.classList.add('fa-sun');
                    } else {
                        icon.classList.remove('fa-sun');
                        icon.classList.add('fa-moon');
                    }
                }
            }
        }
    },

    // Initialize form validation
    initFormValidation: function() {
        const forms = document.querySelectorAll('.admin-form[data-validate]');
        
        forms.forEach(function(form) {
            form.addEventListener('submit', function(e) {
                let isValid = true;
                
                // Check required fields
                const requiredFields = form.querySelectorAll('[required]');
                requiredFields.forEach(function(field) {
                    if (!field.value.trim()) {
                        isValid = false;
                        field.classList.add('is-invalid');
                        
                        // Create or update error message
                        let errorMessage = field.nextElementSibling;
                        if (!errorMessage || !errorMessage.classList.contains('invalid-feedback')) {
                            errorMessage = document.createElement('div');
                            errorMessage.className = 'invalid-feedback';
                            field.parentNode.insertBefore(errorMessage, field.nextSibling);
                        }
                        
                        errorMessage.textContent = 'This field is required.';
                    } else {
                        field.classList.remove('is-invalid');
                    }
                });
                
                // Check email fields
                const emailFields = form.querySelectorAll('[type="email"]');
                const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                
                emailFields.forEach(function(field) {
                    if (field.value.trim() && !emailRegex.test(field.value.trim())) {
                        isValid = false;
                        field.classList.add('is-invalid');
                        
                        // Create or update error message
                        let errorMessage = field.nextElementSibling;
                        if (!errorMessage || !errorMessage.classList.contains('invalid-feedback')) {
                            errorMessage = document.createElement('div');
                            errorMessage.className = 'invalid-feedback';
                            field.parentNode.insertBefore(errorMessage, field.nextSibling);
                        }
                        
                        errorMessage.textContent = 'Please enter a valid email address.';
                    }
                });
                
                if (!isValid) {
                    e.preventDefault();
                    
                    // Focus first invalid field
                    const firstInvalid = form.querySelector('.is-invalid');
                    if (firstInvalid) {
                        firstInvalid.focus();
                    }
                }
            });
            
            // Clear validation on input
            form.querySelectorAll('input, textarea, select').forEach(function(field) {
                field.addEventListener('input', function() {
                    this.classList.remove('is-invalid');
                    
                    const errorMessage = this.nextElementSibling;
                    if (errorMessage && errorMessage.classList.contains('invalid-feedback')) {
                        errorMessage.textContent = '';
                    }
                });
            });
        });
    },

    // Initialize custom file inputs
    initCustomFileInputs: function() {
        const fileInputs = document.querySelectorAll('.custom-file-input');
        
        fileInputs.forEach(function(input) {
            input.addEventListener('change', function() {
                const label = this.nextElementSibling;
                
                if (label && label.classList.contains('custom-file-label')) {
                    if (this.files.length > 1) {
                        label.textContent = this.files.length + ' files selected';
                    } else if (this.files.length === 1) {
                        label.textContent = this.files[0].name;
                    } else {
                        label.textContent = 'Choose file';
                    }
                }
            });
        });
    },

    // Initialize confirm actions
    initConfirmActions: function() {
        const confirmButtons = document.querySelectorAll('[data-confirm]');
        
        confirmButtons.forEach(function(button) {
            button.addEventListener('click', function(e) {
                const message = this.getAttribute('data-confirm') || 'Are you sure you want to perform this action?';
                
                if (!confirm(message)) {
                    e.preventDefault();
                }
            });
        });
    },

    // Initialize AJAX forms
    initAjaxForms: function() {
        const ajaxForms = document.querySelectorAll('form[data-ajax]');
        
        ajaxForms.forEach(function(form) {
            form.addEventListener('submit', function(e) {
                e.preventDefault();
                
                const formData = new FormData(this);
                const url = this.getAttribute('action') || window.location.href;
                const method = this.getAttribute('method') || 'POST';
                const submitButton = this.querySelector('[type="submit"]');
                
                // Disable submit button and show loading state
                if (submitButton) {
                    submitButton.disabled = true;
                    submitButton.classList.add('loading');
                    
                    const originalText = submitButton.innerHTML;
                    submitButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Loading...';
                }
                
                // Send AJAX request
                fetch(url, {
                    method: method,
                    body: formData,
                    credentials: 'same-origin'
                })
                .then(response => response.json())
                .then(data => {
                    // Re-enable submit button
                    if (submitButton) {
                        submitButton.disabled = false;
                        submitButton.classList.remove('loading');
                        submitButton.innerHTML = originalText;
                    }
                    
                    // Handle response
                    if (data.success) {
                        // Show success message
                        AdminCore.showNotification(data.message || 'Action completed successfully.', 'success');
                        
                        // Redirect if specified
                        if (data.redirect) {
                            window.location.href = data.redirect;
                        }
                        
                        // Reload if specified
                        if (data.reload) {
                            window.location.reload();
                        }
                        
                        // Reset form if specified
                        if (data.reset) {
                            form.reset();
                        }
                        
                        // Call custom callback if specified
                        if (data.callback && typeof window[data.callback] === 'function') {
                            window[data.callback](data);
                        }
                    } else {
                        // Show error message
                        AdminCore.showNotification(data.message || 'An error occurred.', 'error');
                        
                        // Show field errors if any
                        if (data.errors) {
                            for (const field in data.errors) {
                                const input = form.querySelector('[name="' + field + '"]');
                                if (input) {
                                    input.classList.add('is-invalid');
                                    
                                    // Create or update error message
                                    let errorMessage = input.nextElementSibling;
                                    if (!errorMessage || !errorMessage.classList.contains('invalid-feedback')) {
                                        errorMessage = document.createElement('div');
                                        errorMessage.className = 'invalid-feedback';
                                        input.parentNode.insertBefore(errorMessage, input.nextSibling);
                                    }
                                    
                                    errorMessage.textContent = data.errors[field];
                                }
                            }
                        }
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    
                    // Re-enable submit button
                    if (submitButton) {
                        submitButton.disabled = false;
                        submitButton.classList.remove('loading');
                        submitButton.innerHTML = originalText;
                    }
                    
                    // Show error message
                    AdminCore.showNotification('An error occurred while processing your request.', 'error');
                });
            });
        });
    },

    // Initialize notifications
    initNotifications: function() {
        // Check for notifications on page load
        this.checkNotifications();
        
        // Set up polling for new notifications
        setInterval(this.checkNotifications, 60000); // Check every minute
    },

    // Check for new notifications
    checkNotifications: function() {
        const notificationBadge = document.querySelector('.notification-badge');
        const notificationsList = document.querySelector('.notifications-list');
        
        if (notificationBadge && notificationsList) {
            fetch('ajax/notifications.php')
                .then(response => response.json())
                .then(data => {
                    if (data.count > 0) {
                        notificationBadge.textContent = data.count;
                        notificationBadge.style.display = 'flex';
                        
                        // Update notifications list
                        notificationsList.innerHTML = '';
                        
                        data.notifications.forEach(function(notification) {
                            const item = document.createElement('div');
                            item.className = 'notification-item' + (notification.read ? '' : ' unread');
                            
                            item.innerHTML = `
                                <div class="notification-icon">
                                    <i class="fas ${notification.icon || 'fa-bell'}"></i>
                                </div>
                                <div class="notification-content">
                                    <div class="notification-title">${notification.title}</div>
                                    <div class="notification-message">${notification.message}</div>
                                    <div class="notification-time">${notification.time}</div>
                                </div>
                            `;
                            
                            notificationsList.appendChild(item);
                            
                            // Mark as read when clicked
                            item.addEventListener('click', function() {
                                fetch('ajax/mark_notification_read.php', {
                                    method: 'POST',
                                    headers: {
                                        'Content-Type': 'application/x-www-form-urlencoded',
                                    },
                                    body: 'id=' + notification.id
                                });
                                
                                this.classList.remove('unread');
                            });
                        });
                    } else {
                        notificationBadge.style.display = 'none';
                        
                        // Show empty state
                        notificationsList.innerHTML = '<div class="notification-empty">No new notifications</div>';
                    }
                })
                .catch(error => {
                    console.error('Error checking notifications:', error);
                });
        }
    },

    // Show notification
    showNotification: function(message, type = 'info') {
        // Create notification element
        const notification = document.createElement('div');
        notification.className = 'admin-notification ' + type;
        
        // Set icon based on type
        let icon = 'fa-info-circle';
        if (type === 'success') icon = 'fa-check-circle';
        if (type === 'warning') icon = 'fa-exclamation-triangle';
        if (type === 'error') icon = 'fa-times-circle';
        
        notification.innerHTML = `
            <div class="admin-notification-icon">
                <i class="fas ${icon}"></i>
            </div>
            <div class="admin-notification-content">
                ${message}
            </div>
            <button class="admin-notification-close">
                <i class="fas fa-times"></i>
            </button>
        `;
        
        // Add to notifications container
        let container = document.querySelector('.admin-notifications-container');
        
        if (!container) {
            container = document.createElement('div');
            container.className = 'admin-notifications-container';
            document.body.appendChild(container);
        }
        
        container.appendChild(notification);
        
        // Show notification
        setTimeout(function() {
            notification.classList.add('active');
        }, 10);
        
        // Auto-dismiss after 5 seconds
        setTimeout(function() {
            notification.classList.remove('active');
            
            setTimeout(function() {
                notification.remove();
            }, 300);
        }, 5000);
        
        // Close button
        const closeButton = notification.querySelector('.admin-notification-close');
        closeButton.addEventListener('click', function() {
            notification.classList.remove('active');
            
            setTimeout(function() {
                notification.remove();
            }, 300);
        });
    }
};

// Utility functions
const AdminUtils = {
    // Format date
    formatDate: function(date, format = 'YYYY-MM-DD') {
        const d = new Date(date);
        
        const year = d.getFullYear();
        const month = String(d.getMonth() + 1).padStart(2, '0');
        const day = String(d.getDate()).padStart(2, '0');
        const hours = String(d.getHours()).padStart(2, '0');
        const minutes = String(d.getMinutes()).padStart(2, '0');
        const seconds = String(d.getSeconds()).padStart(2, '0');
        
        return format
            .replace('YYYY', year)
            .replace('MM', month)
            .replace('DD', day)
            .replace('HH', hours)
            .replace('mm', minutes)
            .replace('ss', seconds);
    },
    
    // Format number
    formatNumber: function(number, decimals = 0) {
        return Number(number).toFixed(decimals).replace(/\B(?=(\d{3})+(?!\d))/g, ',');
    },
    
    // Truncate text
    truncateText: function(text, length = 100) {
        if (text.length <= length) return text;
        return text.substring(0, length) + '...';
    },
    
    // Generate random ID
    generateId: function(prefix = 'id_') {
        return prefix + Math.random().toString(36).substring(2, 9);
    },
    
    // Debounce function
    debounce: function(func, wait = 300) {
        let timeout;
        
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    },
    
    // Throttle function
    throttle: function(func, limit = 300) {
        let inThrottle;
        
        return function executedFunction(...args) {
            if (!inThrottle) {
                func(...args);
                inThrottle = true;
                
                setTimeout(() => {
                    inThrottle = false;
                }, limit);
            }
        };
    }
};
