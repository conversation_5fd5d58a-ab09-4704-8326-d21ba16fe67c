<?php
/**
 * Security wrapper for the admin panel
 * This file should be included at the beginning of every admin page
 */

// Define a constant to indicate that the security wrapper has been included
define('SECURITY_WRAPPER_INCLUDED', true);

// Enable error reporting
ini_set('display_errors', 1);
error_reporting(E_ALL);

// Set up error logging
ini_set('log_errors', 1);
ini_set('error_log', __DIR__ . '/../error.log');

// Apply session security settings before starting the session
if (session_status() === PHP_SESSION_NONE) {
    // Session cookie settings - must be set before session_start()
    ini_set('session.cookie_httponly', 1);
    ini_set('session.use_only_cookies', 1);
    ini_set('session.cookie_secure', isset($_SERVER['HTTPS']) ? 1 : 0);
    ini_set('session.cookie_samesite', 'Strict');

    // Start the session
    session_start();
}

// Clean output buffer
while (ob_get_level()) {
    ob_end_clean();
}

// Start output buffering
ob_start();

/**
 * Apply security headers
 * These headers should be applied before any output is sent to the browser
 */
function apply_security_headers() {
    // Check if headers have already been sent
    if (headers_sent($file, $line)) {
        // Log warning about headers already sent
        error_log("Warning: Cannot send security headers - output started at $file:$line");
        return false;
    }

    // Special case for frontend editor
    if (defined('IS_FRONTEND_EDITOR') && IS_FRONTEND_EDITOR === true) {
        // Set a permissive Content-Security-Policy for the editor
        header("Content-Security-Policy: default-src * 'unsafe-inline' 'unsafe-eval'; img-src * data: blob:; script-src * 'unsafe-inline' 'unsafe-eval'; style-src * 'unsafe-inline';");
    } else {
        // Standard Content Security Policy for other pages
        header("Content-Security-Policy: default-src 'self'; script-src 'self' 'unsafe-inline' https://cdnjs.cloudflare.com; style-src 'self' 'unsafe-inline' https://cdnjs.cloudflare.com https://fonts.googleapis.com; img-src 'self' data:; font-src 'self' https://cdnjs.cloudflare.com https://fonts.gstatic.com data:; connect-src 'self'; style-src-elem 'self' 'unsafe-inline' https://cdnjs.cloudflare.com https://fonts.googleapis.com");
    }

    // XSS Protection
    header('X-XSS-Protection: 1; mode=block');

    // Content Type Options
    header('X-Content-Type-Options: nosniff');

    // Frame Options
    header('X-Frame-Options: SAMEORIGIN');

    // Referrer Policy
    header('Referrer-Policy: strict-origin-when-cross-origin');

    // Content Type
    header('Content-Type: text/html; charset=UTF-8');

    // X-UA-Compatible
    header('X-UA-Compatible: IE=edge');

    return true;
}

/**
 * Apply session security settings
 * This function is kept for backward compatibility but no longer needed
 * as session settings are now applied before session_start()
 */
function apply_session_security() {
    // Function now empty as settings are applied before session_start()
    // to avoid "Session ini settings cannot be changed when a session is active" warnings
}

/**
 * Generate CSRF token
 * @return string CSRF token
 */
if (!function_exists('generate_csrf_token')) {
    function generate_csrf_token() {
        if (!isset($_SESSION['csrf_token'])) {
            $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
        }
        return $_SESSION['csrf_token'];
    }
}

/**
 * Verify CSRF token
 * @param string $token CSRF token to verify
 * @return bool True if token is valid, false otherwise
 */
if (!function_exists('verify_csrf_token')) {
    function verify_csrf_token($token) {
        if (!isset($_SESSION['csrf_token']) || empty($token) || $_SESSION['csrf_token'] !== $token) {
            return false;
        }
        return true;
    }
}

/**
 * Alias for verify_csrf_token for backward compatibility
 * @param string $token CSRF token to validate
 * @return bool True if token is valid, false otherwise
 */
if (!function_exists('validate_csrf_token')) {
    function validate_csrf_token($token) {
        return verify_csrf_token($token);
    }
}

/**
 * End output buffering and apply security headers
 * This function should be called at the end of every admin page
 */
function security_end() {
    // Check if headers have already been sent
    if (!headers_sent()) {
        // Apply security headers only if headers haven't been sent yet
        apply_security_headers();
    } else {
        // Log warning about headers already sent
        error_log("Warning: Headers already sent before security headers could be applied");
    }

    // Get the current buffer content
    $content = ob_get_clean();

    // Check if there was an error
    $error = error_get_last();

    // If there was a fatal error, show it
    if ($error !== null && in_array($error['type'], [E_ERROR, E_PARSE, E_CORE_ERROR, E_COMPILE_ERROR])) {
        // Create an error page
        $error_content = "<!DOCTYPE html>\n";
        $error_content .= "<html lang=\"en\">\n";
        $error_content .= "<head>\n";
        $error_content .= "    <meta charset=\"UTF-8\">\n";
        $error_content .= "    <meta http-equiv=\"X-UA-Compatible\" content=\"IE=edge\">\n";
        $error_content .= "    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n";
        $error_content .= "    <title>Error</title>\n";
        $error_content .= "    <style>\n";
        $error_content .= "        body { font-family: Arial, sans-serif; line-height: 1.6; margin: 0; padding: 20px; background-color: #f5f5f5; }\n";
        $error_content .= "        .container { max-width: 1200px; margin: 0 auto; background-color: white; padding: 20px; border-radius: 5px; box-shadow: 0 2px 5px rgba(0,0,0,0.1); }\n";
        $error_content .= "        h1 { color: #333; margin-top: 0; }\n";
        $error_content .= "        .error { color: red; }\n";
        $error_content .= "        pre { background-color: #f9f9f9; padding: 10px; border-radius: 5px; overflow-x: auto; }\n";
        $error_content .= "    </style>\n";
        $error_content .= "</head>\n";
        $error_content .= "<body>\n";
        $error_content .= "    <div class=\"container\">\n";
        $error_content .= "        <h1>Error</h1>\n";
        $error_content .= "        <p>An error occurred while processing your request.</p>\n";
        $error_content .= "        <div class=\"error\">\n";
        $error_content .= "            <h2>" . htmlspecialchars($error['message']) . "</h2>\n";
        $error_content .= "            <p>File: " . htmlspecialchars($error['file']) . "</p>\n";
        $error_content .= "            <p>Line: " . htmlspecialchars($error['line']) . "</p>\n";
        $error_content .= "        </div>\n";
        $error_content .= "    </div>\n";
        $error_content .= "</body>\n";
        $error_content .= "</html>";

        // Output the error page
        echo $error_content;
        return;
    }

    // If the content is empty, show a debug page only if DEBUG_MODE is enabled
    if (empty(trim($content))) {
        // Check if DEBUG_MODE is defined and enabled
        if (defined('DEBUG_MODE') && DEBUG_MODE) {
            // Create a debug page
            $debug_content = "<!DOCTYPE html>\n";
            $debug_content .= "<html lang=\"en\">\n";
            $debug_content .= "<head>\n";
            $debug_content .= "    <meta charset=\"UTF-8\">\n";
            $debug_content .= "    <meta http-equiv=\"X-UA-Compatible\" content=\"IE=edge\">\n";
            $debug_content .= "    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n";
            $debug_content .= "    <title>Debug</title>\n";
            $debug_content .= "    <style>\n";
            $debug_content .= "        body { font-family: Arial, sans-serif; line-height: 1.6; margin: 0; padding: 20px; background-color: #f5f5f5; }\n";
            $debug_content .= "        .container { max-width: 1200px; margin: 0 auto; background-color: white; padding: 20px; border-radius: 5px; box-shadow: 0 2px 5px rgba(0,0,0,0.1); }\n";
            $debug_content .= "        h1 { color: #333; margin-top: 0; }\n";
            $debug_content .= "        .warning { color: orange; }\n";
            $debug_content .= "        pre { background-color: #f9f9f9; padding: 10px; border-radius: 5px; overflow-x: auto; }\n";
            $debug_content .= "    </style>\n";
            $debug_content .= "</head>\n";
            $debug_content .= "<body>\n";
            $debug_content .= "    <div class=\"container\">\n";
            $debug_content .= "        <h1>Debug</h1>\n";
            $debug_content .= "        <div class=\"warning\">\n";
            $debug_content .= "            <h2>Empty Page Content</h2>\n";
            $debug_content .= "            <p>The page content is empty. This could be caused by:</p>\n";
            $debug_content .= "            <ul>\n";
            $debug_content .= "                <li>A PHP error that is not being displayed</li>\n";
            $debug_content .= "                <li>A redirect that is not working properly</li>\n";
            $debug_content .= "                <li>A missing template or include file</li>\n";
            $debug_content .= "                <li>An empty output buffer</li>\n";
            $debug_content .= "            </ul>\n";
            $debug_content .= "            <p>Check the error log for more information.</p>\n";
            $debug_content .= "        </div>\n";
            $debug_content .= "        <h2>Request Information</h2>\n";
            $debug_content .= "        <pre>" . htmlspecialchars(print_r($_SERVER, true)) . "</pre>\n";
            $debug_content .= "    </div>\n";
            $debug_content .= "</body>\n";
            $debug_content .= "</html>";

            // Output the debug page
            echo $debug_content;
            return;
        } else {
            // In production mode, just return without showing an error message
            // This will allow the page to continue loading without showing debug information
            return;
        }
    }

    // Check if the content already has a DOCTYPE
    if (stripos($content, '<!DOCTYPE') === false) {
        // Add DOCTYPE at the beginning of the content
        $content = "<!DOCTYPE html>\n" . $content;
    }

    // Check if we should add debug information
    if (defined('DEBUG_MODE') && DEBUG_MODE) {
        // Add debug information at the end of the content
        $content .= "<div class=\"debug-info\">\n";
        $content .= "    <h2>Debug</h2>\n";
        $content .= "    <div class=\"debug-content\">\n";
        $content .= "        <h3>Request Information</h3>\n";
        $content .= "        <pre>" . htmlspecialchars(print_r($_SERVER, true)) . "</pre>\n";
        $content .= "    </div>\n";
        $content .= "</div>\n";
    }

    // Output the content
    echo $content;
}

// Register shutdown function to ensure security_end is called
register_shutdown_function('security_end');
