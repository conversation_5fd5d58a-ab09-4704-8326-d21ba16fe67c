/**
 * Simple Partner Logo Carousel Styles
 */

.partner-logos-carousel {
    position: relative;
    width: 100%;
    margin: 0 auto;
    overflow: hidden;
    padding: 20px 40px;
}

.partner-logos-container {
    display: flex;
    flex-wrap: nowrap;
    justify-content: space-between;
    align-items: center;
}

.partner-logo {
    display: inline-block;
    text-align: center;
    padding: 0 10px;
    box-sizing: border-box;
    transition: transform 0.3s ease;
}

.partner-logo img {
    max-width: 100%;
    height: auto;
    max-height: 60px;
    object-fit: contain;
    vertical-align: middle;
}

.partner-logo:hover img {
    transform: scale(1.05);
}

/* Navigation buttons */
.carousel-prev,
.carousel-next {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    width: 40px;
    height: 40px;
    background-color: rgba(255, 255, 255, 0.7);
    border: none;
    border-radius: 50%;
    font-size: 18px;
    color: #666;
    cursor: pointer;
    z-index: 10;
    display: flex;
    justify-content: center;
    align-items: center;
    transition: background-color 0.3s ease;
}

.carousel-prev:hover,
.carousel-next:hover {
    background-color: rgba(255, 255, 255, 0.9);
    color: #333;
}

.carousel-prev {
    left: 0;
}

.carousel-next {
    right: 0;
}

/* Responsive styles */
@media (max-width: 980px) {
    .partner-logos-carousel {
        padding: 15px 30px;
    }
    
    .carousel-prev,
    .carousel-next {
        width: 30px;
        height: 30px;
        font-size: 16px;
    }
}

@media (max-width: 767px) {
    .partner-logos-carousel {
        padding: 10px 25px;
    }
    
    .carousel-prev,
    .carousel-next {
        width: 25px;
        height: 25px;
        font-size: 14px;
    }
    
    .partner-logo img {
        max-height: 40px;
    }
}
