!function(e){"object"==typeof exports&&"object"==typeof module?e(require("../../lib/codemirror"),require("./searchcursor"),require("../dialog/dialog")):"function"==typeof define&&define.amd?define(["../../lib/codemirror","./searchcursor","../dialog/dialog"],e):e(CodeMirror)}(function(f){"use strict";function r(){this.posFrom=this.posTo=this.lastQuery=this.query=null,this.overlay=null}function p(e){return e.state.search||(e.state.search=new r)}function n(e){return"string"==typeof e&&e==e.toLowerCase()}function d(e,r,o){return e.getSearchCursor(r,o,{caseFold:n(r),multiline:!0})}function m(e,r,o,t,n){e.openDialog?e.openDialog(r,n,{value:t,selectValueOnOpen:!0,bottom:e.options.search.bottom}):n(prompt(o,t))}function h(e){return e.replace(/\\([nrt\\])/g,function(e,r){return"n"==r?"\n":"r"==r?"\r":"t"==r?"\t":"\\"==r?"\\":e})}function a(e){var r=e.match(/^\/(.*)\/([a-z]*)$/);if(r)try{e=new RegExp(r[1],-1==r[2].indexOf("i")?"":"i")}catch(e){}else e=h(e);return e=("string"==typeof e?""==e:e.test(""))?/x^/:e}function y(e,r,o){var t;r.queryText=o,r.query=a(o),e.removeOverlay(r.overlay,n(r.query)),r.overlay=(t=r.query,o=n(r.query),"string"==typeof t?t=new RegExp(t.replace(/[\-\[\]\/\{\}\(\)\*\+\?\.\\\^\$\|]/g,"\\$&"),o?"gi":"g"):t.global||(t=new RegExp(t.source,t.ignoreCase?"gi":"g")),{token:function(e){t.lastIndex=e.pos;var r=t.exec(e.string);if(r&&r.index==e.pos)return e.pos+=r[0].length||1,"searching";r?e.pos=r.index:e.skipToEnd()}}),e.addOverlay(r.overlay),e.showMatchesOnScrollbar&&(r.annotate&&(r.annotate.clear(),r.annotate=null),r.annotate=e.showMatchesOnScrollbar(r.query,n(r.query)))}function o(n,r,e,o){var t=p(n);if(t.query)return g(n,r);var a,i,s,c,l,u=n.getSelection()||t.lastQuery;u instanceof RegExp&&"x^"==u.source&&(u=null),e&&n.openDialog?(a=null,i=function(e,r){f.e_stop(r),e&&(e!=t.queryText&&(y(n,t,e),t.posFrom=t.posTo=n.getCursor()),a&&(a.style.opacity=1),g(n,r.shiftKey,function(e,r){var o;r.line<3&&document.querySelector&&(o=n.display.wrapper.querySelector(".CodeMirror-dialog"))&&o.getBoundingClientRect().bottom-4>n.cursorCoords(r,"window").top&&((a=o).style.opacity=.4)}))},e=C(s=n),c=u,l=function(e,r){var o=f.keyName(e),t=n.getOption("extraKeys"),t=t&&t[o]||f.keyMap[n.getOption("keyMap")][o];"findNext"==t||"findPrev"==t||"findPersistentNext"==t||"findPersistentPrev"==t?(f.e_stop(e),y(n,p(n),r),n.execCommand(t)):"find"!=t&&"findPersistent"!=t||(f.e_stop(e),i(r,e))},s.openDialog(e,i,{value:c,selectValueOnOpen:!0,closeOnEnter:!1,onClose:function(){v(s)},onKeyDown:l,bottom:s.options.search.bottom}),o&&u&&(y(n,t,u),g(n,r))):m(n,C(n),"Search for:",u,function(e){e&&!t.query&&n.operation(function(){y(n,t,e),t.posFrom=t.posTo=n.getCursor(),g(n,r)})})}function g(o,t,n){o.operation(function(){var e=p(o),r=d(o,e.query,t?e.posFrom:e.posTo);(r.find(t)||(r=d(o,e.query,t?f.Pos(o.lastLine()):f.Pos(o.firstLine(),0))).find(t))&&(o.setSelection(r.from(),r.to()),o.scrollIntoView({from:r.from(),to:r.to()},20),e.posFrom=r.from(),e.posTo=r.to(),n&&n(r.from(),r.to()))})}function v(r){r.operation(function(){var e=p(r);e.lastQuery=e.query,e.query&&(e.query=e.queryText=null,r.removeOverlay(e.overlay),e.annotate&&(e.annotate.clear(),e.annotate=null))})}function x(e,r){var o,t=e?document.createElement(e):document.createDocumentFragment();for(o in r)t[o]=r[o];for(var n=2;n<arguments.length;n++){var a=arguments[n];t.appendChild("string"==typeof a?document.createTextNode(a):a)}return t}function C(e){var r=x("label",{className:"CodeMirror-search-label"},e.phrase("Search:"),x("input",{type:"text",style:"width: 10em",className:"CodeMirror-search-field",id:"CodeMirror-search-field"}));return r.setAttribute("for","CodeMirror-search-field"),x("",null,r," ",x("span",{style:"color: #666",className:"CodeMirror-search-hint"},e.phrase("(Use /re/ syntax for regexp search)")))}function b(r,t,n){r.operation(function(){for(var o,e=d(r,t);e.findNext();)"string"!=typeof t?(o=r.getRange(e.from(),e.to()).match(t),e.replace(n.replace(/\$(\d)/g,function(e,r){return o[r]}))):e.replace(n)})}function t(u,e){var r,o,t;u.getOption("readOnly")||(r=u.getSelection()||p(u).lastQuery,t=x("",null,x("span",{className:"CodeMirror-search-label"},o=e?u.phrase("Replace all:"):u.phrase("Replace:")),(t=u,x("",null," ",x("input",{type:"text",style:"width: 10em",className:"CodeMirror-search-field"})," ",x("span",{style:"color: #666",className:"CodeMirror-search-hint"},t.phrase("(Use /re/ syntax for regexp search)"))))),m(u,t,o,r,function(l){l&&(l=a(l),m(u,x("",null,x("span",{className:"CodeMirror-search-label"},u.phrase("With:"))," ",x("input",{type:"text",style:"width: 10em",className:"CodeMirror-search-field"})),u.phrase("Replace with:"),"",function(a){var i,s,c;a=h(a),e?b(u,l,a):(v(u),i=d(u,l,u.getCursor("from")),c=function(o){i.replace("string"==typeof l?a:a.replace(/\$(\d)/g,function(e,r){return o[r]})),s()},(s=function(){var e,r,o,t,n=i.from();!(e=i.findNext())&&(i=d(u,l),!(e=i.findNext())||n&&i.from().line==n.line&&i.from().ch==n.ch)||(u.setSelection(i.from(),i.to()),u.scrollIntoView({from:i.from(),to:i.to()}),t=x("",null,x("span",{className:"CodeMirror-search-label"},(t=n=u).phrase("Replace?"))," ",x("button",{},t.phrase("Yes"))," ",x("button",{},t.phrase("No"))," ",x("button",{},t.phrase("All"))," ",x("button",{},t.phrase("Stop"))),r=u.phrase("Replace?"),o=[function(){c(e)},s,function(){b(u,l,a)}],n.openConfirm?n.openConfirm(t,o):confirm(r)&&o[0]())})())}))}))}f.defineOption("search",{bottom:!1}),f.commands.find=function(e){v(e),o(e)},f.commands.findPersistent=function(e){v(e),o(e,!1,!0)},f.commands.findPersistentNext=function(e){o(e,!1,!0,!0)},f.commands.findPersistentPrev=function(e){o(e,!0,!0,!0)},f.commands.findNext=o,f.commands.findPrev=function(e){o(e,!0)},f.commands.clearSearch=v,f.commands.replace=t,f.commands.replaceAll=function(e){t(e,!0)}});