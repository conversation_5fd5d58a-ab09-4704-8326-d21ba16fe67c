/**
 * Admin Navbar CSS
 * 
 * This file contains styles for the admin navigation bar.
 */

/* Navbar Container */
.admin-navbar {
  display: flex;
  align-items: center;
  background-color: var(--white);
  border-bottom: 1px solid var(--border-color);
  padding: 0 var(--spacing-4);
  height: 50px;
  box-shadow: var(--shadow-sm);
}

/* Navbar Fixed */
.admin-navbar-fixed {
  position: sticky;
  top: var(--topbar-height);
  z-index: var(--z-index-sticky);
}

/* Navbar Brand */
.admin-navbar-brand {
  display: flex;
  align-items: center;
  margin-right: var(--spacing-4);
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--text-dark);
  text-decoration: none;
}

.admin-navbar-brand:hover {
  color: var(--primary-color);
}

.admin-navbar-logo {
  height: 30px;
  width: auto;
  margin-right: var(--spacing-2);
}

/* Navbar Nav */
.admin-navbar-nav {
  display: flex;
  align-items: center;
  list-style: none;
  margin: 0;
  padding: 0;
  height: 100%;
}

/* Navbar Item */
.admin-navbar-item {
  height: 100%;
  position: relative;
}

/* Navbar Link */
.admin-navbar-link {
  display: flex;
  align-items: center;
  height: 100%;
  padding: 0 var(--spacing-3);
  color: var(--text-color);
  font-size: var(--font-size-sm);
  text-decoration: none;
  transition: all var(--transition-fast) ease;
}

.admin-navbar-link:hover {
  color: var(--primary-color);
  background-color: var(--gray-50);
}

.admin-navbar-link.active {
  color: var(--primary-color);
  font-weight: var(--font-weight-medium);
  box-shadow: inset 0 -2px 0 var(--primary-color);
}

/* Navbar Link with Icon */
.admin-navbar-link-icon {
  margin-right: var(--spacing-2);
  font-size: var(--font-size-base);
}

/* Navbar Dropdown */
.admin-navbar-dropdown {
  position: relative;
}

.admin-navbar-dropdown-toggle {
  display: flex;
  align-items: center;
  gap: var(--spacing-1);
}

.admin-navbar-dropdown-toggle::after {
  content: "▼";
  font-size: 8px;
  margin-top: 2px;
}

.admin-navbar-dropdown-menu {
  position: absolute;
  top: 100%;
  left: 0;
  z-index: var(--z-index-dropdown);
  display: none;
  min-width: 180px;
  padding: var(--spacing-1) 0;
  margin-top: 0;
  background-color: var(--white);
  border: 1px solid var(--border-color);
  border-radius: 0 0 var(--radius-md) var(--radius-md);
  box-shadow: var(--shadow-md);
}

.admin-navbar-dropdown.show .admin-navbar-dropdown-menu {
  display: block;
}

.admin-navbar-dropdown-item {
  display: block;
  padding: var(--spacing-2) var(--spacing-4);
  font-size: var(--font-size-sm);
  color: var(--text-color);
  text-decoration: none;
  transition: all var(--transition-fast) ease;
  white-space: nowrap;
}

.admin-navbar-dropdown-item:hover {
  background-color: var(--gray-50);
  color: var(--primary-color);
}

.admin-navbar-dropdown-item.active {
  background-color: var(--primary-very-light);
  color: var(--primary-color);
  font-weight: var(--font-weight-medium);
}

.admin-navbar-dropdown-divider {
  height: 1px;
  margin: var(--spacing-1) 0;
  background-color: var(--border-color);
}

/* Navbar Right */
.admin-navbar-right {
  margin-left: auto;
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
}

/* Navbar Search */
.admin-navbar-search {
  position: relative;
  width: 200px;
}

.admin-navbar-search-input {
  width: 100%;
  height: 34px;
  padding: var(--spacing-1) var(--spacing-1) var(--spacing-1) var(--spacing-7);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-full);
  background-color: var(--gray-50);
  color: var(--text-color);
  font-size: var(--font-size-sm);
  transition: all var(--transition-fast) ease;
}

.admin-navbar-search-input:focus {
  outline: none;
  border-color: var(--primary-color);
  background-color: var(--white);
  box-shadow: 0 0 0 3px rgba(241, 202, 47, 0.15);
}

.admin-navbar-search-button {
  position: absolute;
  left: 0;
  top: 0;
  height: 34px;
  width: 34px;
  background: transparent;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-muted);
  cursor: pointer;
  transition: color var(--transition-fast) ease;
}

.admin-navbar-search-button:hover {
  color: var(--text-color);
}

/* Navbar Button */
.admin-navbar-btn {
  height: 34px;
  padding: 0 var(--spacing-3);
  font-size: var(--font-size-sm);
}

/* Navbar Divider */
.admin-navbar-divider {
  width: 1px;
  height: 24px;
  background-color: var(--border-color);
  margin: 0 var(--spacing-2);
}

/* Navbar Notification */
.admin-navbar-notification {
  position: relative;
}

.admin-navbar-notification-btn {
  background: transparent;
  border: none;
  color: var(--text-color);
  font-size: var(--font-size-lg);
  cursor: pointer;
  padding: var(--spacing-1);
  border-radius: var(--radius-full);
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  transition: all var(--transition-fast) ease;
}

.admin-navbar-notification-btn:hover {
  background-color: var(--gray-100);
  color: var(--primary-color);
}

.admin-navbar-notification-badge {
  position: absolute;
  top: 0;
  right: 0;
  min-width: 16px;
  height: 16px;
  padding: 0 var(--spacing-1);
  font-size: 10px;
  font-weight: var(--font-weight-bold);
  line-height: 1;
  border-radius: var(--radius-full);
  background-color: var(--danger-color);
  color: var(--white);
  display: flex;
  align-items: center;
  justify-content: center;
  transform: translate(25%, -25%);
}

/* Navbar with Tabs */
.admin-navbar-tabs {
  height: 100%;
}

.admin-navbar-tabs .admin-navbar-link {
  padding: 0 var(--spacing-4);
}

/* Navbar Mega Menu */
.admin-navbar-mega {
  position: static;
}

.admin-navbar-mega-menu {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  z-index: var(--z-index-dropdown);
  display: none;
  background-color: var(--white);
  border: 1px solid var(--border-color);
  border-top: none;
  box-shadow: var(--shadow-md);
  padding: var(--spacing-4);
}

.admin-navbar-mega.show .admin-navbar-mega-menu {
  display: block;
}

.admin-navbar-mega-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: var(--spacing-4);
}

.admin-navbar-mega-column {
  display: flex;
  flex-direction: column;
}

.admin-navbar-mega-title {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-semibold);
  color: var(--text-dark);
  margin-bottom: var(--spacing-2);
  padding-bottom: var(--spacing-1);
  border-bottom: 1px solid var(--border-light);
}

.admin-navbar-mega-list {
  list-style: none;
  margin: 0;
  padding: 0;
}

.admin-navbar-mega-item {
  margin-bottom: var(--spacing-1);
}

.admin-navbar-mega-link {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  padding: var(--spacing-1) 0;
  font-size: var(--font-size-sm);
  color: var(--text-color);
  text-decoration: none;
  transition: color var(--transition-fast) ease;
}

.admin-navbar-mega-link:hover {
  color: var(--primary-color);
}

.admin-navbar-mega-icon {
  font-size: var(--font-size-base);
  color: var(--text-light);
}

/* Responsive Styles */
@media (max-width: 992px) {
  .admin-navbar-search {
    width: 150px;
  }
  
  .admin-navbar-link {
    padding: 0 var(--spacing-2);
  }
}

@media (max-width: 768px) {
  .admin-navbar {
    padding: 0 var(--spacing-3);
    height: 44px;
  }
  
  .admin-navbar-brand {
    margin-right: var(--spacing-2);
    font-size: var(--font-size-base);
  }
  
  .admin-navbar-logo {
    height: 24px;
  }
  
  .admin-navbar-search {
    display: none;
  }
  
  .admin-navbar-nav {
    display: none;
  }
  
  .admin-navbar-nav.show {
    display: flex;
    flex-direction: column;
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background-color: var(--white);
    border-bottom: 1px solid var(--border-color);
    box-shadow: var(--shadow-md);
    z-index: var(--z-index-dropdown);
    height: auto;
    align-items: stretch;
  }
  
  .admin-navbar-item {
    height: auto;
  }
  
  .admin-navbar-link {
    height: auto;
    padding: var(--spacing-3) var(--spacing-4);
  }
  
  .admin-navbar-dropdown-menu {
    position: static;
    box-shadow: none;
    border: none;
    border-radius: 0;
    border-left: 2px solid var(--primary-color);
    margin-left: var(--spacing-4);
    margin-bottom: var(--spacing-2);
  }
  
  .admin-navbar-mega-menu {
    position: static;
    border: none;
    box-shadow: none;
  }
  
  .admin-navbar-mega-grid {
    grid-template-columns: 1fr;
    gap: var(--spacing-3);
  }
  
  .admin-navbar-toggle {
    display: flex;
    align-items: center;
    justify-content: center;
    background: transparent;
    border: none;
    color: var(--text-color);
    font-size: var(--font-size-lg);
    cursor: pointer;
    padding: var(--spacing-1);
    margin-left: auto;
  }
}
