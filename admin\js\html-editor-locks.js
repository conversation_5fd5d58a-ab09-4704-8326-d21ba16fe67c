/**
 * HTML Editor - File Locking
 *
 * Handles file locking functionality for the HTML editor
 */

// Acquire a lock on a file
function acquireLock(filePath) {
    return fetch('ajax/html_editor.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
            'Accept': 'application/json'
        },
        body: 'action=acquire_lock&file_path=' + encodeURIComponent(filePath)
    })
    .then(response => {
        if (!response.ok) {
            throw new Error('Network response was not ok: ' + response.statusText);
        }
        return response.text().then(text => {
            try {
                // Check if the response contains HTML and JSON
                if (text.includes('<!DOCTYPE html>') && text.includes('{"success":')) {
                    // Extract the JSON part from the response
                    const jsonStart = text.indexOf('{');
                    if (jsonStart !== -1) {
                        const jsonPart = text.substring(jsonStart);
                        return JSON.parse(jsonPart);
                    }
                }
                // Regular JSON parsing
                return JSON.parse(text);
            } catch (e) {
                console.error('Error parsing JSON response:', text);
                // Try to extract any JSON-like structure from the response
                const jsonMatch = text.match(/\{.*\}/);
                if (jsonMatch) {
                    try {
                        return JSON.parse(jsonMatch[0]);
                    } catch (innerError) {
                        console.error('Failed to extract JSON from response');
                    }
                }
                throw new Error('Invalid JSON response from server');
            }
        });
    })
    .then(data => {
        if (data.success) {
            // Set lock acquired flag
            window.lockAcquired = true;
            // Update UI to show lock status
            updateLockStatus(true, null);
            return true;
        } else {
            // Reset lock acquired flag
            window.lockAcquired = false;
            // Show locked by message
            updateLockStatus(false, data.locked_by);
            return false;
        }
    })
    .catch(error => {
        console.error('Error acquiring lock:', error);
        return false;
    });
}

// Extend an existing lock
function extendLock(filePath) {
    return fetch('ajax/html_editor.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
            'Accept': 'application/json'
        },
        body: 'action=extend_lock&file_path=' + encodeURIComponent(filePath)
    })
    .then(response => {
        if (!response.ok) {
            throw new Error('Network response was not ok: ' + response.statusText);
        }
        return response.text().then(text => {
            try {
                // Check if the response contains HTML and JSON
                if (text.includes('<!DOCTYPE html>') && text.includes('{"success":')) {
                    // Extract the JSON part from the response
                    const jsonStart = text.indexOf('{');
                    if (jsonStart !== -1) {
                        const jsonPart = text.substring(jsonStart);
                        return JSON.parse(jsonPart);
                    }
                }
                // Regular JSON parsing
                return JSON.parse(text);
            } catch (e) {
                console.error('Error parsing JSON response:', text);
                // Try to extract any JSON-like structure from the response
                const jsonMatch = text.match(/\{.*\}/);
                if (jsonMatch) {
                    try {
                        return JSON.parse(jsonMatch[0]);
                    } catch (innerError) {
                        console.error('Failed to extract JSON from response');
                    }
                }
                throw new Error('Invalid JSON response from server');
            }
        });
    })
    .then(data => {
        if (data.success) {
            // Lock extended successfully
            return true;
        } else {
            console.error('Failed to extend lock:', data.message);
            return false;
        }
    })
    .catch(error => {
        console.error('Error extending lock:', error);
        return false;
    });
}

// Release a lock on a file
function releaseLock(filePath) {
    // If no file path is provided, return immediately
    if (!filePath) {
        console.warn('No file path provided to releaseLock function');
        return Promise.resolve(false);
    }

    console.log('Attempting to release lock for:', filePath);

    // Reset lock status in UI immediately to prevent UI getting stuck
    // This ensures the user can continue working even if the server request fails
    window.lockAcquired = false;
    updateLockStatus(false, null);

    // Use try-catch to handle potential network errors
    try {
        return fetch('ajax/html_editor.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
                'Accept': 'application/json'
            },
            body: 'action=release_lock&file_path=' + encodeURIComponent(filePath),
            // Add timeout to prevent hanging requests
            signal: AbortSignal.timeout(5000) // 5 second timeout
        })
    .then(response => {
        if (!response.ok) {
            throw new Error('Network response was not ok: ' + response.statusText);
        }
        return response.text().then(text => {
            try {
                // Check if the response contains HTML and JSON
                if (text.includes('<!DOCTYPE html>') && text.includes('{"success":')) {
                    // Extract the JSON part from the response
                    const jsonStart = text.indexOf('{');
                    if (jsonStart !== -1) {
                        const jsonPart = text.substring(jsonStart);
                        return JSON.parse(jsonPart);
                    }
                }
                // Regular JSON parsing
                return JSON.parse(text);
            } catch (e) {
                console.error('Error parsing JSON response:', text);
                // Even if we can't parse the response, assume success to avoid blocking the user
                return { success: true, message: 'Assumed success despite parsing error' };
            }
        });
    })
    .then(data => {
        if (data.success) {
            // Reset lock acquired flag
            window.lockAcquired = false;
            // Update UI to show lock released
            updateLockStatus(false, null);
            console.log('Lock released successfully');
            return true;
        } else {
            console.error('Failed to release lock:', data.message);
            return false;
        }
    })
    .catch(error => {
        console.error('Error releasing lock:', error);
        // We've already reset the lock status in the UI above, so just log the error
        console.log('Lock status already reset before the error');
        return false;
    });
    } catch (error) {
        // Handle any errors that might occur when creating the fetch request
        console.error('Error creating fetch request for lock release:', error);
        // We've already reset the lock status in the UI above
        return Promise.resolve(false);
    }
}

// Force release a lock (admin only)
function forceReleaseLock(filePath) {
    return fetch('ajax/html_editor.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
            'Accept': 'application/json'
        },
        body: 'action=force_release_lock&file_path=' + encodeURIComponent(filePath)
    })
    .then(response => {
        if (!response.ok) {
            throw new Error('Network response was not ok: ' + response.statusText);
        }
        return response.text().then(text => {
            try {
                // Check if the response contains HTML and JSON
                if (text.includes('<!DOCTYPE html>') && text.includes('{"success":')) {
                    // Extract the JSON part from the response
                    const jsonStart = text.indexOf('{');
                    if (jsonStart !== -1) {
                        const jsonPart = text.substring(jsonStart);
                        return JSON.parse(jsonPart);
                    }
                }
                // Regular JSON parsing
                return JSON.parse(text);
            } catch (e) {
                console.error('Error parsing JSON response:', text);
                // Try to extract any JSON-like structure from the response
                const jsonMatch = text.match(/\{.*\}/);
                if (jsonMatch) {
                    try {
                        return JSON.parse(jsonMatch[0]);
                    } catch (innerError) {
                        console.error('Failed to extract JSON from response');
                    }
                }
                throw new Error('Invalid JSON response from server');
            }
        });
    })
    .then(data => {
        if (data.success) {
            // Update UI to show lock released
            updateLockStatus(false, null);
            return true;
        } else {
            console.error('Failed to force release lock:', data.message);
            return false;
        }
    })
    .catch(error => {
        console.error('Error force releasing lock:', error);
        return false;
    });
}

// Check if a file is locked
function checkLock(filePath) {
    return fetch('ajax/html_editor.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
            'Accept': 'application/json'
        },
        body: 'action=check_lock&file_path=' + encodeURIComponent(filePath)
    })
    .then(response => {
        if (!response.ok) {
            throw new Error('Network response was not ok: ' + response.statusText);
        }
        return response.text().then(text => {
            try {
                // Check if the response contains HTML and JSON
                if (text.includes('<!DOCTYPE html>') && text.includes('{"success":')) {
                    // Extract the JSON part from the response
                    const jsonStart = text.indexOf('{');
                    if (jsonStart !== -1) {
                        const jsonPart = text.substring(jsonStart);
                        return JSON.parse(jsonPart);
                    }
                }
                // Regular JSON parsing
                return JSON.parse(text);
            } catch (e) {
                console.error('Error parsing JSON response:', text);
                // Try to extract any JSON-like structure from the response
                const jsonMatch = text.match(/\{.*\}/);
                if (jsonMatch) {
                    try {
                        return JSON.parse(jsonMatch[0]);
                    } catch (innerError) {
                        console.error('Failed to extract JSON from response');
                    }
                }
                throw new Error('Invalid JSON response from server');
            }
        });
    })
    .then(data => {
        if (data.locked) {
            // File is locked
            if (data.locked_by_current_user) {
                // Locked by current user
                window.lockAcquired = true;
                updateLockStatus(true, null);
            } else {
                // Locked by another user
                window.lockAcquired = false;
                updateLockStatus(false, data.locked_by);
            }
            return data;
        } else {
            // File is not locked
            updateLockStatus(false, null);
            return null;
        }
    })
    .catch(error => {
        console.error('Error checking lock:', error);
        return null;
    });
}

// Update lock status in UI
function updateLockStatus(isLockedByCurrentUser, lockedBy) {
    console.log("Updating lock status:", isLockedByCurrentUser, lockedBy, window.lockAcquired);

    const lockStatus = document.getElementById('lockStatus');
    const saveButton = document.querySelector('button[name="save_file"]');
    const unlockButton = document.getElementById('unlockFileBtn');
    const forceUnlockButton = document.getElementById('forceUnlockBtn');

    // Override with window.lockAcquired if it's true
    if (window.lockAcquired) {
        isLockedByCurrentUser = true;
    }

    if (lockStatus) {
        if (isLockedByCurrentUser) {
            // Locked by current user
            lockStatus.innerHTML = '<i class="fas fa-lock"></i> You have locked this file for editing';
            lockStatus.className = 'lock-status locked-by-me';

            // Enable save button
            if (saveButton) {
                saveButton.disabled = false;
            }

            // Show unlock button with green color
            if (unlockButton) {
                unlockButton.style.display = 'inline-block';
                unlockButton.className = 'btn btn-success btn-sm';
            }

            console.log("Showing unlock button because file is locked by current user");
        } else if (lockedBy) {
            // Locked by another user
            lockStatus.innerHTML = `<i class="fas fa-lock"></i> This file is locked by ${lockedBy}`;
            lockStatus.className = 'lock-status locked-by-other';

            // Disable save button
            if (saveButton) {
                saveButton.disabled = true;
            }

            // Hide unlock button
            if (unlockButton) {
                unlockButton.style.display = 'none';
            }

            // Show force unlock button for admins
            if (forceUnlockButton && isAdmin) {
                forceUnlockButton.style.display = 'inline-block';
            }
        } else {
            // Not locked
            lockStatus.innerHTML = '<i class="fas fa-unlock"></i> File is not locked';
            lockStatus.className = 'lock-status not-locked';

            // Enable save button
            if (saveButton) {
                saveButton.disabled = false;
            }

            // Hide unlock button
            if (unlockButton) {
                unlockButton.style.display = 'none';
            }

            // Hide force unlock button
            if (forceUnlockButton) {
                forceUnlockButton.style.display = 'none';
            }
        }
    }
}
