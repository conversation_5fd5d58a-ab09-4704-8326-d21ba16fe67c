/**
 * Admin Dashboard Layout CSS
 * 
 * This file contains styles for the dashboard layout structure.
 */

/* Dashboard Container */
.dashboard-container {
  display: grid;
  grid-template-columns: 1fr;
  gap: var(--spacing-6);
}

/* Dashboard Header */
.dashboard-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--spacing-6);
}

.dashboard-title {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-bold);
  color: var(--text-dark);
  margin: 0;
}

.dashboard-subtitle {
  font-size: var(--font-size-base);
  color: var(--text-light);
  margin: var(--spacing-1) 0 0;
}

.dashboard-actions {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
}

/* Dashboard Stats */
.dashboard-stats {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: var(--spacing-4);
  margin-bottom: var(--spacing-6);
}

/* Dashboard Quick Actions */
.dashboard-quick-actions {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: var(--spacing-4);
  margin-bottom: var(--spacing-6);
}

/* Dashboard Charts */
.dashboard-charts {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: var(--spacing-4);
  margin-bottom: var(--spacing-6);
}

/* Dashboard Recent Activity */
.dashboard-activity {
  margin-bottom: var(--spacing-6);
}

/* Dashboard Recent News */
.dashboard-news {
  margin-bottom: var(--spacing-6);
}

/* Dashboard Tasks */
.dashboard-tasks {
  margin-bottom: var(--spacing-6);
}

/* Dashboard Calendar */
.dashboard-calendar {
  margin-bottom: var(--spacing-6);
}

/* Dashboard Messages */
.dashboard-messages {
  margin-bottom: var(--spacing-6);
}

/* Dashboard Notifications */
.dashboard-notifications {
  margin-bottom: var(--spacing-6);
}

/* Dashboard Grid Layouts */
.dashboard-grid-2 {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: var(--spacing-4);
  margin-bottom: var(--spacing-6);
}

.dashboard-grid-3 {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: var(--spacing-4);
  margin-bottom: var(--spacing-6);
}

.dashboard-grid-4 {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: var(--spacing-4);
  margin-bottom: var(--spacing-6);
}

.dashboard-grid-2-1 {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: var(--spacing-4);
  margin-bottom: var(--spacing-6);
}

.dashboard-grid-1-2 {
  display: grid;
  grid-template-columns: 1fr 2fr;
  gap: var(--spacing-4);
  margin-bottom: var(--spacing-6);
}

.dashboard-grid-1-1-1 {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: var(--spacing-4);
  margin-bottom: var(--spacing-6);
}

/* Dashboard Section */
.dashboard-section {
  margin-bottom: var(--spacing-6);
}

.dashboard-section:last-child {
  margin-bottom: 0;
}

.dashboard-section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--spacing-4);
}

.dashboard-section-title {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  color: var(--text-dark);
  margin: 0;
}

.dashboard-section-subtitle {
  font-size: var(--font-size-sm);
  color: var(--text-light);
  margin: var(--spacing-1) 0 0;
}

.dashboard-section-actions {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
}

/* Dashboard Card */
.dashboard-card {
  background-color: var(--white);
  border-radius: var(--card-border-radius);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--border-color);
  overflow: hidden;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.dashboard-card-header {
  padding: var(--spacing-4);
  border-bottom: 1px solid var(--border-color);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.dashboard-card-title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--text-dark);
  margin: 0;
}

.dashboard-card-subtitle {
  font-size: var(--font-size-sm);
  color: var(--text-light);
  margin: var(--spacing-1) 0 0;
}

.dashboard-card-actions {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
}

.dashboard-card-body {
  padding: var(--spacing-4);
  flex: 1;
  overflow: auto;
}

.dashboard-card-footer {
  padding: var(--spacing-4);
  border-top: 1px solid var(--border-color);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

/* Dashboard Welcome Card */
.dashboard-welcome {
  background-color: var(--primary-very-light);
  border-radius: var(--card-border-radius);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--primary-light);
  padding: var(--spacing-6);
  margin-bottom: var(--spacing-6);
  position: relative;
  overflow: hidden;
}

.dashboard-welcome-content {
  position: relative;
  z-index: 1;
  max-width: 600px;
}

.dashboard-welcome-title {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-bold);
  color: var(--text-dark);
  margin: 0 0 var(--spacing-2);
}

.dashboard-welcome-text {
  font-size: var(--font-size-base);
  color: var(--text-color);
  margin: 0 0 var(--spacing-4);
}

.dashboard-welcome-actions {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
}

.dashboard-welcome-decoration {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  width: 30%;
  background-image: url('../../../images/welcome-decoration.svg');
  background-repeat: no-repeat;
  background-position: center right;
  background-size: contain;
  opacity: 0.2;
}

/* Dashboard Stat Card */
.dashboard-stat-card {
  background-color: var(--white);
  border-radius: var(--card-border-radius);
  box-shadow: var(--shadow-sm);
  padding: var(--spacing-4);
  display: flex;
  flex-direction: column;
  border: 1px solid var(--border-color);
  transition: box-shadow var(--transition-fast) ease, transform var(--transition-fast) ease;
}

.dashboard-stat-card:hover {
  box-shadow: var(--shadow-md);
  transform: translateY(-2px);
}

.dashboard-stat-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--spacing-2);
}

.dashboard-stat-title {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--text-light);
  margin: 0;
}

.dashboard-stat-icon {
  width: 40px;
  height: 40px;
  border-radius: var(--radius-md);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--font-size-xl);
}

.dashboard-stat-icon.primary {
  background-color: var(--primary-very-light);
  color: var(--primary-color);
}

.dashboard-stat-icon.success {
  background-color: rgba(16, 185, 129, 0.1);
  color: var(--success-color);
}

.dashboard-stat-icon.warning {
  background-color: rgba(245, 158, 11, 0.1);
  color: var(--warning-color);
}

.dashboard-stat-icon.danger {
  background-color: rgba(239, 68, 68, 0.1);
  color: var(--danger-color);
}

.dashboard-stat-value {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-bold);
  color: var(--text-dark);
  margin: var(--spacing-1) 0;
}

.dashboard-stat-change {
  font-size: var(--font-size-xs);
  display: flex;
  align-items: center;
  gap: var(--spacing-1);
}

.dashboard-stat-change.positive {
  color: var(--success-color);
}

.dashboard-stat-change.negative {
  color: var(--danger-color);
}

/* Responsive Styles */
@media (max-width: 1200px) {
  .dashboard-stats {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .dashboard-charts {
    grid-template-columns: 1fr;
  }
  
  .dashboard-grid-4 {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .dashboard-header {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-3);
  }
  
  .dashboard-stats {
    grid-template-columns: 1fr;
  }
  
  .dashboard-grid-2,
  .dashboard-grid-3,
  .dashboard-grid-2-1,
  .dashboard-grid-1-2,
  .dashboard-grid-1-1-1 {
    grid-template-columns: 1fr;
  }
  
  .dashboard-welcome {
    padding: var(--spacing-4);
  }
  
  .dashboard-welcome-decoration {
    display: none;
  }
}
