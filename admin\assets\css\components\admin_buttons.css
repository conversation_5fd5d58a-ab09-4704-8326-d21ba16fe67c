/**
 * Admin Buttons CSS
 *
 * This file contains button styles for the admin panel.
 * The buttons use the black and yellow brand colors.
 */

/* Base Button */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: flex-start;
  padding: var(--spacing-1) var(--spacing-3);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  line-height: 1.5;
  text-align: center;
  text-decoration: none;
  vertical-align: middle;
  cursor: pointer;
  user-select: none;
  border: 1px solid transparent;
  border-radius: var(--radius-md);
  transition: all var(--transition-fast) ease;
  position: relative;
  overflow: hidden;
  gap: var(--spacing-2);
  height: auto;
  min-height: 36px;
  letter-spacing: 0.01em;
  white-space: nowrap;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

/* Button with icon */
.btn i {
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

/* Button hover effect */
.btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

/* Button active effect */
.btn:active {
  transform: translateY(0);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.btn:focus {
  outline: 0;
  box-shadow: 0 0 0 3px rgba(241, 202, 47, 0.25);
}

.btn:disabled,
.btn.disabled {
  opacity: 0.65;
  pointer-events: none;
  transform: none;
  box-shadow: none;
}

/* Button Sizes */
.btn-sm {
  padding: var(--spacing-1) var(--spacing-2);
  font-size: var(--font-size-xs);
  border-radius: var(--radius-sm);
  min-height: 30px;
  gap: var(--spacing-1);
}

.btn-lg {
  padding: var(--spacing-2) var(--spacing-4);
  font-size: var(--font-size-base);
  border-radius: var(--radius-lg);
  min-height: 44px;
  font-weight: var(--font-weight-semibold);
  letter-spacing: 0.02em;
}

/* Button Variants */
.btn-primary {
  color: var(--secondary-dark);
  background-color: var(--primary-color);
  border-color: var(--primary-dark);
  font-weight: var(--font-weight-semibold);
  border-width: 1px;
}

.btn-primary:hover {
  color: var(--secondary-dark);
  background-color: var(--primary-dark);
  border-color: var(--primary-darker);
}

.btn-primary:active {
  background-color: var(--primary-dark);
  border-color: var(--primary-darker);
}

.btn-primary:focus {
  box-shadow: 0 0 0 3px rgba(241, 202, 47, 0.4);
  outline: none;
}

.btn-secondary {
  color: var(--white);
  background-color: var(--secondary-color);
  border-color: var(--secondary-color);
  background-image: linear-gradient(to bottom, var(--secondary-light), var(--secondary-color));
  text-shadow: 0 1px 0 rgba(0, 0, 0, 0.1);
}

.btn-secondary:hover {
  color: var(--white);
  background-color: var(--secondary-dark);
  border-color: var(--secondary-dark);
  background-image: linear-gradient(to bottom, var(--secondary-color), var(--secondary-dark));
}

.btn-secondary:active {
  background-image: none;
  background-color: var(--secondary-dark);
}

.btn-secondary:focus {
  box-shadow: 0 0 0 3px rgba(44, 62, 80, 0.4);
}

.btn-success {
  color: var(--white);
  background-color: var(--success-color);
  border-color: var(--success-color);
}

.btn-success:hover {
  color: var(--white);
  background-color: var(--success-dark);
  border-color: var(--success-dark);
}

.btn-success:focus {
  box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.4);
}

.btn-danger {
  color: var(--white);
  background-color: var(--danger-color);
  border-color: var(--danger-color);
}

.btn-danger:hover {
  color: var(--white);
  background-color: var(--danger-dark);
  border-color: var(--danger-dark);
}

.btn-danger:focus {
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.4);
}

.btn-warning {
  color: var(--secondary-dark);
  background-color: var(--warning-color);
  border-color: var(--warning-color);
}

.btn-warning:hover {
  color: var(--secondary-dark);
  background-color: var(--warning-dark);
  border-color: var(--warning-dark);
}

.btn-warning:focus {
  box-shadow: 0 0 0 3px rgba(245, 158, 11, 0.4);
}

.btn-info {
  color: var(--white);
  background-color: var(--info-color);
  border-color: var(--info-color);
}

.btn-info:hover {
  color: var(--white);
  background-color: var(--info-dark);
  border-color: var(--info-dark);
}

.btn-info:focus {
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.4);
}

.btn-light {
  color: var(--text-dark);
  background-color: var(--gray-100);
  border-color: var(--gray-100);
}

.btn-light:hover {
  color: var(--text-dark);
  background-color: var(--gray-200);
  border-color: var(--gray-200);
}

.btn-light:focus {
  box-shadow: 0 0 0 3px rgba(243, 244, 246, 0.4);
}

.btn-dark {
  color: var(--white);
  background-color: var(--gray-800);
  border-color: var(--gray-800);
}

.btn-dark:hover {
  color: var(--white);
  background-color: var(--gray-900);
  border-color: var(--gray-900);
}

.btn-dark:focus {
  box-shadow: 0 0 0 3px rgba(31, 41, 55, 0.4);
}

/* Outline Buttons */
.btn-outline-primary {
  color: var(--primary-color);
  background-color: transparent;
  border-color: var(--primary-color);
  box-shadow: none;
}

.btn-outline-primary:hover {
  color: var(--secondary-dark);
  background-color: var(--primary-color);
  border-color: var(--primary-color);
  background-image: linear-gradient(to bottom, var(--primary-light), var(--primary-color));
}

.btn-outline-primary:active {
  background-image: none;
  background-color: var(--primary-dark);
}

.btn-outline-secondary {
  color: var(--secondary-color);
  background-color: transparent;
  border-color: var(--secondary-color);
}

.btn-outline-secondary:hover {
  color: var(--white);
  background-color: var(--secondary-color);
  border-color: var(--secondary-color);
}

.btn-outline-success {
  color: var(--success-color);
  background-color: transparent;
  border-color: var(--success-color);
}

.btn-outline-success:hover {
  color: var(--white);
  background-color: var(--success-color);
  border-color: var(--success-color);
}

.btn-outline-danger {
  color: var(--danger-color);
  background-color: transparent;
  border-color: var(--danger-color);
}

.btn-outline-danger:hover {
  color: var(--white);
  background-color: var(--danger-color);
  border-color: var(--danger-color);
}

.btn-outline-warning {
  color: var(--warning-color);
  background-color: transparent;
  border-color: var(--warning-color);
}

.btn-outline-warning:hover {
  color: var(--secondary-dark);
  background-color: var(--warning-color);
  border-color: var(--warning-color);
}

.btn-outline-info {
  color: var(--info-color);
  background-color: transparent;
  border-color: var(--info-color);
}

.btn-outline-info:hover {
  color: var(--white);
  background-color: var(--info-color);
  border-color: var(--info-color);
}

.btn-outline-light {
  color: var(--gray-100);
  background-color: transparent;
  border-color: var(--gray-100);
}

.btn-outline-light:hover {
  color: var(--text-dark);
  background-color: var(--gray-100);
  border-color: var(--gray-100);
}

.btn-outline-dark {
  color: var(--gray-800);
  background-color: transparent;
  border-color: var(--gray-800);
}

.btn-outline-dark:hover {
  color: var(--white);
  background-color: var(--gray-800);
  border-color: var(--gray-800);
}

/* Link Button */
.btn-link {
  color: var(--primary-color);
  background-color: transparent;
  border-color: transparent;
  text-decoration: none;
  padding: var(--spacing-1) var(--spacing-2);
}

.btn-link:hover {
  color: var(--primary-dark);
  text-decoration: underline;
}

/* Icon Button */
.btn-icon {
  width: 36px;
  height: 36px;
  padding: 0;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border-radius: var(--radius-full);
  min-height: unset;
  gap: 0;
  transition: all var(--transition-fast) ease;
}

.btn-icon:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.btn-icon:active {
  transform: translateY(0);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.btn-icon.btn-sm {
  width: 30px;
  height: 30px;
}

.btn-icon.btn-lg {
  width: 44px;
  height: 44px;
}

.btn-icon i {
  font-size: var(--font-size-base);
  line-height: 1;
}

.btn-icon.btn-sm i {
  font-size: var(--font-size-sm);
}

.btn-icon.btn-lg i {
  font-size: var(--font-size-lg);
}

/* Icon Button Variants */
.btn-icon.btn-primary,
.btn-icon.btn-secondary,
.btn-icon.btn-success,
.btn-icon.btn-danger,
.btn-icon.btn-warning,
.btn-icon.btn-info {
  background-image: none;
}

/* Rounded Button */
.btn-rounded {
  border-radius: var(--radius-full);
}

/* Block Button */
.btn-block {
  display: flex;
  width: 100%;
  justify-content: flex-start;
}

/* Button Group */
.btn-group {
  display: inline-flex;
  position: relative;
  vertical-align: middle;
}

.btn-group > .btn {
  position: relative;
  flex: 1 1 auto;
}

.btn-group > .btn:not(:first-child) {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
  margin-left: -1px;
}

.btn-group > .btn:not(:last-child) {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}

.btn-group > .btn:hover,
.btn-group > .btn:focus,
.btn-group > .btn:active,
.btn-group > .btn.active {
  z-index: 1;
}

/* Responsive Button Group */
@media (max-width: 768px) {
  .btn-group-responsive {
    flex-direction: column;
    width: 100%;
  }

  .btn-group-responsive > .btn {
    width: 100%;
    border-radius: var(--radius-md) !important;
    margin-left: 0 !important;
    margin-bottom: var(--spacing-2);
  }

  .btn-group-responsive > .btn:not(:first-child) {
    margin-top: -1px;
  }
}

/* Button with Loading State */
.btn-loading {
  position: relative;
  color: transparent !important;
}

.btn-loading::after {
  content: "";
  position: absolute;
  width: 16px;
  height: 16px;
  top: 50%;
  left: 50%;
  margin-top: -8px;
  margin-left: -8px;
  border-radius: 50%;
  border: 2px solid rgba(255, 255, 255, 0.25);
  border-top-color: var(--white);
  animation: btn-loading-spinner 0.6s linear infinite;
}

.btn-loading.btn-outline-primary::after,
.btn-loading.btn-outline-secondary::after,
.btn-loading.btn-outline-success::after,
.btn-loading.btn-outline-danger::after,
.btn-loading.btn-outline-warning::after,
.btn-loading.btn-outline-info::after,
.btn-loading.btn-outline-light::after,
.btn-loading.btn-outline-dark::after,
.btn-loading.btn-link::after {
  border: 2px solid rgba(0, 0, 0, 0.25);
  border-top-color: currentColor;
}

@keyframes btn-loading-spinner {
  to {
    transform: rotate(360deg);
  }
}

/* Subtle Button - Good for dashboard actions */
.btn-subtle {
  background-color: var(--background-light);
  color: var(--text-color);
  border-color: var(--border-light);
  box-shadow: none;
}

.btn-subtle:hover {
  background-color: var(--background-color);
  color: var(--primary-color);
  border-color: var(--primary-very-light);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.btn-subtle:active {
  background-color: var(--primary-very-light);
  color: var(--primary-dark);
  border-color: var(--primary-light);
}

.btn-subtle:focus {
  box-shadow: 0 0 0 2px rgba(241, 202, 47, 0.2);
}

/* Dashboard Action Button */
.btn-dashboard-action {
  padding: var(--spacing-1) var(--spacing-2);
  font-size: var(--font-size-xs);
  min-height: 32px;
  background-color: var(--white);
  color: var(--text-color);
  border-color: var(--border-color);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.btn-dashboard-action:hover {
  background-color: var(--primary-very-light);
  color: var(--primary-color);
  border-color: var(--primary-light);
}

.btn-dashboard-action i {
  font-size: var(--font-size-sm);
  color: var(--text-light);
  transition: color var(--transition-fast) ease;
}

.btn-dashboard-action:hover i {
  color: var(--primary-color);
}

/* Mobile Responsive Buttons */
@media (max-width: 768px) {
  /* Adjust button sizes for better touch targets */
  .btn {
    padding: 8px 16px;
    font-size: 14px;
    min-height: 40px;
  }

  .btn-sm {
    padding: 6px 12px;
    font-size: 13px;
    min-height: 34px;
  }

  .btn-lg {
    padding: 10px 20px;
    font-size: 16px;
    min-height: 48px;
  }

  /* Ensure buttons have enough space between them */
  .btn + .btn {
    margin-left: var(--spacing-2);
  }

  /* Make action buttons more touch-friendly */
  .btn-icon {
    width: 40px;
    height: 40px;
  }

  .btn-icon.btn-sm {
    width: 34px;
    height: 34px;
  }

  .btn-icon.btn-lg {
    width: 48px;
    height: 48px;
  }

  /* Ensure button text doesn't wrap awkwardly */
  .btn {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  /* Adjust button groups for mobile */
  .btn-group {
    display: flex;
    flex-wrap: wrap;
  }

  /* Make dashboard action buttons more visible */
  .btn-dashboard-action {
    min-height: 36px;
    padding: 8px 12px;
    font-size: 13px;
  }
}

@media (max-width: 480px) {
  /* For very small screens, stack buttons in button groups */
  .btn-group:not(.no-stack) {
    flex-direction: column;
    width: 100%;
  }

  .btn-group:not(.no-stack) > .btn {
    width: 100%;
    margin-left: 0 !important;
    margin-bottom: var(--spacing-2);
    border-radius: var(--radius-md) !important;
  }

  .btn-group:not(.no-stack) > .btn:not(:first-child) {
    margin-top: -1px;
  }

  /* Ensure buttons in forms take full width */
  .form-group .btn,
  .form-actions .btn {
    width: 100%;
    margin-bottom: var(--spacing-2);
  }

  /* Adjust spacing for stacked buttons */
  .btn + .btn {
    margin-left: 0;
    margin-top: var(--spacing-2);
  }
}

/* Admin Button Styles */
.admin-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 8px 16px;
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  line-height: 1.5;
  text-align: center;
  text-decoration: none;
  vertical-align: middle;
  cursor: pointer;
  user-select: none;
  border: 1px solid transparent;
  border-radius: var(--radius-md);
  transition: all 0.2s ease;
  gap: 8px;
  min-height: 36px;
}

.admin-btn i {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  font-size: var(--font-size-base);
}

.admin-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.admin-btn:active {
  transform: translateY(0);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.admin-btn.primary {
  color: var(--secondary-dark);
  background-color: var(--primary-color);
  border-color: var(--primary-dark);
}

.admin-btn.primary:hover {
  background-color: var(--primary-dark);
  border-color: var(--primary-darker);
}

.admin-btn.secondary {
  color: var(--white);
  background-color: var(--secondary-color);
  border-color: var(--secondary-dark);
}

.admin-btn.secondary:hover {
  background-color: var(--secondary-dark);
  border-color: var(--secondary-darker);
}

.admin-btn.success {
  color: var(--white);
  background-color: var(--success-color);
  border-color: var(--success-dark);
}

.admin-btn.success:hover {
  background-color: var(--success-dark);
  border-color: var(--success-darker);
}

.admin-btn.warning {
  color: var(--secondary-dark);
  background-color: var(--warning-color);
  border-color: var(--warning-dark);
}

.admin-btn.warning:hover {
  background-color: var(--warning-dark);
  border-color: var(--warning-darker);
}

.admin-btn.danger {
  color: var(--white);
  background-color: var(--danger-color);
  border-color: var(--danger-dark);
}

.admin-btn.danger:hover {
  background-color: var(--danger-dark);
  border-color: var(--danger-darker);
}

.admin-btn.info {
  color: var(--white);
  background-color: var(--info-color);
  border-color: var(--info-dark);
}

.admin-btn.info:hover {
  background-color: var(--info-dark);
  border-color: var(--info-darker);
}