/**
 * Admin Forms CSS
 *
 * This file contains form styles for the admin panel.
 */

/* Form Group */
.form-group {
  margin-bottom: var(--spacing-4);
}

/* Form Label */
.form-label {
  display: inline-block;
  margin-bottom: var(--spacing-2);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--text-dark);
}

.form-label.required::after {
  content: "*";
  color: var(--danger-color);
  margin-left: var(--spacing-1);
}

/* Form Control */
.form-control {
  display: block;
  width: 100%;
  padding: var(--spacing-2) var(--spacing-3);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-normal);
  line-height: 1.5;
  color: var(--text-color);
  background-color: var(--white);
  background-clip: padding-box;
  border: 1px solid var(--border-color);
  border-radius: var(--input-border-radius);
  transition: border-color var(--transition-fast) ease, box-shadow var(--transition-fast) ease;
}

.form-control:focus {
  color: var(--text-dark);
  background-color: var(--white);
  border-color: var(--primary-color);
  outline: 0;
  box-shadow: 0 0 0 3px rgba(241, 202, 47, 0.15);
}

.form-control::placeholder {
  color: var(--text-muted);
  opacity: 1;
}

.form-control:disabled,
.form-control[readonly] {
  background-color: var(--gray-100);
  opacity: 1;
}

/* Form Control Sizes */
.form-control-sm {
  padding: var(--spacing-1) var(--spacing-2);
  font-size: var(--font-size-xs);
  border-radius: calc(var(--input-border-radius) - 1px);
}

.form-control-lg {
  padding: var(--spacing-3) var(--spacing-4);
  font-size: var(--font-size-base);
  border-radius: calc(var(--input-border-radius) + 1px);
}

/* Form Select */
.form-select {
  display: block;
  width: 100%;
  padding: var(--spacing-2) var(--spacing-3);
  padding-right: var(--spacing-8);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-normal);
  line-height: 1.5;
  color: var(--text-color);
  background-color: var(--white);
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3E%3Cpath fill='none' stroke='%23343a40' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M2 5l6 6 6-6'/%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: right var(--spacing-3) center;
  background-size: 16px 12px;
  border: 1px solid var(--border-color);
  border-radius: var(--input-border-radius);
  appearance: none;
  transition: border-color var(--transition-fast) ease, box-shadow var(--transition-fast) ease;
}

.form-select:focus {
  border-color: var(--primary-color);
  outline: 0;
  box-shadow: 0 0 0 3px rgba(241, 202, 47, 0.15);
}

.form-select:disabled {
  background-color: var(--gray-100);
  opacity: 1;
}

/* Form Select Sizes */
.form-select-sm {
  padding-top: var(--spacing-1);
  padding-bottom: var(--spacing-1);
  padding-left: var(--spacing-2);
  font-size: var(--font-size-xs);
  border-radius: calc(var(--input-border-radius) - 1px);
}

.form-select-lg {
  padding-top: var(--spacing-3);
  padding-bottom: var(--spacing-3);
  padding-left: var(--spacing-4);
  font-size: var(--font-size-base);
  border-radius: calc(var(--input-border-radius) + 1px);
}

/* Form Check */
.form-check {
  display: block;
  min-height: 1.5rem;
  padding-left: 1.5rem;
  margin-bottom: var(--spacing-2);
}

.form-check:last-child {
  margin-bottom: 0;
}

.form-check-input {
  width: 1rem;
  height: 1rem;
  margin-top: 0.25rem;
  margin-left: -1.5rem;
  vertical-align: top;
  background-color: var(--white);
  background-repeat: no-repeat;
  background-position: center;
  background-size: contain;
  border: 1px solid var(--border-color);
  appearance: none;
  transition: background-color var(--transition-fast) ease, border-color var(--transition-fast) ease;
}

.form-check-input[type="checkbox"] {
  border-radius: 0.25rem;
}

.form-check-input[type="radio"] {
  border-radius: 50%;
}

.form-check-input:checked {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
}

.form-check-input:checked[type="checkbox"] {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3E%3Cpath fill='%23fff' d='M12.207 4.793a1 1 0 010 1.414l-5 5a1 1 0 01-1.414 0l-2-2a1 1 0 011.414-1.414L6.5 9.086l4.293-4.293a1 1 0 011.414 0z'/%3E%3C/svg%3E");
}

.form-check-input:checked[type="radio"] {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3E%3Ccircle cx='8' cy='8' r='3' fill='%23fff'/%3E%3C/svg%3E");
}

.form-check-input:focus {
  border-color: var(--primary-color);
  outline: 0;
  box-shadow: 0 0 0 3px rgba(241, 202, 47, 0.15);
}

.form-check-input:disabled {
  pointer-events: none;
  filter: none;
  opacity: 0.5;
}

.form-check-input:disabled ~ .form-check-label {
  opacity: 0.5;
}

.form-check-label {
  font-size: var(--font-size-sm);
  color: var(--text-color);
  cursor: pointer;
}

/* Form Check Inline */
.form-check-inline {
  display: inline-flex;
  align-items: center;
  margin-right: var(--spacing-3);
}

/* Form Switch */
.form-switch {
  padding-left: 2.5rem;
}

.form-switch .form-check-input {
  width: 2rem;
  margin-left: -2.5rem;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3E%3Ccircle cx='7' cy='8' r='3' fill='rgba%280, 0, 0, 0.25%29'/%3E%3C/svg%3E");
  background-position: left center;
  border-radius: 2rem;
  transition: background-position var(--transition-fast) ease, background-color var(--transition-fast) ease, border-color var(--transition-fast) ease;
}

.form-switch .form-check-input:checked {
  background-position: right center;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3E%3Ccircle cx='9' cy='8' r='3' fill='%23fff'/%3E%3C/svg%3E");
}

/* Form Range */
.form-range {
  width: 100%;
  height: 1.5rem;
  padding: 0;
  background-color: transparent;
  appearance: none;
}

.form-range:focus {
  outline: 0;
}

.form-range:focus::-webkit-slider-thumb {
  box-shadow: 0 0 0 1px var(--white), 0 0 0 3px rgba(241, 202, 47, 0.25);
}

.form-range::-webkit-slider-thumb {
  width: 1rem;
  height: 1rem;
  margin-top: -0.25rem;
  background-color: var(--primary-color);
  border: 0;
  border-radius: 1rem;
  transition: background-color var(--transition-fast) ease;
  appearance: none;
}

.form-range::-webkit-slider-thumb:active {
  background-color: var(--primary-dark);
}

.form-range::-webkit-slider-runnable-track {
  width: 100%;
  height: 0.5rem;
  color: transparent;
  cursor: pointer;
  background-color: var(--gray-200);
  border-color: transparent;
  border-radius: 1rem;
}

/* Form Text */
.form-text {
  margin-top: var(--spacing-1);
  font-size: var(--font-size-xs);
  color: var(--text-muted);
}

/* Form Validation */
.form-control.is-valid,
.was-validated .form-control:valid {
  border-color: var(--success-color);
  padding-right: calc(1.5em + 0.75rem);
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3E%3Cpath fill='%2310b981' d='M12.207 4.793a1 1 0 010 1.414l-5 5a1 1 0 01-1.414 0l-2-2a1 1 0 011.414-1.414L6.5 9.086l4.293-4.293a1 1 0 011.414 0z'/%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: right calc(0.375em + 0.1875rem) center;
  background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
}

.form-control.is-valid:focus,
.was-validated .form-control:valid:focus {
  border-color: var(--success-color);
  box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.25);
}

.form-control.is-invalid,
.was-validated .form-control:invalid {
  border-color: var(--danger-color);
  padding-right: calc(1.5em + 0.75rem);
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3E%3Cpath fill='%23ef4444' d='M8 0a8 8 0 100 16A8 8 0 008 0zM7 11a1 1 0 112 0 1 1 0 01-2 0zm.5-6.5a.5.5 0 01.5-.5h1a.5.5 0 01.5.5v4a.5.5 0 01-.5.5h-1a.5.5 0 01-.5-.5v-4z'/%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: right calc(0.375em + 0.1875rem) center;
  background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
}

.form-control.is-invalid:focus,
.was-validated .form-control:invalid:focus {
  border-color: var(--danger-color);
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.25);
}

.invalid-feedback {
  display: none;
  width: 100%;
  margin-top: var(--spacing-1);
  font-size: var(--font-size-xs);
  color: var(--danger-color);
}

.valid-feedback {
  display: none;
  width: 100%;
  margin-top: var(--spacing-1);
  font-size: var(--font-size-xs);
  color: var(--success-color);
}

.was-validated .form-control:invalid ~ .invalid-feedback,
.form-control.is-invalid ~ .invalid-feedback {
  display: block;
}

.was-validated .form-control:valid ~ .valid-feedback,
.form-control.is-valid ~ .valid-feedback {
  display: block;
}

/* Form Layout */
.form-row {
  display: flex;
  flex-wrap: wrap;
  margin-right: -15px;
  margin-left: -15px;
}

.form-row > .col,
.form-row > [class*="col-"] {
  padding-right: 15px;
  padding-left: 15px;
}

.form-inline {
  display: flex;
  flex-flow: row wrap;
  align-items: center;
}

.form-inline .form-check {
  width: 100%;
}

@media (min-width: 576px) {
  .form-inline label {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 0;
  }

  .form-inline .form-group {
    display: flex;
    flex: 0 0 auto;
    flex-flow: row wrap;
    align-items: center;
    margin-bottom: 0;
  }

  .form-inline .form-control {
    display: inline-block;
    width: auto;
    vertical-align: middle;
  }

  .form-inline .form-control-plaintext {
    display: inline-block;
  }

  .form-inline .form-check {
    display: flex;
    align-items: center;
    justify-content: center;
    width: auto;
    padding-left: 0;
  }

  .form-inline .form-check-input {
    position: relative;
    flex-shrink: 0;
    margin-top: 0;
    margin-right: 0.25rem;
    margin-left: 0;
  }
}

/* Form Grid */
.form-grid {
  display: grid;
  grid-template-columns: repeat(12, 1fr);
  gap: var(--spacing-4);
}

.form-grid-1 {
  grid-column: span 1;
}

.form-grid-2 {
  grid-column: span 2;
}

.form-grid-3 {
  grid-column: span 3;
}

.form-grid-4 {
  grid-column: span 4;
}

.form-grid-5 {
  grid-column: span 5;
}

.form-grid-6 {
  grid-column: span 6;
}

.form-grid-7 {
  grid-column: span 7;
}

.form-grid-8 {
  grid-column: span 8;
}

.form-grid-9 {
  grid-column: span 9;
}

.form-grid-10 {
  grid-column: span 10;
}

.form-grid-11 {
  grid-column: span 11;
}

.form-grid-12 {
  grid-column: span 12;
}

@media (max-width: 992px) {
  .form-row {
    flex-direction: column;
    margin-right: 0;
    margin-left: 0;
  }

  .form-row > .col,
  .form-row > [class*="col-"] {
    padding-right: 0;
    padding-left: 0;
    width: 100%;
  }

  .form-inline {
    flex-direction: column;
    align-items: flex-start;
  }

  .form-inline .form-group {
    width: 100%;
    margin-bottom: var(--spacing-3);
  }

  .form-inline .form-control {
    width: 100%;
  }
}

@media (max-width: 768px) {
  /* Form grid */
  .form-grid {
    grid-template-columns: 1fr;
    gap: var(--spacing-3);
  }

  .form-grid-1,
  .form-grid-2,
  .form-grid-3,
  .form-grid-4,
  .form-grid-5,
  .form-grid-6,
  .form-grid-7,
  .form-grid-8,
  .form-grid-9,
  .form-grid-10,
  .form-grid-11,
  .form-grid-12 {
    grid-column: span 1;
  }

  /* Form controls */
  .form-group {
    margin-bottom: var(--spacing-3);
  }

  .form-control,
  .form-select {
    font-size: 16px !important; /* Prevent zoom on iOS */
    padding: 10px 12px;
  }

  .form-label {
    font-size: 14px;
    margin-bottom: 6px;
  }

  /* Form check */
  .form-check-inline {
    display: block;
    margin-right: 0;
    margin-bottom: var(--spacing-2);
  }

  /* Form buttons */
  .form-group .admin-btn,
  .form-actions .admin-btn {
    width: 100%;
    margin-bottom: var(--spacing-2);
  }

  .form-actions {
    flex-direction: column;
  }

  /* File inputs */
  .custom-file-label {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  /* Image preview */
  .image-preview {
    max-width: 100%;
    height: auto;
  }

  /* Rich text editor */
  .tox-tinymce {
    min-height: 200px !important;
  }
}

@media (max-width: 480px) {
  .form-control,
  .form-select {
    padding: 8px 10px;
  }

  .form-text {
    font-size: 12px;
  }
}
