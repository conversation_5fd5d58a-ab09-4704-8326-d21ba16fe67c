<?php
/**
 * Get Notifications AJAX Handler
 */

// Start output buffering to prevent any output before JSON
ob_start();

// Enable error logging but disable display
error_reporting(E_ALL);
ini_set('display_errors', 0);
ini_set('log_errors', 1);

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Set proper JSON headers
header('Content-Type: application/json');
header('Cache-Control: no-store, no-cache, must-revalidate, max-age=0');
header('Cache-Control: post-check=0, pre-check=0', false);
header('Pragma: no-cache');

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    echo json_encode([
        'success' => false,
        'message' => 'You must be logged in to perform this action'
    ]);
    exit;
}

try {
    // Include database connection
    require_once '../config.php';
    require_once '../lib/Notifications.php';

    // Initialize notification system
    $notifications = new Notifications($conn);
    
    // Get parameters
    $limit = isset($_GET['limit']) ? (int)$_GET['limit'] : 10;
    $unread_only = isset($_GET['unread_only']) ? (bool)$_GET['unread_only'] : false;
    
    // Get notifications for the current user
    $user_notifications = $notifications->getNotifications($_SESSION['user_id'], $limit, $unread_only);
    $unread_count = $notifications->getUnreadCount($_SESSION['user_id']);
    
    // Format notifications for display
    $formatted_notifications = [];
    foreach ($user_notifications as $notification) {
        $formatted_notifications[] = [
            'id' => $notification['id'],
            'title' => htmlspecialchars($notification['title']),
            'message' => htmlspecialchars($notification['message']),
            'type' => $notification['type'],
            'link' => $notification['link'],
            'is_read' => (bool)$notification['is_read'],
            'created_at' => $notification['created_at'],
            'time_ago' => timeAgo($notification['created_at'])
        ];
    }
    
    // Return the result
    echo json_encode([
        'success' => true,
        'notifications' => $formatted_notifications,
        'unread_count' => $unread_count
    ]);
    
} catch (Exception $e) {
    // Log the error
    error_log('Error in get_notifications.php: ' . $e->getMessage());
    
    // Return a generic error message
    echo json_encode([
        'success' => false,
        'message' => 'An unexpected error occurred',
        'notifications' => [],
        'unread_count' => 0
    ]);
}

/**
 * Convert timestamp to time ago format
 */
function timeAgo($datetime) {
    $time = time() - strtotime($datetime);
    
    if ($time < 60) return 'just now';
    if ($time < 3600) return floor($time/60) . 'm ago';
    if ($time < 86400) return floor($time/3600) . 'h ago';
    if ($time < 2592000) return floor($time/86400) . 'd ago';
    if ($time < 31536000) return floor($time/2592000) . 'mo ago';
    return floor($time/31536000) . 'y ago';
}

// End output buffering and flush
ob_end_flush();
exit;
