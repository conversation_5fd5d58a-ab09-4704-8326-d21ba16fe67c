<?php
/**
 * Save Email Template
 *
 * Handles both AJAX requests and form submissions to save email templates
 */

// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Check if this is an AJAX request
$is_ajax = isset($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) === 'xmlhttprequest';

// Set content type to JSON for AJAX requests
if ($is_ajax) {
    header('Content-Type: application/json');
}

// Function to log messages
function log_template_save($message) {
    $log_file = 'logs/template_save_' . date('Y-m-d') . '.log';
    $log_dir = dirname($log_file);
    if (!is_dir($log_dir)) {
        mkdir($log_dir, 0755, true);
    }
    $timestamp = date('Y-m-d H:i:s');
    file_put_contents($log_file, "[$timestamp] $message" . PHP_EOL, FILE_APPEND);
}

// Error handling to catch any PHP errors
try {
    // Include required files
    require_once 'config.php';
    require_once 'auth.php'; // This will include lib/Auth.php
    require_once 'email_templates.php';

    // Check if user is logged in
    $auth = new Auth($conn);
    if (!$auth->isLoggedIn()) {
        if ($is_ajax) {
            echo json_encode(['success' => false, 'message' => 'Authentication required']);
            exit;
        } else {
            header('Location: login.php');
            exit;
        }
    }

    // Check if user has permission to edit email templates
    if (!$auth->hasPermission('manage_email_templates')) {
        if ($is_ajax) {
            echo json_encode(['success' => false, 'message' => 'You do not have permission to edit email templates']);
            exit;
        } else {
            $_SESSION['error_message'] = 'You do not have permission to edit email templates';
            header('Location: settings.php');
            exit;
        }
    }

    // Check if request is POST
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        if ($is_ajax) {
            echo json_encode(['success' => false, 'message' => 'Invalid request method']);
            exit;
        } else {
            header('Location: settings.php#email-templates');
            exit;
        }
    }

    // Initialize EmailTemplates
    $emailTemplates = new EmailTemplates($conn);

    // Get parameters - handle both AJAX and form submissions
    $template_key = isset($_POST['template_key']) ? $_POST['template_key'] : '';
    $subject = isset($_POST['subject']) ? $_POST['subject'] : '';
    $content = isset($_POST['content']) ? $_POST['content'] : '';
    $action = isset($_POST['action']) ? $_POST['action'] : 'save_template';

    // Debug output to error log
    log_template_save("Request method: " . $_SERVER['REQUEST_METHOD']);
    log_template_save("POST data: " . print_r($_POST, true));
    log_template_save("Template key: $template_key");
    log_template_save("Subject length: " . strlen($subject));
    log_template_save("Content length: " . strlen($content));

    // Validate template data
    if (empty($template_key)) {
        if ($is_ajax) {
            echo json_encode(['success' => false, 'message' => 'Template key cannot be empty']);
            exit;
        } else {
            $_SESSION['error_message'] = 'Template key cannot be empty';
            header('Location: settings.php#email-templates');
            exit;
        }
    }

    if (empty($subject)) {
        if ($is_ajax) {
            echo json_encode(['success' => false, 'message' => 'Subject cannot be empty']);
            exit;
        } else {
            $_SESSION['error_message'] = 'Subject cannot be empty';
            header('Location: edit_template.php?key=' . urlencode($template_key));
            exit;
        }
    }

    if (empty($content)) {
        if ($is_ajax) {
            echo json_encode(['success' => false, 'message' => 'Content cannot be empty']);
            exit;
        } else {
            $_SESSION['error_message'] = 'Content cannot be empty';
            header('Location: edit_template.php?key=' . urlencode($template_key));
            exit;
        }
    }

    // Check if template exists
    $template = $emailTemplates->getTemplate($template_key);
    if (!$template) {
        if ($is_ajax) {
            echo json_encode(['success' => false, 'message' => 'Template not found: ' . $template_key]);
            exit;
        } else {
            $_SESSION['error_message'] = 'Template not found: ' . $template_key;
            header('Location: settings.php#email-templates');
            exit;
        }
    }

    // Update template
    $result = $emailTemplates->updateTemplate($template_key, $subject, $content);

    if ($result) {
        // Log the action if activity_log table exists
        if ($conn->query("SHOW TABLES LIKE 'activity_log'")->num_rows > 0) {
            $user_id = $_SESSION['user_id'];
            $log_sql = "INSERT INTO activity_log (user_id, action, details, ip_address)
                        VALUES (?, ?, ?, ?)";
            $stmt = $conn->prepare($log_sql);
            $details = "Updated email template: " . $template_key;
            $ip = $_SERVER['REMOTE_ADDR'];
            $stmt->bind_param("isss", $user_id, $action, $details, $ip);
            $stmt->execute();
        }

        log_template_save("Template saved successfully");

        if ($is_ajax) {
            echo json_encode(['success' => true, 'message' => 'Template saved successfully']);
            exit;
        } else {
            $_SESSION['success_message'] = 'Template saved successfully!';
            header('Location: edit_template.php?key=' . urlencode($template_key));
            exit;
        }
    } else {
        // Get the database error message
        $error_message = $conn->error ? $conn->error : 'Failed to save template';
        log_template_save("Database error: " . $error_message);

        if ($is_ajax) {
            echo json_encode(['success' => false, 'message' => 'Failed to save template: ' . $error_message]);
            exit;
        } else {
            $_SESSION['error_message'] = 'Failed to save template: ' . $error_message;
            header('Location: edit_template.php?key=' . urlencode($template_key));
            exit;
        }
    }
} catch (Exception $e) {
    // Log the error
    log_template_save("Error in save_template.php: " . $e->getMessage());

    if ($is_ajax) {
        // Return error response for AJAX
        echo json_encode([
            'success' => false,
            'message' => 'An error occurred while processing your request',
            'error' => $e->getMessage()
        ]);
    } else {
        // Redirect with error for form submission
        $_SESSION['error_message'] = 'An error occurred: ' . $e->getMessage();
        header('Location: settings.php#email-templates');
    }
}
exit;
?>
