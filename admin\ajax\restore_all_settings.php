<?php
/**
 * Restore All Settings AJAX Handler
 *
 * This script restores all settings from a backup file.
 */

// Start output buffering to prevent any output before JSON
ob_start();

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Set proper JSON headers
header('Content-Type: application/json');
header('Cache-Control: no-store, no-cache, must-revalidate, max-age=0');
header('Cache-Control: post-check=0, pre-check=0', false);
header('Pragma: no-cache');

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    echo json_encode([
        'success' => false,
        'message' => 'You must be logged in to perform this action'
    ]);
    exit;
}

// Include database connection
require_once '../config.php';

// Include permissions class
require_once '../lib/Permissions.php';

// Initialize permission system
$permissions = new Permissions($conn, $_SESSION['user_id']);

// Check if user has permission to manage settings
if (!$permissions->hasPermission('manage_email_settings')) {
    echo json_encode([
        'success' => false,
        'message' => 'You do not have permission to manage system settings'
    ]);
    exit;
}

// Get JSON data from request
$json_data = file_get_contents('php://input');
$data = json_decode($json_data, true);

if (!$data || !isset($data['settings'])) {
    echo json_encode([
        'success' => false,
        'message' => 'Invalid data format'
    ]);
    exit;
}

// Function to update a setting
function updateSetting($conn, $category, $key, $value, $type = 'text', $display_name = '', $description = '', $options = '', $is_required = 0, $order_index = 99) {
    $category = $conn->real_escape_string($category);
    $key = $conn->real_escape_string($key);
    $value = $conn->real_escape_string($value);
    $type = $conn->real_escape_string($type);
    $display_name = $conn->real_escape_string($display_name);
    $description = $conn->real_escape_string($description);
    $options = $conn->real_escape_string($options);
    $is_required = (int)$is_required;
    $order_index = (int)$order_index;

    // Check if setting exists
    $check_sql = "SELECT id FROM system_settings WHERE category = '$category' AND setting_key = '$key'";
    $check_result = $conn->query($check_sql);

    if ($check_result && $check_result->num_rows > 0) {
        // Setting exists, update it
        $sql = "UPDATE system_settings SET
                setting_value = '$value',
                setting_type = '$type',
                display_name = '$display_name',
                description = '$description',
                options = '$options',
                is_required = $is_required,
                order_index = $order_index
                WHERE category = '$category' AND setting_key = '$key'";
    } else {
        // Setting doesn't exist, insert it
        $sql = "INSERT INTO system_settings
                (category, setting_key, setting_value, setting_type, display_name, description, options, is_required, order_index)
                VALUES
                ('$category', '$key', '$value', '$type', '$display_name', '$description', '$options', $is_required, $order_index)";
    }

    return $conn->query($sql);
}

// Function to update an email template
function updateEmailTemplate($conn, $template_key, $subject, $content, $description = '') {
    try {
        require_once '../email_templates.php';
        $emailTemplates = new EmailTemplates($conn);

        // Check if template exists
        $template = $emailTemplates->getTemplate($template_key);

        if ($template) {
            // Update existing template
            return $emailTemplates->updateTemplate($template_key, $subject, $content, $description);
        } else {
            // Create new template
            return $emailTemplates->createTemplate($template_key, $subject, $content, $description);
        }
    } catch (Exception $e) {
        error_log('Error updating email template: ' . $e->getMessage());
        return false;
    }
}

// Start transaction
$conn->begin_transaction();

try {
    $settings = $data['settings'];
    $success = true;
    $error_message = '';

    // Process regular settings
    foreach ($settings as $category => $category_settings) {
        // Skip email templates, they will be processed separately
        if ($category === 'email_templates') {
            continue;
        }

        foreach ($category_settings as $key => $setting) {
            $value = $setting['value'];
            $type = $setting['type'] ?? 'text';
            $display_name = $setting['display_name'] ?? $key;
            $description = $setting['description'] ?? '';
            $options = $setting['options'] ?? '';
            $is_required = $setting['is_required'] ?? 0;
            $order_index = $setting['order_index'] ?? 99;

            if (!updateSetting($conn, $category, $key, $value, $type, $display_name, $description, $options, $is_required, $order_index)) {
                $success = false;
                $error_message = "Failed to update setting $category.$key: " . $conn->error;
                break 2; // Break out of both loops
            }
        }
    }

    // Process email templates if available
    if ($success && isset($settings['email_templates'])) {
        foreach ($settings['email_templates'] as $template_key => $template) {
            $subject = $template['subject'];
            $content = $template['content'];
            $description = $template['description'] ?? '';

            if (!updateEmailTemplate($conn, $template_key, $subject, $content, $description)) {
                $success = false;
                $error_message = "Failed to update email template $template_key";
                break;
            }
        }
    }

    if ($success) {
        // Commit transaction
        $conn->commit();

        echo json_encode([
            'success' => true,
            'message' => 'All settings restored successfully'
        ], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
    } else {
        // Rollback transaction
        $conn->rollback();

        echo json_encode([
            'success' => false,
            'message' => $error_message
        ], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
    }
} catch (Exception $e) {
    // Rollback transaction
    $conn->rollback();

    echo json_encode([
        'success' => false,
        'message' => 'Error restoring settings: ' . $e->getMessage()
    ], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
}

// Check for JSON encoding errors
if (json_last_error() !== JSON_ERROR_NONE) {
    // Clear any previous output
    ob_clean();

    // Return a simplified error response
    echo json_encode([
        'success' => false,
        'message' => 'JSON encoding error: ' . json_last_error_msg()
    ], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
}

// End output buffering and flush
ob_end_flush();
exit;
