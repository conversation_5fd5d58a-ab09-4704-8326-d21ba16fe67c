/**
 * Admin Tabs CSS
 * 
 * This file contains styles for tabbed navigation in the admin panel.
 */

/* Base Tabs */
.tabs {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: var(--spacing-4);
}

/* Tab List */
.tab-list {
  display: flex;
  flex-wrap: wrap;
  list-style: none;
  margin: 0;
  padding: 0;
  border-bottom: 1px solid var(--border-color);
  width: 100%;
}

/* Tab Item */
.tab-item {
  margin-bottom: -1px;
}

/* Tab Link */
.tab-link {
  display: inline-flex;
  align-items: center;
  padding: var(--spacing-3) var(--spacing-4);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--text-light);
  text-decoration: none;
  border-bottom: 2px solid transparent;
  transition: all var(--transition-fast) ease;
  cursor: pointer;
}

.tab-link:hover {
  color: var(--text-dark);
}

.tab-link.active {
  color: var(--primary-color);
  border-bottom-color: var(--primary-color);
}

/* Tab Link with Icon */
.tab-link-with-icon {
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-2);
}

.tab-link-icon {
  font-size: var(--font-size-base);
}

/* Tab Content */
.tab-content {
  padding-top: var(--spacing-4);
}

.tab-pane {
  display: none;
}

.tab-pane.active {
  display: block;
}

/* Tabs Variants */
/* Pills Tabs */
.tabs-pills .tab-list {
  border-bottom: none;
  gap: var(--spacing-2);
}

.tabs-pills .tab-link {
  border: 1px solid transparent;
  border-radius: var(--radius-full);
  padding: var(--spacing-2) var(--spacing-4);
}

.tabs-pills .tab-link:hover {
  background-color: var(--gray-50);
}

.tabs-pills .tab-link.active {
  color: var(--secondary-dark);
  background-color: var(--primary-color);
  border-color: var(--primary-color);
}

/* Underline Tabs */
.tabs-underline .tab-list {
  border-bottom: none;
}

.tabs-underline .tab-link {
  position: relative;
  border-bottom: none;
}

.tabs-underline .tab-link::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 50%;
  width: 0;
  height: 2px;
  background-color: var(--primary-color);
  transition: all var(--transition-fast) ease;
  transform: translateX(-50%);
}

.tabs-underline .tab-link:hover::after {
  width: 20px;
}

.tabs-underline .tab-link.active::after {
  width: 100%;
}

/* Button Tabs */
.tabs-buttons .tab-list {
  border-bottom: none;
  background-color: var(--gray-100);
  padding: var(--spacing-1);
  border-radius: var(--radius-full);
  gap: var(--spacing-1);
}

.tabs-buttons .tab-link {
  border-radius: var(--radius-full);
  padding: var(--spacing-2) var(--spacing-4);
  border: none;
}

.tabs-buttons .tab-link.active {
  color: var(--secondary-dark);
  background-color: var(--primary-color);
}

/* Vertical Tabs */
.tabs-vertical {
  display: flex;
  flex-wrap: nowrap;
}

.tabs-vertical .tab-list {
  flex-direction: column;
  border-bottom: none;
  border-right: 1px solid var(--border-color);
  width: 200px;
  flex-shrink: 0;
}

.tabs-vertical .tab-item {
  margin-bottom: 0;
  margin-right: -1px;
}

.tabs-vertical .tab-link {
  border-bottom: none;
  border-right: 2px solid transparent;
  padding: var(--spacing-3) var(--spacing-4);
  width: 100%;
  text-align: left;
}

.tabs-vertical .tab-link.active {
  border-right-color: var(--primary-color);
}

.tabs-vertical .tab-content {
  flex: 1;
  padding-top: 0;
  padding-left: var(--spacing-4);
}

/* Tabs with Badge */
.tab-badge {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-width: 18px;
  height: 18px;
  padding: 0 var(--spacing-1);
  font-size: 10px;
  font-weight: var(--font-weight-bold);
  line-height: 1;
  border-radius: var(--radius-full);
  background-color: var(--gray-200);
  color: var(--text-dark);
  margin-left: var(--spacing-2);
}

.tab-link.active .tab-badge {
  background-color: var(--white);
  color: var(--primary-color);
}

.tabs-pills .tab-link.active .tab-badge {
  background-color: var(--primary-dark);
  color: var(--white);
}

/* Tabs with Dropdown */
.tab-dropdown {
  position: relative;
}

.tab-dropdown-toggle {
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-1);
}

.tab-dropdown-toggle::after {
  content: "▼";
  font-size: 8px;
  margin-top: 2px;
}

.tab-dropdown-menu {
  position: absolute;
  top: 100%;
  left: 0;
  z-index: var(--z-index-dropdown);
  display: none;
  min-width: 160px;
  padding: var(--spacing-1) 0;
  margin-top: var(--spacing-1);
  background-color: var(--white);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  box-shadow: var(--shadow-md);
}

.tab-dropdown.show .tab-dropdown-menu {
  display: block;
}

.tab-dropdown-item {
  display: block;
  padding: var(--spacing-2) var(--spacing-4);
  font-size: var(--font-size-sm);
  color: var(--text-color);
  text-decoration: none;
  transition: all var(--transition-fast) ease;
}

.tab-dropdown-item:hover {
  background-color: var(--gray-50);
  color: var(--text-dark);
}

.tab-dropdown-item.active {
  background-color: var(--primary-very-light);
  color: var(--primary-color);
}

/* Tabs with Icons Only */
.tabs-icons .tab-link {
  padding: var(--spacing-3);
}

.tabs-icons .tab-link-text {
  display: none;
}

.tabs-icons .tab-link-icon {
  font-size: var(--font-size-lg);
}

/* Tabs with Icons and Text */
.tabs-icons-text .tab-link {
  flex-direction: column;
  gap: var(--spacing-1);
  padding: var(--spacing-3) var(--spacing-4);
}

.tabs-icons-text .tab-link-icon {
  font-size: var(--font-size-lg);
}

/* Responsive Tabs */
@media (max-width: 768px) {
  .tabs-vertical {
    flex-direction: column;
  }
  
  .tabs-vertical .tab-list {
    flex-direction: row;
    border-right: none;
    border-bottom: 1px solid var(--border-color);
    width: 100%;
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
  }
  
  .tabs-vertical .tab-item {
    margin-right: 0;
    margin-bottom: -1px;
  }
  
  .tabs-vertical .tab-link {
    border-right: none;
    border-bottom: 2px solid transparent;
    white-space: nowrap;
  }
  
  .tabs-vertical .tab-link.active {
    border-right-color: transparent;
    border-bottom-color: var(--primary-color);
  }
  
  .tabs-vertical .tab-content {
    padding-left: 0;
    padding-top: var(--spacing-4);
  }
  
  .tab-list {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
    flex-wrap: nowrap;
  }
  
  .tab-link {
    white-space: nowrap;
  }
}
