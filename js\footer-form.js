/**
 * Footer Contact Form Handler
 *
 * This script handles the submission of the footer contact form via AJAX.
 * It validates the form inputs and displays success/error messages.
 */
document.addEventListener('DOMContentLoaded', function() {
    // Find the footer contact form
    const footerForm = document.getElementById('footer-contact-form');

    // Check for URL parameters that might indicate form submission status
    const urlParams = new URLSearchParams(window.location.search);
    if (urlParams.has('footer_form_status') && footerForm && footerForm.offsetParent !== null) {
        const status = urlParams.get('footer_form_status');
        if (status === 'success') {
            // Find or create the message element
            let formMessage = footerForm.querySelector('.form-message');
            if (!formMessage) {
                formMessage = document.createElement('div');
                formMessage.className = 'form-message';
                formMessage.style.display = 'block';
                formMessage.style.marginTop = '10px';
                formMessage.style.padding = '8px';
                formMessage.style.borderRadius = '4px';
                formMessage.style.backgroundColor = '#d4edda';
                formMessage.style.color = '#155724';
                formMessage.style.border = '1px solid #c3e6cb';
                formMessage.textContent = 'Thank you for your message. We\'ll get back to you soon!';
                footerForm.appendChild(formMessage);

                // Auto-dismiss after 5 seconds
                setTimeout(() => {
                    formMessage.style.opacity = '0';
                    setTimeout(() => {
                        formMessage.style.display = 'none';
                    }, 500);
                }, 5000);
            }
        }
    }

    // If form exists and is visible, add event listener
    if (footerForm && footerForm.offsetParent !== null) {
        // Create message element if it doesn't exist
        if (!footerForm.querySelector('.form-message')) {
            const messageDiv = document.createElement('div');
            messageDiv.className = 'form-message';
            messageDiv.style.display = 'none';
            messageDiv.style.marginTop = '10px';
            messageDiv.style.padding = '8px';
            messageDiv.style.borderRadius = '4px';
            messageDiv.style.transition = 'opacity 0.5s ease-in-out';
            footerForm.appendChild(messageDiv);
        }

        // Add submit event listener
        footerForm.addEventListener('submit', function(e) {
            // Only prevent default if we're using AJAX
            if (window.location.href.includes('.html')) {
                e.preventDefault();
            } else {
                // If we're not using AJAX, let the form submit normally
                return true;
            }

            const formMessage = footerForm.querySelector('.form-message');

            // Basic validation
            const name = footerForm.querySelector('input[name="name"]').value.trim();
            const email = footerForm.querySelector('input[name="email"]').value.trim();
            const message = footerForm.querySelector('textarea[name="message"]').value.trim();

            if (!name || !email || !message) {
                showFooterMessage('error', 'Please fill in all required fields.');
                return;
            }

            // Email validation
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailRegex.test(email)) {
                showFooterMessage('error', 'Please enter a valid email address.');
                return;
            }

            // Show sending message
            showFooterMessage('info', 'Sending your message...');

            // Create form data
            const formData = new FormData(footerForm);

            // Determine the correct path to process-contact.php
            let actionUrl = footerForm.getAttribute('action');

            // Send AJAX request
            fetch(actionUrl, {
                method: 'POST',
                body: formData,
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showFooterMessage('success', data.message);
                    footerForm.reset();
                } else {
                    showFooterMessage('error', data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showFooterMessage('error', 'An error occurred while sending your message. Please try again later.');
            });
        });
    }

    // Function to show message in footer form with auto-dismiss
    function showFooterMessage(type, text) {
        const footerFormElement = document.getElementById('footer-contact-form');
        if (!footerFormElement || footerFormElement.offsetParent === null) return;

        const formMessage = footerFormElement.querySelector('.form-message');
        if (!formMessage) return;

        // Clear any existing timeout
        if (window.footerMessageTimeout) {
            clearTimeout(window.footerMessageTimeout);
        }

        formMessage.style.display = 'block';
        formMessage.style.transition = 'opacity 0.5s ease-in-out';
        formMessage.style.opacity = '1';

        if (type === 'success') {
            formMessage.style.backgroundColor = '#d4edda';
            formMessage.style.color = '#155724';
            formMessage.style.border = '1px solid #c3e6cb';
        } else if (type === 'error') {
            formMessage.style.backgroundColor = '#f8d7da';
            formMessage.style.color = '#721c24';
            formMessage.style.border = '1px solid #f5c6cb';
        } else if (type === 'info') {
            formMessage.style.backgroundColor = '#d1ecf1';
            formMessage.style.color = '#0c5460';
            formMessage.style.border = '1px solid #bee5eb';
        }

        formMessage.textContent = text;

        // Auto-dismiss after 5 seconds for success messages
        if (type === 'success') {
            window.footerMessageTimeout = setTimeout(() => {
                formMessage.style.opacity = '0';
                setTimeout(() => {
                    formMessage.style.display = 'none';
                }, 500);
            }, 5000);
        }
    }
});
