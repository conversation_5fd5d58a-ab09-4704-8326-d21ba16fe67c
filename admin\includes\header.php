<?php
/**
 * Admin Panel Header
 *
 * This file contains the header for the admin panel.
 * It includes the DOCTYPE declaration, HTML head, and the beginning of the body.
 */

// Make sure security wrapper is included first, before any output
if (!defined('SECURITY_WRAPPER_INCLUDED')) {
    require_once 'includes/security.php';
}

// Get current page filename
$current_page = basename($_SERVER['PHP_SELF']);

// Include permissions class if user is logged in
if (isset($_SESSION['user_id'])) {
    require_once 'lib/Permissions.php';
    $permissions = new Permissions($conn, $_SESSION['user_id']);
}

// Start HTML output
ob_start();
?><!DOCTYPE html>
<html lang="en" data-theme="<?php echo isset($user_settings['theme']) ? $user_settings['theme'] : 'light'; ?>">
<head>
    <!-- Force Standards Mode -->
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Expires" content="0">

    <?php
    // Generate base URL for HTML editor and other components
    // This handles both subdirectory installations (like /manageinc/) and document root installations
    $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
    $host = $_SERVER['HTTP_HOST'];
    $script_name = $_SERVER['SCRIPT_NAME'];
    $admin_path = dirname($script_name);

    // Remove '/admin' to get the site root path
    $site_root_path = str_replace('/admin', '', $admin_path);

    // If site_root_path is empty or just '/', we're in document root
    if (empty($site_root_path) || $site_root_path === '/') {
        $site_root = $protocol . '://' . $host;
    } else {
        $site_root = $protocol . '://' . $host . $site_root_path;
    }

    // Ensure trailing slash for consistency
    if (substr($site_root, -1) !== '/') {
        $site_root .= '/';
    }
    ?>
    <meta name="site-base-url" content="<?php echo htmlspecialchars($site_root); ?>">
    <meta name="admin-base-url" content="<?php echo htmlspecialchars($protocol . '://' . $host . $admin_path); ?>">

    <title><?php echo isset($page_title) ? $page_title : 'Admin Panel'; ?> | Manage Inc.</title>

    <?php
    // Better versioning system - use a static version or file modification time
    $css_version = '4.0.0'; // Increment this when CSS changes - ALL COMPONENTS RIGHT ALIGNED
    $js_version = '1.0.2';  // Increment this when JS changes

    // Alternative: Use file modification time for automatic versioning
    function getFileVersion($file) {
        if (file_exists($file)) {
            return filemtime($file);
        }
        return time();
    }
    ?>

    <!-- Load CSS in correct order: Reset first, then base, then components -->
    <link rel="stylesheet" href="assets/css/base/admin_variables.css?v=<?php echo $css_version; ?>">
    <link rel="stylesheet" href="assets/css/base/admin_reset.css?v=<?php echo $css_version; ?>">
    <link rel="stylesheet" href="assets/css/layout/admin_header.css?v=<?php echo $css_version; ?>">
    <link rel="stylesheet" href="assets/css/components/admin_buttons.css?v=<?php echo $css_version; ?>">
    <link rel="stylesheet" href="assets/css/components/admin_forms.css?v=<?php echo $css_version; ?>">

    <!-- Main CSS File (with @import statements) -->
    <link rel="stylesheet" href="assets/css/main.css?v=<?php echo $css_version; ?>">

    <!-- Consolidated CSS File (fallback for hosting providers with @import issues) -->
    <link rel="stylesheet" href="assets/css/consolidated.css?v=<?php echo $css_version; ?>">

    <!-- User Dropdown CSS - Load AFTER everything else to ensure it's not overridden -->
    <link rel="stylesheet" href="assets/css/components/admin_user-dropdown.css?v=<?php echo $css_version; ?>">

    <!-- EMERGENCY CSS - Load LAST to override any conflicts -->
    <link rel="stylesheet" href="assets/css/emergency-dropdown.css?v=<?php echo $css_version; ?>">

    <!-- Dynamic CSS Files -->
    <link rel="stylesheet" href="assets/css/dynamic/font-settings.css.php?v=<?php echo $css_version; ?>">
    <link rel="stylesheet" href="assets/css/dynamic/admin-settings.css.php?v=<?php echo $css_version; ?>">
    <link rel="stylesheet" href="assets/css/dynamic/logo-background.css.php?v=<?php echo $css_version; ?>">

    <!-- JavaScript Files -->
    <script src="js/appearance-settings.js?v=<?php echo $js_version; ?>"></script>
    <script src="js/alert-auto-dismiss.js?v=<?php echo $js_version; ?>"></script>
    <script src="js/dark-mode.js?v=<?php echo $js_version; ?>"></script>
    <script src="js/tooltips.js?v=<?php echo $js_version; ?>"></script>

    <!-- Page Loading Control -->
    <script>
        // Ensure page content loads only after admin header is ready
        document.addEventListener('DOMContentLoaded', function() {
            // Wait for admin header to be fully loaded
            function waitForAdminHeader() {
                const adminHeader = document.querySelector('.admin-topbar');
                const adminSidebar = document.querySelector('.admin-sidebar');

                if (adminHeader && adminSidebar) {
                    // Admin header is ready, show page content
                    document.body.classList.add('admin-header-loaded');

                    // Dispatch custom event to notify other scripts
                    const headerLoadedEvent = new CustomEvent('adminHeaderLoaded');
                    document.dispatchEvent(headerLoadedEvent);
                } else {
                    // Wait a bit more and try again
                    setTimeout(waitForAdminHeader, 50);
                }
            }

            waitForAdminHeader();
        });

        // Sidebar toggle functionality
        document.addEventListener('DOMContentLoaded', function() {
            const sidebarToggle = document.getElementById('sidebarToggle');
            const mobileMenuToggle = document.getElementById('mobileMenuToggle');
            const sidebar = document.querySelector('.admin-sidebar');
            const body = document.body;

            // Debug logging
            console.log('Sidebar Toggle Debug:', {
                sidebarToggle: !!sidebarToggle,
                mobileMenuToggle: !!mobileMenuToggle,
                sidebar: !!sidebar,
                bodyClasses: body.className,
                sidebarClasses: sidebar ? sidebar.className : 'not found'
            });

            // White hamburger button removed - functionality moved to black hamburger

            // Mobile sidebar toggle - admin.js initMobileMenu() handles the click events

            // Click-outside functionality handled by admin.js

            // Handle window resize
            window.addEventListener('resize', function() {
                if (window.innerWidth > 768) {
                    sidebar.classList.remove('show');
                    body.classList.remove('sidebar-open');
                }
            });
        });
    </script>

    <style>
        /* Hide page content until admin header is loaded */
        body:not(.admin-header-loaded) .admin-main {
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        body.admin-header-loaded .admin-main {
            opacity: 1;
        }

        /* EMERGENCY SIDEBAR FIX - FORCE CORRECT LAYOUT */
        .admin-sidebar {
            position: fixed !important;
            top: 0 !important;
            left: 0 !important;
            width: 260px !important;
            height: 100vh !important;
            background: #212529 !important;
            color: #ffffff !important;
            z-index: 1000 !important;
            transition: width 0.3s ease !important;
        }

        body.sidebar-collapsed .admin-sidebar {
            width: 70px !important;
        }

        .admin-main {
            margin-left: 260px !important;
            transition: margin-left 0.3s ease !important;
        }

        body.sidebar-collapsed .admin-main {
            margin-left: 70px !important;
        }

        /* JavaScript will handle the floating toggle button positioning */

        /* FORCE TOPBAR LAYOUT - ABOVE SIDEBAR */
        .admin-topbar {
            position: sticky !important;
            top: 0 !important;
            z-index: 1050 !important; /* Above sidebar but below toggle button */
            background: #ffffff !important;
            border-bottom: 1px solid #e9ecef !important;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05) !important;
        }

        .topbar-actions {
            display: flex !important;
            align-items: center !important;
            justify-content: flex-end !important;
            width: 100% !important;
            position: relative !important;
            z-index: 1051 !important; /* Ensure actions are above topbar */
            padding: 0 1rem !important;
        }

        .topbar-right {
            display: flex !important;
            align-items: center !important;
            gap: 1rem !important;
            width: 100% !important;
            justify-content: flex-end !important;
        }

        /* Black hamburger button styling */
        .topbar-mobile-toggle {
            background: #2c3e50 !important;
            border: none !important;
            border-radius: 6px !important;
            padding: 0.5rem !important;
            color: #ffffff !important;
            cursor: pointer !important;
            transition: all 0.2s ease !important;
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;
            width: 40px !important;
            height: 40px !important;
        }

        .topbar-mobile-toggle:hover {
            background: #34495e !important;
            color: #ffffff !important;
        }

        .topbar-mobile-toggle:active {
            background: #f1ca2f !important;
            color: #2c3e50 !important;
        }

        /* Hide black hamburger on desktop */
        @media (min-width: 769px) {
            .topbar-mobile-toggle {
                display: none !important;
            }
        }

        /* FORCE SIDEBAR TEXT HIDING IN COLLAPSED MODE */
        body.sidebar-collapsed .sidebar-text,
        body.sidebar-collapsed .sidebar-footer-text {
            display: none !important;
        }

        /* FORCE CONTAINER WIDTH */
        .container,
        .dashboard-container,
        .admin-container {
            max-width: none !important;
            width: 100% !important;
            padding: 1.5rem !important;
        }

        /* MOBILE RESPONSIVE DESIGN */
        @media (max-width: 768px) {
            /* Hide floating toggle button on mobile */
            .floating-sidebar-toggle {
                display: none !important;
            }

            /* Mobile sidebar behavior - OVERRIDE desktop styles */
            .admin-sidebar {
                position: fixed !important;
                top: 0 !important;
                left: 0 !important;
                width: 280px !important;
                height: 100vh !important;
                background: #212529 !important;
                color: #ffffff !important;
                z-index: 9999 !important;
                transform: translateX(-100%) !important;
                transition: transform 0.3s ease !important;
            }

            .admin-sidebar.show {
                transform: translateX(0) !important;
                visibility: visible !important;
                opacity: 1 !important;
                display: block !important;
            }

            /* Reset main content for mobile */
            .admin-main {
                margin-left: 0 !important;
                width: 100% !important;
            }

            body.sidebar-collapsed .admin-main {
                margin-left: 0 !important;
            }

            /* Mobile sidebar overlay - admin.js creates #sidebar-overlay element */
            #sidebar-overlay,
            .sidebar-overlay {
                position: fixed !important;
                top: 0 !important;
                left: 0 !important;
                width: 100% !important;
                height: 100% !important;
                background: rgba(0, 0, 0, 0.5) !important;
                z-index: 9998 !important;
                display: none !important;
            }

            /* Alternative overlay using body class */
            body.sidebar-open::before {
                content: '' !important;
                position: fixed !important;
                top: 0 !important;
                left: 0 !important;
                width: 100% !important;
                height: 100% !important;
                background: rgba(0, 0, 0, 0.5) !important;
                z-index: 9998 !important;
                pointer-events: auto !important;
            }

            /* Ensure sidebar content is visible */
            .admin-sidebar .sidebar-text,
            .admin-sidebar .sidebar-footer-text {
                display: inline !important;
            }

            /* Hide desktop toggle button container on mobile */
            #sidebarToggleContainer {
                display: none !important;
            }
        }
    </style>

    <!-- External Resources -->
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">

    <!-- Page-specific CSS -->
    <?php if (isset($extra_css)) echo $extra_css; ?>

    <!-- Page-specific head content -->
    <?php if (isset($extra_head)) echo $extra_head; ?>

    <!-- COMPREHENSIVE ADMIN PROTECTION - Load ABSOLUTELY LAST to prevent any style conflicts -->
    <link rel="stylesheet" href="assets/css/admin-override-protection.css?v=<?php echo $css_version; ?>">
    <!-- USER DROPDOWN SPECIFIC PROTECTION - Load after general protection -->
    <link rel="stylesheet" href="assets/css/dropdown-override.css?v=<?php echo $css_version; ?>">

    <?php
    // Get user settings if logged in
    if (isset($_SESSION['user_id'])) {
        $user_id = $_SESSION['user_id'];

        // Check if user_settings table exists
        $check_table = $conn->query("SHOW TABLES LIKE 'user_settings'");
        $user_settings_table_exists = ($check_table && $check_table->num_rows > 0);

        if ($user_settings_table_exists) {
            $settings_query = "SELECT * FROM user_settings WHERE user_id = $user_id";
            $settings_result = $conn->query($settings_query);
        } else {
            // If table doesn't exist, create an empty result
            $settings_result = false;
        }

        // Default settings
        $user_settings = [
            'timezone' => 'UTC',
            'language' => 'en',
            'notifications_enabled' => 1,
            'theme' => 'light',
            'sidebar_collapsed' => 0,
            'items_per_page' => 10,
            'date_format' => 'Y-m-d',
            'time_format' => 'H:i',
            'auto_refresh' => 0,
            'refresh_interval' => 0
        ];

        // Override with user settings if they exist
        if ($settings_result && $settings_result->num_rows > 0) {
            // Check if the table has setting_key and setting_value columns
            $check_column_key = $conn->query("SHOW COLUMNS FROM user_settings LIKE 'setting_key'");
            $check_column_value = $conn->query("SHOW COLUMNS FROM user_settings LIKE 'setting_value'");

            $has_key_value_structure = ($check_column_key && $check_column_key->num_rows > 0 &&
                                       $check_column_value && $check_column_value->num_rows > 0);

            if ($has_key_value_structure) {
                // If the table has setting_key and setting_value columns
                while ($row = $settings_result->fetch_assoc()) {
                    if (isset($row['setting_key']) && isset($row['setting_value'])) {
                        $user_settings[$row['setting_key']] = $row['setting_value'];
                    }
                }
            } else {
                // If the table has direct column names
                $row = $settings_result->fetch_assoc();
                foreach ($row as $key => $value) {
                    if ($key != 'id' && $key != 'user_id' && $key != 'created_at' && $key != 'updated_at') {
                        $user_settings[$key] = $value;
                    }
                }
            }
        }

        // Check if theme column exists in users table
        $check_theme_column = $conn->query("SHOW COLUMNS FROM users LIKE 'theme'");
        $theme_column_exists = ($check_theme_column && $check_theme_column->num_rows > 0);

        // Check if sidebar_collapsed column exists in users table
        $check_sidebar_column = $conn->query("SHOW COLUMNS FROM users LIKE 'sidebar_collapsed'");
        $sidebar_column_exists = ($check_sidebar_column && $check_sidebar_column->num_rows > 0);

        // Check if accent_color column exists in users table
        $check_accent_column = $conn->query("SHOW COLUMNS FROM users LIKE 'accent_color'");
        $accent_column_exists = ($check_accent_column && $check_accent_column->num_rows > 0);

        // Build the query dynamically based on which columns exist
        $select_fields = [];
        if ($theme_column_exists) $select_fields[] = "theme";
        if ($sidebar_column_exists) $select_fields[] = "sidebar_collapsed";
        if ($accent_column_exists) $select_fields[] = "accent_color";

        // Only query if there are fields to select
        if (!empty($select_fields)) {
            $user_sql = "SELECT " . implode(", ", $select_fields) . " FROM users WHERE id = $user_id";
            $user_result = $conn->query($user_sql);

            if ($user_result && $user_result->num_rows > 0) {
                $user_data = $user_result->fetch_assoc();

                // Only use these values if not already set in user_settings
                if ($theme_column_exists && !isset($user_settings['theme']) && !empty($user_data['theme'])) {
                    $user_settings['theme'] = $user_data['theme'];
                }

                if ($sidebar_column_exists && !isset($user_settings['sidebar_collapsed']) && isset($user_data['sidebar_collapsed'])) {
                    $user_settings['sidebar_collapsed'] = $user_data['sidebar_collapsed'];
                }

                if ($accent_column_exists && !isset($user_settings['accent_color']) && !empty($user_data['accent_color'])) {
                    $user_settings['accent_color'] = $user_data['accent_color'];
                }
            }
        }

        // Add theme-specific CSS class to body
        $theme_class = 'theme-' . $user_settings['theme'];

        // Encode user settings for JavaScript
        $user_settings_json = json_encode($user_settings);
    }

    // Get current page for page-specific styling
    $current_page_name = basename($_SERVER['PHP_SELF']);
    $page_class = 'page-' . str_replace('.php', '', $current_page_name);

    // Add special class for specific pages
    $special_page_class = '';
    if ($current_page_name === 'inbox.php') {
        $special_page_class = 'message-view-page';
    }
    ?>
</head>
<body class="<?php echo isset($theme_class) ? $theme_class : ''; ?> <?php echo $page_class; ?> <?php echo $special_page_class; ?> <?php echo (isset($user_settings['theme']) && $user_settings['theme'] === 'dark') ? 'dark-mode' : ''; ?> <?php echo isset($body_class) ? $body_class : ''; ?>"><?php // Temporarily removed sidebar-collapsed class to debug ?>
    <?php if (isset($_SESSION['user_id'])): ?>
    <!-- Hidden container for user settings data -->
    <div id="user-settings-data" data-settings='<?php echo isset($user_settings_json) ? $user_settings_json : "{}"; ?>' style="display: none;"></div>

    <!-- Debug info for refresh interval (hidden in production) -->
    <?php if (isset($user_settings['refresh_interval']) && $user_settings['refresh_interval'] > 0): ?>
    <script>
        console.log('Debug: Refresh interval set to <?php echo $user_settings['refresh_interval']; ?> seconds');
    </script>
    <?php endif; ?>
    <div class="admin-wrapper">
        <!-- Sidebar -->
        <aside class="admin-sidebar"><?php // Temporarily removed collapsed class to debug ?>
            <div class="admin-sidebar-header">
                <div class="sidebar-logo">
                    <?php
                    // Get admin logo path from settings - add a cache-busting timestamp to ensure we get the latest value
                    $admin_logo_query = "SELECT setting_value FROM system_settings WHERE category = 'general' AND setting_key = 'admin_logo'";
                    $admin_logo_result = $conn->query($admin_logo_query);
                    $admin_logo_path = "images/logo.png"; // Default path
                    $logo_from_settings = false;

                    if ($admin_logo_result && $admin_logo_result->num_rows > 0) {
                        $admin_logo_row = $admin_logo_result->fetch_assoc();
                        $db_logo_path = $admin_logo_row['setting_value'];

                        // Only use the path from settings if it's not empty
                        if (!empty($db_logo_path)) {
                            $logo_from_settings = true;

                            // Normalize the path - remove 'admin/' prefix if present
                            if (strpos($db_logo_path, 'admin/') === 0) {
                                $db_logo_path = substr($db_logo_path, 6);
                            }

                            // Try different path combinations
                            $possible_paths = [
                                $db_logo_path,                // As is
                                "../" . $db_logo_path,        // One directory up
                                "images/" . basename($db_logo_path), // In images directory
                                "../images/" . basename($db_logo_path) // In parent images directory
                            ];

                            // Check each path
                            foreach ($possible_paths as $path) {
                                if (file_exists($path)) {
                                    $admin_logo_path = $path;
                                    break;
                                }
                            }
                        }
                    }

                    // Add a hidden debug div
                    echo "<!-- Debug: Logo path from settings: " . ($logo_from_settings ? "Yes" : "No") . " -->";
                    echo "<!-- Debug: Final logo path: " . $admin_logo_path . " -->";

                    // Add a version to prevent caching
                    $logo_src = $admin_logo_path . '?v=' . $css_version;
                    ?>
                    <img src="<?php echo $logo_src; ?>" alt="Manage Inc." id="admin-sidebar-logo" class="sidebar-logo-img" onerror="this.src='images/logo.png?v=<?php echo time(); ?>'; console.log('Logo not found, using default');">
                </div>
            </div>
            <div class="admin-sidebar-menu">
                <ul class="sidebar-nav">
                    <li class="sidebar-item">
                        <a href="dashboard.php" class="sidebar-link <?php echo $current_page == 'dashboard.php' ? 'active' : ''; ?>">
                            <i class="sidebar-icon fas fa-tachometer-alt"></i>
                            <span class="sidebar-text">Dashboard</span>
                        </a>
                    </li>
                    <?php if ($permissions->hasPermission('manage_inbox')): ?>
                    <li class="sidebar-item">
                        <a href="inbox.php" class="sidebar-link <?php echo $current_page == 'inbox.php' ? 'active' : ''; ?>">
                            <i class="sidebar-icon fas fa-inbox"></i>
                            <span class="sidebar-text">Inbox</span>
                            <?php
                            // Get unread contact submissions count with enhanced query
                            $unread_count_sql = "SELECT
                                COUNT(*) as unread,
                                COUNT(CASE WHEN created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR) THEN 1 END) as recent
                                FROM contact_submissions
                                WHERE is_read = 0";
                            $unread_result = $conn->query($unread_count_sql);
                            $unread_count = 0;
                            $recent_count = 0;

                            if ($unread_result && $unread_result->num_rows > 0) {
                                $unread_row = $unread_result->fetch_assoc();
                                $unread_count = (int)$unread_row['unread'];
                                $recent_count = (int)$unread_row['recent'];

                                if ($unread_count > 0):
                                    // Determine badge style based on urgency
                                    $badge_class = 'sidebar-badge';
                                    if ($recent_count > 0) {
                                        $badge_class .= ' urgent'; // New messages in last 24 hours
                                    }
                                    if ($unread_count > 10) {
                                        $badge_class .= ' high-count'; // Many unread messages
                                    }
                            ?>
                            <div class="<?php echo $badge_class; ?>" title="<?php echo $unread_count; ?> unread messages (<?php echo $recent_count; ?> in last 24h)">
                                <span class="badge-text"><?php echo $unread_count > 99 ? '99+' : $unread_count; ?></span>
                                <?php if ($recent_count > 0): ?>
                                <span class="badge-pulse"></span>
                                <?php endif; ?>
                            </div>
                            <?php endif; } ?>
                        </a>
                    </li>
                    <?php endif; ?>
                    <?php if ($permissions->hasPermission('manage_news') || $permissions->hasPermission('view_news')): ?>
                    <li class="sidebar-item has-submenu <?php echo in_array($current_page, ['all_news.php', 'create_news.php', 'edit_news.php', 'categories.php']) ? 'open' : ''; ?>">
                        <a href="#" class="sidebar-link menu-toggle" data-toggle="news">
                            <i class="sidebar-icon fas fa-newspaper"></i>
                            <span class="sidebar-text">News</span>
                            <i class="submenu-icon fas fa-chevron-down"></i>
                        </a>
                        <ul class="sidebar-submenu">
                            <?php if ($permissions->hasPermission('manage_news') || $permissions->hasPermission('view_news')): ?>
                            <li class="sidebar-subitem">
                                <a href="all_news.php" class="sidebar-sublink <?php echo $current_page == 'all_news.php' ? 'active' : ''; ?>">
                                    <span>All News</span>
                                </a>
                            </li>
                            <?php endif; ?>
                            <?php if ($permissions->hasPermission('create_news')): ?>
                            <li class="sidebar-subitem">
                                <a href="create_news.php" class="sidebar-sublink <?php echo $current_page == 'create_news.php' ? 'active' : ''; ?>">
                                    <span>Add News</span>
                                </a>
                            </li>
                            <?php endif; ?>
                            <?php if ($permissions->hasPermission('manage_categories')): ?>
                            <li class="sidebar-subitem">
                                <a href="categories.php" class="sidebar-sublink <?php echo $current_page == 'categories.php' ? 'active' : ''; ?>">
                                    <span>Categories</span>
                                </a>
                            </li>
                            <?php endif; ?>
                        </ul>
                    </li>
                    <?php endif; ?>
                    <?php if (isset($_SESSION['is_admin']) && $_SESSION['is_admin'] == 1): ?>
                    <li class="sidebar-item has-submenu <?php echo in_array($current_page, ['users.php', 'roles.php']) ? 'open' : ''; ?>">
                        <a href="#" class="sidebar-link menu-toggle" data-toggle="users">
                            <i class="sidebar-icon fas fa-users"></i>
                            <span class="sidebar-text">Users</span>
                            <i class="submenu-icon fas fa-chevron-down"></i>
                        </a>
                        <ul class="sidebar-submenu">
                            <li class="sidebar-subitem">
                                <a href="users.php" class="sidebar-sublink <?php echo $current_page == 'users.php' ? 'active' : ''; ?>">
                                    <span>Users</span>
                                </a>
                            </li>
                            <li class="sidebar-subitem">
                                <a href="roles.php" class="sidebar-sublink <?php echo $current_page == 'roles.php' ? 'active' : ''; ?>">
                                    <span>Roles</span>
                                </a>
                            </li>
                        </ul>
                    </li>
                    <?php endif; ?>
                    <?php if ($permissions->hasPermission('view_files')): ?>
                    <li class="sidebar-item <?php echo $current_page == 'html_editor.php' ? 'active' : ''; ?>">
                        <a href="html_editor.php" class="sidebar-link">
                            <i class="sidebar-icon fas fa-code"></i>
                            <span class="sidebar-text">HTML Editor</span>
                        </a>
                    </li>
                    <?php endif; ?>
                    <?php if ($permissions->hasPermission('manage_email_settings')): ?>
                    <li class="sidebar-item has-submenu <?php echo in_array($current_page, ['settings.php', 'email_templates.php']) ? 'open' : ''; ?>">
                        <a href="#" class="sidebar-link menu-toggle" data-toggle="settings">
                            <i class="sidebar-icon fas fa-cog"></i>
                            <span class="sidebar-text">Settings</span>
                            <i class="submenu-icon fas fa-chevron-down"></i>
                        </a>
                        <ul class="sidebar-submenu">
                            <li class="sidebar-subitem">
                                <a href="settings.php" class="sidebar-sublink <?php echo $current_page == 'settings.php' ? 'active' : ''; ?>">
                                    <span>System Settings</span>
                                </a>
                            </li>
                            <li class="sidebar-subitem">
                                <a href="email_templates.php" class="sidebar-sublink <?php echo $current_page == 'email_templates.php' ? 'active' : ''; ?>">
                                    <span>Email Templates</span>
                                </a>
                            </li>
                        </ul>
                    </li>
                    <?php endif; ?>
                    <li class="sidebar-item">
                        <a href="help.php" target="_blank" class="sidebar-link <?php echo $current_page == 'help.php' ? 'active' : ''; ?>">
                            <i class="sidebar-icon fas fa-question-circle"></i>
                            <span class="sidebar-text">Help</span>
                        </a>
                    </li>
                    <li class="sidebar-item">
                        <a href="../index.html" target="_blank" class="sidebar-link">
                            <i class="sidebar-icon fas fa-external-link-alt"></i>
                            <span class="sidebar-text">View Site</span>
                        </a>
                    </li>
                </ul>
            </div>
            <div class="admin-sidebar-footer">
                <a href="logout.php" class="sidebar-footer-link">
                    <i class="sidebar-footer-icon fas fa-sign-out-alt"></i>
                    <span class="sidebar-footer-text">Logout</span>
                </a>
                <!-- Sidebar toggle button will be added here via JavaScript -->
                <div id="sidebarToggleContainer" class="sidebar-toggle-container"></div>
            </div>
        </aside>

        <!-- Main Content -->
        <main class="admin-main">
            <div class="admin-topbar">
                <!-- Mobile Top Row -->
                <div class="admin-topbar-main">
                    <!-- Logo for mobile view (now first for left alignment) -->
                    <div class="admin-topbar-logo-mobile">
                        <img src="images/logo.png" alt="Manage Inc." class="topbar-logo-img">
                    </div>

                    <!-- Hamburger menu for mobile view (now second for right alignment) -->
                    <button class="topbar-mobile-toggle" id="mobileMenuToggle">
                        <i class="fas fa-bars"></i>
                    </button>

                    <!-- Removed inline JavaScript that was controlling layout -->
                    <!-- User icon removed from mobile view as requested -->
                </div>

                <!-- Mobile Bottom Row / Desktop Main Row -->
                <div class="topbar-actions">
                    <!-- Right Side: Search, Notifications, User Menu -->
                    <div class="topbar-right">
                        <!-- Enhanced Search Bar -->
                    <div class="admin-search">
                        <button type="button" class="admin-search-button" aria-label="Search">
                            <i class="fas fa-search"></i>
                        </button>
                        <input type="search" class="admin-search-input" placeholder="Search dashboard..." aria-label="Search">
                        <button type="button" class="admin-search-clear" aria-label="Clear search">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>

                    <!-- Search functionality script -->
                    <script>
                        document.addEventListener('DOMContentLoaded', function() {
                            const searchInput = document.querySelector('.admin-search-input');
                            const searchClear = document.querySelector('.admin-search-clear');

                            if (searchInput && searchClear) {
                                // Show/hide clear button based on input content
                                searchInput.addEventListener('input', function() {
                                    if (this.value.length > 0) {
                                        searchClear.style.opacity = '0.7';
                                        searchClear.style.visibility = 'visible';
                                    } else {
                                        searchClear.style.opacity = '0';
                                        searchClear.style.visibility = 'hidden';
                                    }
                                });

                                // Clear input when clear button is clicked
                                searchClear.addEventListener('click', function() {
                                    searchInput.value = '';
                                    searchInput.focus();
                                    this.style.opacity = '0';
                                    this.style.visibility = 'hidden';
                                });

                                // Focus input when search button is clicked
                                document.querySelector('.admin-search-button').addEventListener('click', function() {
                                    searchInput.focus();
                                });
                            }
                        });
                    </script>



                    <!-- Notifications -->
                    <div class="topbar-notifications">
                        <button type="button" class="notifications-toggle" id="notificationsToggle">
                            <i class="fas fa-bell"></i>
                            <?php
                            // Include the Notifications class
                            require_once 'lib/Notifications.php';

                            // Get unread notification count
                            $notification_system = new Notifications($conn);
                            $header_unread_count = $notification_system->getUnreadCount($_SESSION['user_id']);

                            // Display badge if there are unread notifications
                            if ($header_unread_count > 0):
                            ?>
                            <span class="notifications-badge" id="notificationsBadge"><?php echo $header_unread_count; ?></span>
                            <?php endif; ?>
                        </button>

                        <script>
                            // Add click event for notifications toggle
                            document.addEventListener('DOMContentLoaded', function() {
                                const notificationsToggle = document.getElementById('notificationsToggle');
                                const notificationsDropdown = document.getElementById('notificationsDropdown');

                                if (notificationsToggle && notificationsDropdown) {
                                    notificationsToggle.addEventListener('click', function(e) {
                                        e.preventDefault();
                                        e.stopPropagation();

                                        // Toggle dropdown visibility
                                        notificationsDropdown.classList.toggle('show');

                                        // Add active class to toggle button
                                        this.classList.toggle('active');

                                        // Position the dropdown correctly in mobile view
                                        if (window.innerWidth <= 768) {
                                            // Get the topbar height
                                            const topbarHeight = document.querySelector('.admin-topbar-main').offsetHeight +
                                                               document.querySelector('.topbar-actions').offsetHeight;

                                            // Set the dropdown position
                                            notificationsDropdown.style.top = (topbarHeight + 5) + 'px';
                                            notificationsDropdown.style.right = '10px';
                                            notificationsDropdown.style.position = 'fixed';
                                            notificationsDropdown.style.zIndex = '9999';
                                        }

                                        // Close user dropdown if open
                                        const userDropdown = document.getElementById('userDropdown');
                                        const userMenuToggle = document.getElementById('userMenuToggle');
                                        if (userDropdown && userDropdown.classList.contains('show')) {
                                            userDropdown.classList.remove('show');
                                            if (userMenuToggle) {
                                                userMenuToggle.classList.remove('active');
                                            }
                                        }
                                    });
                                }
                            });
                        </script>
                        <div class="notifications-dropdown" id="notificationsDropdown">
                            <div class="notifications-header">
                                <h3 class="notifications-title">Notifications</h3>
                                <button class="mark-all-read" id="markAllRead">Mark all as read</button>
                            </div>
                            <div class="notifications-body">
                                <ul class="notification-list" id="notificationList">
                                    <!-- Notifications will be loaded here via JavaScript -->
                                    <li class="notification-item loading">
                                        <div class="notification-content">
                                            <p class="notification-text">Loading notifications...</p>
                                        </div>
                                    </li>
                                </ul>
                            </div>
                            <div class="notifications-footer">
                                <div class="notifications-actions">
                                    <a href="add_notification.php" class="notifications-action-btn">
                                        <i class="fas fa-plus"></i> Add
                                    </a>
                                    <a href="all_notifications.php" class="notifications-action-btn">
                                        <i class="fas fa-list"></i> View all
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- User Menu (Redesigned) -->
                    <div class="topbar-user-menu">
                        <button type="button" class="user-menu-toggle" id="userMenuToggle" aria-label="User menu" aria-expanded="false" aria-controls="userDropdown">
                            <?php
                            // Get user profile image and additional info
                            $user_id = $_SESSION['user_id'];
                            $user_sql = "SELECT profile_image, email, created_at FROM users WHERE id = $user_id";
                            $user_result = $conn->query($user_sql);
                            $user_data = $user_result->fetch_assoc();
                            $profile_image = $user_data['profile_image'] ?? null;
                            $user_email = $user_data['email'] ?? '';
                            $join_date = isset($user_data['created_at']) ? date('M d, Y', strtotime($user_data['created_at'])) : '';
                            ?>
                            <span class="user-name"><?php echo $_SESSION['username']; ?></span>
                            <div class="user-avatar">
                                <?php if (!empty($profile_image) && file_exists("../uploads/profiles/" . $profile_image)): ?>
                                    <img src="../uploads/profiles/<?php echo $profile_image; ?>" alt="<?php echo $_SESSION['username']; ?>" class="avatar-img">
                                <?php else: ?>
                                    <div class="avatar-placeholder">
                                        <span><?php echo substr($_SESSION['username'], 0, 1); ?></span>
                                    </div>
                                <?php endif; ?>
                                <span class="avatar-status online"></span>
                            </div>
                            <i class="dropdown-icon fas fa-chevron-down"></i>
                        </button>

                        <div class="user-dropdown" id="userDropdown">
                            <!-- User Profile Header -->
                            <div class="user-dropdown-header">
                                <div class="user-dropdown-avatar">
                                    <?php if (!empty($profile_image) && file_exists("../uploads/profiles/" . $profile_image)): ?>
                                        <img src="../uploads/profiles/<?php echo $profile_image; ?>" alt="<?php echo $_SESSION['username']; ?>" class="avatar-img-large">
                                    <?php else: ?>
                                        <div class="avatar-placeholder-large">
                                            <span><?php echo substr($_SESSION['username'], 0, 1); ?></span>
                                        </div>
                                    <?php endif; ?>
                                </div>
                                <div class="user-dropdown-info">
                                    <h4 class="user-dropdown-name"><?php echo $_SESSION['username']; ?></h4>
                                    <p class="user-dropdown-email"><?php echo $user_email; ?></p>
                                    <span class="user-dropdown-badge">
                                        <?php echo $_SESSION['is_admin'] == 1 ? 'Administrator' : 'User'; ?>
                                    </span>
                                </div>
                            </div>

                            <!-- User Menu Items -->
                            <div class="dropdown-menu">
                                <!-- Profile Menu Section -->
                                <div class="dropdown-section profile-menu-section">
                                    <h6 class="dropdown-section-title">Profile Menu</h6>
                                    <a href="profile.php" class="dropdown-item dropdown-link" id="profileLink">
                                        <i class="dropdown-icon fas fa-user-circle"></i>
                                        <div class="dropdown-link-content">
                                            <span class="dropdown-link-title">My Profile</span>
                                            <span class="dropdown-link-description">View and edit your profile</span>
                                        </div>
                                    </a>
                                </div>

                                <!-- Security Section -->
                                <div class="dropdown-section password-menu-section">
                                    <h6 class="dropdown-section-title">Security</h6>
                                    <a href="change_password.php" class="dropdown-item dropdown-link">
                                        <i class="dropdown-icon fas fa-key"></i>
                                        <div class="dropdown-link-content">
                                            <span class="dropdown-link-title">Change Password</span>
                                            <span class="dropdown-link-description">Update your login credentials</span>
                                        </div>
                                    </a>
                                </div>

                                <!-- Activity Section -->
                                <div class="dropdown-section activity-menu-section">
                                    <h6 class="dropdown-section-title">Activity</h6>
                                    <a href="activity_log.php" class="dropdown-item dropdown-link">
                                        <i class="dropdown-icon fas fa-history"></i>
                                        <div class="dropdown-link-content">
                                            <span class="dropdown-link-title">Activity Log</span>
                                            <span class="dropdown-link-description">View your recent activities</span>
                                        </div>
                                    </a>
                                </div>

                                <!-- Settings Section -->
                                <div class="dropdown-section settings-menu-section">
                                    <h6 class="dropdown-section-title">Settings</h6>
                                    <a href="user_settings.php" class="dropdown-item dropdown-link">
                                        <i class="dropdown-icon fas fa-cog"></i>
                                        <div class="dropdown-link-content">
                                            <span class="dropdown-link-title">Account Settings</span>
                                            <span class="dropdown-link-description">Manage your preferences</span>
                                        </div>
                                    </a>
                                <?php
                                // Check if dark mode is enabled in system settings
                                $dark_mode_enabled = true;

                                // First check system settings - look for the new admin_appearance category
                                $dark_mode_query = "SELECT setting_value FROM system_settings WHERE setting_key = 'admin_enable_dark_mode' AND category = 'admin_appearance'";
                                $dark_mode_result = $conn->query($dark_mode_query);

                                if ($dark_mode_result && $dark_mode_result->num_rows > 0) {
                                    $dark_mode_setting = $dark_mode_result->fetch_assoc();
                                    if ($dark_mode_setting['setting_value'] == '0') {
                                        $dark_mode_enabled = false;
                                    }
                                } else {
                                    // Fallback to old appearance category if new one doesn't exist
                                    $dark_mode_query = "SELECT setting_value FROM system_settings WHERE setting_key = 'enable_dark_mode' AND category = 'appearance'";
                                    $dark_mode_result = $conn->query($dark_mode_query);

                                    if ($dark_mode_result && $dark_mode_result->num_rows > 0) {
                                        $dark_mode_setting = $dark_mode_result->fetch_assoc();
                                        if ($dark_mode_setting['setting_value'] == '0') {
                                            $dark_mode_enabled = false;
                                        }
                                    }
                                }

                                // Then check user settings (which may override system settings)
                                if (isset($user_settings['enable_dark_mode']) && $user_settings['enable_dark_mode'] == 0) {
                                    $dark_mode_enabled = false;
                                }

                                if ($dark_mode_enabled):
                                ?>
                                    <div class="dropdown-item">
                                        <button type="button" id="toggle-dark-mode" class="dropdown-link">
                                            <i class="dropdown-icon fas fa-moon"></i>
                                            <div class="dropdown-link-content">
                                                <span class="dropdown-link-title"><span id="dark-mode-text">Dark Mode</span></span>
                                                <span class="dropdown-link-description">Toggle light/dark theme</span>
                                            </div>
                                            <div class="toggle-switch">
                                                <input type="checkbox" id="dark-mode-toggle" class="toggle-input" <?php echo (isset($user_settings['theme']) && $user_settings['theme'] === 'dark') ? 'checked' : ''; ?>>
                                                <label for="dark-mode-toggle" class="toggle-label"></label>
                                            </div>
                                        </button>
                                    </div>
                                <?php endif; ?>
                                </div>

                                <!-- Account Section -->
                                <div class="dropdown-section account-menu-section">
                                    <h6 class="dropdown-section-title">Account</h6>
                                    <a href="logout.php" class="dropdown-item dropdown-link logout-link">
                                        <i class="dropdown-icon fas fa-sign-out-alt"></i>
                                        <div class="dropdown-link-content">
                                            <span class="dropdown-link-title">Logout</span>
                                            <span class="dropdown-link-description">Sign out of your account</span>
                                        </div>
                                    </a>
                                </div>
                            </div>

                            <div class="dropdown-footer">
                                <span class="dropdown-footer-info">Member since: <?php echo $join_date; ?></span>
                            </div>
                        </div>

                        <script>
                            // Add click event for user menu toggle
                            document.addEventListener('DOMContentLoaded', function() {
                                const userMenuToggle = document.getElementById('userMenuToggle');
                                const userDropdown = document.getElementById('userDropdown');
                                const darkModeToggle = document.getElementById('dark-mode-toggle');
                                const darkModeText = document.getElementById('dark-mode-text');

                                if (userMenuToggle && userDropdown) {
                                    userMenuToggle.addEventListener('click', function(e) {
                                        e.preventDefault();
                                        e.stopPropagation();

                                        // Toggle dropdown visibility
                                        userDropdown.classList.toggle('show');

                                        // Update ARIA attributes
                                        const expanded = userDropdown.classList.contains('show');
                                        this.setAttribute('aria-expanded', expanded);

                                        // Add active class to toggle button
                                        this.classList.toggle('active');

                                        // Position the dropdown correctly in mobile view
                                        if (window.innerWidth <= 768) {
                                            // Get the actual topbar height more accurately
                                            const adminTopbar = document.querySelector('.admin-topbar');
                                            const topbarHeight = adminTopbar ? adminTopbar.offsetHeight : 60;

                                            // Set the dropdown position
                                            userDropdown.style.top = (topbarHeight + 10) + 'px';
                                            userDropdown.style.right = '10px';
                                            userDropdown.style.left = 'auto';
                                            userDropdown.style.position = 'fixed';
                                            userDropdown.style.zIndex = '9999';
                                            userDropdown.style.maxHeight = `calc(100vh - ${topbarHeight + 20}px)`;
                                            userDropdown.style.overflowY = 'auto';

                                            console.log('Mobile dropdown positioned:', {
                                                topbarHeight: topbarHeight,
                                                dropdownTop: userDropdown.style.top,
                                                maxHeight: userDropdown.style.maxHeight
                                            });
                                        } else {
                                            // Reset styles for desktop
                                            userDropdown.style.position = '';
                                            userDropdown.style.top = '';
                                            userDropdown.style.right = '';
                                            userDropdown.style.left = '';
                                            userDropdown.style.maxHeight = '';
                                            userDropdown.style.overflowY = '';
                                        }

                                        // Close notifications dropdown if open
                                        const notificationsDropdown = document.getElementById('notificationsDropdown');
                                        const notificationsToggle = document.getElementById('notificationsToggle');
                                        if (notificationsDropdown && notificationsDropdown.classList.contains('show')) {
                                            notificationsDropdown.classList.remove('show');
                                            if (notificationsToggle) {
                                                notificationsToggle.classList.remove('active');
                                                notificationsToggle.setAttribute('aria-expanded', 'false');
                                            }
                                        }
                                    });
                                }

                                // Handle profile link click
                                const profileLink = document.getElementById('profileLink');
                                if (profileLink) {
                                    profileLink.addEventListener('click', function(e) {
                                        // If we're in the HTML editor and there are unsaved changes, confirm before navigating
                                        if (typeof isModified !== 'undefined' && isModified) {
                                            if (!confirm('You have unsaved changes. Are you sure you want to leave?')) {
                                                e.preventDefault();
                                                return false;
                                            }

                                            // If we have a lock, release it before navigating
                                            if (typeof currentFilePath !== 'undefined' && currentFilePath && typeof releaseLock !== 'undefined') {
                                                try {
                                                    // Release lock but don't wait for the response
                                                    releaseLock(currentFilePath);
                                                } catch (error) {
                                                    console.error('Error releasing lock before navigation:', error);
                                                }
                                            }
                                        }
                                    });
                                }

                                // Handle dark mode toggle
                                if (darkModeToggle && darkModeText) {
                                    darkModeToggle.addEventListener('change', function() {
                                        const isDarkMode = this.checked;
                                        document.body.classList.toggle('dark-mode', isDarkMode);
                                        darkModeText.textContent = isDarkMode ? 'Light Mode' : 'Dark Mode';

                                        // Save preference via AJAX
                                        fetch('ajax/save_user_setting.php', {
                                            method: 'POST',
                                            headers: {
                                                'Content-Type': 'application/x-www-form-urlencoded',
                                            },
                                            body: 'setting=theme&value=' + (isDarkMode ? 'dark' : 'light')
                                        })
                                        .then(response => response.json())
                                        .then(data => {
                                            console.log('Theme preference saved:', data);
                                        })
                                        .catch(error => {
                                            console.error('Error saving theme preference:', error);
                                        });
                                    });
                                }

                                // Handle window resize to reposition dropdown
                                window.addEventListener('resize', function() {
                                    if (userDropdown && userDropdown.classList.contains('show')) {
                                        if (window.innerWidth <= 768) {
                                            // Reposition for mobile
                                            const adminTopbar = document.querySelector('.admin-topbar');
                                            const topbarHeight = adminTopbar ? adminTopbar.offsetHeight : 60;

                                            userDropdown.style.top = (topbarHeight + 10) + 'px';
                                            userDropdown.style.right = '10px';
                                            userDropdown.style.left = 'auto';
                                            userDropdown.style.position = 'fixed';
                                            userDropdown.style.zIndex = '9999';
                                            userDropdown.style.maxHeight = `calc(100vh - ${topbarHeight + 20}px)`;
                                            userDropdown.style.overflowY = 'auto';
                                        } else {
                                            // Reset styles for desktop
                                            userDropdown.style.position = '';
                                            userDropdown.style.top = '';
                                            userDropdown.style.right = '';
                                            userDropdown.style.left = '';
                                            userDropdown.style.maxHeight = '';
                                            userDropdown.style.overflowY = '';
                                        }
                                    }
                                });

                                // Close dropdowns when clicking outside
                                document.addEventListener('click', function(e) {
                                    // Close user dropdown if clicking outside
                                    if (userDropdown && userMenuToggle &&
                                        !userDropdown.contains(e.target) &&
                                        !userMenuToggle.contains(e.target) &&
                                        userDropdown.classList.contains('show')) {
                                        userDropdown.classList.remove('show');
                                        userMenuToggle.classList.remove('active');
                                        userMenuToggle.setAttribute('aria-expanded', 'false');
                                    }

                                    // Close notifications dropdown if clicking outside
                                    const notificationsDropdown = document.getElementById('notificationsDropdown');
                                    const notificationsToggle = document.getElementById('notificationsToggle');
                                    if (notificationsDropdown && notificationsToggle &&
                                        !notificationsDropdown.contains(e.target) &&
                                        !notificationsToggle.contains(e.target) &&
                                        notificationsDropdown.classList.contains('show')) {
                                        notificationsDropdown.classList.remove('show');
                                        notificationsToggle.classList.remove('active');
                                        notificationsToggle.setAttribute('aria-expanded', 'false');
                                    }
                                });
                            });
                        </script>
                    </div>
                    </div> <!-- Close topbar-right -->
                </div>
            </div>

            <!-- Submenu removed as requested -->
    <?php endif; ?>
