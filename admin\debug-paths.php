<?php
/**
 * Debug Paths and CSS Loading
 * This script helps diagnose path and CSS loading issues on hosting providers
 */

// Start session
session_start();

// Include config
require_once 'config.php';

echo "<!DOCTYPE html>";
echo "<html><head><title>Path Debug Tool</title>";
echo "<style>body{font-family:Arial,sans-serif;margin:20px;} .section{margin:20px 0;padding:15px;border:1px solid #ddd;} .good{color:green;} .bad{color:red;} .warning{color:orange;}</style>";
echo "</head><body>";

echo "<h1>🔍 Path and CSS Debug Tool</h1>";

// 1. Server Environment
echo "<div class='section'>";
echo "<h2>Server Environment</h2>";
echo "<strong>HTTP_HOST:</strong> " . $_SERVER['HTTP_HOST'] . "<br>";
echo "<strong>SCRIPT_NAME:</strong> " . $_SERVER['SCRIPT_NAME'] . "<br>";
echo "<strong>REQUEST_URI:</strong> " . $_SERVER['REQUEST_URI'] . "<br>";
echo "<strong>DOCUMENT_ROOT:</strong> " . $_SERVER['DOCUMENT_ROOT'] . "<br>";
echo "<strong>Current Directory:</strong> " . __DIR__ . "<br>";
echo "</div>";

// 2. Path Detection Logic
echo "<div class='section'>";
echo "<h2>Path Detection Logic</h2>";

$protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
$host = $_SERVER['HTTP_HOST'];
$script_name = $_SERVER['SCRIPT_NAME'];
$admin_path = dirname($script_name);
$site_root_path = str_replace('/admin', '', $admin_path);

if (empty($site_root_path) || $site_root_path === '/') {
    $site_root = $protocol . '://' . $host;
} else {
    $site_root = $protocol . '://' . $host . $site_root_path;
}

echo "<strong>Protocol:</strong> $protocol<br>";
echo "<strong>Host:</strong> $host<br>";
echo "<strong>Admin Path:</strong> $admin_path<br>";
echo "<strong>Site Root Path:</strong> $site_root_path<br>";
echo "<strong>Site Root URL:</strong> $site_root<br>";
echo "</div>";

// 3. CSS File Checks
echo "<div class='section'>";
echo "<h2>CSS File Existence Check</h2>";

$css_files = [
    'assets/css/main.css',
    'assets/css/base/admin_variables.css',
    'assets/css/components/admin_user-dropdown.css',
    'assets/css/layout/admin_header.css',
    'assets/css/layout/admin_sidebar.css'
];

foreach ($css_files as $css_file) {
    $full_path = __DIR__ . '/' . $css_file;
    $exists = file_exists($full_path);
    $readable = $exists ? is_readable($full_path) : false;
    
    echo "<strong>$css_file:</strong> ";
    if ($exists && $readable) {
        echo "<span class='good'>✅ EXISTS & READABLE</span>";
        echo " (Size: " . filesize($full_path) . " bytes)";
    } elseif ($exists) {
        echo "<span class='warning'>⚠️ EXISTS BUT NOT READABLE</span>";
    } else {
        echo "<span class='bad'>❌ NOT FOUND</span>";
    }
    echo "<br>";
}
echo "</div>";

// 4. URL Accessibility Test
echo "<div class='section'>";
echo "<h2>CSS URL Accessibility Test</h2>";
echo "<p>Testing if CSS files are accessible via HTTP:</p>";

foreach ($css_files as $css_file) {
    $css_url = $protocol . '://' . $host . $admin_path . '/' . $css_file;
    echo "<strong>$css_file:</strong><br>";
    echo "URL: <a href='$css_url' target='_blank'>$css_url</a><br>";
    
    // Try to fetch the file
    $headers = @get_headers($css_url);
    if ($headers && strpos($headers[0], '200') !== false) {
        echo "<span class='good'>✅ ACCESSIBLE</span><br>";
    } else {
        echo "<span class='bad'>❌ NOT ACCESSIBLE</span><br>";
        if ($headers) {
            echo "Response: " . $headers[0] . "<br>";
        }
    }
    echo "<br>";
}
echo "</div>";

// 5. Database Connection Test
echo "<div class='section'>";
echo "<h2>Database Connection</h2>";
if (isset($conn) && $conn instanceof mysqli) {
    if ($conn->ping()) {
        echo "<span class='good'>✅ Database connected successfully</span><br>";
        echo "Database: " . DB_NAME . "<br>";
        echo "Host: " . DB_HOST . "<br>";
    } else {
        echo "<span class='bad'>❌ Database connection lost</span><br>";
    }
} else {
    echo "<span class='bad'>❌ No database connection</span><br>";
}
echo "</div>";

// 6. Configuration Constants
echo "<div class='section'>";
echo "<h2>Configuration Constants</h2>";
if (defined('SITE_URL')) {
    echo "<strong>SITE_URL:</strong> " . SITE_URL . "<br>";
}
if (defined('APP_URL')) {
    echo "<strong>APP_URL:</strong> " . APP_URL . "<br>";
}
if (defined('ADMIN_URL')) {
    echo "<strong>ADMIN_URL:</strong> " . ADMIN_URL . "<br>";
}
echo "</div>";

// 7. Session Information
echo "<div class='section'>";
echo "<h2>Session Information</h2>";
if (isset($_SESSION['user_id'])) {
    echo "<span class='good'>✅ User logged in</span><br>";
    echo "User ID: " . $_SESSION['user_id'] . "<br>";
    if (isset($_SESSION['username'])) {
        echo "Username: " . $_SESSION['username'] . "<br>";
    }
} else {
    echo "<span class='warning'>⚠️ No user session</span><br>";
}
echo "</div>";

echo "<div class='section'>";
echo "<h2>Quick Fix Test</h2>";
echo "<p>Click the button below to test if the main CSS loads correctly:</p>";
echo "<button onclick=\"testCSS()\">Test CSS Loading</button>";
echo "<div id='css-test-result'></div>";
echo "</div>";

echo "<script>";
echo "function testCSS() {";
echo "  var link = document.createElement('link');";
echo "  link.rel = 'stylesheet';";
echo "  link.href = 'assets/css/main.css?v=' + Date.now();";
echo "  link.onload = function() {";
echo "    document.getElementById('css-test-result').innerHTML = '<span style=\"color:green;\">✅ CSS loaded successfully</span>';";
echo "  };";
echo "  link.onerror = function() {";
echo "    document.getElementById('css-test-result').innerHTML = '<span style=\"color:red;\">❌ CSS failed to load</span>';";
echo "  };";
echo "  document.head.appendChild(link);";
echo "}";
echo "</script>";

echo "</body></html>";
?>
