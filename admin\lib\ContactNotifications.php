<?php
/**
 * Contact Notifications Class
 * 
 * Handles creating notifications for contact form submissions
 * and integrating them with the notification system
 */

class ContactNotifications {
    private $conn;
    private $notifications;
    
    public function __construct($database_connection) {
        $this->conn = $database_connection;
        
        // Include and initialize the notifications system
        require_once 'Notifications.php';
        $this->notifications = new Notifications($this->conn);
    }
    
    /**
     * Create a notification for a new contact form submission
     * 
     * @param array $submission Contact submission data
     * @return bool Success status
     */
    public function createContactSubmissionNotification($submission) {
        try {
            // Prepare notification data
            $title = "New Contact Form Submission";
            $sender_name = htmlspecialchars($submission['name']);
            $sender_email = htmlspecialchars($submission['email']);
            $source = isset($submission['source']) ? htmlspecialchars($submission['source']) : 'Contact Form';
            
            // Create a preview of the message (first 100 characters)
            $message_preview = strlen($submission['message']) > 100 
                ? substr($submission['message'], 0, 100) . '...' 
                : $submission['message'];
            $message_preview = htmlspecialchars($message_preview);
            
            // Format the notification message
            $message = "From: {$sender_name} ({$sender_email})\n";
            $message .= "Source: {$source}\n\n";
            $message .= "Message: {$message_preview}";
            
            // Create link to view the submission
            $link = "inbox.php?id=" . $submission['id'];
            
            // Add notification for all admin users (user_id = 0 means all admins)
            $success = $this->notifications->addNotification(
                0, // Send to all admin users
                $title,
                $message,
                'info', // Notification type
                $link
            );
            
            if ($success) {
                // Log the notification creation
                error_log("Contact notification created for submission ID: " . $submission['id']);
                return true;
            } else {
                error_log("Failed to create contact notification for submission ID: " . $submission['id']);
                return false;
            }
            
        } catch (Exception $e) {
            error_log("Error creating contact notification: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Create a notification when a contact submission is replied to
     * 
     * @param array $submission Contact submission data
     * @param array $reply Reply data
     * @return bool Success status
     */
    public function createReplyNotification($submission, $reply) {
        try {
            $title = "Contact Form Reply Sent";
            $sender_name = htmlspecialchars($submission['name']);
            $sender_email = htmlspecialchars($submission['email']);
            $reply_subject = htmlspecialchars($reply['subject']);
            
            $message = "Reply sent to: {$sender_name} ({$sender_email})\n";
            $message .= "Subject: {$reply_subject}";
            
            // Create link to view the submission
            $link = "inbox.php?id=" . $submission['id'];
            
            // Add notification for all admin users
            $success = $this->notifications->addNotification(
                0, // Send to all admin users
                $title,
                $message,
                'success', // Success type for sent replies
                $link
            );
            
            return $success;
            
        } catch (Exception $e) {
            error_log("Error creating reply notification: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Update notification when a contact submission is marked as read
     * 
     * @param int $submission_id Contact submission ID
     * @return bool Success status
     */
    public function markContactNotificationAsRead($submission_id) {
        try {
            // Find notifications related to this contact submission
            $link = "inbox.php?id=" . $submission_id;
            
            $sql = "UPDATE notifications 
                    SET is_read = 1 
                    WHERE link = ? 
                    AND title LIKE '%Contact Form%'
                    AND is_read = 0";
            
            $stmt = $this->conn->prepare($sql);
            if ($stmt) {
                $stmt->bind_param("s", $link);
                $success = $stmt->execute();
                $stmt->close();
                
                if ($success) {
                    error_log("Marked contact notifications as read for submission ID: " . $submission_id);
                }
                
                return $success;
            }
            
            return false;
            
        } catch (Exception $e) {
            error_log("Error marking contact notification as read: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Delete notifications when a contact submission is deleted
     * 
     * @param int $submission_id Contact submission ID
     * @return bool Success status
     */
    public function deleteContactNotifications($submission_id) {
        try {
            // Delete notifications related to this contact submission
            $link = "inbox.php?id=" . $submission_id;
            
            $sql = "DELETE FROM notifications 
                    WHERE link = ? 
                    AND title LIKE '%Contact Form%'";
            
            $stmt = $this->conn->prepare($sql);
            if ($stmt) {
                $stmt->bind_param("s", $link);
                $success = $stmt->execute();
                $stmt->close();
                
                if ($success) {
                    error_log("Deleted contact notifications for submission ID: " . $submission_id);
                }
                
                return $success;
            }
            
            return false;
            
        } catch (Exception $e) {
            error_log("Error deleting contact notifications: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Get unread contact submission count for dashboard
     * 
     * @return int Number of unread contact submissions
     */
    public function getUnreadContactCount() {
        try {
            $sql = "SELECT COUNT(*) as count 
                    FROM contact_submissions 
                    WHERE is_read = 0";
            
            $result = $this->conn->query($sql);
            if ($result && $result->num_rows > 0) {
                $row = $result->fetch_assoc();
                return (int)$row['count'];
            }
            
            return 0;
            
        } catch (Exception $e) {
            error_log("Error getting unread contact count: " . $e->getMessage());
            return 0;
        }
    }
    
    /**
     * Sync existing contact submissions with notifications
     * This is useful for migrating existing data
     * 
     * @return bool Success status
     */
    public function syncExistingSubmissions() {
        try {
            // Get all contact submissions that don't have notifications yet
            $sql = "SELECT cs.* 
                    FROM contact_submissions cs 
                    LEFT JOIN notifications n ON n.link = CONCAT('inbox.php?id=', cs.id) 
                    WHERE n.id IS NULL 
                    ORDER BY cs.created_at DESC 
                    LIMIT 50"; // Limit to prevent overwhelming the system
            
            $result = $this->conn->query($sql);
            $synced_count = 0;
            
            if ($result && $result->num_rows > 0) {
                while ($submission = $result->fetch_assoc()) {
                    if ($this->createContactSubmissionNotification($submission)) {
                        $synced_count++;
                    }
                }
            }
            
            error_log("Synced {$synced_count} existing contact submissions with notifications");
            return true;
            
        } catch (Exception $e) {
            error_log("Error syncing existing submissions: " . $e->getMessage());
            return false;
        }
    }
}
?>
