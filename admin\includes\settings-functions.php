<?php
/**
 * Settings Functions
 * Functions for the settings module
 * Consolidated from multiple PHP files
 */

/**
 * Get all settings
 * 
 * @return array Array of settings
 */
function get_all_settings() {
    global $conn;
    
    $result = $conn->query("SELECT * FROM settings");
    
    $settings = [];
    
    while ($row = $result->fetch_assoc()) {
        $settings[$row['key']] = $row['value'];
    }
    
    return $settings;
}

/**
 * Get setting value
 * 
 * @param string $key Setting key
 * @param mixed $default Default value if setting not found
 * @return mixed Setting value or default
 */
function get_setting($key, $default = '') {
    global $conn;
    
    $stmt = $conn->prepare("SELECT value FROM settings WHERE `key` = ?");
    $stmt->bind_param("s", $key);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows === 0) {
        return $default;
    }
    
    $row = $result->fetch_assoc();
    return $row['value'];
}

/**
 * Update setting value
 * 
 * @param string $key Setting key
 * @param mixed $value Setting value
 * @return bool True on success, false on failure
 */
function update_setting($key, $value) {
    global $conn;
    
    // Check if setting exists
    $stmt = $conn->prepare("SELECT id FROM settings WHERE `key` = ?");
    $stmt->bind_param("s", $key);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows === 0) {
        // Insert new setting
        $stmt = $conn->prepare("INSERT INTO settings (`key`, value) VALUES (?, ?)");
        $stmt->bind_param("ss", $key, $value);
    } else {
        // Update existing setting
        $stmt = $conn->prepare("UPDATE settings SET value = ? WHERE `key` = ?");
        $stmt->bind_param("ss", $value, $key);
    }
    
    return $stmt->execute();
}

/**
 * Delete setting
 * 
 * @param string $key Setting key
 * @return bool True on success, false on failure
 */
function delete_setting($key) {
    global $conn;
    
    $stmt = $conn->prepare("DELETE FROM settings WHERE `key` = ?");
    $stmt->bind_param("s", $key);
    
    return $stmt->execute();
}

/**
 * Get settings by category
 * 
 * @param string $category Settings category
 * @return array Array of settings in category
 */
function get_settings_by_category($category) {
    global $conn;
    
    $stmt = $conn->prepare("SELECT * FROM settings WHERE `category` = ?");
    $stmt->bind_param("s", $category);
    $stmt->execute();
    $result = $stmt->get_result();
    
    $settings = [];
    
    while ($row = $result->fetch_assoc()) {
        $settings[$row['key']] = $row['value'];
    }
    
    return $settings;
}

/**
 * Update settings from form data
 * 
 * @param array $data Form data
 * @return bool True on success, false on failure
 */
function update_settings_from_form($data) {
    global $conn;
    
    // Begin transaction
    $conn->begin_transaction();
    
    try {
        foreach ($data as $key => $value) {
            // Skip non-setting fields
            if ($key === 'submit' || $key === 'action') {
                continue;
            }
            
            update_setting($key, $value);
        }
        
        // Commit transaction
        $conn->commit();
        
        return true;
    } catch (Exception $e) {
        // Rollback transaction
        $conn->rollback();
        
        return false;
    }
}

/**
 * Create backup of settings
 * 
 * @return string|bool Backup filename on success, false on failure
 */
function create_settings_backup() {
    global $conn;
    
    $settings = get_all_settings();
    $backup_data = json_encode($settings);
    
    $backup_dir = __DIR__ . '/../backups';
    
    if (!file_exists($backup_dir)) {
        mkdir($backup_dir, 0755, true);
    }
    
    $backup_file = $backup_dir . '/backup_' . date('Y-m-d_H-i-s') . '.json';
    
    if (file_put_contents($backup_file, $backup_data)) {
        return basename($backup_file);
    }
    
    return false;
}

/**
 * Restore settings from backup
 * 
 * @param string $backup_file Backup filename
 * @return bool True on success, false on failure
 */
function restore_settings_backup($backup_file) {
    global $conn;
    
    $backup_path = __DIR__ . '/../backups/' . $backup_file;
    
    if (!file_exists($backup_path)) {
        return false;
    }
    
    $backup_data = file_get_contents($backup_path);
    $settings = json_decode($backup_data, true);
    
    if (!$settings) {
        return false;
    }
    
    // Begin transaction
    $conn->begin_transaction();
    
    try {
        // Clear existing settings
        $conn->query("DELETE FROM settings");
        
        // Insert new settings
        foreach ($settings as $key => $value) {
            update_setting($key, $value);
        }
        
        // Commit transaction
        $conn->commit();
        
        return true;
    } catch (Exception $e) {
        // Rollback transaction
        $conn->rollback();
        
        return false;
    }
}

/**
 * Get available backups
 * 
 * @return array Array of backup files
 */
function get_available_backups() {
    $backup_dir = __DIR__ . '/../backups';
    
    if (!file_exists($backup_dir)) {
        return [];
    }
    
    $backups = glob($backup_dir . '/backup_*.json');
    $backups = array_map('basename', $backups);
    rsort($backups); // Sort by newest first
    
    return $backups;
}

/**
 * Delete backup
 * 
 * @param string $backup_file Backup filename
 * @return bool True on success, false on failure
 */
function delete_backup($backup_file) {
    $backup_path = __DIR__ . '/../backups/' . $backup_file;
    
    if (!file_exists($backup_path)) {
        return false;
    }
    
    return unlink($backup_path);
}

/**
 * Get email settings
 * 
 * @return array Email settings
 */
function get_email_settings() {
    $settings = [
        'smtp_enabled' => get_setting('smtp_enabled', '0'),
        'smtp_host' => get_setting('smtp_host', ''),
        'smtp_port' => get_setting('smtp_port', '587'),
        'smtp_username' => get_setting('smtp_username', ''),
        'smtp_password' => get_setting('smtp_password', ''),
        'smtp_encryption' => get_setting('smtp_encryption', 'tls'),
        'from_email' => get_setting('from_email', ''),
        'from_name' => get_setting('from_name', ''),
        'admin_email' => get_setting('admin_email', '')
    ];
    
    return $settings;
}

/**
 * Send test email
 * 
 * @param array $settings Email settings
 * @return bool True on success, false on failure
 */
function send_test_email($settings) {
    // Include PHPMailer
    require_once __DIR__ . '/../lib/phpmailer/PHPMailer.php';
    require_once __DIR__ . '/../lib/phpmailer/SMTP.php';
    require_once __DIR__ . '/../lib/phpmailer/Exception.php';
    
    $mail = new PHPMailer\PHPMailer\PHPMailer(true);
    
    try {
        // Server settings
        $mail->isSMTP();
        $mail->Host = $settings['smtp_host'];
        $mail->SMTPAuth = true;
        $mail->Username = $settings['smtp_username'];
        $mail->Password = $settings['smtp_password'];
        $mail->SMTPSecure = $settings['smtp_encryption'];
        $mail->Port = $settings['smtp_port'];
        
        // Recipients
        $mail->setFrom($settings['from_email'], $settings['from_name']);
        $mail->addAddress($settings['admin_email']);
        
        // Content
        $mail->isHTML(true);
        $mail->Subject = 'Test Email from ' . get_setting('site_name', 'Your Website');
        $mail->Body = 'This is a test email from your website. If you received this email, your email settings are configured correctly.';
        $mail->AltBody = 'This is a test email from your website. If you received this email, your email settings are configured correctly.';
        
        $mail->send();
        return true;
    } catch (Exception $e) {
        error_log('Error sending test email: ' . $mail->ErrorInfo);
        return false;
    }
}

/**
 * Get email templates
 * 
 * @return array Array of email templates
 */
function get_email_templates() {
    global $conn;
    
    $result = $conn->query("SELECT * FROM email_templates ORDER BY name");
    
    $templates = [];
    
    while ($row = $result->fetch_assoc()) {
        $templates[] = $row;
    }
    
    return $templates;
}

/**
 * Get email template by ID
 * 
 * @param int $id Template ID
 * @return array|null Template data or null if not found
 */
function get_email_template($id) {
    global $conn;
    
    $stmt = $conn->prepare("SELECT * FROM email_templates WHERE id = ?");
    $stmt->bind_param("i", $id);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows === 0) {
        return null;
    }
    
    return $result->fetch_assoc();
}

/**
 * Get email template by slug
 * 
 * @param string $slug Template slug
 * @return array|null Template data or null if not found
 */
function get_email_template_by_slug($slug) {
    global $conn;
    
    $stmt = $conn->prepare("SELECT * FROM email_templates WHERE slug = ?");
    $stmt->bind_param("s", $slug);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows === 0) {
        return null;
    }
    
    return $result->fetch_assoc();
}

/**
 * Update email template
 * 
 * @param int $id Template ID
 * @param array $data Template data
 * @return bool True on success, false on failure
 */
function update_email_template($id, $data) {
    global $conn;
    
    $stmt = $conn->prepare("UPDATE email_templates SET subject = ?, content = ?, updated_at = NOW() WHERE id = ?");
    $stmt->bind_param("ssi", $data['subject'], $data['content'], $id);
    
    return $stmt->execute();
}

/**
 * Reset email template to default
 * 
 * @param int $id Template ID
 * @return bool True on success, false on failure
 */
function reset_email_template($id) {
    global $conn;
    
    $stmt = $conn->prepare("UPDATE email_templates SET subject = default_subject, content = default_content, updated_at = NOW() WHERE id = ?");
    $stmt->bind_param("i", $id);
    
    return $stmt->execute();
}

/**
 * Get system information
 * 
 * @return array System information
 */
function get_system_info() {
    $info = [
        'php_version' => PHP_VERSION,
        'mysql_version' => mysqli_get_client_info(),
        'server_software' => $_SERVER['SERVER_SOFTWARE'],
        'os' => PHP_OS,
        'max_upload_size' => ini_get('upload_max_filesize'),
        'max_post_size' => ini_get('post_max_size'),
        'memory_limit' => ini_get('memory_limit'),
        'max_execution_time' => ini_get('max_execution_time') . ' seconds',
        'extensions' => get_loaded_extensions(),
        'disabled_functions' => ini_get('disable_functions'),
        'server_time' => date('Y-m-d H:i:s'),
        'server_timezone' => date_default_timezone_get()
    ];
    
    return $info;
}

/**
 * Check if system meets requirements
 * 
 * @return array Requirements check results
 */
function check_system_requirements() {
    $requirements = [
        'php_version' => [
            'name' => 'PHP Version',
            'required' => '7.0.0',
            'current' => PHP_VERSION,
            'status' => version_compare(PHP_VERSION, '7.0.0', '>=')
        ],
        'mysql_version' => [
            'name' => 'MySQL Version',
            'required' => '5.6.0',
            'current' => mysqli_get_client_info(),
            'status' => version_compare(mysqli_get_client_info(), '5.6.0', '>=')
        ],
        'extensions' => [
            'mysqli' => [
                'name' => 'MySQLi Extension',
                'status' => extension_loaded('mysqli')
            ],
            'gd' => [
                'name' => 'GD Extension',
                'status' => extension_loaded('gd')
            ],
            'curl' => [
                'name' => 'cURL Extension',
                'status' => extension_loaded('curl')
            ],
            'json' => [
                'name' => 'JSON Extension',
                'status' => extension_loaded('json')
            ],
            'mbstring' => [
                'name' => 'Multibyte String Extension',
                'status' => extension_loaded('mbstring')
            ]
        ],
        'writable_directories' => [
            'uploads' => [
                'name' => 'Uploads Directory',
                'path' => __DIR__ . '/../../uploads',
                'status' => is_writable(__DIR__ . '/../../uploads')
            ],
            'backups' => [
                'name' => 'Backups Directory',
                'path' => __DIR__ . '/../backups',
                'status' => is_writable(__DIR__ . '/../backups') || mkdir(__DIR__ . '/../backups', 0755, true)
            ]
        ]
    ];
    
    return $requirements;
}

/**
 * Get admin logo path
 * 
 * @return string Admin logo path
 */
function get_admin_logo_path() {
    $logo_path = get_setting('admin_logo_path', '../admin/images/logo.png');
    
    // Check if file exists
    if (!file_exists(__DIR__ . '/../../' . $logo_path)) {
        // Use default logo
        $logo_path = '../admin/images/logo.png';
    }
    
    return $logo_path;
}

/**
 * Update admin logo path
 * 
 * @param string $path Logo path
 * @return bool True on success, false on failure
 */
function update_admin_logo_path($path) {
    return update_setting('admin_logo_path', $path);
}
