/* Inbox Page Styles */
.inbox-page {
    background-color: #f8f9fa;
}

.inbox-wrapper {
    max-width: 100%;
    margin: 0 auto;
}

/* Override admin-container max-width for inbox page */
.inbox-page .admin-container,
body.inbox-page .admin-container {
    max-width: none !important;
    width: 100% !important;
    padding: var(--spacing-4);
}

/* Ensure inbox wrapper uses full width */
.inbox-page .inbox-wrapper,
body.inbox-page .inbox-wrapper {
    max-width: none !important;
    width: 100% !important;
    margin: 0;
}

/* Inbox Header */
.inbox-header {
    background: #fff;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.inbox-stats {
    display: flex;
    gap: 20px;
    margin-top: 15px;
}

.inbox-stat {
    background: #f8f9fa;
    padding: 10px 15px;
    border-radius: 6px;
    text-align: center;
    min-width: 100px;
}

.inbox-stat-number {
    font-size: 24px;
    font-weight: bold;
    color: #333;
}

.inbox-stat-label {
    font-size: 12px;
    color: #666;
    text-transform: uppercase;
}

/* Search Input Group Styling */
.search-input-group {
    display: flex;
    align-items: center;
    gap: 5px;
    flex-wrap: nowrap;
}

.search-input-group input {
    min-width: 200px;
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
    flex: 1;
}

.search-input-group .admin-btn {
    padding: 8px 12px;
    min-width: auto;
    flex-shrink: 0;
}

.search-input-group .admin-btn.secondary {
    background: #6c757d;
    color: #fff;
    border-color: #6c757d;
}

.search-input-group .admin-btn.secondary:hover {
    background: #5a6268;
    border-color: #545b62;
}

/* Fix for admin-table-actions layout */
.admin-table-actions {
    display: flex !important;
    justify-content: space-between !important;
    align-items: center !important;
    flex-wrap: wrap !important;
    gap: 16px !important;
    background: #fff;
    padding: 20px;
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
    margin-bottom: 24px;
}

.admin-table-filters {
    display: flex !important;
    flex-wrap: wrap !important;
    gap: 16px !important;
    flex: 1 !important;
    align-items: center !important;
}

.admin-table-filter {
    display: flex !important;
    align-items: center !important;
    gap: 8px !important;
    white-space: nowrap !important;
}

.admin-table-bulk-actions {
    display: flex !important;
    align-items: center !important;
    gap: 10px !important;
    flex-shrink: 0 !important;
}

.filter-label {
    font-weight: 500;
    color: #333;
    font-size: 14px;
}

/* Message List */
.message-list {
    background: #fff;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.message-item {
    border-bottom: 1px solid #e9ecef;
    padding: 15px 20px;
    transition: background-color 0.2s ease;
    cursor: pointer;
}

.message-item:hover {
    background-color: #f8f9fa;
}

.message-item.unread {
    background-color: #fff3cd;
    border-left: 4px solid #ffc107;
}

.message-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.message-sender {
    font-weight: 600;
    color: #333;
}

.message-email {
    color: #666;
    font-size: 14px;
    margin-left: 10px;
}

.message-date {
    color: #666;
    font-size: 13px;
}

.message-subject {
    font-weight: 500;
    color: #333;
    margin-bottom: 5px;
}

.message-preview {
    color: #666;
    font-size: 14px;
    line-height: 1.4;
    max-height: 40px;
    overflow: hidden;
}

/* Message Actions */
.message-actions {
    display: flex;
    gap: 10px;
    margin-top: 10px;
}

.message-action {
    padding: 4px 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
    background: #fff;
    color: #666;
    text-decoration: none;
    font-size: 12px;
    transition: all 0.2s ease;
}

.message-action:hover {
    background: #f8f9fa;
    color: #333;
}

.message-action.primary {
    background: #007bff;
    color: #fff;
    border-color: #007bff;
}

.message-action.primary:hover {
    background: #0056b3;
    border-color: #0056b3;
}

.message-action.danger {
    background: #dc3545;
    color: #fff;
    border-color: #dc3545;
}

.message-action.danger:hover {
    background: #c82333;
    border-color: #c82333;
}

/* Single Message View */
.message-detail {
    background: #fff;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.message-detail-header {
    background: #f8f9fa;
    padding: 20px;
    border-bottom: 1px solid #e9ecef;
}

.message-detail-body {
    padding: 20px;
}

.message-meta {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin-bottom: 20px;
}

.meta-item {
    display: flex;
    flex-direction: column;
}

.meta-label {
    font-weight: 600;
    color: #333;
    font-size: 12px;
    text-transform: uppercase;
    margin-bottom: 5px;
}

.meta-value {
    color: #666;
    font-size: 14px;
}

.message-content {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 6px;
    border: 1px solid #e9ecef;
    line-height: 1.6;
    white-space: pre-wrap;
}

/* Bulk Actions */
.bulk-actions {
    background: #fff;
    padding: 15px 20px;
    border-bottom: 1px solid #e9ecef;
    display: flex;
    align-items: center;
    gap: 15px;
}

.bulk-select {
    display: flex;
    align-items: center;
    gap: 10px;
}

/* Pagination */
.pagination-wrapper {
    background: #fff;
    padding: 15px 20px;
    border-top: 1px solid #e9ecef;
    display: flex;
    justify-content: center;
}

/* Empty State */
.empty-inbox {
    text-align: center;
    padding: 60px 20px;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.empty-inbox-icon {
    font-size: 48px;
    color: #ddd;
    margin-bottom: 20px;
}

.empty-inbox h3 {
    color: #333;
    margin-bottom: 10px;
}

.empty-inbox p {
    color: #666;
    font-size: 14px;
}

/* Responsive Design */
@media (max-width: 768px) {
    /* Search input group responsive */
    .search-input-group {
        flex-wrap: wrap;
        gap: 8px;
    }

    .search-input-group input {
        min-width: auto;
        flex: 1;
        width: 100%;
    }

    /* Message layout responsive */
    .message-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 5px;
    }

    .message-actions {
        flex-wrap: wrap;
    }

    .message-meta {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 480px) {
    .search-input-group input {
        font-size: 16px; /* Prevent zoom on iOS */
    }
}

/* Dark Mode Support */
body.dark-mode .inbox-page {
    background-color: #1a1a1a;
}

body.dark-mode .inbox-header,
body.dark-mode .message-list,
body.dark-mode .message-detail,
body.dark-mode .empty-inbox {
    background-color: #2d2d2d;
    color: #e0e0e0;
}

body.dark-mode .search-input-group input {
    background-color: #3a3a3a;
    border-color: #404040;
    color: #e0e0e0;
}

body.dark-mode .search-input-group .admin-btn.secondary {
    background-color: #6c757d;
    border-color: #6c757d;
}

body.dark-mode .search-input-group .admin-btn.secondary:hover {
    background-color: #5a6268;
    border-color: #545b62;
}

body.dark-mode .message-item {
    border-bottom-color: #404040;
}

body.dark-mode .message-item:hover {
    background-color: #3a3a3a;
}

body.dark-mode .message-item.unread {
    background-color: #3d3520;
    border-left-color: #ffc107;
}

body.dark-mode .message-content {
    background-color: #3a3a3a;
    border-color: #404040;
}

body.dark-mode .message-detail-header {
    background-color: #3a3a3a;
    border-bottom-color: #404040;
}
