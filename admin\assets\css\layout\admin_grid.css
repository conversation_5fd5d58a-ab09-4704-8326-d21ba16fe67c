/**
 * Admin Grid CSS
 * 
 * This file contains grid and responsive container styles for the admin panel.
 */

/* Container */
.container {
  width: 100%;
  padding-right: var(--spacing-4);
  padding-left: var(--spacing-4);
  margin-right: auto;
  margin-left: auto;
}

.container-fluid {
  width: 100%;
  padding-right: var(--spacing-4);
  padding-left: var(--spacing-4);
  margin-right: auto;
  margin-left: auto;
}

.container-sm {
  max-width: 540px;
}

.container-md {
  max-width: 720px;
}

.container-lg {
  max-width: 960px;
}

.container-xl {
  max-width: 1140px;
}

/* Grid System */
.row {
  display: flex;
  flex-wrap: wrap;
  margin-right: -15px;
  margin-left: -15px;
}

.no-gutters {
  margin-right: 0;
  margin-left: 0;
}

.no-gutters > .col,
.no-gutters > [class*="col-"] {
  padding-right: 0;
  padding-left: 0;
}

.col-1, .col-2, .col-3, .col-4, .col-5, .col-6, 
.col-7, .col-8, .col-9, .col-10, .col-11, .col-12, 
.col, .col-auto, .col-sm-1, .col-sm-2, .col-sm-3, 
.col-sm-4, .col-sm-5, .col-sm-6, .col-sm-7, .col-sm-8, 
.col-sm-9, .col-sm-10, .col-sm-11, .col-sm-12, .col-sm, 
.col-sm-auto, .col-md-1, .col-md-2, .col-md-3, .col-md-4, 
.col-md-5, .col-md-6, .col-md-7, .col-md-8, .col-md-9, 
.col-md-10, .col-md-11, .col-md-12, .col-md, .col-md-auto, 
.col-lg-1, .col-lg-2, .col-lg-3, .col-lg-4, .col-lg-5, 
.col-lg-6, .col-lg-7, .col-lg-8, .col-lg-9, .col-lg-10, 
.col-lg-11, .col-lg-12, .col-lg, .col-lg-auto, .col-xl-1, 
.col-xl-2, .col-xl-3, .col-xl-4, .col-xl-5, .col-xl-6, 
.col-xl-7, .col-xl-8, .col-xl-9, .col-xl-10, .col-xl-11, 
.col-xl-12, .col-xl, .col-xl-auto {
  position: relative;
  width: 100%;
  padding-right: 15px;
  padding-left: 15px;
}

.col {
  flex-basis: 0;
  flex-grow: 1;
  max-width: 100%;
}

.col-auto {
  flex: 0 0 auto;
  width: auto;
  max-width: 100%;
}

.col-1 {
  flex: 0 0 8.333333%;
  max-width: 8.333333%;
}

.col-2 {
  flex: 0 0 16.666667%;
  max-width: 16.666667%;
}

.col-3 {
  flex: 0 0 25%;
  max-width: 25%;
}

.col-4 {
  flex: 0 0 33.333333%;
  max-width: 33.333333%;
}

.col-5 {
  flex: 0 0 41.666667%;
  max-width: 41.666667%;
}

.col-6 {
  flex: 0 0 50%;
  max-width: 50%;
}

.col-7 {
  flex: 0 0 58.333333%;
  max-width: 58.333333%;
}

.col-8 {
  flex: 0 0 66.666667%;
  max-width: 66.666667%;
}

.col-9 {
  flex: 0 0 75%;
  max-width: 75%;
}

.col-10 {
  flex: 0 0 83.333333%;
  max-width: 83.333333%;
}

.col-11 {
  flex: 0 0 91.666667%;
  max-width: 91.666667%;
}

.col-12 {
  flex: 0 0 100%;
  max-width: 100%;
}

/* Modern Grid Layout */
.grid {
  display: grid;
  gap: var(--spacing-4);
}

.grid-2 {
  grid-template-columns: repeat(2, 1fr);
}

.grid-3 {
  grid-template-columns: repeat(3, 1fr);
}

.grid-4 {
  grid-template-columns: repeat(4, 1fr);
}

.grid-5 {
  grid-template-columns: repeat(5, 1fr);
}

.grid-6 {
  grid-template-columns: repeat(6, 1fr);
}

.grid-auto-sm {
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
}

.grid-auto-md {
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
}

.grid-auto-lg {
  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
}

.gap-0 {
  gap: 0;
}

.gap-1 {
  gap: var(--spacing-1);
}

.gap-2 {
  gap: var(--spacing-2);
}

.gap-3 {
  gap: var(--spacing-3);
}

.gap-4 {
  gap: var(--spacing-4);
}

.gap-5 {
  gap: var(--spacing-5);
}

.gap-6 {
  gap: var(--spacing-6);
}

/* Admin Layout Specific Grids */
.admin-content-grid {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: var(--spacing-4);
}

.admin-dashboard-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--spacing-4);
}

.admin-stats-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: var(--spacing-4);
}

.admin-form-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: var(--spacing-4);
}

/* Responsive Breakpoints */
@media (min-width: 576px) {
  .container {
    max-width: 540px;
  }
  
  .col-sm {
    flex-basis: 0;
    flex-grow: 1;
    max-width: 100%;
  }
  
  .col-sm-auto {
    flex: 0 0 auto;
    width: auto;
    max-width: 100%;
  }
  
  .col-sm-1 {
    flex: 0 0 8.333333%;
    max-width: 8.333333%;
  }
  
  .col-sm-2 {
    flex: 0 0 16.666667%;
    max-width: 16.666667%;
  }
  
  .col-sm-3 {
    flex: 0 0 25%;
    max-width: 25%;
  }
  
  .col-sm-4 {
    flex: 0 0 33.333333%;
    max-width: 33.333333%;
  }
  
  .col-sm-5 {
    flex: 0 0 41.666667%;
    max-width: 41.666667%;
  }
  
  .col-sm-6 {
    flex: 0 0 50%;
    max-width: 50%;
  }
  
  .col-sm-7 {
    flex: 0 0 58.333333%;
    max-width: 58.333333%;
  }
  
  .col-sm-8 {
    flex: 0 0 66.666667%;
    max-width: 66.666667%;
  }
  
  .col-sm-9 {
    flex: 0 0 75%;
    max-width: 75%;
  }
  
  .col-sm-10 {
    flex: 0 0 83.333333%;
    max-width: 83.333333%;
  }
  
  .col-sm-11 {
    flex: 0 0 91.666667%;
    max-width: 91.666667%;
  }
  
  .col-sm-12 {
    flex: 0 0 100%;
    max-width: 100%;
  }
}

@media (min-width: 768px) {
  .container {
    max-width: 720px;
  }
  
  .col-md {
    flex-basis: 0;
    flex-grow: 1;
    max-width: 100%;
  }
  
  .col-md-auto {
    flex: 0 0 auto;
    width: auto;
    max-width: 100%;
  }
  
  .col-md-1 {
    flex: 0 0 8.333333%;
    max-width: 8.333333%;
  }
  
  .col-md-2 {
    flex: 0 0 16.666667%;
    max-width: 16.666667%;
  }
  
  .col-md-3 {
    flex: 0 0 25%;
    max-width: 25%;
  }
  
  .col-md-4 {
    flex: 0 0 33.333333%;
    max-width: 33.333333%;
  }
  
  .col-md-5 {
    flex: 0 0 41.666667%;
    max-width: 41.666667%;
  }
  
  .col-md-6 {
    flex: 0 0 50%;
    max-width: 50%;
  }
  
  .col-md-7 {
    flex: 0 0 58.333333%;
    max-width: 58.333333%;
  }
  
  .col-md-8 {
    flex: 0 0 66.666667%;
    max-width: 66.666667%;
  }
  
  .col-md-9 {
    flex: 0 0 75%;
    max-width: 75%;
  }
  
  .col-md-10 {
    flex: 0 0 83.333333%;
    max-width: 83.333333%;
  }
  
  .col-md-11 {
    flex: 0 0 91.666667%;
    max-width: 91.666667%;
  }
  
  .col-md-12 {
    flex: 0 0 100%;
    max-width: 100%;
  }
}

@media (min-width: 992px) {
  .container {
    max-width: 960px;
  }
  
  .col-lg {
    flex-basis: 0;
    flex-grow: 1;
    max-width: 100%;
  }
  
  .col-lg-auto {
    flex: 0 0 auto;
    width: auto;
    max-width: 100%;
  }
  
  .col-lg-1 {
    flex: 0 0 8.333333%;
    max-width: 8.333333%;
  }
  
  .col-lg-2 {
    flex: 0 0 16.666667%;
    max-width: 16.666667%;
  }
  
  .col-lg-3 {
    flex: 0 0 25%;
    max-width: 25%;
  }
  
  .col-lg-4 {
    flex: 0 0 33.333333%;
    max-width: 33.333333%;
  }
  
  .col-lg-5 {
    flex: 0 0 41.666667%;
    max-width: 41.666667%;
  }
  
  .col-lg-6 {
    flex: 0 0 50%;
    max-width: 50%;
  }
  
  .col-lg-7 {
    flex: 0 0 58.333333%;
    max-width: 58.333333%;
  }
  
  .col-lg-8 {
    flex: 0 0 66.666667%;
    max-width: 66.666667%;
  }
  
  .col-lg-9 {
    flex: 0 0 75%;
    max-width: 75%;
  }
  
  .col-lg-10 {
    flex: 0 0 83.333333%;
    max-width: 83.333333%;
  }
  
  .col-lg-11 {
    flex: 0 0 91.666667%;
    max-width: 91.666667%;
  }
  
  .col-lg-12 {
    flex: 0 0 100%;
    max-width: 100%;
  }
}

@media (min-width: 1200px) {
  .container {
    max-width: 1140px;
  }
  
  .col-xl {
    flex-basis: 0;
    flex-grow: 1;
    max-width: 100%;
  }
  
  .col-xl-auto {
    flex: 0 0 auto;
    width: auto;
    max-width: 100%;
  }
  
  .col-xl-1 {
    flex: 0 0 8.333333%;
    max-width: 8.333333%;
  }
  
  .col-xl-2 {
    flex: 0 0 16.666667%;
    max-width: 16.666667%;
  }
  
  .col-xl-3 {
    flex: 0 0 25%;
    max-width: 25%;
  }
  
  .col-xl-4 {
    flex: 0 0 33.333333%;
    max-width: 33.333333%;
  }
  
  .col-xl-5 {
    flex: 0 0 41.666667%;
    max-width: 41.666667%;
  }
  
  .col-xl-6 {
    flex: 0 0 50%;
    max-width: 50%;
  }
  
  .col-xl-7 {
    flex: 0 0 58.333333%;
    max-width: 58.333333%;
  }
  
  .col-xl-8 {
    flex: 0 0 66.666667%;
    max-width: 66.666667%;
  }
  
  .col-xl-9 {
    flex: 0 0 75%;
    max-width: 75%;
  }
  
  .col-xl-10 {
    flex: 0 0 83.333333%;
    max-width: 83.333333%;
  }
  
  .col-xl-11 {
    flex: 0 0 91.666667%;
    max-width: 91.666667%;
  }
  
  .col-xl-12 {
    flex: 0 0 100%;
    max-width: 100%;
  }
}
