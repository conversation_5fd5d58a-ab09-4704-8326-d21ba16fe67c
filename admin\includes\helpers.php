<?php
/**
 * Helper Functions
 *
 * This file contains helper functions for redirection, error handling, and other common tasks.
 */

/**
 * Improved redirect function that handles various edge cases
 *
 * @param string $url URL to redirect to
 * @param array $params Optional query parameters to add to the URL
 * @param bool $use_js_fallback Whether to use JavaScript fallback if headers already sent
 * @return void
 */
function safe_redirect($url, $params = [], $use_js_fallback = true) {
    // Validate URL to prevent header injection
    if (strpos($url, "\r") !== false || strpos($url, "\n") !== false) {
        // URL contains line breaks, which could be used for header injection
        $url = 'index.php'; // Default to index if potentially malicious URL
    }

    // Add query parameters if provided
    if (!empty($params) && is_array($params)) {
        $separator = (strpos($url, '?') !== false) ? '&' : '?';
        $query_string = http_build_query($params);
        $url .= $separator . $query_string;
    }

    // Add cache-busting parameter for sensitive operations
    if (strpos($url, 'logout.php') !== false ||
        strpos($url, 'login.php') !== false ||
        strpos($url, 'index.php') !== false) {
        $separator = (strpos($url, '?') !== false) ? '&' : '?';
        $url .= $separator . 'nocache=' . time();
    }

    // Ensure headers haven't been sent yet
    if (!headers_sent($file, $line)) {
        // Set cache control headers for sensitive pages
        if (strpos($url, 'logout.php') !== false ||
            strpos($url, 'login.php') !== false ||
            strpos($url, 'index.php') !== false) {
            header("Cache-Control: no-store, no-cache, must-revalidate, max-age=0");
            header("Cache-Control: post-check=0, pre-check=0", false);
            header("Pragma: no-cache");
            header("Expires: Mon, 26 Jul 1997 05:00:00 GMT");
        }

        header('Location: ' . $url);
    } else {
        // Log that headers were already sent
        error_log("Headers already sent in $file on line $line. Using JavaScript redirect to $url");

        if ($use_js_fallback) {
            // If headers already sent, use JavaScript redirect
            echo '<script>window.location.href="' . htmlspecialchars($url, ENT_QUOTES, 'UTF-8') . '";</script>';
            echo '<noscript><meta http-equiv="refresh" content="0;url=' . htmlspecialchars($url, ENT_QUOTES, 'UTF-8') . '"></noscript>';
            echo '<p>If you are not redirected automatically, please <a href="' . htmlspecialchars($url, ENT_QUOTES, 'UTF-8') . '">click here</a>.</p>';
        }
    }
    exit;
}

/**
 * Display an error message and redirect after a delay
 *
 * @param string $message Error message to display
 * @param string $redirect_url URL to redirect to
 * @param int $delay Delay in seconds before redirecting
 * @return void
 */
function show_error_and_redirect($message, $redirect_url = 'index.php', $delay = 3) {
    $_SESSION['error_message'] = $message;

    // If headers haven't been sent, redirect with the session message
    if (!headers_sent()) {
        safe_redirect($redirect_url);
    } else {
        // Otherwise, display the error and use JavaScript to redirect
        echo '<!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Error</title>
            <style>
                body { font-family: Arial, sans-serif; line-height: 1.6; margin: 0; padding: 20px; background-color: #f5f5f5; }
                .container { max-width: 600px; margin: 50px auto; background-color: white; padding: 20px; border-radius: 5px; box-shadow: 0 2px 5px rgba(0,0,0,0.1); }
                .error { color: #721c24; background-color: #f8d7da; border: 1px solid #f5c6cb; padding: 15px; border-radius: 4px; margin-bottom: 20px; }
                .redirect-message { font-size: 14px; color: #666; margin-top: 20px; }
            </style>
            <script>
                setTimeout(function() {
                    window.location.href = "' . htmlspecialchars($redirect_url, ENT_QUOTES, 'UTF-8') . '";
                }, ' . ($delay * 1000) . ');
            </script>
        </head>
        <body>
            <div class="container">
                <div class="error">' . htmlspecialchars($message, ENT_QUOTES, 'UTF-8') . '</div>
                <div class="redirect-message">You will be redirected in ' . $delay . ' seconds. If not, <a href="' . htmlspecialchars($redirect_url, ENT_QUOTES, 'UTF-8') . '">click here</a>.</div>
            </div>
        </body>
        </html>';
        exit;
    }
}

/**
 * Display a success message and redirect after a delay
 *
 * @param string $message Success message to display
 * @param string $redirect_url URL to redirect to
 * @param int $delay Delay in seconds before redirecting
 * @return void
 */
function show_success_and_redirect($message, $redirect_url = 'index.php', $delay = 2) {
    $_SESSION['success_message'] = $message;

    // If headers haven't been sent, redirect with the session message
    if (!headers_sent()) {
        safe_redirect($redirect_url);
    } else {
        // Otherwise, display the success message and use JavaScript to redirect
        echo '<!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Success</title>
            <style>
                body { font-family: Arial, sans-serif; line-height: 1.6; margin: 0; padding: 20px; background-color: #f5f5f5; }
                .container { max-width: 600px; margin: 50px auto; background-color: white; padding: 20px; border-radius: 5px; box-shadow: 0 2px 5px rgba(0,0,0,0.1); }
                .success { color: #155724; background-color: #d4edda; border: 1px solid #c3e6cb; padding: 15px; border-radius: 4px; margin-bottom: 20px; }
                .redirect-message { font-size: 14px; color: #666; margin-top: 20px; }
            </style>
            <script>
                setTimeout(function() {
                    window.location.href = "' . htmlspecialchars($redirect_url, ENT_QUOTES, 'UTF-8') . '";
                }, ' . ($delay * 1000) . ');
            </script>
        </head>
        <body>
            <div class="container">
                <div class="success">' . htmlspecialchars($message, ENT_QUOTES, 'UTF-8') . '</div>
                <div class="redirect-message">You will be redirected in ' . $delay . ' seconds. If not, <a href="' . htmlspecialchars($redirect_url, ENT_QUOTES, 'UTF-8') . '">click here</a>.</div>
            </div>
        </body>
        </html>';
        exit;
    }
}

/**
 * Check if a file exists and is readable
 *
 * @param string $file_path Path to the file
 * @return bool True if file exists and is readable, false otherwise
 */
function file_exists_and_readable($file_path) {
    return file_exists($file_path) && is_readable($file_path);
}

/**
 * Check if a directory exists and is writable
 *
 * @param string $dir_path Path to the directory
 * @return bool True if directory exists and is writable, false otherwise
 */
function dir_exists_and_writable($dir_path) {
    return is_dir($dir_path) && is_writable($dir_path);
}

/**
 * Simple redirect function for backward compatibility
 * This is a wrapper for the safe_redirect function
 *
 * @param string $url URL to redirect to
 * @param array $params Optional query parameters to add to the URL
 * @return void
 */
function redirect($url, $params = []) {
    safe_redirect($url, $params);
}
