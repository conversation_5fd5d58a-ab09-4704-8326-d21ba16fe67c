/**
 * Enhanced HTML Editor
 *
 * A comprehensive WYSIWYG editor with modern features
 */

class EnhancedEditor {
    constructor(selector, options = {}) {
        this.selector = selector;
        this.options = Object.assign({
            height: 400,
            toolbar: true,
            onChange: null,
            placeholder: 'Start typing...',
            autosave: true,
            autosaveInterval: 30000, // 30 seconds
            showWordCount: true,
            allowCodeView: true,
            allowFullscreen: true,
            theme: 'light'
        }, options);

        this.isCodeView = false;
        this.isFullscreen = false;
        this.autosaveTimer = null;
        this.wordCount = 0;
        this.lastSavedContent = '';

        this.init();
    }

    init() {
        // Get the textarea
        this.textarea = document.querySelector(this.selector);
        if (!this.textarea) return;

        // Store original textarea
        this.originalTextarea = this.textarea;
        this.originalTextarea.style.display = 'none';

        // Create editor container
        this.container = document.createElement('div');
        this.container.className = 'enhanced-editor-container';
        this.container.setAttribute('data-theme', this.options.theme);
        this.textarea.parentNode.insertBefore(this.container, this.textarea);

        // Create toolbar
        if (this.options.toolbar) {
            this.createToolbar();
        }

        // Create editor areas
        this.createEditorAreas();

        // Create status bar
        this.createStatusBar();

        // Set initial content
        this.setContent(this.originalTextarea.value);

        // Set up event listeners
        this.setupEventListeners();

        // Ensure visual mode is active by default
        this.isCodeView = false;
        if (this.visualBtn) this.visualBtn.classList.add('active');
        if (this.codeBtn) this.codeBtn.classList.remove('active');

        // Start autosave if enabled
        if (this.options.autosave) {
            this.startAutosave();
        }

        // Add CSS styles
        this.addStyles();
    }

    createToolbar() {
        this.toolbar = document.createElement('div');
        this.toolbar.className = 'enhanced-editor-toolbar';
        this.container.appendChild(this.toolbar);

        // Create toolbar sections
        const leftSection = document.createElement('div');
        leftSection.className = 'toolbar-section toolbar-left';

        const rightSection = document.createElement('div');
        rightSection.className = 'toolbar-section toolbar-right';

        this.toolbar.appendChild(leftSection);
        this.toolbar.appendChild(rightSection);

        // Add view toggle buttons to right section
        if (this.options.allowCodeView) {
            const viewToggle = document.createElement('div');
            viewToggle.className = 'view-toggle-group';

            this.visualBtn = this.createButton('visual', 'fas fa-eye', 'Visual', true);
            this.codeBtn = this.createButton('code', 'fas fa-code', 'HTML');

            this.visualBtn.addEventListener('click', () => this.switchToVisualMode());
            this.codeBtn.addEventListener('click', () => this.switchToCodeMode());

            viewToggle.appendChild(this.visualBtn);
            viewToggle.appendChild(this.codeBtn);
            rightSection.appendChild(viewToggle);
        }

        if (this.options.allowFullscreen) {
            this.fullscreenBtn = this.createButton('fullscreen', 'fas fa-expand', 'Fullscreen');
            this.fullscreenBtn.addEventListener('click', () => this.toggleFullscreen());
            rightSection.appendChild(this.fullscreenBtn);
        }

        // Add toolbar buttons to left section
        this.createToolbarButtons(leftSection);
    }

    createButton(id, iconClass, text, active = false) {
        const button = document.createElement('button');
        button.type = 'button';
        button.className = `editor-btn ${active ? 'active' : ''}`;
        button.setAttribute('data-command', id);
        button.title = text;

        if (iconClass) {
            const icon = document.createElement('i');
            icon.className = iconClass;
            button.appendChild(icon);
        }

        if (text && !iconClass) {
            button.textContent = text;
        }

        return button;
    }

    createToolbarButtons(container) {
        const buttonGroups = [
            [
                { command: 'undo', icon: 'fas fa-undo', title: 'Undo (Ctrl+Z)' },
                { command: 'redo', icon: 'fas fa-redo', title: 'Redo (Ctrl+Y)' }
            ],
            [
                { command: 'bold', icon: 'fas fa-bold', title: 'Bold (Ctrl+B)' },
                { command: 'italic', icon: 'fas fa-italic', title: 'Italic (Ctrl+I)' },
                { command: 'underline', icon: 'fas fa-underline', title: 'Underline (Ctrl+U)' },
                { command: 'strikethrough', icon: 'fas fa-strikethrough', title: 'Strikethrough' }
            ],
            [
                { command: 'formatBlock', value: 'h1', text: 'H1', title: 'Heading 1' },
                { command: 'formatBlock', value: 'h2', text: 'H2', title: 'Heading 2' },
                { command: 'formatBlock', value: 'h3', text: 'H3', title: 'Heading 3' },
                { command: 'formatBlock', value: 'p', text: 'P', title: 'Paragraph' }
            ],
            [
                { command: 'justifyLeft', icon: 'fas fa-align-left', title: 'Align Left' },
                { command: 'justifyCenter', icon: 'fas fa-align-center', title: 'Align Center' },
                { command: 'justifyRight', icon: 'fas fa-align-right', title: 'Align Right' },
                { command: 'justifyFull', icon: 'fas fa-align-justify', title: 'Justify' }
            ],
            [
                { command: 'insertUnorderedList', icon: 'fas fa-list-ul', title: 'Bullet List' },
                { command: 'insertOrderedList', icon: 'fas fa-list-ol', title: 'Numbered List' },
                { command: 'indent', icon: 'fas fa-indent', title: 'Indent' },
                { command: 'outdent', icon: 'fas fa-outdent', title: 'Outdent' }
            ],
            [
                { command: 'createLink', icon: 'fas fa-link', title: 'Insert Link' },
                { command: 'unlink', icon: 'fas fa-unlink', title: 'Remove Link' },
                { command: 'insertImage', icon: 'fas fa-image', title: 'Insert Image' },
                { command: 'insertTable', icon: 'fas fa-table', title: 'Insert Table' }
            ],
            [
                { command: 'insertHorizontalRule', icon: 'fas fa-minus', title: 'Insert Horizontal Rule' },
                { command: 'removeFormat', icon: 'fas fa-eraser', title: 'Clear Formatting' }
            ]
        ];

        buttonGroups.forEach(group => {
            const groupDiv = document.createElement('div');
            groupDiv.className = 'toolbar-group';

            group.forEach(button => {
                const btn = document.createElement('button');
                btn.type = 'button';
                btn.className = 'toolbar-btn';
                btn.title = button.title;
                btn.setAttribute('data-command', button.command);

                if (button.value) {
                    btn.setAttribute('data-value', button.value);
                }

                if (button.icon) {
                    const icon = document.createElement('i');
                    icon.className = button.icon;
                    btn.appendChild(icon);
                } else if (button.text) {
                    btn.textContent = button.text;
                    btn.classList.add('text-btn');
                }

                btn.addEventListener('click', (e) => {
                    e.preventDefault();
                    this.execCommand(button.command, button.value);
                });

                groupDiv.appendChild(btn);
            });

            container.appendChild(groupDiv);
        });
    }

    createEditorAreas() {
        // Create editor wrapper
        this.editorWrapper = document.createElement('div');
        this.editorWrapper.className = 'editor-wrapper';
        this.container.appendChild(this.editorWrapper);

        // Create visual editor
        this.visualEditor = document.createElement('div');
        this.visualEditor.className = 'visual-editor';
        this.visualEditor.contentEditable = true;
        this.visualEditor.style.height = this.options.height + 'px';
        this.visualEditor.style.display = 'block'; // Ensure it's visible by default
        this.visualEditor.setAttribute('data-placeholder', this.options.placeholder);
        this.editorWrapper.appendChild(this.visualEditor);

        // Create code editor
        this.codeEditor = document.createElement('textarea');
        this.codeEditor.className = 'code-editor';
        this.codeEditor.style.height = this.options.height + 'px';
        this.codeEditor.style.display = 'none';
        this.codeEditor.placeholder = 'Enter HTML code...';
        this.editorWrapper.appendChild(this.codeEditor);
    }

    createStatusBar() {
        this.statusBar = document.createElement('div');
        this.statusBar.className = 'editor-status-bar';
        this.container.appendChild(this.statusBar);

        // Word count
        if (this.options.showWordCount) {
            this.wordCountElement = document.createElement('span');
            this.wordCountElement.className = 'word-count';
            this.wordCountElement.textContent = '0 words';
            this.statusBar.appendChild(this.wordCountElement);
        }

        // Autosave indicator
        if (this.options.autosave) {
            this.autosaveIndicator = document.createElement('span');
            this.autosaveIndicator.className = 'autosave-indicator';
            this.autosaveIndicator.textContent = 'Saved';
            this.statusBar.appendChild(this.autosaveIndicator);
        }
    }

    setupEventListeners() {
        // Visual editor events
        this.visualEditor.addEventListener('input', () => {
            this.updateTextarea();
            this.updateWordCount();
            this.markAsUnsaved();

            if (typeof this.options.onChange === 'function') {
                this.options.onChange(this.getContent());
            }
        });

        // Code editor events
        this.codeEditor.addEventListener('input', () => {
            this.updateFromCodeEditor();
            this.updateWordCount();
            this.markAsUnsaved();

            if (typeof this.options.onChange === 'function') {
                this.options.onChange(this.getContent());
            }
        });

        // Keyboard shortcuts
        this.visualEditor.addEventListener('keydown', (e) => this.handleKeyboardShortcuts(e));
        this.codeEditor.addEventListener('keydown', (e) => this.handleKeyboardShortcuts(e));

        // Prevent form submission on Enter in toolbar
        this.toolbar.addEventListener('keydown', (e) => {
            if (e.key === 'Enter') {
                e.preventDefault();
            }
        });
    }

    handleKeyboardShortcuts(e) {
        if (e.ctrlKey || e.metaKey) {
            switch (e.key.toLowerCase()) {
                case 'b':
                    e.preventDefault();
                    this.execCommand('bold');
                    break;
                case 'i':
                    e.preventDefault();
                    this.execCommand('italic');
                    break;
                case 'u':
                    e.preventDefault();
                    this.execCommand('underline');
                    break;
                case 'z':
                    e.preventDefault();
                    this.execCommand('undo');
                    break;
                case 'y':
                    e.preventDefault();
                    this.execCommand('redo');
                    break;
                case 's':
                    e.preventDefault();
                    this.save();
                    break;
            }
        }
    }

    execCommand(command, value = null) {
        if (this.isCodeView) {
            // Handle commands in code view
            this.handleCodeViewCommand(command, value);
            return;
        }

        // Focus the visual editor
        this.visualEditor.focus();

        // Handle special commands
        switch (command) {
            case 'createLink':
                this.insertLink();
                break;
            case 'insertImage':
                this.insertImage();
                break;
            case 'insertTable':
                this.insertTable();
                break;
            case 'formatBlock':
                document.execCommand(command, false, value);
                break;
            default:
                document.execCommand(command, false, value);
                break;
        }

        // Update textarea and word count
        this.updateTextarea();
        this.updateWordCount();
        this.markAsUnsaved();
    }

    handleCodeViewCommand(command, value) {
        const textarea = this.codeEditor;
        const start = textarea.selectionStart;
        const end = textarea.selectionEnd;
        const selectedText = textarea.value.substring(start, end);

        let insertText = '';

        switch (command) {
            case 'bold':
                insertText = `<strong>${selectedText}</strong>`;
                break;
            case 'italic':
                insertText = `<em>${selectedText}</em>`;
                break;
            case 'underline':
                insertText = `<u>${selectedText}</u>`;
                break;
            case 'createLink':
                const url = prompt('Enter URL:', 'https://');
                if (url) {
                    insertText = `<a href="${url}">${selectedText || 'Link text'}</a>`;
                }
                break;
            case 'insertImage':
                const imgUrl = prompt('Enter image URL:', 'images/');
                if (imgUrl) {
                    insertText = `<img src="${imgUrl}" alt="Image">`;
                }
                break;
            default:
                return;
        }

        if (insertText) {
            textarea.value = textarea.value.substring(0, start) + insertText + textarea.value.substring(end);
            textarea.selectionStart = textarea.selectionEnd = start + insertText.length;
            this.updateFromCodeEditor();
        }
    }

    insertLink() {
        const selection = window.getSelection();
        const selectedText = selection.toString();

        // Create link dialog
        const dialog = this.createDialog('Insert Link', `
            <div class="form-group">
                <label for="link-url">URL:</label>
                <input type="url" id="link-url" class="form-control" placeholder="https://example.com" required>
            </div>
            <div class="form-group">
                <label for="link-text">Link Text:</label>
                <input type="text" id="link-text" class="form-control" value="${selectedText}" placeholder="Link text">
            </div>
            <div class="form-group">
                <label>
                    <input type="checkbox" id="link-new-window"> Open in new window
                </label>
            </div>
        `);

        const urlInput = dialog.querySelector('#link-url');
        const textInput = dialog.querySelector('#link-text');
        const newWindowCheckbox = dialog.querySelector('#link-new-window');

        dialog.onConfirm = () => {
            const url = urlInput.value.trim();
            const text = textInput.value.trim() || url;
            const target = newWindowCheckbox.checked ? ' target="_blank"' : '';

            if (url) {
                const linkHtml = `<a href="${url}"${target}>${text}</a>`;
                document.execCommand('insertHTML', false, linkHtml);
                this.updateTextarea();
            }
        };
    }

    insertImage() {
        // Create image dialog
        const dialog = this.createDialog('Insert Image', `
            <div class="form-group">
                <label for="img-url">Image URL:</label>
                <input type="url" id="img-url" class="form-control" placeholder="https://example.com/image.jpg" required>
            </div>
            <div class="form-group">
                <label for="img-alt">Alt Text:</label>
                <input type="text" id="img-alt" class="form-control" placeholder="Describe the image">
            </div>
            <div class="form-group">
                <label for="img-width">Width (optional):</label>
                <input type="number" id="img-width" class="form-control" placeholder="Auto">
            </div>
            <div class="form-group">
                <label for="img-height">Height (optional):</label>
                <input type="number" id="img-height" class="form-control" placeholder="Auto">
            </div>
        `);

        const urlInput = dialog.querySelector('#img-url');
        const altInput = dialog.querySelector('#img-alt');
        const widthInput = dialog.querySelector('#img-width');
        const heightInput = dialog.querySelector('#img-height');

        dialog.onConfirm = () => {
            const url = urlInput.value.trim();
            const alt = altInput.value.trim();
            const width = widthInput.value.trim();
            const height = heightInput.value.trim();

            if (url) {
                let imgHtml = `<img src="${url}" alt="${alt}"`;
                if (width) imgHtml += ` width="${width}"`;
                if (height) imgHtml += ` height="${height}"`;
                imgHtml += '>';

                document.execCommand('insertHTML', false, imgHtml);
                this.updateTextarea();
            }
        };
    }

    insertTable() {
        // Create table dialog
        const dialog = this.createDialog('Insert Table', `
            <div class="form-group">
                <label for="table-rows">Rows:</label>
                <input type="number" id="table-rows" class="form-control" value="3" min="1" max="20">
            </div>
            <div class="form-group">
                <label for="table-cols">Columns:</label>
                <input type="number" id="table-cols" class="form-control" value="3" min="1" max="10">
            </div>
            <div class="form-group">
                <label>
                    <input type="checkbox" id="table-header" checked> Include header row
                </label>
            </div>
        `);

        const rowsInput = dialog.querySelector('#table-rows');
        const colsInput = dialog.querySelector('#table-cols');
        const headerCheckbox = dialog.querySelector('#table-header');

        dialog.onConfirm = () => {
            const rows = parseInt(rowsInput.value) || 3;
            const cols = parseInt(colsInput.value) || 3;
            const hasHeader = headerCheckbox.checked;

            let tableHtml = '<table class="table table-bordered"><tbody>';

            for (let i = 0; i < rows; i++) {
                tableHtml += '<tr>';
                for (let j = 0; j < cols; j++) {
                    if (i === 0 && hasHeader) {
                        tableHtml += `<th>Header ${j + 1}</th>`;
                    } else {
                        tableHtml += `<td>Cell ${i + 1},${j + 1}</td>`;
                    }
                }
                tableHtml += '</tr>';
            }

            tableHtml += '</tbody></table>';

            document.execCommand('insertHTML', false, tableHtml);
            this.updateTextarea();
        };
    }

    createDialog(title, content) {
        // Create overlay
        const overlay = document.createElement('div');
        overlay.className = 'editor-dialog-overlay';

        // Create dialog
        const dialog = document.createElement('div');
        dialog.className = 'editor-dialog';

        // Create header
        const header = document.createElement('div');
        header.className = 'editor-dialog-header';
        header.innerHTML = `
            <h3>${title}</h3>
            <button type="button" class="editor-dialog-close">&times;</button>
        `;

        // Create body
        const body = document.createElement('div');
        body.className = 'editor-dialog-body';
        body.innerHTML = content;

        // Create footer
        const footer = document.createElement('div');
        footer.className = 'editor-dialog-footer';
        footer.innerHTML = `
            <button type="button" class="editor-btn secondary" data-action="cancel">Cancel</button>
            <button type="button" class="editor-btn primary" data-action="confirm">OK</button>
        `;

        dialog.appendChild(header);
        dialog.appendChild(body);
        dialog.appendChild(footer);
        overlay.appendChild(dialog);

        // Add to document
        document.body.appendChild(overlay);

        // Event handlers
        const closeBtn = header.querySelector('.editor-dialog-close');
        const cancelBtn = footer.querySelector('[data-action="cancel"]');
        const confirmBtn = footer.querySelector('[data-action="confirm"]');

        const close = () => {
            document.body.removeChild(overlay);
        };

        closeBtn.addEventListener('click', close);
        cancelBtn.addEventListener('click', close);
        overlay.addEventListener('click', (e) => {
            if (e.target === overlay) close();
        });

        confirmBtn.addEventListener('click', () => {
            if (dialog.onConfirm) {
                dialog.onConfirm();
            }
            close();
        });

        // Focus first input
        const firstInput = body.querySelector('input, textarea, select');
        if (firstInput) {
            setTimeout(() => firstInput.focus(), 100);
        }

        return dialog;
    }

    switchToVisualMode() {
        if (!this.isCodeView) return;

        this.isCodeView = false;
        this.visualEditor.innerHTML = this.codeEditor.value;
        this.visualEditor.style.display = 'block';
        this.codeEditor.style.display = 'none';

        if (this.visualBtn) this.visualBtn.classList.add('active');
        if (this.codeBtn) this.codeBtn.classList.remove('active');

        this.updateTextarea();
    }

    switchToCodeMode() {
        if (this.isCodeView) return;

        this.isCodeView = true;
        this.codeEditor.value = this.visualEditor.innerHTML;
        this.visualEditor.style.display = 'none';
        this.codeEditor.style.display = 'block';

        if (this.visualBtn) this.visualBtn.classList.remove('active');
        if (this.codeBtn) this.codeBtn.classList.add('active');

        this.codeEditor.focus();
    }

    toggleFullscreen() {
        this.isFullscreen = !this.isFullscreen;

        if (this.isFullscreen) {
            this.container.classList.add('fullscreen');
            if (this.fullscreenBtn) {
                this.fullscreenBtn.querySelector('i').className = 'fas fa-compress';
                this.fullscreenBtn.title = 'Exit Fullscreen';
            }
        } else {
            this.container.classList.remove('fullscreen');
            if (this.fullscreenBtn) {
                this.fullscreenBtn.querySelector('i').className = 'fas fa-expand';
                this.fullscreenBtn.title = 'Fullscreen';
            }
        }
    }

    updateTextarea() {
        const content = this.isCodeView ? this.codeEditor.value : this.visualEditor.innerHTML;
        this.originalTextarea.value = content;
    }

    updateFromCodeEditor() {
        this.visualEditor.innerHTML = this.codeEditor.value;
        this.updateTextarea();
    }

    updateWordCount() {
        if (!this.options.showWordCount || !this.wordCountElement) return;

        const text = this.isCodeView ?
            this.codeEditor.value.replace(/<[^>]*>/g, '') :
            this.visualEditor.textContent || this.visualEditor.innerText || '';

        const words = text.trim().split(/\s+/).filter(word => word.length > 0);
        this.wordCount = words.length;
        this.wordCountElement.textContent = `${this.wordCount} word${this.wordCount !== 1 ? 's' : ''}`;
    }

    markAsUnsaved() {
        if (this.autosaveIndicator) {
            this.autosaveIndicator.textContent = 'Unsaved changes';
            this.autosaveIndicator.classList.add('unsaved');
        }
    }

    markAsSaved() {
        if (this.autosaveIndicator) {
            this.autosaveIndicator.textContent = 'Saved';
            this.autosaveIndicator.classList.remove('unsaved');
        }
    }

    startAutosave() {
        if (this.autosaveTimer) {
            clearInterval(this.autosaveTimer);
        }

        this.autosaveTimer = setInterval(() => {
            this.save();
        }, this.options.autosaveInterval);
    }

    save() {
        const currentContent = this.getContent();
        if (currentContent === this.lastSavedContent) return;

        this.lastSavedContent = currentContent;
        this.markAsSaved();

        // Trigger save event
        const event = new CustomEvent('editorSave', {
            detail: { content: currentContent }
        });
        this.container.dispatchEvent(event);
    }

    getContent() {
        return this.isCodeView ? this.codeEditor.value : this.visualEditor.innerHTML;
    }

    setContent(content) {
        if (this.isCodeView) {
            this.codeEditor.value = content;
            this.updateFromCodeEditor();
        } else {
            this.visualEditor.innerHTML = content;
            this.updateTextarea();
        }
        this.updateWordCount();
        this.lastSavedContent = content;
    }

    getValue() {
        return this.originalTextarea.value;
    }

    setValue(value) {
        this.setContent(value);
    }

    destroy() {
        if (this.autosaveTimer) {
            clearInterval(this.autosaveTimer);
        }

        if (this.container && this.container.parentNode) {
            this.container.parentNode.removeChild(this.container);
        }

        if (this.originalTextarea) {
            this.originalTextarea.style.display = '';
        }
    }

    addStyles() {
        if (document.getElementById('enhanced-editor-styles')) return;

        const style = document.createElement('style');
        style.id = 'enhanced-editor-styles';
        style.textContent = `
            .enhanced-editor-container {
                border: 1px solid #ddd;
                border-radius: 4px;
                background: #fff;
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                position: relative;
            }

            .enhanced-editor-container.fullscreen {
                position: fixed;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                z-index: 9999;
                border-radius: 0;
                border: none;
            }

            .enhanced-editor-toolbar {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 8px 12px;
                background: #f8f9fa;
                border-bottom: 1px solid #ddd;
                flex-wrap: wrap;
                gap: 8px;
            }

            .toolbar-section {
                display: flex;
                align-items: center;
                gap: 4px;
            }

            .toolbar-left {
                flex: 1;
                flex-wrap: wrap;
            }

            .toolbar-group {
                display: flex;
                margin-right: 8px;
                border: 1px solid #ddd;
                border-radius: 4px;
                overflow: hidden;
            }

            .toolbar-btn {
                background: #fff;
                border: none;
                border-right: 1px solid #ddd;
                padding: 6px 8px;
                cursor: pointer;
                font-size: 14px;
                color: #333;
                transition: all 0.2s;
                min-width: 32px;
                height: 32px;
                display: flex;
                align-items: center;
                justify-content: center;
            }

            .toolbar-btn:last-child {
                border-right: none;
            }

            .toolbar-btn:hover {
                background: #e9ecef;
            }

            .toolbar-btn.active {
                background: #007bff;
                color: #fff;
            }

            .toolbar-btn.text-btn {
                font-weight: 600;
                min-width: 36px;
            }

            .view-toggle-group {
                display: flex;
                border: 1px solid #ddd;
                border-radius: 4px;
                overflow: hidden;
                margin-right: 8px;
            }

            .editor-btn {
                background: #fff;
                border: none;
                border-right: 1px solid #ddd;
                padding: 6px 12px;
                cursor: pointer;
                font-size: 14px;
                color: #333;
                transition: all 0.2s;
                display: flex;
                align-items: center;
                gap: 4px;
            }

            .editor-btn:last-child {
                border-right: none;
            }

            .editor-btn:hover {
                background: #e9ecef;
            }

            .editor-btn.active {
                background: #007bff;
                color: #fff;
            }

            .editor-btn.primary {
                background: #007bff;
                color: #fff;
            }

            .editor-btn.secondary {
                background: #6c757d;
                color: #fff;
            }

            .editor-wrapper {
                position: relative;
            }

            .visual-editor {
                padding: 16px;
                min-height: 200px;
                outline: none;
                line-height: 1.6;
                font-size: 14px;
                overflow-y: auto;
            }

            .visual-editor:empty:before {
                content: attr(data-placeholder);
                color: #999;
                font-style: italic;
            }

            .code-editor {
                width: 100%;
                padding: 16px;
                border: none;
                outline: none;
                font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
                font-size: 13px;
                line-height: 1.5;
                resize: none;
                background: #f8f9fa;
            }

            .editor-status-bar {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 8px 12px;
                background: #f8f9fa;
                border-top: 1px solid #ddd;
                font-size: 12px;
                color: #666;
            }

            .word-count {
                font-weight: 500;
            }

            .autosave-indicator {
                font-weight: 500;
                color: #28a745;
            }

            .autosave-indicator.unsaved {
                color: #ffc107;
            }

            .editor-dialog-overlay {
                position: fixed;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background: rgba(0, 0, 0, 0.5);
                z-index: 10000;
                display: flex;
                align-items: center;
                justify-content: center;
            }

            .editor-dialog {
                background: #fff;
                border-radius: 8px;
                box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
                max-width: 500px;
                width: 90%;
                max-height: 80vh;
                overflow: hidden;
            }

            .editor-dialog-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 16px 20px;
                border-bottom: 1px solid #ddd;
                background: #f8f9fa;
            }

            .editor-dialog-header h3 {
                margin: 0;
                font-size: 18px;
                font-weight: 600;
            }

            .editor-dialog-close {
                background: none;
                border: none;
                font-size: 24px;
                cursor: pointer;
                color: #666;
                padding: 0;
                width: 30px;
                height: 30px;
                display: flex;
                align-items: center;
                justify-content: center;
            }

            .editor-dialog-body {
                padding: 20px;
                max-height: 60vh;
                overflow-y: auto;
            }

            .editor-dialog-footer {
                display: flex;
                justify-content: flex-end;
                gap: 8px;
                padding: 16px 20px;
                border-top: 1px solid #ddd;
                background: #f8f9fa;
            }

            .form-group {
                margin-bottom: 16px;
            }

            .form-group label {
                display: block;
                margin-bottom: 4px;
                font-weight: 500;
                color: #333;
            }

            .form-control {
                width: 100%;
                padding: 8px 12px;
                border: 1px solid #ddd;
                border-radius: 4px;
                font-size: 14px;
                transition: border-color 0.2s;
            }

            .form-control:focus {
                outline: none;
                border-color: #007bff;
                box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
            }

            @media (max-width: 768px) {
                .enhanced-editor-toolbar {
                    flex-direction: column;
                    align-items: stretch;
                }

                .toolbar-section {
                    justify-content: center;
                }

                .toolbar-left {
                    order: 2;
                    margin-top: 8px;
                }

                .toolbar-right {
                    order: 1;
                }

                .toolbar-group {
                    margin: 2px;
                }

                .editor-dialog {
                    margin: 20px;
                    width: calc(100% - 40px);
                }
            }
        `;

        document.head.appendChild(style);
    }
}

// Backward compatibility - alias for existing code
window.SimpleEditor = EnhancedEditor;
