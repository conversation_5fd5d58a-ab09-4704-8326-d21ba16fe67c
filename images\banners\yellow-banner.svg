<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg width="1920" height="200" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="yellowGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#f1ca2f;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#e0bc20;stop-opacity:1" />
    </linearGradient>
    <pattern id="pattern" patternUnits="userSpaceOnUse" width="30" height="30" patternTransform="rotate(45)">
      <rect width="30" height="30" fill="url(#yellowGradient)"/>
      <rect width="15" height="15" fill="rgba(255,255,255,0.1)"/>
      <rect x="15" y="15" width="15" height="15" fill="rgba(255,255,255,0.1)"/>
    </pattern>
  </defs>
  <rect width="100%" height="100%" fill="url(#yellowGradient)" />
  <rect width="100%" height="100%" fill="url(#pattern)" opacity="0.3" />
</svg>
