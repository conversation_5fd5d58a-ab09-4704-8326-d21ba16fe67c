<?php
session_start();
require_once 'config.php';

// Check if user is already logged in
if (isset($_SESSION['user_id'])) {
    redirect('inbox.php');
}

$error = '';
$success = '';
$token_valid = false;
$user_id = 0;

// Check if token is provided
if (isset($_GET['token']) && !empty($_GET['token'])) {
    $token = sanitize($_GET['token']);

    // Check if token exists and is valid
    $sql = "SELECT id, username FROM users WHERE reset_token = '$token' AND reset_token_expires > NOW()";
    $result = $conn->query($sql);

    if ($result->num_rows == 1) {
        $user = $result->fetch_assoc();
        $token_valid = true;
        $user_id = $user['id'];
    } else {
        $error = "Invalid or expired token. Please request a new password reset link.";
    }
} else {
    $error = "No reset token provided. Please request a password reset link.";
}

// Process form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST' && $token_valid) {
    $password = $_POST['password'];
    $confirm_password = $_POST['confirm_password'];

    // Validate input
    if (empty($password) || empty($confirm_password)) {
        $error = "Please fill in all required fields";
    } elseif ($password !== $confirm_password) {
        $error = "Passwords do not match";
    } elseif (strlen($password) < 8) {
        $error = "Password must be at least 8 characters long";
    } else {
        // Hash password
        $hashed_password = password_hash($password, PASSWORD_DEFAULT);

        // Update user password and clear reset token
        $update_sql = "UPDATE users SET password = '$hashed_password', reset_token = NULL, reset_token_expires = NULL WHERE id = $user_id";

        if ($conn->query($update_sql) === TRUE) {
            $success = "Your password has been reset successfully. You can now login with your new password.";
            $token_valid = false; // Hide the form
        } else {
            $error = "An error occurred. Please try again later.";
        }
    }
}

// Set page title
$page_title = "Reset Password";
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> | Manage Inc.</title>
    <link rel="stylesheet" href="../css/style.css?v=<?php echo time(); ?>">
    <link rel="stylesheet" href="css/admin-style.css?v=<?php echo time(); ?>">
    <link href="https://fonts.googleapis.com/css2?family=Open+Sans:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <style>
        body {
            background-color: #f5f5f5;
            background-image: linear-gradient(135deg, #f5f5f5 0%, #e0e0e0 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            min-height: 100vh;
            padding: 20px;
            margin: 0;
            font-family: 'Open Sans', sans-serif;
        }

        .login-container {
            max-width: 420px;
            width: 100%;
            padding: 40px;
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            position: relative;
            overflow: hidden;
        }

        .login-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 5px;
            background: #f1ca2f;
        }

        .login-header {
            text-align: center;
            margin-bottom: 35px;
        }

        .login-header img {
            max-width: 200px;
            margin-bottom: 25px;
        }

        .login-header h2 {
            color: #3c3c45;
            font-weight: 600;
            margin-bottom: 10px;
            font-size: 24px;
        }

        .login-header p {
            color: #666;
            margin-top: 0;
            font-size: 14px;
        }

        .admin-alert {
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 25px;
            font-size: 14px;
            display: flex;
            align-items: center;
        }

        .admin-alert.error {
            background-color: #ffebee;
            color: #c62828;
            border-left: 4px solid #c62828;
        }

        .admin-alert.success {
            background-color: #e8f5e9;
            color: #2e7d32;
            border-left: 4px solid #2e7d32;
        }

        .admin-alert i {
            margin-right: 10px;
            font-size: 16px;
        }

        .login-form .form-group {
            margin-bottom: 20px;
        }

        .login-form label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #3c3c45;
            font-size: 14px;
        }

        .login-form .input-group {
            position: relative;
        }

        .login-form .input-icon {
            position: absolute;
            left: 15px;
            top: 50%;
            transform: translateY(-50%);
            color: #999;
        }

        .login-form input {
            width: 100%;
            padding: 14px 15px 14px 45px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 15px;
            transition: all 0.3s ease;
            background-color: #f9f9f9;
        }

        .login-form input:focus {
            border-color: #f1ca2f;
            background-color: #fff;
            box-shadow: 0 0 0 3px rgba(241, 202, 47, 0.2);
            outline: none;
        }

        .login-form button {
            width: 100%;
            padding: 14px;
            background-color: #3c3c45;
            color: #fff;
            border: none;
            border-radius: 4px;
            font-weight: 600;
            font-size: 16px;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-top: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .login-form button i {
            margin-right: 8px;
        }

        .login-form button:hover {
            background-color: #4a4a52;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }

        .login-footer {
            text-align: center;
            margin-top: 35px;
            color: #777;
            font-size: 13px;
            border-top: 1px solid #eee;
            padding-top: 20px;
        }

        .back-to-login {
            display: block;
            text-align: center;
            margin-top: 20px;
            color: #666;
            font-size: 14px;
            text-decoration: none;
            transition: color 0.3s ease;
        }

        .back-to-login:hover {
            color: #f1ca2f;
            text-decoration: underline;
        }

        .back-to-login i {
            margin-right: 5px;
        }

        .password-strength {
            margin-top: 8px;
            font-size: 12px;
        }

        .password-strength-meter {
            height: 4px;
            background-color: #eee;
            margin-top: 5px;
            border-radius: 2px;
            overflow: hidden;
        }

        .password-strength-meter div {
            height: 100%;
            width: 0;
            transition: width 0.3s ease;
        }

        .password-strength-meter.weak div {
            width: 25%;
            background-color: #f44336;
        }

        .password-strength-meter.medium div {
            width: 50%;
            background-color: #ff9800;
        }

        .password-strength-meter.strong div {
            width: 75%;
            background-color: #4caf50;
        }

        .password-strength-meter.very-strong div {
            width: 100%;
            background-color: #2e7d32;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-header">
            <img src="../images/logo.png" alt="Manage Incorporated">
            <h2>Reset Password</h2>
            <p>Create a new password for your account</p>
        </div>

        <?php if (!empty($error)): ?>
            <div class="admin-alert error">
                <i class="fas fa-exclamation-circle"></i> <?php echo $error; ?>
            </div>
        <?php endif; ?>

        <?php if (!empty($success)): ?>
            <div class="admin-alert success">
                <i class="fas fa-check-circle"></i> <?php echo $success; ?>
            </div>
        <?php endif; ?>

        <?php if ($token_valid): ?>
            <form class="login-form" method="post" action="">
                <div class="form-group">
                    <label for="password">New Password</label>
                    <div class="input-group">
                        <i class="fas fa-lock input-icon"></i>
                        <input type="password" id="password" name="password" placeholder="Enter new password" required minlength="8">
                    </div>
                    <div class="password-strength">
                        <span id="password-strength-text">Password strength</span>
                        <div class="password-strength-meter">
                            <div></div>
                        </div>
                    </div>
                </div>
                <div class="form-group">
                    <label for="confirm_password">Confirm Password</label>
                    <div class="input-group">
                        <i class="fas fa-lock input-icon"></i>
                        <input type="password" id="confirm_password" name="confirm_password" placeholder="Confirm new password" required minlength="8">
                    </div>
                </div>
                <button type="submit">
                    <i class="fas fa-key"></i> Reset Password
                </button>
            </form>
        <?php endif; ?>

        <a href="index.php" class="back-to-login">
            <i class="fas fa-arrow-left"></i> Back to Login
        </a>

        <div class="login-footer">
            <p>&copy; <?php echo date('Y'); ?> Manage Inc. All Rights Reserved.</p>
        </div>
    </div>

    <script>
        // Password strength meter
        const passwordInput = document.getElementById('password');
        const strengthMeter = document.querySelector('.password-strength-meter');
        const strengthText = document.getElementById('password-strength-text');

        if (passwordInput) {
            passwordInput.addEventListener('input', function() {
                const password = this.value;
                let strength = 0;

                // Length check
                if (password.length >= 8) strength += 1;

                // Uppercase check
                if (/[A-Z]/.test(password)) strength += 1;

                // Lowercase check
                if (/[a-z]/.test(password)) strength += 1;

                // Number check
                if (/[0-9]/.test(password)) strength += 1;

                // Special character check
                if (/[^A-Za-z0-9]/.test(password)) strength += 1;

                // Update UI
                strengthMeter.className = 'password-strength-meter';

                if (password.length === 0) {
                    strengthText.textContent = 'Password strength';
                } else if (strength < 2) {
                    strengthMeter.classList.add('weak');
                    strengthText.textContent = 'Weak password';
                } else if (strength < 3) {
                    strengthMeter.classList.add('medium');
                    strengthText.textContent = 'Medium password';
                } else if (strength < 5) {
                    strengthMeter.classList.add('strong');
                    strengthText.textContent = 'Strong password';
                } else {
                    strengthMeter.classList.add('very-strong');
                    strengthText.textContent = 'Very strong password';
                }
            });
        }
    </script>
</body>
</html>
