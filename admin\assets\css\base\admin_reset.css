/**
 * Admin Reset CSS
 * 
 * This file resets or normalizes styles for the admin panel.
 */

/* Box sizing rules */
*,
*::before,
*::after {
  box-sizing: border-box;
}

/* Remove default margin and padding */
html,
body,
h1,
h2,
h3,
h4,
h5,
h6,
p,
ul,
ol,
li,
figure,
figcaption,
blockquote,
dl,
dd {
  margin: 0;
  padding: 0;
}

/* Set core body defaults */
body {
  min-height: 100vh;
  scroll-behavior: smooth;
  text-rendering: optimizeSpeed;
  line-height: 1.5;
  font-family: var(--font-family-sans);
  color: var(--text-color);
  background-color: var(--background-color);
  overflow-x: hidden;
  max-width: 100vw;
}

/* Remove list styles on ul, ol elements */
ul,
ol {
  list-style: none;
}

/* Make images easier to work with */
img {
  max-width: 100%;
  display: block;
}

/* Inherit fonts for inputs and buttons */
input,
button,
textarea,
select {
  font: inherit;
}

/* Remove all animations and transitions for people that prefer not to see them */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

/* Remove default button styles */
button {
  background: none;
  border: none;
  cursor: pointer;
  padding: 0;
}

/* Remove default anchor styles */
a {
  text-decoration: none;
  color: inherit;
}

/* Set default focus styles */
:focus {
  outline: 2px solid var(--primary-color);
  outline-offset: 2px;
}

/* Remove outline for mouse users, keep for keyboard */
:focus:not(:focus-visible) {
  outline: none;
}

/* Force Standards Mode */
html {
  overflow-y: scroll;
}

/* Tables */
table {
  border-collapse: collapse;
  width: 100%;
}

/* Form elements */
input,
textarea,
select {
  width: 100%;
  padding: var(--spacing-2) var(--spacing-3);
  border: 1px solid var(--border-color);
  border-radius: var(--input-border-radius);
  background-color: var(--white);
  transition: border-color var(--transition-fast) ease, box-shadow var(--transition-fast) ease;
}

input:focus,
textarea:focus,
select:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(241, 202, 47, 0.15);
}

/* Placeholder text */
::placeholder {
  color: var(--text-muted);
  opacity: 1;
}

/* Selection */
::selection {
  background-color: var(--primary-color);
  color: var(--secondary-color);
}

/* Scrollbar */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--gray-100);
}

::-webkit-scrollbar-thumb {
  background: var(--gray-300);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--gray-400);
}
