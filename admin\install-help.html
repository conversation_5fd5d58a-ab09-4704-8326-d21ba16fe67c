<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Installation Guide - Manage Inc</title>
    <link rel="stylesheet" href="admin/css/admin-style.css">
    <link rel="stylesheet" href="admin/css/help-documentation.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <style>
        :root {
            --primary-color: #4a6cf7;
            --secondary-color: #2e3856;
            --text-color: #333;
            --text-light: #666;
            --bg-light: #f5f7fa;
            --border-color: #e1e5eb;
            --success-color: #28a745;
            --warning-color: #ffc107;
            --danger-color: #dc3545;
            --info-color: #17a2b8;
            --radius-sm: 4px;
            --radius-md: 6px;
            --radius-lg: 8px;
            --shadow-sm: 0 1px 3px rgba(0,0,0,0.1);
            --shadow-md: 0 4px 6px rgba(0,0,0,0.1);
            --shadow-lg: 0 10px 15px rgba(0,0,0,0.1);
            --font-size-xs: 12px;
            --font-size-sm: 14px;
            --font-size-md: 16px;
            --font-size-lg: 18px;
            --font-size-xl: 20px;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: var(--text-color);
            background-color: var(--bg-light);
            margin: 0;
            padding: 0;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background-color: white;
            box-shadow: var(--shadow-sm);
            padding: 15px 0;
            margin-bottom: 30px;
        }

        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logo {
            font-size: 24px;
            font-weight: bold;
            color: var(--primary-color);
        }

        .logo i {
            margin-right: 10px;
        }

        .nav-links a {
            color: var(--text-color);
            text-decoration: none;
            margin-left: 20px;
            transition: color 0.3s;
        }

        .nav-links a:hover {
            color: var(--primary-color);
        }

        .help-layout {
            display: grid;
            grid-template-columns: 280px 1fr;
            gap: 30px;
            background-color: white;
            border-radius: var(--radius-lg);
            box-shadow: var(--shadow-md);
            overflow: hidden;
        }

        .help-sidebar {
            background-color: var(--secondary-color);
            color: white;
            padding: 20px;
            height: 100%;
        }

        .help-content {
            padding: 30px;
        }

        .help-navigation {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .help-navigation li {
            margin-bottom: 10px;
            border-left: 3px solid transparent;
            transition: all 0.2s ease;
        }

        .help-navigation li a {
            display: block;
            padding: 10px 15px;
            color: white;
            text-decoration: none;
            transition: all 0.2s ease;
        }

        .help-navigation li:hover,
        .help-navigation li.active {
            border-left-color: var(--primary-color);
            background-color: rgba(255, 255, 255, 0.1);
        }

        .help-navigation li.active a {
            color: white;
            font-weight: bold;
        }

        .help-section {
            margin-bottom: 40px;
        }

        .help-section h2 {
            color: var(--primary-color);
            border-bottom: 2px solid var(--border-color);
            padding-bottom: 10px;
            margin-top: 0;
        }

        .help-section h3 {
            color: var(--secondary-color);
            margin-top: 25px;
        }

        .step-list {
            list-style: none;
            padding: 0;
            counter-reset: step-counter;
        }

        .step-list li {
            position: relative;
            padding: 15px 15px 15px 50px;
            margin-bottom: 15px;
            background-color: var(--bg-light);
            border-radius: var(--radius-md);
            counter-increment: step-counter;
        }

        .step-list li::before {
            content: counter(step-counter);
            position: absolute;
            left: 15px;
            top: 15px;
            width: 25px;
            height: 25px;
            background-color: var(--primary-color);
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
        }

        .note-box {
            background-color: #e7f3fe;
            border-left: 4px solid var(--info-color);
            padding: 15px;
            margin: 20px 0;
            border-radius: var(--radius-sm);
        }

        .warning-box {
            background-color: #fff3cd;
            border-left: 4px solid var(--warning-color);
            padding: 15px;
            margin: 20px 0;
            border-radius: var(--radius-sm);
        }

        .tip-box {
            background-color: #d4edda;
            border-left: 4px solid var(--success-color);
            padding: 15px;
            margin: 20px 0;
            border-radius: var(--radius-sm);
        }

        .screenshot {
            max-width: 100%;
            height: auto;
            border: 1px solid var(--border-color);
            border-radius: var(--radius-md);
            margin: 15px 0;
            box-shadow: var(--shadow-sm);
        }

        code {
            background-color: #f8f9fa;
            padding: 2px 5px;
            border-radius: var(--radius-sm);
            font-family: Consolas, Monaco, 'Andale Mono', monospace;
            color: #e83e8c;
        }

        pre {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: var(--radius-md);
            overflow-x: auto;
            border: 1px solid var(--border-color);
        }

        pre code {
            background-color: transparent;
            padding: 0;
            color: var(--text-color);
        }

        table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }

        table th, table td {
            padding: 12px 15px;
            text-align: left;
            border-bottom: 1px solid var(--border-color);
        }

        table th {
            background-color: var(--bg-light);
            font-weight: bold;
        }

        @media (max-width: 768px) {
            .help-layout {
                grid-template-columns: 1fr;
            }

            .help-sidebar {
                padding: 15px;
            }

            .help-content {
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <header class="header">
        <div class="container">
            <div class="header-content">
                <div class="logo">
                    <i class="fas fa-cogs"></i> Manage Inc
                </div>
                <div class="nav-links">
                    <a href="index.php"><i class="fas fa-home"></i> Home</a>
                    <a href="admin/index.php"><i class="fas fa-user-shield"></i> Admin Login</a>
                </div>
            </div>
        </div>
    </header>

    <div class="container">
        <div class="help-layout">
            <aside class="help-sidebar">
                <h2>Installation Guide</h2>
                <ul class="help-navigation">
                    <li class="active"><a href="#requirements"><i class="fas fa-check-square"></i> Requirements</a></li>
                    <li><a href="#installation"><i class="fas fa-download"></i> Installation Process</a></li>
                    <li><a href="#configuration"><i class="fas fa-cog"></i> Configuration</a></li>
                    <li><a href="#troubleshooting"><i class="fas fa-wrench"></i> Troubleshooting</a></li>
                    <li><a href="#faq"><i class="fas fa-question-circle"></i> FAQ</a></li>
                </ul>
            </aside>

            <div class="help-content">
                <section id="requirements" class="help-section">
                    <h2>System Requirements</h2>
                    <p>Before installing Manage Inc, ensure your server meets the following requirements:</p>

                    <table>
                        <tr>
                            <th>Component</th>
                            <th>Requirement</th>
                        </tr>
                        <tr>
                            <td>Web Server</td>
                            <td>Apache 2.4+ or Nginx 1.18+</td>
                        </tr>
                        <tr>
                            <td>PHP Version</td>
                            <td>PHP 7.4 or higher (8.0+ recommended)</td>
                        </tr>
                        <tr>
                            <td>Database</td>
                            <td>MySQL 5.7+ or MariaDB 10.3+</td>
                        </tr>
                        <tr>
                            <td>PHP Extensions</td>
                            <td>mysqli, gd, json, mbstring, xml, fileinfo</td>
                        </tr>
                        <tr>
                            <td>Server Memory</td>
                            <td>128MB minimum (256MB+ recommended)</td>
                        </tr>
                    </table>

                    <div class="note-box">
                        <strong>Note:</strong> You can check your PHP configuration by creating a file named <code>phpinfo.php</code> with the content <code>&lt;?php phpinfo(); ?&gt;</code> and accessing it through your browser.
                    </div>
                </section>

                <section id="installation" class="help-section">
                    <h2>Installation Process</h2>
                    <p>Follow these steps to install Manage Inc on your server:</p>

                    <ol class="step-list">
                        <li>
                            <h3>Upload Files</h3>
                            <p>Upload all files from the Manage Inc package to your web server using FTP or your hosting control panel's file manager.</p>
                        </li>
                        <li>
                            <h3>Create Database</h3>
                            <p>Create a new MySQL database and user with full permissions on that database.</p>
                            <div class="tip-box">
                                <strong>Tip:</strong> Make note of your database name, username, password, and host as you'll need these during the installation process.
                            </div>
                        </li>
                        <li>
                            <h3>Run the Installer</h3>
                            <p>Navigate to <code>http://your-domain.com/install.php</code> in your web browser to start the installation wizard.</p>
                        </li>
                        <li>
                            <h3>Database Configuration</h3>
                            <p>Enter your database connection details in the form provided.</p>
                        </li>
                        <li>
                            <h3>Admin Account Setup</h3>
                            <p>Create your administrator account by providing a username, email address, and secure password.</p>
                            <div class="warning-box">
                                <strong>Important:</strong> Choose a strong password that includes uppercase and lowercase letters, numbers, and special characters.
                            </div>
                        </li>
                        <li>
                            <h3>Site Configuration</h3>
                            <p>Configure basic site settings including:</p>
                            <ul>
                                <li><strong>Site Name</strong>: The name of your website</li>
                                <li><strong>Site Description</strong>: A brief description of your business</li>
                                <li><strong>Admin Email</strong>: Main administrator email address</li>
                                <li><strong>From Email</strong>: Default sender email for system emails</li>
                                <li><strong>From Name</strong>: Default sender name for system emails</li>
                                <li><strong>Reply-To Email</strong>: Default reply-to email address (optional)</li>
                                <li><strong>Admin Logo</strong>: Upload a custom logo for the admin panel (optional)</li>
                            </ul>
                            <div class="tip-box">
                                <strong>Logo Requirements:</strong> JPG, PNG, or GIF format, maximum 2MB file size. Recommended dimensions: 200x50px for best appearance.
                            </div>
                        </li>
                        <li>
                            <h3>Configuration File Generation</h3>
                            <p>The installer will automatically generate a <code>config.php</code> file with your database settings and security configurations.</p>
                        </li>
                        <li>
                            <h3>Complete Installation</h3>
                            <p>Review your settings and click "Complete Installation" to finalize the setup process. The installer will:</p>
                            <ul>
                                <li>Create all necessary database tables</li>
                                <li>Insert default settings and sample data</li>
                                <li>Generate secure configuration files</li>
                                <li>Set up the admin user account</li>
                                <li>Configure email settings</li>
                            </ul>
                        </li>
                    </ol>

                    <div class="note-box">
                        <strong>Security Note:</strong> After successful installation, it's recommended to delete the <code>install.php</code> file from your server.
                    </div>
                </section>

                <section id="configuration" class="help-section">
                    <h2>Post-Installation Configuration</h2>
                    <p>After installing Manage Inc, you should configure these important settings:</p>

                    <h3>Email Configuration</h3>
                    <p>Configure your email settings to ensure proper delivery of system emails:</p>
                    <ol>
                        <li>Log in to the admin panel</li>
                        <li>Navigate to Settings &gt; Email Settings</li>
                        <li>Configure your SMTP settings or use the default PHP mail function</li>
                        <li>Send a test email to verify your configuration</li>
                    </ol>

                    <h3>Admin Logo</h3>
                    <p>Customize the admin panel with your own logo:</p>
                    <ol>
                        <li>Prepare a logo image (recommended size: 200x50px)</li>
                        <li>Navigate to Settings &gt; General Settings</li>
                        <li>Upload your logo or specify the path to your logo file</li>
                    </ol>
                    <div class="tip-box">
                        <strong>Tip:</strong> The default admin logo path is set to '../admin/images/logo.png'
                    </div>
                </section>

                <section id="troubleshooting" class="help-section">
                    <h2>Troubleshooting</h2>
                    <p>Common installation issues and their solutions:</p>

                    <h3>Database Connection Errors</h3>
                    <p>If you encounter database connection errors during installation:</p>
                    <ul>
                        <li>Verify your database credentials are correct</li>
                        <li>Ensure the database user has sufficient permissions</li>
                        <li>Check if your database server is running</li>
                        <li>Verify that the mysqli PHP extension is enabled</li>
                    </ul>

                    <h3>Permission Issues</h3>
                    <p>If you encounter file permission errors:</p>
                    <ul>
                        <li>Ensure the web server has write permissions to the following directories:
                            <ul>
                                <li><code>/images/</code></li>
                                <li><code>/images/news/</code></li>
                                <li><code>/admin/backups/</code></li>
                            </ul>
                        </li>
                        <li>On Linux/Unix systems, you may need to set permissions using: <code>chmod 755</code> for directories and <code>chmod 644</code> for files</li>
                    </ul>

                    <h3>Blank Page After Installation</h3>
                    <p>If you see a blank page after completing installation:</p>
                    <ul>
                        <li>Check your server's error logs for PHP errors</li>
                        <li>Ensure PHP has enough memory allocated (min 128MB)</li>
                        <li>Verify all required PHP extensions are enabled</li>
                    </ul>
                </section>

                <section id="faq" class="help-section">
                    <h2>Frequently Asked Questions</h2>

                    <h3>Can I install Manage Inc in a subdirectory?</h3>
                    <p>Yes, you can install Manage Inc in a subdirectory of your website. Simply upload the files to that subdirectory and run the installer as normal.</p>

                    <h3>How do I update Manage Inc to a newer version?</h3>
                    <p>The update process involves backing up your current installation, uploading the new files, and running the update script. Detailed update instructions are provided with each new release.</p>

                    <h3>Is it possible to migrate from another CMS to Manage Inc?</h3>
                    <p>There's no automatic migration tool, but you can manually import content after installation. Contact support for assistance with large migrations.</p>

                    <h3>What should I do if I forget my admin password?</h3>
                    <p>Use the "Forgot Password" link on the admin login page. If you can't access your email, you may need to reset the password directly in the database.</p>

                    <h3>Can I customize the installation process?</h3>
                    <p>Advanced users can modify the installation process by editing the <code>install.php</code> file, but this is not recommended unless you have experience with PHP development.</p>
                </section>
            </div>
        </div>
    </div>

    <script>
        // Simple navigation highlighting
        document.addEventListener('DOMContentLoaded', function() {
            const navLinks = document.querySelectorAll('.help-navigation a');
            const sections = document.querySelectorAll('.help-section');

            // Highlight active section on scroll
            window.addEventListener('scroll', function() {
                let current = '';

                sections.forEach(section => {
                    const sectionTop = section.offsetTop;
                    const sectionHeight = section.clientHeight;

                    if (pageYOffset >= (sectionTop - 200)) {
                        current = section.getAttribute('id');
                    }
                });

                navLinks.forEach(link => {
                    link.parentElement.classList.remove('active');
                    if (link.getAttribute('href').substring(1) === current) {
                        link.parentElement.classList.add('active');
                    }
                });
            });

            // Smooth scroll to section when clicking navigation links
            navLinks.forEach(link => {
                link.addEventListener('click', function(e) {
                    e.preventDefault();

                    const targetId = this.getAttribute('href').substring(1);
                    const targetSection = document.getElementById(targetId);

                    window.scrollTo({
                        top: targetSection.offsetTop - 20,
                        behavior: 'smooth'
                    });

                    // Update active class
                    navLinks.forEach(link => {
                        link.parentElement.classList.remove('active');
                    });
                    this.parentElement.classList.add('active');
                });
            });
        });
    </script>
</body>
</html>
