/**
 * Enhanced Tooltips for Admin Panel
 * Provides consistent tooltip behavior across the admin interface
 */

/**
 * Global tooltip initialization
 * This code automatically runs on all admin pages
 */
document.addEventListener('DOMContentLoaded', function() {
    // Initialize tooltips
    initTooltips();

    // Initialize dynamic tooltips (for elements added after page load)
    initDynamicTooltips();

    // Add tooltips to common admin panel elements
    addTooltipsToCommonElements();
});

/**
 * Add tooltips to common elements across the admin panel
 * This function adds tooltips to elements that appear on multiple pages
 */
function addTooltipsToCommonElements() {
    // Add tooltips to action buttons
    addTooltipsToActionButtons();

    // Add tooltips to form elements
    addTooltipsToFormElements();

    // Add tooltips to table headers
    addTooltipsToTableHeaders();

    // Add tooltips to user interface elements
    addTooltipsToUIElements();
}

/**
 * Add tooltips to sidebar menu items
 * This function is disabled as per user request to remove tooltips from the sidebar
 */
function addTooltipsToSidebar() {
    // Function disabled - tooltips removed from sidebar
    return;
}

/**
 * Add tooltips to common action buttons
 */
function addTooltipsToActionButtons() {
    // Add tooltips to common action buttons by their icon or text
    const actionButtons = document.querySelectorAll('.admin-btn, .icon-btn, button[type="submit"], button[type="reset"]');

    actionButtons.forEach(button => {
        // Skip buttons that already have tooltips
        if (button.hasAttribute('data-tooltip')) return;

        const buttonText = button.textContent.trim();
        const hasIcon = button.querySelector('i.fas, i.far, i.fab') !== null;
        let tooltip = '';

        // Set tooltip based on button text or icon
        if (hasIcon) {
            const icon = button.querySelector('i');
            const iconClass = icon.className;

            switch(true) {
                case /fa-save|fa-check/.test(iconClass) && /save|submit/i.test(buttonText):
                    tooltip = 'Save changes';
                    break;
                case /fa-plus|fa-add/.test(iconClass):
                    tooltip = 'Add new item';
                    break;
                case /fa-edit|fa-pencil/.test(iconClass):
                    tooltip = 'Edit this item';
                    break;
                case /fa-trash|fa-times/.test(iconClass):
                    tooltip = 'Delete this item';
                    break;
                case /fa-download/.test(iconClass):
                    tooltip = 'Download this item';
                    break;
                case /fa-upload/.test(iconClass):
                    tooltip = 'Upload a file';
                    break;
                case /fa-search/.test(iconClass):
                    tooltip = 'Search';
                    break;
                case /fa-filter/.test(iconClass):
                    tooltip = 'Filter results';
                    break;
                case /fa-undo|fa-refresh/.test(iconClass):
                    tooltip = 'Reset or refresh';
                    break;
                case /fa-eye/.test(iconClass):
                    tooltip = 'View details';
                    break;
                case /fa-eye-slash/.test(iconClass):
                    tooltip = 'Hide details';
                    break;
                case /fa-lock/.test(iconClass):
                    tooltip = 'Lock item';
                    break;
                case /fa-unlock/.test(iconClass):
                    tooltip = 'Unlock item';
                    break;
                case /fa-cog|fa-gear/.test(iconClass):
                    tooltip = 'Settings';
                    break;
                case /fa-user/.test(iconClass):
                    tooltip = 'User actions';
                    break;
                case /fa-bell/.test(iconClass):
                    tooltip = 'Notifications';
                    break;
                case /fa-paper-plane|fa-send/.test(iconClass):
                    tooltip = 'Send or submit';
                    break;
            }
        } else {
            // Text-only buttons
            switch(true) {
                case /save|submit/i.test(buttonText):
                    tooltip = 'Save changes';
                    break;
                case /add|create|new/i.test(buttonText):
                    tooltip = 'Add new item';
                    break;
                case /edit|modify/i.test(buttonText):
                    tooltip = 'Edit this item';
                    break;
                case /delete|remove/i.test(buttonText):
                    tooltip = 'Delete this item';
                    break;
                case /cancel/i.test(buttonText):
                    tooltip = 'Cancel and discard changes';
                    break;
                case /reset/i.test(buttonText):
                    tooltip = 'Reset to default values';
                    break;
                case /search/i.test(buttonText):
                    tooltip = 'Search for items';
                    break;
                case /filter/i.test(buttonText):
                    tooltip = 'Filter results';
                    break;
                case /view|details/i.test(buttonText):
                    tooltip = 'View item details';
                    break;
                case /download/i.test(buttonText):
                    tooltip = 'Download this item';
                    break;
                case /upload/i.test(buttonText):
                    tooltip = 'Upload a file';
                    break;
            }
        }

        if (tooltip) {
            button.setAttribute('data-tooltip', tooltip);
            button.setAttribute('data-tooltip-position', 'top');
        }
    });
}

/**
 * Add tooltips to form elements
 */
function addTooltipsToFormElements() {
    // Add tooltips to form elements with specific attributes or classes
    const formElements = document.querySelectorAll('input, select, textarea');

    formElements.forEach(element => {
        // Skip elements that already have tooltips
        if (element.hasAttribute('data-tooltip')) return;

        // Add tooltips to elements with title attributes
        if (element.hasAttribute('title') && element.getAttribute('title').trim() !== '') {
            const title = element.getAttribute('title');
            element.setAttribute('data-tooltip', title);
            element.setAttribute('data-tooltip-position', 'top');
            // Remove the title attribute to prevent double tooltips
            element.removeAttribute('title');
        }

        // Add tooltips to elements with placeholder attributes
        if (element.hasAttribute('placeholder') && element.getAttribute('placeholder').trim() !== '') {
            // Only add tooltip if the placeholder is informative (more than 15 characters)
            const placeholder = element.getAttribute('placeholder');
            if (placeholder.length > 15) {
                element.setAttribute('data-tooltip', placeholder);
                element.setAttribute('data-tooltip-position', 'top');
            }
        }

        // Add tooltips to required fields
        if (element.hasAttribute('required') && !element.hasAttribute('data-tooltip')) {
            element.setAttribute('data-tooltip', 'This field is required');
            element.setAttribute('data-tooltip-position', 'right');
            element.setAttribute('data-tooltip-type', 'warning');
        }
    });
}

/**
 * Add tooltips to table headers
 */
function addTooltipsToTableHeaders() {
    // Add tooltips to table headers with specific classes
    const tableHeaders = document.querySelectorAll('th');

    tableHeaders.forEach(header => {
        // Skip headers that already have tooltips
        if (header.hasAttribute('data-tooltip')) return;

        // Add tooltips to sortable headers
        if (header.classList.contains('sortable')) {
            header.setAttribute('data-tooltip', 'Click to sort by this column');
            header.setAttribute('data-tooltip-position', 'top');
        }

        // Add tooltips to headers with title attributes
        if (header.hasAttribute('title') && header.getAttribute('title').trim() !== '') {
            const title = header.getAttribute('title');
            header.setAttribute('data-tooltip', title);
            header.setAttribute('data-tooltip-position', 'top');
            // Remove the title attribute to prevent double tooltips
            header.removeAttribute('title');
        }
    });
}

/**
 * Add tooltips to UI elements
 */
function addTooltipsToUIElements() {
    // Add tooltips to UI elements with specific classes or attributes

    // Add tooltips to toggle switches
    const toggleSwitches = document.querySelectorAll('.toggle-switch, .switch');
    toggleSwitches.forEach(toggle => {
        // Skip elements that already have tooltips
        if (toggle.hasAttribute('data-tooltip')) return;

        // Check if the toggle has a label
        const label = toggle.closest('label') || toggle.querySelector('label');
        if (label) {
            const labelText = label.textContent.trim();
            toggle.setAttribute('data-tooltip', `Toggle ${labelText}`);
            toggle.setAttribute('data-tooltip-position', 'right');
        } else {
            toggle.setAttribute('data-tooltip', 'Toggle this setting');
            toggle.setAttribute('data-tooltip-position', 'right');
        }
    });

    // Add tooltips to help icons
    const helpIcons = document.querySelectorAll('.help-icon, .info-icon, i.fa-question-circle, i.fa-info-circle');
    helpIcons.forEach(icon => {
        // Skip icons that already have tooltips
        if (icon.hasAttribute('data-tooltip')) return;

        // Check if the icon has a title attribute
        if (icon.hasAttribute('title') && icon.getAttribute('title').trim() !== '') {
            const title = icon.getAttribute('title');
            icon.setAttribute('data-tooltip', title);
            icon.setAttribute('data-tooltip-position', 'top');
            // Remove the title attribute to prevent double tooltips
            icon.removeAttribute('title');
        } else {
            // Add a generic tooltip
            icon.setAttribute('data-tooltip', 'Click for more information');
            icon.setAttribute('data-tooltip-position', 'top');
        }
    });

    // Tooltips for notification icons are disabled as per user request
    // const notificationIcons = document.querySelectorAll('.notification-icon, i.fa-bell');
    // notificationIcons.forEach(icon => {
    //     // Skip icons that already have tooltips
    //     if (icon.hasAttribute('data-tooltip')) return;
    //
    //     icon.setAttribute('data-tooltip', 'View notifications');
    //     icon.setAttribute('data-tooltip-position', 'bottom');
    // });

    // Add tooltips to user profile icons
    const userIcons = document.querySelectorAll('.user-icon, .profile-icon, i.fa-user');
    userIcons.forEach(icon => {
        // Skip icons that already have tooltips
        if (icon.hasAttribute('data-tooltip')) return;

        icon.setAttribute('data-tooltip', 'User profile and settings');
        icon.setAttribute('data-tooltip-position', 'bottom');
    });
}

/**
 * Initialize tooltips for static elements
 */
function initTooltips() {
    // Get all elements with data-tooltip attribute
    const tooltipElements = document.querySelectorAll('[data-tooltip]');

    tooltipElements.forEach(element => {
        // Skip tooltips in the sidebar
        if (element.closest('.admin-sidebar-menu') || element.closest('.admin-sidebar')) {
            // Remove tooltip attributes from sidebar elements
            element.removeAttribute('data-tooltip');
            element.removeAttribute('data-tooltip-position');
            element.removeAttribute('data-tooltip-type');
            element.removeAttribute('data-tooltip-initialized');

            // Remove any existing tooltip elements
            const existingTooltip = element.querySelector('.tooltip');
            if (existingTooltip) {
                existingTooltip.remove();
            }
            return;
        }

        setupTooltip(element);
    });
}

/**
 * Initialize tooltips for dynamically added elements
 */
function initDynamicTooltips() {
    // Create a mutation observer to watch for new elements
    const observer = new MutationObserver(mutations => {
        mutations.forEach(mutation => {
            if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
                mutation.addedNodes.forEach(node => {
                    // Check if the added node is an element
                    if (node.nodeType === Node.ELEMENT_NODE) {
                        // Check if the element has a data-tooltip attribute
                        if (node.hasAttribute && node.hasAttribute('data-tooltip')) {
                            // Skip tooltips in the sidebar
                            if (!node.closest('.admin-sidebar-menu') && !node.closest('.admin-sidebar')) {
                                setupTooltip(node);
                            }
                        }

                        // Check child elements for data-tooltip attribute
                        const childTooltips = node.querySelectorAll ? node.querySelectorAll('[data-tooltip]') : [];
                        childTooltips.forEach(child => {
                            // Skip tooltips in the sidebar
                            if (!child.closest('.admin-sidebar-menu') && !child.closest('.admin-sidebar')) {
                                setupTooltip(child);
                            }
                        });
                    }
                });
            }
        });
    });

    // Start observing the document body for changes
    observer.observe(document.body, {
        childList: true,
        subtree: true
    });
}

/**
 * Setup tooltip for a single element
 * @param {HTMLElement} element - The element to setup tooltip for
 */
function setupTooltip(element) {
    // Skip if element already has a tooltip setup
    if (element.hasAttribute('data-tooltip-initialized')) {
        return;
    }

    // Mark element as initialized
    element.setAttribute('data-tooltip-initialized', 'true');

    // Get tooltip text and options
    const tooltipText = element.getAttribute('data-tooltip');
    const tooltipPosition = element.getAttribute('data-tooltip-position') || 'top';
    const tooltipType = element.getAttribute('data-tooltip-type') || '';
    const tooltipWidth = element.getAttribute('data-tooltip-width') || '';
    const tooltipTitle = element.getAttribute('data-tooltip-title') || '';
    const tooltipIcon = element.getAttribute('data-tooltip-content-icon') || '';
    const isMultiline = element.hasAttribute('data-tooltip-multiline') ||
                        tooltipText.includes('\n') ||
                        tooltipText.includes('<br>') ||
                        tooltipText.includes('&lt;br&gt;');
    const isAnimated = !element.hasAttribute('data-tooltip-no-animation');

    // Create tooltip element if it doesn't exist
    let tooltip = element.querySelector('.tooltip');

    if (!tooltip) {
        tooltip = document.createElement('div');
        tooltip.className = `tooltip tooltip-${tooltipPosition}`;

        // Add type class if specified
        if (tooltipType) {
            tooltip.classList.add(`tooltip-${tooltipType}`);
        }

        // Add multiline class if needed
        if (isMultiline) {
            tooltip.classList.add('multiline');
            tooltip.setAttribute('data-multiline', 'true');
        }

        // Add animation class if needed
        if (isAnimated) {
            tooltip.classList.add('animated');
        }

        // Set custom width if specified
        if (tooltipWidth) {
            tooltip.style.maxWidth = tooltipWidth;
            tooltip.style.width = tooltipWidth;
        }

        // Process tooltip text for multiline support
        // Replace \n with actual line breaks and handle HTML entities
        const processedText = tooltipText
            .replace(/\\n/g, '\n')
            .replace(/&lt;br&gt;/g, '\n')
            .replace(/&lt;br\/&gt;/g, '\n')
            .replace(/&lt;br \/&gt;/g, '\n')
            .replace(/<br>/g, '\n')
            .replace(/<br\/>/g, '\n')
            .replace(/<br \/>/g, '\n');

        // Create tooltip content
        if (tooltipTitle || tooltipIcon) {
            // Create structured tooltip with title and/or icon
            if (tooltipTitle) {
                const titleElement = document.createElement('div');
                titleElement.className = 'tooltip-title';
                titleElement.textContent = tooltipTitle;
                tooltip.appendChild(titleElement);
            }

            if (tooltipIcon) {
                // Create tooltip with icon
                tooltip.classList.add('tooltip-with-icon');

                const iconContainer = document.createElement('div');
                iconContainer.className = 'tooltip-icon-container';

                const iconElement = document.createElement('i');
                iconElement.className = tooltipIcon;
                iconContainer.appendChild(iconElement);

                const contentElement = document.createElement('div');
                contentElement.className = 'tooltip-content';
                contentElement.textContent = processedText;

                tooltip.appendChild(iconContainer);
                tooltip.appendChild(contentElement);
            } else {
                // Just add the content after the title
                const contentElement = document.createElement('div');
                contentElement.textContent = processedText;
                tooltip.appendChild(contentElement);
            }
        } else {
            // Simple tooltip with just text
            tooltip.textContent = processedText;
        }

        // Add tooltip to element
        element.appendChild(tooltip);

        // Add tooltip container class if not already present
        if (!element.classList.contains('tooltip-container')) {
            element.classList.add('tooltip-container');
        }
    }

    // Add tooltip icon if requested and not already present
    if (element.hasAttribute('data-tooltip-icon') && !element.querySelector('.tooltip-icon')) {
        const iconClass = element.getAttribute('data-tooltip-icon') || 'fas fa-question-circle';
        const tooltipIcon = document.createElement('i');
        tooltipIcon.className = `tooltip-icon ${iconClass}`;
        element.appendChild(tooltipIcon);
    }

    // Handle mouseenter event
    element.addEventListener('mouseenter', function() {
        // Position the tooltip
        positionTooltip(tooltip, element, tooltipPosition);

        // Add active class for animation
        tooltip.classList.add('active');
    });

    // Handle mouseleave event
    element.addEventListener('mouseleave', function() {
        // Remove active class
        tooltip.classList.remove('active');
    });

    // Handle focus event for accessibility
    element.addEventListener('focus', function() {
        // Position the tooltip
        positionTooltip(tooltip, element, tooltipPosition);

        // Add active class for animation
        tooltip.classList.add('active');
    });

    // Handle blur event for accessibility
    element.addEventListener('blur', function() {
        // Remove active class
        tooltip.classList.remove('active');
    });

    // Handle click event for mobile devices
    element.addEventListener('click', function(e) {
        // Only handle click for tooltip if the element is not interactive
        const isInteractive = element.tagName === 'A' ||
                             element.tagName === 'BUTTON' ||
                             element.tagName === 'INPUT' ||
                             element.tagName === 'SELECT' ||
                             element.tagName === 'TEXTAREA';

        if (!isInteractive) {
            e.preventDefault();

            // Toggle tooltip visibility
            if (tooltip.classList.contains('active')) {
                tooltip.classList.remove('active');
            } else {
                // Hide all other tooltips
                document.querySelectorAll('.tooltip.active').forEach(t => {
                    if (t !== tooltip) {
                        t.classList.remove('active');
                    }
                });

                // Position the tooltip
                positionTooltip(tooltip, element, tooltipPosition);

                // Show this tooltip
                tooltip.classList.add('active');
            }
        }
    });
}

/**
 * Position tooltip relative to its parent element
 * @param {HTMLElement} tooltip - The tooltip element
 * @param {HTMLElement} element - The parent element
 * @param {string} position - The tooltip position (top, bottom, left, right)
 */
function positionTooltip(tooltip, element, position) {
    // Reset any previous inline styles
    tooltip.style.left = '';
    tooltip.style.right = '';
    tooltip.style.top = '';
    tooltip.style.bottom = '';
    tooltip.style.transform = '';

    // Get element dimensions and position
    const rect = element.getBoundingClientRect();
    const tooltipRect = tooltip.getBoundingClientRect();

    // Calculate available space in each direction
    const spaceTop = rect.top;
    const spaceBottom = window.innerHeight - rect.bottom;
    const spaceLeft = rect.left;
    const spaceRight = window.innerWidth - rect.right;

    // Determine best position based on available space if position is 'auto'
    let bestPosition = position;
    if (position === 'auto') {
        const spaces = [
            { position: 'top', space: spaceTop },
            { position: 'bottom', space: spaceBottom },
            { position: 'left', space: spaceLeft },
            { position: 'right', space: spaceRight }
        ];

        // Sort by available space (descending)
        spaces.sort((a, b) => b.space - a.space);

        // Use position with most available space
        bestPosition = spaces[0].position;

        // Update tooltip class
        tooltip.className = `tooltip tooltip-${bestPosition}`;
    }

    // Position tooltip based on best position
    switch (bestPosition) {
        case 'top':
            // Check if tooltip would go off-screen to the left
            if (rect.left + rect.width / 2 - tooltipRect.width / 2 < 0) {
                tooltip.style.left = '0';
                tooltip.style.transform = 'translateY(0)';
            }
            // Check if tooltip would go off-screen to the right
            else if (rect.left + rect.width / 2 + tooltipRect.width / 2 > window.innerWidth) {
                tooltip.style.right = '0';
                tooltip.style.left = 'auto';
                tooltip.style.transform = 'translateY(0)';
            }
            break;

        case 'bottom':
            // Check if tooltip would go off-screen to the left
            if (rect.left + rect.width / 2 - tooltipRect.width / 2 < 0) {
                tooltip.style.left = '0';
                tooltip.style.transform = 'translateY(0)';
            }
            // Check if tooltip would go off-screen to the right
            else if (rect.left + rect.width / 2 + tooltipRect.width / 2 > window.innerWidth) {
                tooltip.style.right = '0';
                tooltip.style.left = 'auto';
                tooltip.style.transform = 'translateY(0)';
            }
            break;

        case 'left':
            // Check if tooltip would go off-screen to the top
            if (rect.top + rect.height / 2 - tooltipRect.height / 2 < 0) {
                tooltip.style.top = '0';
                tooltip.style.transform = 'translateX(0)';
            }
            // Check if tooltip would go off-screen to the bottom
            else if (rect.top + rect.height / 2 + tooltipRect.height / 2 > window.innerHeight) {
                tooltip.style.bottom = '0';
                tooltip.style.top = 'auto';
                tooltip.style.transform = 'translateX(0)';
            }
            break;

        case 'right':
            // Check if tooltip would go off-screen to the top
            if (rect.top + rect.height / 2 - tooltipRect.height / 2 < 0) {
                tooltip.style.top = '0';
                tooltip.style.transform = 'translateX(0)';
            }
            // Check if tooltip would go off-screen to the bottom
            else if (rect.top + rect.height / 2 + tooltipRect.height / 2 > window.innerHeight) {
                tooltip.style.bottom = '0';
                tooltip.style.top = 'auto';
                tooltip.style.transform = 'translateX(0)';
            }
            break;
    }
}
