<?php
/**
 * Insert Sample Inbox Data
 *
 * This script inserts sample contact submissions and replies
 * to test the modern inbox interface.
 */

session_start();
require_once 'config.php';

// Check if user is logged in and is admin
if (!isset($_SESSION['user_id'])) {
    die('Access denied. Please log in as admin.');
}

// Set content type
header('Content-Type: text/html; charset=utf-8');
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Insert Sample Inbox Data</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 40px auto;
            padding: 20px;
            background: #f8f9fa;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2c3e50;
            margin-bottom: 20px;
        }
        .success {
            background: #d4edda;
            color: #155724;
            padding: 12px 16px;
            border-radius: 8px;
            margin: 10px 0;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            padding: 12px 16px;
            border-radius: 8px;
            margin: 10px 0;
            border: 1px solid #f5c6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            padding: 12px 16px;
            border-radius: 8px;
            margin: 10px 0;
            border: 1px solid #bee5eb;
        }
        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            text-decoration: none;
            display: inline-block;
            margin: 10px 5px;
            cursor: pointer;
            font-weight: 500;
        }
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
        }
        .btn-secondary {
            background: #6c757d;
        }
        pre {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            overflow-x: auto;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🗂️ Insert Sample Inbox Data</h1>

        <div class="info">
            <strong>Purpose:</strong> This script will insert sample contact submissions and replies to test the modern inbox interface.
        </div>

        <?php
        if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['insert_data'])) {
            echo "<h2>📥 Inserting Sample Data...</h2>";

            try {
                // Read the SQL file
                $sql_file = __DIR__ . '/sample_inbox_data.sql';

                if (!file_exists($sql_file)) {
                    throw new Exception("SQL file not found: $sql_file");
                }

                $sql_content = file_get_contents($sql_file);

                if ($sql_content === false) {
                    throw new Exception("Failed to read SQL file");
                }

                // Split SQL into individual statements
                $statements = array_filter(
                    array_map('trim', explode(';', $sql_content)),
                    function($stmt) {
                        return !empty($stmt) && !preg_match('/^\s*--/', $stmt);
                    }
                );

                $success_count = 0;
                $error_count = 0;

                // First, disable foreign key checks to avoid constraint issues
                $conn->query("SET FOREIGN_KEY_CHECKS = 0");

                foreach ($statements as $statement) {
                    if (trim($statement)) {
                        $result = $conn->query($statement);

                        if ($result) {
                            $success_count++;

                            // Check if this was an INSERT statement and show affected rows
                            if (stripos($statement, 'INSERT') === 0) {
                                $affected_rows = $conn->affected_rows;
                                echo "<div class='success'>✅ Inserted $affected_rows record(s)</div>";
                            } elseif (stripos($statement, 'CREATE TABLE') === 0) {
                                // Extract table name for better feedback
                                preg_match('/CREATE TABLE.*?`([^`]+)`/', $statement, $matches);
                                $table_name = isset($matches[1]) ? $matches[1] : 'table';
                                echo "<div class='success'>✅ Created table: $table_name</div>";
                            } else {
                                echo "<div class='success'>✅ Statement executed successfully</div>";
                            }
                        } else {
                            $error_count++;
                            $error_msg = $conn->error;

                            // Handle specific errors more gracefully
                            if (strpos($error_msg, 'already exists') !== false) {
                                echo "<div class='info'>ℹ️ Table already exists (skipped)</div>";
                                $error_count--; // Don't count this as an error
                                $success_count++;
                            } else {
                                echo "<div class='error'>❌ Error: " . htmlspecialchars($error_msg) . "</div>";
                            }
                        }
                    }
                }

                // Re-enable foreign key checks
                $conn->query("SET FOREIGN_KEY_CHECKS = 1");

                echo "<div class='success'>";
                echo "<h3>🎉 Data Insertion Complete!</h3>";
                echo "<p><strong>Successful statements:</strong> $success_count</p>";
                echo "<p><strong>Failed statements:</strong> $error_count</p>";
                echo "</div>";

                // Show summary of inserted data
                echo "<div class='info'>";
                echo "<h3>📊 Database Summary:</h3>";

                // Check contact_submissions table
                $contact_result = $conn->query("SELECT COUNT(*) as count FROM contact_submissions");
                if ($contact_result) {
                    $contact_count = $contact_result->fetch_assoc()['count'];
                    echo "<p><strong>Total Contact Submissions:</strong> $contact_count</p>";
                } else {
                    echo "<p><strong>Contact Submissions:</strong> Table not found</p>";
                }

                // Check contact_replies table
                $reply_result = $conn->query("SELECT COUNT(*) as count FROM contact_replies");
                if ($reply_result) {
                    $reply_count = $reply_result->fetch_assoc()['count'];
                    echo "<p><strong>Total Replies:</strong> $reply_count</p>";
                } else {
                    echo "<p><strong>Replies:</strong> Table not found</p>";
                }

                echo "</div>";

            } catch (Exception $e) {
                echo "<div class='error'>";
                echo "<h3>❌ Error occurred:</h3>";
                echo "<p>" . htmlspecialchars($e->getMessage()) . "</p>";
                echo "</div>";
            }

            echo "<a href='inbox.php' class='btn'>📧 View Inbox</a>";
            echo "<a href='insert_sample_data.php' class='btn btn-secondary'>🔄 Refresh Page</a>";

        } else {
            ?>

            <h2>📋 Sample Data Overview</h2>
            <p>This will insert the following sample data:</p>

            <h3>👥 Contact Submissions (10 records):</h3>
            <ul>
                <li><strong>John Smith</strong> - Cloud hosting inquiry (Unread)</li>
                <li><strong>Sarah Johnson</strong> - Managed services issue (Read, with reply)</li>
                <li><strong>Michael Chen</strong> - Infrastructure consultation (Unread)</li>
                <li><strong>Emily Rodriguez</strong> - Migration assistance (Read, with reply)</li>
                <li><strong>David Wilson</strong> - Security enhancement (Unread)</li>
                <li><strong>Lisa Thompson</strong> - Nonprofit hosting (Read, with reply)</li>
                <li><strong>Robert Garcia</strong> - HIPAA compliance (Unread)</li>
                <li><strong>Amanda Foster</strong> - Healthcare hosting (Read, with reply)</li>
                <li><strong>James Parker</strong> - Manufacturing solutions (Unread)</li>
                <li><strong>Rachel Green</strong> - Educational platform (Read)</li>
            </ul>

            <h3>💬 Sample Replies (4 records):</h3>
            <ul>
                <li>Reply to Sarah Johnson's managed services issue</li>
                <li>Reply to Emily Rodriguez's migration inquiry</li>
                <li>Reply to Lisa Thompson's nonprofit request</li>
                <li>Reply to Amanda Foster's healthcare requirements</li>
            </ul>

            <div class="info">
                <strong>Note:</strong> This data includes a mix of read/unread messages, various contact sources,
                and realistic business inquiries to thoroughly test the inbox interface.
            </div>

            <form method="post">
                <button type="submit" name="insert_data" class="btn">
                    📥 Insert Sample Data
                </button>
            </form>

            <a href="inbox.php" class="btn btn-secondary">📧 View Current Inbox</a>

            <?php
        }
        ?>
    </div>
</body>
</html>
