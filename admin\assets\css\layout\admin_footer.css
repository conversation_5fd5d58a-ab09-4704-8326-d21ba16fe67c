/**
 * Admin Footer CSS
 *
 * This file contains styles for the admin footer.
 * The footer is designed to stay at the bottom of the page.
 */

/* Footer Container */
.admin-footer {
  background-color: var(--footer-bg);
  border-top: 1px solid var(--border-color);
  padding: var(--spacing-4);
  margin-top: auto; /* Push to the bottom */
  width: calc(100% - var(--sidebar-width)); /* Adjust width for sidebar */
  margin-left: var(--sidebar-width); /* Offset for sidebar */
  position: relative; /* For z-index to work */
  z-index: 10; /* Ensure footer stays above content when needed */
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.05); /* Subtle shadow at the top */
  transition: all var(--transition-normal) ease; /* Smooth transition when sidebar collapses */
}

/* Footer Content */
.admin-footer-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-wrap: wrap;
  gap: var(--spacing-4);
  max-width: var(--content-max-width);
  margin: 0 auto; /* Center the content */
  width: 100%; /* Ensure full width */
  padding: 0 var(--spacing-2); /* Add padding to prevent text from touching edges */
}

/* Footer Copyright Section */
.admin-footer-copyright {
  flex: 1;
  min-width: 0; /* Enable text truncation */
  max-width: 100%; /* Ensure it doesn't overflow */
}

/* Footer Text */
.admin-footer-text {
  font-size: var(--font-size-xs);
  color: var(--text-light);
  margin: 0;
  line-height: 1.5; /* Improved readability */
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis; /* Add ellipsis for long text */
  max-width: 100%; /* Ensure text doesn't overflow container */
}

/* Footer Links */
.admin-footer-links {
  display: flex;
  align-items: center;
  gap: var(--spacing-3); /* Reduced gap for better fit */
  flex-shrink: 0; /* Prevent links from shrinking */
  flex-wrap: nowrap; /* Keep links in a single row */
  overflow-x: auto; /* Allow horizontal scrolling if needed */
  -ms-overflow-style: none; /* Hide scrollbar in IE and Edge */
  scrollbar-width: none; /* Hide scrollbar in Firefox */
  padding-bottom: 2px; /* Space for potential scrollbar */
}

/* Hide scrollbar but allow scrolling */
.admin-footer-links::-webkit-scrollbar {
  display: none;
}

.admin-footer-link {
  font-size: var(--font-size-xs);
  color: var(--text-light);
  text-decoration: none;
  transition: all var(--transition-fast) ease;
  padding: var(--spacing-1) var(--spacing-2); /* Add padding for better clickability */
  border-radius: var(--radius-sm); /* Rounded corners */
  white-space: nowrap; /* Prevent text wrapping */
  display: inline-block; /* Ensure proper sizing */
}

.admin-footer-link:hover {
  color: var(--primary-color);
  background-color: rgba(var(--primary-rgb), 0.05); /* Subtle background on hover */
}

/* Footer with Version */
.admin-footer-version {
  font-size: var(--font-size-xs);
  color: var(--text-muted);
  padding: var(--spacing-1) var(--spacing-2);
  background-color: var(--background-light);
  border-radius: var(--radius-sm);
}

/* Footer with Social Icons */
.admin-footer-social {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
}

.admin-footer-social-link {
  width: 32px; /* Slightly larger */
  height: 32px; /* Slightly larger */
  border-radius: var(--radius-full);
  background-color: var(--gray-100);
  color: var(--text-light);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all var(--transition-fast) ease;
  border: 1px solid transparent; /* For hover effect */
}

.admin-footer-social-link:hover {
  background-color: var(--primary-color);
  color: var(--secondary-dark);
  transform: translateY(-2px); /* Slight lift on hover */
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1); /* Shadow on hover */
}

/* Footer with Logo */
.admin-footer-logo {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
}

.admin-footer-logo img {
  height: 24px;
  width: auto;
  transition: all var(--transition-fast) ease;
}

.admin-footer-logo:hover img {
  transform: scale(1.05); /* Slight scale on hover */
}

.admin-footer-logo-text {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--text-dark);
}

/* Sticky Footer - Ensure footer stays at bottom */
html, body {
  height: 100%;
  margin: 0;
}

.admin-wrapper {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.admin-main {
  flex: 1 0 auto; /* Flex grow, no shrink, auto basis */
  display: flex;
  flex-direction: column;
}

/* Ensure content fills available space */
.admin-container {
  flex: 1 0 auto; /* Make container grow to fill space */
  display: flex;
  flex-direction: column;
}

/* Responsive Styles */
@media (max-width: 992px) {
  .admin-footer {
    padding: var(--spacing-3);
  }

  .admin-footer-content {
    gap: var(--spacing-3);
    padding: 0 var(--spacing-3); /* Increased padding for better spacing */
  }

  /* Adjust for sidebar collapsed state */
  body.sidebar-collapsed .admin-footer-content {
    max-width: calc(100% - var(--spacing-4)); /* Ensure content fits */
  }
}

@media (max-width: 768px) {
  .admin-footer {
    padding: var(--spacing-3) var(--spacing-2);
    margin-top: var(--spacing-4); /* Add some space above footer on mobile */
  }

  .admin-footer-content {
    flex-direction: row; /* Keep horizontal layout on tablets */
    align-items: center;
    gap: var(--spacing-3);
    padding: 0 var(--spacing-2);
  }

  .admin-footer-copyright {
    max-width: 60%; /* Limit width to ensure text doesn't overflow */
  }

  .admin-footer-text {
    max-width: 100%; /* Ensure text fits within container */
  }

  .admin-footer-links {
    justify-content: flex-end;
    gap: var(--spacing-2);
  }

  .admin-footer-social {
    margin-top: 0;
  }

  /* Adjust for double-height topbar */
  .admin-main {
    padding-bottom: var(--spacing-4); /* Add padding at the bottom for mobile */
  }

  /* Ensure footer is visible with sidebar expanded */
  body:not(.sidebar-collapsed) .admin-footer-content {
    padding-left: var(--spacing-3);
  }
}

@media (max-width: 576px) {
  .admin-footer {
    padding: var(--spacing-2);
  }

  .admin-footer-content {
    flex-direction: column; /* Stack vertically on mobile */
    align-items: center;
    text-align: center;
    gap: var(--spacing-2);
  }

  .admin-footer-copyright {
    max-width: 100%;
    width: 100%;
    text-align: center;
  }

  .admin-footer-text {
    font-size: 11px; /* Smaller text on very small screens */
    text-align: center;
    margin: 0 auto;
  }

  .admin-footer-links {
    width: 100%;
    justify-content: center;
    margin-top: var(--spacing-2);
  }

  .admin-footer-link {
    font-size: 11px;
    padding: var(--spacing-1);
  }

  .admin-footer-social-link {
    width: 28px;
    height: 28px;
  }
}

/* Specific adjustments for sidebar states */
body.sidebar-collapsed .admin-footer {
  width: calc(100% - var(--sidebar-collapsed-width));
  margin-left: var(--sidebar-collapsed-width);
}

/* Ensure footer is properly positioned in mobile view */
@media (max-width: 768px) {
  .admin-footer {
    left: 0 !important;
    width: 100% !important;
    margin-left: 0 !important;
  }

  .admin-footer-content {
    padding: 0 var(--spacing-3);
  }

  .admin-footer-copyright {
    max-width: 60%;
  }
}
